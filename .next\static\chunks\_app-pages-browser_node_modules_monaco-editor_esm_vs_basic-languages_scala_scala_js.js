"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_scala_scala_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/scala/scala.js":
/*!**************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/scala/scala.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/scala/scala.ts\nvar conf = {\n  /*\n   * `...` is allowed as an identifier.\n   * $ is allowed in identifiers.\n   * unary_<op> is allowed as an identifier.\n   * <name>_= is allowed as an identifier.\n   */\n  wordPattern: /(unary_[@~!#%^&*()\\-=+\\\\|:<>\\/?]+)|([a-zA-Z_$][\\w$]*?_=)|(`[^`]+`)|([a-zA-Z_$][\\w$]*)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?region\\\\b)|(?:<editor-fold\\\\b))\"),\n      end: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?endregion\\\\b)|(?:</editor-fold>))\")\n    }\n  }\n};\nvar language = {\n  tokenPostfix: \".scala\",\n  // We can't easily add everything from Dotty, but we can at least add some of its keywords\n  keywords: [\n    \"asInstanceOf\",\n    \"catch\",\n    \"class\",\n    \"classOf\",\n    \"def\",\n    \"do\",\n    \"else\",\n    \"extends\",\n    \"finally\",\n    \"for\",\n    \"foreach\",\n    \"forSome\",\n    \"if\",\n    \"import\",\n    \"isInstanceOf\",\n    \"macro\",\n    \"match\",\n    \"new\",\n    \"object\",\n    \"package\",\n    \"return\",\n    \"throw\",\n    \"trait\",\n    \"try\",\n    \"type\",\n    \"until\",\n    \"val\",\n    \"var\",\n    \"while\",\n    \"with\",\n    \"yield\",\n    // Dotty-specific:\n    \"given\",\n    \"enum\",\n    \"then\"\n  ],\n  // Dotty-specific:\n  softKeywords: [\"as\", \"export\", \"extension\", \"end\", \"derives\", \"on\"],\n  constants: [\"true\", \"false\", \"null\", \"this\", \"super\"],\n  modifiers: [\n    \"abstract\",\n    \"final\",\n    \"implicit\",\n    \"lazy\",\n    \"override\",\n    \"private\",\n    \"protected\",\n    \"sealed\"\n  ],\n  // Dotty-specific:\n  softModifiers: [\"inline\", \"opaque\", \"open\", \"transparent\", \"using\"],\n  name: /(?:[a-z_$][\\w$]*|`[^`]+`)/,\n  type: /(?:[A-Z][\\w$]*)/,\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/^\\\\%@#]+/,\n  digits: /\\d+(_+\\d+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  // C# style strings\n  escapes: /\\\\(?:[btnfr\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  fstring_conv: /[bBhHsScCdoxXeEfgGaAt]|[Tn](?:[HIklMSLNpzZsQ]|[BbhAaCYyjmde]|[RTrDFC])/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // strings\n      [/\\braw\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@rawstringt\" }],\n      [/\\braw\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@rawstring\" }],\n      [/\\bs\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@sstringt\" }],\n      [/\\bs\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@sstring\" }],\n      [/\\bf\"\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@fstringt\" }],\n      [/\\bf\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@fstring\" }],\n      [/\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@stringt\" }],\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }],\n      // numbers\n      [/(@digits)[eE]([\\-+]?(@digits))?[fFdD]?/, \"number.float\", \"@allowMethod\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?[fFdD]?/, \"number.float\", \"@allowMethod\"],\n      [/0[xX](@hexdigits)[Ll]?/, \"number.hex\", \"@allowMethod\"],\n      [/(@digits)[fFdD]/, \"number.float\", \"@allowMethod\"],\n      [/(@digits)[lL]?/, \"number\", \"@allowMethod\"],\n      [/\\b_\\*/, \"key\"],\n      [/\\b(_)\\b/, \"keyword\", \"@allowMethod\"],\n      // identifiers and keywords\n      [/\\bimport\\b/, \"keyword\", \"@import\"],\n      [/\\b(case)([ \\t]+)(class)\\b/, [\"keyword.modifier\", \"white\", \"keyword\"]],\n      [/\\bcase\\b/, \"keyword\", \"@case\"],\n      [/\\bva[lr]\\b/, \"keyword\", \"@vardef\"],\n      [\n        /\\b(def)([ \\t]+)((?:unary_)?@symbols|@name(?:_=)|@name)/,\n        [\"keyword\", \"white\", \"identifier\"]\n      ],\n      [/@name(?=[ \\t]*:(?!:))/, \"variable\"],\n      [/(\\.)(@name|@symbols)/, [\"operator\", { token: \"@rematch\", next: \"@allowMethod\" }]],\n      [/([{(])(\\s*)(@name(?=\\s*=>))/, [\"@brackets\", \"white\", \"variable\"]],\n      [\n        /@name/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@softKeywords\": \"keyword\",\n            \"@modifiers\": \"keyword.modifier\",\n            \"@softModifiers\": \"keyword.modifier\",\n            \"@constants\": {\n              token: \"constant\",\n              next: \"@allowMethod\"\n            },\n            \"@default\": {\n              token: \"identifier\",\n              next: \"@allowMethod\"\n            }\n          }\n        }\n      ],\n      [/@type/, \"type\", \"@allowMethod\"],\n      // whitespace\n      { include: \"@whitespace\" },\n      // @ annotations.\n      [/@[a-zA-Z_$][\\w$]*(?:\\.[a-zA-Z_$][\\w$]*)*/, \"annotation\"],\n      // delimiters and operators\n      [/[{(]/, \"@brackets\"],\n      [/[})]/, \"@brackets\", \"@allowMethod\"],\n      [/\\[/, \"operator.square\"],\n      [/](?!\\s*(?:va[rl]|def|type)\\b)/, \"operator.square\", \"@allowMethod\"],\n      [/]/, \"operator.square\"],\n      [/([=-]>|<-|>:|<:|:>|<%)(?=[\\s\\w()[\\]{},\\.\"'`])/, \"keyword\"],\n      [/@symbols/, \"operator\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,\\.]/, \"delimiter\"],\n      // symbols\n      [/'[a-zA-Z$][\\w$]*(?!')/, \"attribute.name\"],\n      // characters\n      [/'[^\\\\']'/, \"string\", \"@allowMethod\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", { token: \"string\", next: \"@allowMethod\" }]],\n      [/'/, \"string.invalid\"]\n    ],\n    import: [\n      [/;/, \"delimiter\", \"@pop\"],\n      [/^|$/, \"\", \"@pop\"],\n      [/[ \\t]+/, \"white\"],\n      [/[\\n\\r]+/, \"white\", \"@pop\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/@name|@type/, \"type\"],\n      [/[(){}]/, \"@brackets\"],\n      [/[[\\]]/, \"operator.square\"],\n      [/[\\.,]/, \"delimiter\"]\n    ],\n    allowMethod: [\n      [/^|$/, \"\", \"@pop\"],\n      [/[ \\t]+/, \"white\"],\n      [/[\\n\\r]+/, \"white\", \"@pop\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/(?==>[\\s\\w([{])/, \"keyword\", \"@pop\"],\n      [\n        /(@name|@symbols)(?=[ \\t]*[[({\"'`]|[ \\t]+(?:[+-]?\\.?\\d|\\w))/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword\", next: \"@pop\" },\n            \"->|<-|>:|<:|<%\": { token: \"keyword\", next: \"@pop\" },\n            \"@default\": { token: \"@rematch\", next: \"@pop\" }\n          }\n        }\n      ],\n      [\"\", \"\", \"@pop\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@push\"],\n      // nested comment\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    case: [\n      [/\\b_\\*/, \"key\"],\n      [/\\b(_|true|false|null|this|super)\\b/, \"keyword\", \"@allowMethod\"],\n      [/\\bif\\b|=>/, \"keyword\", \"@pop\"],\n      [/`[^`]+`/, \"identifier\", \"@allowMethod\"],\n      [/@name/, \"variable\", \"@allowMethod\"],\n      [/:::?|\\||@(?![a-z_$])/, \"keyword\"],\n      { include: \"@root\" }\n    ],\n    vardef: [\n      [/\\b_\\*/, \"key\"],\n      [/\\b(_|true|false|null|this|super)\\b/, \"keyword\"],\n      [/@name/, \"variable\"],\n      [/:::?|\\||@(?![a-z_$])/, \"keyword\"],\n      [/=|:(?!:)/, \"operator\", \"@pop\"],\n      [/$/, \"white\", \"@pop\"],\n      { include: \"@root\" }\n    ],\n    string: [\n      [/[^\\\\\"\\n\\r]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ]\n    ],\n    stringt: [\n      [/[^\\\\\"\\n\\r]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\"/, \"string\"]\n    ],\n    fstring: [\n      [/@escapes/, \"string.escape\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/%%/, \"string\"],\n      [\n        /(%)([\\-#+ 0,(])(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/,\n        [\"metatag\", \"keyword.modifier\", \"number\", \"metatag\"]\n      ],\n      [/(%)(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/, [\"metatag\", \"number\", \"metatag\"]],\n      [/(%)([\\-#+ 0,(])(@fstring_conv)/, [\"metatag\", \"keyword.modifier\", \"metatag\"]],\n      [/(%)(@fstring_conv)/, [\"metatag\", \"metatag\"]],\n      [/./, \"string\"]\n    ],\n    fstringt: [\n      [/@escapes/, \"string.escape\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/%%/, \"string\"],\n      [\n        /(%)([\\-#+ 0,(])(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/,\n        [\"metatag\", \"keyword.modifier\", \"number\", \"metatag\"]\n      ],\n      [/(%)(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/, [\"metatag\", \"number\", \"metatag\"]],\n      [/(%)([\\-#+ 0,(])(@fstring_conv)/, [\"metatag\", \"keyword.modifier\", \"metatag\"]],\n      [/(%)(@fstring_conv)/, [\"metatag\", \"metatag\"]],\n      [/./, \"string\"]\n    ],\n    sstring: [\n      [/@escapes/, \"string.escape\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/./, \"string\"]\n    ],\n    sstringt: [\n      [/@escapes/, \"string.escape\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/./, \"string\"]\n    ],\n    interp: [[/{/, \"operator\", \"@push\"], [/}/, \"operator\", \"@pop\"], { include: \"@root\" }],\n    rawstring: [\n      [/[^\"]/, \"string\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ]\n    ],\n    rawstringt: [\n      [/[^\"]/, \"string\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\"/, \"string\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb25hY28tZWRpdG9yL2VzbS92cy9iYXNpYy1sYW5ndWFnZXMvc2NhbGEvc2NhbGEuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQUdBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLE9BQU8sS0FBSztBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSxRQUFRLFlBQVksR0FBRztBQUM3QixNQUFNLHVCQUF1QjtBQUM3QixNQUFNLHVCQUF1QjtBQUM3QixNQUFNLHVCQUF1QjtBQUM3QixNQUFNO0FBQ047QUFDQTtBQUNBLE1BQU0sUUFBUSxZQUFZLEdBQUc7QUFDN0IsTUFBTSx1QkFBdUI7QUFDN0IsTUFBTSx1QkFBdUI7QUFDN0IsTUFBTSx1QkFBdUI7QUFDN0IsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLElBQUksY0FBYyxFQUFFLGNBQWMsRUFBRTtBQUM5RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLDhEQUE4RDtBQUNuRixtQkFBbUIsNkRBQTZEO0FBQ2hGLG1CQUFtQiw0REFBNEQ7QUFDL0UsaUJBQWlCLDJEQUEyRDtBQUM1RSxvQkFBb0IsNERBQTREO0FBQ2hGLGlCQUFpQiwyREFBMkQ7QUFDNUUsZ0JBQWdCLDJEQUEyRDtBQUMzRSxjQUFjLDBEQUEwRDtBQUN4RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEMseUNBQXlDO0FBQ3ZGLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsd0JBQXdCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVixVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5REFBeUQsdUNBQXVDO0FBQ2hHO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxzQ0FBc0M7QUFDdEM7QUFDQTtBQUNBLDJCQUEyQixnQ0FBZ0M7QUFDM0QsZ0NBQWdDLGdDQUFnQztBQUNoRSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxnQkFBZ0IsNEJBQTRCLDBCQUEwQixrQkFBa0I7QUFDeEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFJRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbW9uYWNvLWVkaXRvci9lc20vdnMvYmFzaWMtbGFuZ3VhZ2VzL3NjYWxhL3NjYWxhLmpzPzg2NjYiXSwic291cmNlc0NvbnRlbnQiOlsiLyohLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICogVmVyc2lvbjogMC41Mi4yKDQwNDU0NWJkZWQxZGY2ZmZhNDFlYTBhZjRlOGRkYjIxOTAxOGM2YzEpXG4gKiBSZWxlYXNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2VcbiAqIGh0dHBzOi8vZ2l0aHViLmNvbS9taWNyb3NvZnQvbW9uYWNvLWVkaXRvci9ibG9iL21haW4vTElDRU5TRS50eHRcbiAqLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5cbi8vIHNyYy9iYXNpYy1sYW5ndWFnZXMvc2NhbGEvc2NhbGEudHNcbnZhciBjb25mID0ge1xuICAvKlxuICAgKiBgLi4uYCBpcyBhbGxvd2VkIGFzIGFuIGlkZW50aWZpZXIuXG4gICAqICQgaXMgYWxsb3dlZCBpbiBpZGVudGlmaWVycy5cbiAgICogdW5hcnlfPG9wPiBpcyBhbGxvd2VkIGFzIGFuIGlkZW50aWZpZXIuXG4gICAqIDxuYW1lPl89IGlzIGFsbG93ZWQgYXMgYW4gaWRlbnRpZmllci5cbiAgICovXG4gIHdvcmRQYXR0ZXJuOiAvKHVuYXJ5X1tAfiEjJV4mKigpXFwtPStcXFxcfDo8PlxcLz9dKyl8KFthLXpBLVpfJF1bXFx3JF0qP189KXwoYFteYF0rYCl8KFthLXpBLVpfJF1bXFx3JF0qKS9nLFxuICBjb21tZW50czoge1xuICAgIGxpbmVDb21tZW50OiBcIi8vXCIsXG4gICAgYmxvY2tDb21tZW50OiBbXCIvKlwiLCBcIiovXCJdXG4gIH0sXG4gIGJyYWNrZXRzOiBbXG4gICAgW1wie1wiLCBcIn1cIl0sXG4gICAgW1wiW1wiLCBcIl1cIl0sXG4gICAgW1wiKFwiLCBcIilcIl1cbiAgXSxcbiAgYXV0b0Nsb3NpbmdQYWlyczogW1xuICAgIHsgb3BlbjogXCJ7XCIsIGNsb3NlOiBcIn1cIiB9LFxuICAgIHsgb3BlbjogXCJbXCIsIGNsb3NlOiBcIl1cIiB9LFxuICAgIHsgb3BlbjogXCIoXCIsIGNsb3NlOiBcIilcIiB9LFxuICAgIHsgb3BlbjogJ1wiJywgY2xvc2U6ICdcIicgfSxcbiAgICB7IG9wZW46IFwiJ1wiLCBjbG9zZTogXCInXCIgfVxuICBdLFxuICBzdXJyb3VuZGluZ1BhaXJzOiBbXG4gICAgeyBvcGVuOiBcIntcIiwgY2xvc2U6IFwifVwiIH0sXG4gICAgeyBvcGVuOiBcIltcIiwgY2xvc2U6IFwiXVwiIH0sXG4gICAgeyBvcGVuOiBcIihcIiwgY2xvc2U6IFwiKVwiIH0sXG4gICAgeyBvcGVuOiAnXCInLCBjbG9zZTogJ1wiJyB9LFxuICAgIHsgb3BlbjogXCInXCIsIGNsb3NlOiBcIidcIiB9XG4gIF0sXG4gIGZvbGRpbmc6IHtcbiAgICBtYXJrZXJzOiB7XG4gICAgICBzdGFydDogbmV3IFJlZ0V4cChcIl5cXFxccyovL1xcXFxzKig/Oig/OiM/cmVnaW9uXFxcXGIpfCg/OjxlZGl0b3ItZm9sZFxcXFxiKSlcIiksXG4gICAgICBlbmQ6IG5ldyBSZWdFeHAoXCJeXFxcXHMqLy9cXFxccyooPzooPzojP2VuZHJlZ2lvblxcXFxiKXwoPzo8L2VkaXRvci1mb2xkPikpXCIpXG4gICAgfVxuICB9XG59O1xudmFyIGxhbmd1YWdlID0ge1xuICB0b2tlblBvc3RmaXg6IFwiLnNjYWxhXCIsXG4gIC8vIFdlIGNhbid0IGVhc2lseSBhZGQgZXZlcnl0aGluZyBmcm9tIERvdHR5LCBidXQgd2UgY2FuIGF0IGxlYXN0IGFkZCBzb21lIG9mIGl0cyBrZXl3b3Jkc1xuICBrZXl3b3JkczogW1xuICAgIFwiYXNJbnN0YW5jZU9mXCIsXG4gICAgXCJjYXRjaFwiLFxuICAgIFwiY2xhc3NcIixcbiAgICBcImNsYXNzT2ZcIixcbiAgICBcImRlZlwiLFxuICAgIFwiZG9cIixcbiAgICBcImVsc2VcIixcbiAgICBcImV4dGVuZHNcIixcbiAgICBcImZpbmFsbHlcIixcbiAgICBcImZvclwiLFxuICAgIFwiZm9yZWFjaFwiLFxuICAgIFwiZm9yU29tZVwiLFxuICAgIFwiaWZcIixcbiAgICBcImltcG9ydFwiLFxuICAgIFwiaXNJbnN0YW5jZU9mXCIsXG4gICAgXCJtYWNyb1wiLFxuICAgIFwibWF0Y2hcIixcbiAgICBcIm5ld1wiLFxuICAgIFwib2JqZWN0XCIsXG4gICAgXCJwYWNrYWdlXCIsXG4gICAgXCJyZXR1cm5cIixcbiAgICBcInRocm93XCIsXG4gICAgXCJ0cmFpdFwiLFxuICAgIFwidHJ5XCIsXG4gICAgXCJ0eXBlXCIsXG4gICAgXCJ1bnRpbFwiLFxuICAgIFwidmFsXCIsXG4gICAgXCJ2YXJcIixcbiAgICBcIndoaWxlXCIsXG4gICAgXCJ3aXRoXCIsXG4gICAgXCJ5aWVsZFwiLFxuICAgIC8vIERvdHR5LXNwZWNpZmljOlxuICAgIFwiZ2l2ZW5cIixcbiAgICBcImVudW1cIixcbiAgICBcInRoZW5cIlxuICBdLFxuICAvLyBEb3R0eS1zcGVjaWZpYzpcbiAgc29mdEtleXdvcmRzOiBbXCJhc1wiLCBcImV4cG9ydFwiLCBcImV4dGVuc2lvblwiLCBcImVuZFwiLCBcImRlcml2ZXNcIiwgXCJvblwiXSxcbiAgY29uc3RhbnRzOiBbXCJ0cnVlXCIsIFwiZmFsc2VcIiwgXCJudWxsXCIsIFwidGhpc1wiLCBcInN1cGVyXCJdLFxuICBtb2RpZmllcnM6IFtcbiAgICBcImFic3RyYWN0XCIsXG4gICAgXCJmaW5hbFwiLFxuICAgIFwiaW1wbGljaXRcIixcbiAgICBcImxhenlcIixcbiAgICBcIm92ZXJyaWRlXCIsXG4gICAgXCJwcml2YXRlXCIsXG4gICAgXCJwcm90ZWN0ZWRcIixcbiAgICBcInNlYWxlZFwiXG4gIF0sXG4gIC8vIERvdHR5LXNwZWNpZmljOlxuICBzb2Z0TW9kaWZpZXJzOiBbXCJpbmxpbmVcIiwgXCJvcGFxdWVcIiwgXCJvcGVuXCIsIFwidHJhbnNwYXJlbnRcIiwgXCJ1c2luZ1wiXSxcbiAgbmFtZTogLyg/OlthLXpfJF1bXFx3JF0qfGBbXmBdK2ApLyxcbiAgdHlwZTogLyg/OltBLVpdW1xcdyRdKikvLFxuICAvLyB3ZSBpbmNsdWRlIHRoZXNlIGNvbW1vbiByZWd1bGFyIGV4cHJlc3Npb25zXG4gIHN5bWJvbHM6IC9bPT48IX4/OiZ8K1xcLSpcXC9eXFxcXCVAI10rLyxcbiAgZGlnaXRzOiAvXFxkKyhfK1xcZCspKi8sXG4gIGhleGRpZ2l0czogL1tbMC05YS1mQS1GXSsoXytbMC05YS1mQS1GXSspKi8sXG4gIC8vIEMjIHN0eWxlIHN0cmluZ3NcbiAgZXNjYXBlczogL1xcXFwoPzpbYnRuZnJcXFxcXCInXXx4WzAtOUEtRmEtZl17MSw0fXx1WzAtOUEtRmEtZl17NH18VVswLTlBLUZhLWZdezh9KS8sXG4gIGZzdHJpbmdfY29udjogL1tiQmhIc1NjQ2RveFhlRWZnR2FBdF18W1RuXSg/OltISWtsTVNMTnB6WnNRXXxbQmJoQWFDWXlqbWRlXXxbUlRyREZDXSkvLFxuICAvLyBUaGUgbWFpbiB0b2tlbml6ZXIgZm9yIG91ciBsYW5ndWFnZXNcbiAgdG9rZW5pemVyOiB7XG4gICAgcm9vdDogW1xuICAgICAgLy8gc3RyaW5nc1xuICAgICAgWy9cXGJyYXdcIlwiXCIvLCB7IHRva2VuOiBcInN0cmluZy5xdW90ZVwiLCBicmFja2V0OiBcIkBvcGVuXCIsIG5leHQ6IFwiQHJhd3N0cmluZ3RcIiB9XSxcbiAgICAgIFsvXFxicmF3XCIvLCB7IHRva2VuOiBcInN0cmluZy5xdW90ZVwiLCBicmFja2V0OiBcIkBvcGVuXCIsIG5leHQ6IFwiQHJhd3N0cmluZ1wiIH1dLFxuICAgICAgWy9cXGJzXCJcIlwiLywgeyB0b2tlbjogXCJzdHJpbmcucXVvdGVcIiwgYnJhY2tldDogXCJAb3BlblwiLCBuZXh0OiBcIkBzc3RyaW5ndFwiIH1dLFxuICAgICAgWy9cXGJzXCIvLCB7IHRva2VuOiBcInN0cmluZy5xdW90ZVwiLCBicmFja2V0OiBcIkBvcGVuXCIsIG5leHQ6IFwiQHNzdHJpbmdcIiB9XSxcbiAgICAgIFsvXFxiZlwiXCJcIlwiLywgeyB0b2tlbjogXCJzdHJpbmcucXVvdGVcIiwgYnJhY2tldDogXCJAb3BlblwiLCBuZXh0OiBcIkBmc3RyaW5ndFwiIH1dLFxuICAgICAgWy9cXGJmXCIvLCB7IHRva2VuOiBcInN0cmluZy5xdW90ZVwiLCBicmFja2V0OiBcIkBvcGVuXCIsIG5leHQ6IFwiQGZzdHJpbmdcIiB9XSxcbiAgICAgIFsvXCJcIlwiLywgeyB0b2tlbjogXCJzdHJpbmcucXVvdGVcIiwgYnJhY2tldDogXCJAb3BlblwiLCBuZXh0OiBcIkBzdHJpbmd0XCIgfV0sXG4gICAgICBbL1wiLywgeyB0b2tlbjogXCJzdHJpbmcucXVvdGVcIiwgYnJhY2tldDogXCJAb3BlblwiLCBuZXh0OiBcIkBzdHJpbmdcIiB9XSxcbiAgICAgIC8vIG51bWJlcnNcbiAgICAgIFsvKEBkaWdpdHMpW2VFXShbXFwtK10/KEBkaWdpdHMpKT9bZkZkRF0/LywgXCJudW1iZXIuZmxvYXRcIiwgXCJAYWxsb3dNZXRob2RcIl0sXG4gICAgICBbLyhAZGlnaXRzKVxcLihAZGlnaXRzKShbZUVdW1xcLStdPyhAZGlnaXRzKSk/W2ZGZERdPy8sIFwibnVtYmVyLmZsb2F0XCIsIFwiQGFsbG93TWV0aG9kXCJdLFxuICAgICAgWy8wW3hYXShAaGV4ZGlnaXRzKVtMbF0/LywgXCJudW1iZXIuaGV4XCIsIFwiQGFsbG93TWV0aG9kXCJdLFxuICAgICAgWy8oQGRpZ2l0cylbZkZkRF0vLCBcIm51bWJlci5mbG9hdFwiLCBcIkBhbGxvd01ldGhvZFwiXSxcbiAgICAgIFsvKEBkaWdpdHMpW2xMXT8vLCBcIm51bWJlclwiLCBcIkBhbGxvd01ldGhvZFwiXSxcbiAgICAgIFsvXFxiX1xcKi8sIFwia2V5XCJdLFxuICAgICAgWy9cXGIoXylcXGIvLCBcImtleXdvcmRcIiwgXCJAYWxsb3dNZXRob2RcIl0sXG4gICAgICAvLyBpZGVudGlmaWVycyBhbmQga2V5d29yZHNcbiAgICAgIFsvXFxiaW1wb3J0XFxiLywgXCJrZXl3b3JkXCIsIFwiQGltcG9ydFwiXSxcbiAgICAgIFsvXFxiKGNhc2UpKFsgXFx0XSspKGNsYXNzKVxcYi8sIFtcImtleXdvcmQubW9kaWZpZXJcIiwgXCJ3aGl0ZVwiLCBcImtleXdvcmRcIl1dLFxuICAgICAgWy9cXGJjYXNlXFxiLywgXCJrZXl3b3JkXCIsIFwiQGNhc2VcIl0sXG4gICAgICBbL1xcYnZhW2xyXVxcYi8sIFwia2V5d29yZFwiLCBcIkB2YXJkZWZcIl0sXG4gICAgICBbXG4gICAgICAgIC9cXGIoZGVmKShbIFxcdF0rKSgoPzp1bmFyeV8pP0BzeW1ib2xzfEBuYW1lKD86Xz0pfEBuYW1lKS8sXG4gICAgICAgIFtcImtleXdvcmRcIiwgXCJ3aGl0ZVwiLCBcImlkZW50aWZpZXJcIl1cbiAgICAgIF0sXG4gICAgICBbL0BuYW1lKD89WyBcXHRdKjooPyE6KSkvLCBcInZhcmlhYmxlXCJdLFxuICAgICAgWy8oXFwuKShAbmFtZXxAc3ltYm9scykvLCBbXCJvcGVyYXRvclwiLCB7IHRva2VuOiBcIkByZW1hdGNoXCIsIG5leHQ6IFwiQGFsbG93TWV0aG9kXCIgfV1dLFxuICAgICAgWy8oW3soXSkoXFxzKikoQG5hbWUoPz1cXHMqPT4pKS8sIFtcIkBicmFja2V0c1wiLCBcIndoaXRlXCIsIFwidmFyaWFibGVcIl1dLFxuICAgICAgW1xuICAgICAgICAvQG5hbWUvLFxuICAgICAgICB7XG4gICAgICAgICAgY2FzZXM6IHtcbiAgICAgICAgICAgIFwiQGtleXdvcmRzXCI6IFwia2V5d29yZFwiLFxuICAgICAgICAgICAgXCJAc29mdEtleXdvcmRzXCI6IFwia2V5d29yZFwiLFxuICAgICAgICAgICAgXCJAbW9kaWZpZXJzXCI6IFwia2V5d29yZC5tb2RpZmllclwiLFxuICAgICAgICAgICAgXCJAc29mdE1vZGlmaWVyc1wiOiBcImtleXdvcmQubW9kaWZpZXJcIixcbiAgICAgICAgICAgIFwiQGNvbnN0YW50c1wiOiB7XG4gICAgICAgICAgICAgIHRva2VuOiBcImNvbnN0YW50XCIsXG4gICAgICAgICAgICAgIG5leHQ6IFwiQGFsbG93TWV0aG9kXCJcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBcIkBkZWZhdWx0XCI6IHtcbiAgICAgICAgICAgICAgdG9rZW46IFwiaWRlbnRpZmllclwiLFxuICAgICAgICAgICAgICBuZXh0OiBcIkBhbGxvd01ldGhvZFwiXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgWy9AdHlwZS8sIFwidHlwZVwiLCBcIkBhbGxvd01ldGhvZFwiXSxcbiAgICAgIC8vIHdoaXRlc3BhY2VcbiAgICAgIHsgaW5jbHVkZTogXCJAd2hpdGVzcGFjZVwiIH0sXG4gICAgICAvLyBAIGFubm90YXRpb25zLlxuICAgICAgWy9AW2EtekEtWl8kXVtcXHckXSooPzpcXC5bYS16QS1aXyRdW1xcdyRdKikqLywgXCJhbm5vdGF0aW9uXCJdLFxuICAgICAgLy8gZGVsaW1pdGVycyBhbmQgb3BlcmF0b3JzXG4gICAgICBbL1t7KF0vLCBcIkBicmFja2V0c1wiXSxcbiAgICAgIFsvW30pXS8sIFwiQGJyYWNrZXRzXCIsIFwiQGFsbG93TWV0aG9kXCJdLFxuICAgICAgWy9cXFsvLCBcIm9wZXJhdG9yLnNxdWFyZVwiXSxcbiAgICAgIFsvXSg/IVxccyooPzp2YVtybF18ZGVmfHR5cGUpXFxiKS8sIFwib3BlcmF0b3Iuc3F1YXJlXCIsIFwiQGFsbG93TWV0aG9kXCJdLFxuICAgICAgWy9dLywgXCJvcGVyYXRvci5zcXVhcmVcIl0sXG4gICAgICBbLyhbPS1dPnw8LXw+Onw8Onw6Pnw8JSkoPz1bXFxzXFx3KClbXFxde30sXFwuXCInYF0pLywgXCJrZXl3b3JkXCJdLFxuICAgICAgWy9Ac3ltYm9scy8sIFwib3BlcmF0b3JcIl0sXG4gICAgICAvLyBkZWxpbWl0ZXI6IGFmdGVyIG51bWJlciBiZWNhdXNlIG9mIC5cXGQgZmxvYXRzXG4gICAgICBbL1s7LFxcLl0vLCBcImRlbGltaXRlclwiXSxcbiAgICAgIC8vIHN5bWJvbHNcbiAgICAgIFsvJ1thLXpBLVokXVtcXHckXSooPyEnKS8sIFwiYXR0cmlidXRlLm5hbWVcIl0sXG4gICAgICAvLyBjaGFyYWN0ZXJzXG4gICAgICBbLydbXlxcXFwnXScvLCBcInN0cmluZ1wiLCBcIkBhbGxvd01ldGhvZFwiXSxcbiAgICAgIFsvKCcpKEBlc2NhcGVzKSgnKS8sIFtcInN0cmluZ1wiLCBcInN0cmluZy5lc2NhcGVcIiwgeyB0b2tlbjogXCJzdHJpbmdcIiwgbmV4dDogXCJAYWxsb3dNZXRob2RcIiB9XV0sXG4gICAgICBbLycvLCBcInN0cmluZy5pbnZhbGlkXCJdXG4gICAgXSxcbiAgICBpbXBvcnQ6IFtcbiAgICAgIFsvOy8sIFwiZGVsaW1pdGVyXCIsIFwiQHBvcFwiXSxcbiAgICAgIFsvXnwkLywgXCJcIiwgXCJAcG9wXCJdLFxuICAgICAgWy9bIFxcdF0rLywgXCJ3aGl0ZVwiXSxcbiAgICAgIFsvW1xcblxccl0rLywgXCJ3aGl0ZVwiLCBcIkBwb3BcIl0sXG4gICAgICBbL1xcL1xcKi8sIFwiY29tbWVudFwiLCBcIkBjb21tZW50XCJdLFxuICAgICAgWy9AbmFtZXxAdHlwZS8sIFwidHlwZVwiXSxcbiAgICAgIFsvWygpe31dLywgXCJAYnJhY2tldHNcIl0sXG4gICAgICBbL1tbXFxdXS8sIFwib3BlcmF0b3Iuc3F1YXJlXCJdLFxuICAgICAgWy9bXFwuLF0vLCBcImRlbGltaXRlclwiXVxuICAgIF0sXG4gICAgYWxsb3dNZXRob2Q6IFtcbiAgICAgIFsvXnwkLywgXCJcIiwgXCJAcG9wXCJdLFxuICAgICAgWy9bIFxcdF0rLywgXCJ3aGl0ZVwiXSxcbiAgICAgIFsvW1xcblxccl0rLywgXCJ3aGl0ZVwiLCBcIkBwb3BcIl0sXG4gICAgICBbL1xcL1xcKi8sIFwiY29tbWVudFwiLCBcIkBjb21tZW50XCJdLFxuICAgICAgWy8oPz09PltcXHNcXHcoW3tdKS8sIFwia2V5d29yZFwiLCBcIkBwb3BcIl0sXG4gICAgICBbXG4gICAgICAgIC8oQG5hbWV8QHN5bWJvbHMpKD89WyBcXHRdKltbKHtcIidgXXxbIFxcdF0rKD86WystXT9cXC4/XFxkfFxcdykpLyxcbiAgICAgICAge1xuICAgICAgICAgIGNhc2VzOiB7XG4gICAgICAgICAgICBcIkBrZXl3b3Jkc1wiOiB7IHRva2VuOiBcImtleXdvcmRcIiwgbmV4dDogXCJAcG9wXCIgfSxcbiAgICAgICAgICAgIFwiLT58PC18Pjp8PDp8PCVcIjogeyB0b2tlbjogXCJrZXl3b3JkXCIsIG5leHQ6IFwiQHBvcFwiIH0sXG4gICAgICAgICAgICBcIkBkZWZhdWx0XCI6IHsgdG9rZW46IFwiQHJlbWF0Y2hcIiwgbmV4dDogXCJAcG9wXCIgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIFtcIlwiLCBcIlwiLCBcIkBwb3BcIl1cbiAgICBdLFxuICAgIGNvbW1lbnQ6IFtcbiAgICAgIFsvW15cXC8qXSsvLCBcImNvbW1lbnRcIl0sXG4gICAgICBbL1xcL1xcKi8sIFwiY29tbWVudFwiLCBcIkBwdXNoXCJdLFxuICAgICAgLy8gbmVzdGVkIGNvbW1lbnRcbiAgICAgIFsvXFwqXFwvLywgXCJjb21tZW50XCIsIFwiQHBvcFwiXSxcbiAgICAgIFsvW1xcLypdLywgXCJjb21tZW50XCJdXG4gICAgXSxcbiAgICBjYXNlOiBbXG4gICAgICBbL1xcYl9cXCovLCBcImtleVwiXSxcbiAgICAgIFsvXFxiKF98dHJ1ZXxmYWxzZXxudWxsfHRoaXN8c3VwZXIpXFxiLywgXCJrZXl3b3JkXCIsIFwiQGFsbG93TWV0aG9kXCJdLFxuICAgICAgWy9cXGJpZlxcYnw9Pi8sIFwia2V5d29yZFwiLCBcIkBwb3BcIl0sXG4gICAgICBbL2BbXmBdK2AvLCBcImlkZW50aWZpZXJcIiwgXCJAYWxsb3dNZXRob2RcIl0sXG4gICAgICBbL0BuYW1lLywgXCJ2YXJpYWJsZVwiLCBcIkBhbGxvd01ldGhvZFwiXSxcbiAgICAgIFsvOjo6P3xcXHx8QCg/IVthLXpfJF0pLywgXCJrZXl3b3JkXCJdLFxuICAgICAgeyBpbmNsdWRlOiBcIkByb290XCIgfVxuICAgIF0sXG4gICAgdmFyZGVmOiBbXG4gICAgICBbL1xcYl9cXCovLCBcImtleVwiXSxcbiAgICAgIFsvXFxiKF98dHJ1ZXxmYWxzZXxudWxsfHRoaXN8c3VwZXIpXFxiLywgXCJrZXl3b3JkXCJdLFxuICAgICAgWy9AbmFtZS8sIFwidmFyaWFibGVcIl0sXG4gICAgICBbLzo6Oj98XFx8fEAoPyFbYS16XyRdKS8sIFwia2V5d29yZFwiXSxcbiAgICAgIFsvPXw6KD8hOikvLCBcIm9wZXJhdG9yXCIsIFwiQHBvcFwiXSxcbiAgICAgIFsvJC8sIFwid2hpdGVcIiwgXCJAcG9wXCJdLFxuICAgICAgeyBpbmNsdWRlOiBcIkByb290XCIgfVxuICAgIF0sXG4gICAgc3RyaW5nOiBbXG4gICAgICBbL1teXFxcXFwiXFxuXFxyXSsvLCBcInN0cmluZ1wiXSxcbiAgICAgIFsvQGVzY2FwZXMvLCBcInN0cmluZy5lc2NhcGVcIl0sXG4gICAgICBbL1xcXFwuLywgXCJzdHJpbmcuZXNjYXBlLmludmFsaWRcIl0sXG4gICAgICBbXG4gICAgICAgIC9cIi8sXG4gICAgICAgIHtcbiAgICAgICAgICB0b2tlbjogXCJzdHJpbmcucXVvdGVcIixcbiAgICAgICAgICBicmFja2V0OiBcIkBjbG9zZVwiLFxuICAgICAgICAgIHN3aXRjaFRvOiBcIkBhbGxvd01ldGhvZFwiXG4gICAgICAgIH1cbiAgICAgIF1cbiAgICBdLFxuICAgIHN0cmluZ3Q6IFtcbiAgICAgIFsvW15cXFxcXCJcXG5cXHJdKy8sIFwic3RyaW5nXCJdLFxuICAgICAgWy9AZXNjYXBlcy8sIFwic3RyaW5nLmVzY2FwZVwiXSxcbiAgICAgIFsvXFxcXC4vLCBcInN0cmluZy5lc2NhcGUuaW52YWxpZFwiXSxcbiAgICAgIFsvXCIoPz1cIlwiXCIpLywgXCJzdHJpbmdcIl0sXG4gICAgICBbXG4gICAgICAgIC9cIlwiXCIvLFxuICAgICAgICB7XG4gICAgICAgICAgdG9rZW46IFwic3RyaW5nLnF1b3RlXCIsXG4gICAgICAgICAgYnJhY2tldDogXCJAY2xvc2VcIixcbiAgICAgICAgICBzd2l0Y2hUbzogXCJAYWxsb3dNZXRob2RcIlxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgWy9cIi8sIFwic3RyaW5nXCJdXG4gICAgXSxcbiAgICBmc3RyaW5nOiBbXG4gICAgICBbL0Blc2NhcGVzLywgXCJzdHJpbmcuZXNjYXBlXCJdLFxuICAgICAgW1xuICAgICAgICAvXCIvLFxuICAgICAgICB7XG4gICAgICAgICAgdG9rZW46IFwic3RyaW5nLnF1b3RlXCIsXG4gICAgICAgICAgYnJhY2tldDogXCJAY2xvc2VcIixcbiAgICAgICAgICBzd2l0Y2hUbzogXCJAYWxsb3dNZXRob2RcIlxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgWy9cXCRcXCQvLCBcInN0cmluZ1wiXSxcbiAgICAgIFsvKFxcJCkoW2Etel9dXFx3KikvLCBbXCJvcGVyYXRvclwiLCBcImlkZW50aWZpZXJcIl1dLFxuICAgICAgWy9cXCRcXHsvLCBcIm9wZXJhdG9yXCIsIFwiQGludGVycFwiXSxcbiAgICAgIFsvJSUvLCBcInN0cmluZ1wiXSxcbiAgICAgIFtcbiAgICAgICAgLyglKShbXFwtIysgMCwoXSkoXFxkK3xcXC5cXGQrfFxcZCtcXC5cXGQrKShAZnN0cmluZ19jb252KS8sXG4gICAgICAgIFtcIm1ldGF0YWdcIiwgXCJrZXl3b3JkLm1vZGlmaWVyXCIsIFwibnVtYmVyXCIsIFwibWV0YXRhZ1wiXVxuICAgICAgXSxcbiAgICAgIFsvKCUpKFxcZCt8XFwuXFxkK3xcXGQrXFwuXFxkKykoQGZzdHJpbmdfY29udikvLCBbXCJtZXRhdGFnXCIsIFwibnVtYmVyXCIsIFwibWV0YXRhZ1wiXV0sXG4gICAgICBbLyglKShbXFwtIysgMCwoXSkoQGZzdHJpbmdfY29udikvLCBbXCJtZXRhdGFnXCIsIFwia2V5d29yZC5tb2RpZmllclwiLCBcIm1ldGF0YWdcIl1dLFxuICAgICAgWy8oJSkoQGZzdHJpbmdfY29udikvLCBbXCJtZXRhdGFnXCIsIFwibWV0YXRhZ1wiXV0sXG4gICAgICBbLy4vLCBcInN0cmluZ1wiXVxuICAgIF0sXG4gICAgZnN0cmluZ3Q6IFtcbiAgICAgIFsvQGVzY2FwZXMvLCBcInN0cmluZy5lc2NhcGVcIl0sXG4gICAgICBbL1wiKD89XCJcIlwiKS8sIFwic3RyaW5nXCJdLFxuICAgICAgW1xuICAgICAgICAvXCJcIlwiLyxcbiAgICAgICAge1xuICAgICAgICAgIHRva2VuOiBcInN0cmluZy5xdW90ZVwiLFxuICAgICAgICAgIGJyYWNrZXQ6IFwiQGNsb3NlXCIsXG4gICAgICAgICAgc3dpdGNoVG86IFwiQGFsbG93TWV0aG9kXCJcbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIFsvXFwkXFwkLywgXCJzdHJpbmdcIl0sXG4gICAgICBbLyhcXCQpKFthLXpfXVxcdyopLywgW1wib3BlcmF0b3JcIiwgXCJpZGVudGlmaWVyXCJdXSxcbiAgICAgIFsvXFwkXFx7LywgXCJvcGVyYXRvclwiLCBcIkBpbnRlcnBcIl0sXG4gICAgICBbLyUlLywgXCJzdHJpbmdcIl0sXG4gICAgICBbXG4gICAgICAgIC8oJSkoW1xcLSMrIDAsKF0pKFxcZCt8XFwuXFxkK3xcXGQrXFwuXFxkKykoQGZzdHJpbmdfY29udikvLFxuICAgICAgICBbXCJtZXRhdGFnXCIsIFwia2V5d29yZC5tb2RpZmllclwiLCBcIm51bWJlclwiLCBcIm1ldGF0YWdcIl1cbiAgICAgIF0sXG4gICAgICBbLyglKShcXGQrfFxcLlxcZCt8XFxkK1xcLlxcZCspKEBmc3RyaW5nX2NvbnYpLywgW1wibWV0YXRhZ1wiLCBcIm51bWJlclwiLCBcIm1ldGF0YWdcIl1dLFxuICAgICAgWy8oJSkoW1xcLSMrIDAsKF0pKEBmc3RyaW5nX2NvbnYpLywgW1wibWV0YXRhZ1wiLCBcImtleXdvcmQubW9kaWZpZXJcIiwgXCJtZXRhdGFnXCJdXSxcbiAgICAgIFsvKCUpKEBmc3RyaW5nX2NvbnYpLywgW1wibWV0YXRhZ1wiLCBcIm1ldGF0YWdcIl1dLFxuICAgICAgWy8uLywgXCJzdHJpbmdcIl1cbiAgICBdLFxuICAgIHNzdHJpbmc6IFtcbiAgICAgIFsvQGVzY2FwZXMvLCBcInN0cmluZy5lc2NhcGVcIl0sXG4gICAgICBbXG4gICAgICAgIC9cIi8sXG4gICAgICAgIHtcbiAgICAgICAgICB0b2tlbjogXCJzdHJpbmcucXVvdGVcIixcbiAgICAgICAgICBicmFja2V0OiBcIkBjbG9zZVwiLFxuICAgICAgICAgIHN3aXRjaFRvOiBcIkBhbGxvd01ldGhvZFwiXG4gICAgICAgIH1cbiAgICAgIF0sXG4gICAgICBbL1xcJFxcJC8sIFwic3RyaW5nXCJdLFxuICAgICAgWy8oXFwkKShbYS16X11cXHcqKS8sIFtcIm9wZXJhdG9yXCIsIFwiaWRlbnRpZmllclwiXV0sXG4gICAgICBbL1xcJFxcey8sIFwib3BlcmF0b3JcIiwgXCJAaW50ZXJwXCJdLFxuICAgICAgWy8uLywgXCJzdHJpbmdcIl1cbiAgICBdLFxuICAgIHNzdHJpbmd0OiBbXG4gICAgICBbL0Blc2NhcGVzLywgXCJzdHJpbmcuZXNjYXBlXCJdLFxuICAgICAgWy9cIig/PVwiXCJcIikvLCBcInN0cmluZ1wiXSxcbiAgICAgIFtcbiAgICAgICAgL1wiXCJcIi8sXG4gICAgICAgIHtcbiAgICAgICAgICB0b2tlbjogXCJzdHJpbmcucXVvdGVcIixcbiAgICAgICAgICBicmFja2V0OiBcIkBjbG9zZVwiLFxuICAgICAgICAgIHN3aXRjaFRvOiBcIkBhbGxvd01ldGhvZFwiXG4gICAgICAgIH1cbiAgICAgIF0sXG4gICAgICBbL1xcJFxcJC8sIFwic3RyaW5nXCJdLFxuICAgICAgWy8oXFwkKShbYS16X11cXHcqKS8sIFtcIm9wZXJhdG9yXCIsIFwiaWRlbnRpZmllclwiXV0sXG4gICAgICBbL1xcJFxcey8sIFwib3BlcmF0b3JcIiwgXCJAaW50ZXJwXCJdLFxuICAgICAgWy8uLywgXCJzdHJpbmdcIl1cbiAgICBdLFxuICAgIGludGVycDogW1svey8sIFwib3BlcmF0b3JcIiwgXCJAcHVzaFwiXSwgWy99LywgXCJvcGVyYXRvclwiLCBcIkBwb3BcIl0sIHsgaW5jbHVkZTogXCJAcm9vdFwiIH1dLFxuICAgIHJhd3N0cmluZzogW1xuICAgICAgWy9bXlwiXS8sIFwic3RyaW5nXCJdLFxuICAgICAgW1xuICAgICAgICAvXCIvLFxuICAgICAgICB7XG4gICAgICAgICAgdG9rZW46IFwic3RyaW5nLnF1b3RlXCIsXG4gICAgICAgICAgYnJhY2tldDogXCJAY2xvc2VcIixcbiAgICAgICAgICBzd2l0Y2hUbzogXCJAYWxsb3dNZXRob2RcIlxuICAgICAgICB9XG4gICAgICBdXG4gICAgXSxcbiAgICByYXdzdHJpbmd0OiBbXG4gICAgICBbL1teXCJdLywgXCJzdHJpbmdcIl0sXG4gICAgICBbL1wiKD89XCJcIlwiKS8sIFwic3RyaW5nXCJdLFxuICAgICAgW1xuICAgICAgICAvXCJcIlwiLyxcbiAgICAgICAge1xuICAgICAgICAgIHRva2VuOiBcInN0cmluZy5xdW90ZVwiLFxuICAgICAgICAgIGJyYWNrZXQ6IFwiQGNsb3NlXCIsXG4gICAgICAgICAgc3dpdGNoVG86IFwiQGFsbG93TWV0aG9kXCJcbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIFsvXCIvLCBcInN0cmluZ1wiXVxuICAgIF0sXG4gICAgd2hpdGVzcGFjZTogW1xuICAgICAgWy9bIFxcdFxcclxcbl0rLywgXCJ3aGl0ZVwiXSxcbiAgICAgIFsvXFwvXFwqLywgXCJjb21tZW50XCIsIFwiQGNvbW1lbnRcIl0sXG4gICAgICBbL1xcL1xcLy4qJC8sIFwiY29tbWVudFwiXVxuICAgIF1cbiAgfVxufTtcbmV4cG9ydCB7XG4gIGNvbmYsXG4gIGxhbmd1YWdlXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/scala/scala.js\n"));

/***/ })

}]);