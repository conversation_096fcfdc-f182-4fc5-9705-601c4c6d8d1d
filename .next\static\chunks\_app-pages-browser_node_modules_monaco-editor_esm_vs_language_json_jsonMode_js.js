"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_language_json_jsonMode_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/language/json/jsonMode.js":
/*!*********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/language/json/jsonMode.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompletionAdapter: function() { return /* binding */ CompletionAdapter; },\n/* harmony export */   DefinitionAdapter: function() { return /* binding */ DefinitionAdapter; },\n/* harmony export */   DiagnosticsAdapter: function() { return /* binding */ DiagnosticsAdapter; },\n/* harmony export */   DocumentColorAdapter: function() { return /* binding */ DocumentColorAdapter; },\n/* harmony export */   DocumentFormattingEditProvider: function() { return /* binding */ DocumentFormattingEditProvider; },\n/* harmony export */   DocumentHighlightAdapter: function() { return /* binding */ DocumentHighlightAdapter; },\n/* harmony export */   DocumentLinkAdapter: function() { return /* binding */ DocumentLinkAdapter; },\n/* harmony export */   DocumentRangeFormattingEditProvider: function() { return /* binding */ DocumentRangeFormattingEditProvider; },\n/* harmony export */   DocumentSymbolAdapter: function() { return /* binding */ DocumentSymbolAdapter; },\n/* harmony export */   FoldingRangeAdapter: function() { return /* binding */ FoldingRangeAdapter; },\n/* harmony export */   HoverAdapter: function() { return /* binding */ HoverAdapter; },\n/* harmony export */   ReferenceAdapter: function() { return /* binding */ ReferenceAdapter; },\n/* harmony export */   RenameAdapter: function() { return /* binding */ RenameAdapter; },\n/* harmony export */   SelectionRangeAdapter: function() { return /* binding */ SelectionRangeAdapter; },\n/* harmony export */   WorkerManager: function() { return /* binding */ WorkerManager; },\n/* harmony export */   fromPosition: function() { return /* binding */ fromPosition; },\n/* harmony export */   fromRange: function() { return /* binding */ fromRange; },\n/* harmony export */   getWorker: function() { return /* binding */ getWorker; },\n/* harmony export */   setupMode: function() { return /* binding */ setupMode; },\n/* harmony export */   toRange: function() { return /* binding */ toRange; },\n/* harmony export */   toTextEdit: function() { return /* binding */ toTextEdit; }\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/editor.api.js?ce9f\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/language/json/workerManager.ts\nvar STOP_WHEN_IDLE_FOR = 2 * 60 * 1e3;\nvar WorkerManager = class {\n  constructor(defaults) {\n    this._defaults = defaults;\n    this._worker = null;\n    this._client = null;\n    this._idleCheckInterval = window.setInterval(() => this._checkIfIdle(), 30 * 1e3);\n    this._lastUsedTime = 0;\n    this._configChangeListener = this._defaults.onDidChange(() => this._stopWorker());\n  }\n  _stopWorker() {\n    if (this._worker) {\n      this._worker.dispose();\n      this._worker = null;\n    }\n    this._client = null;\n  }\n  dispose() {\n    clearInterval(this._idleCheckInterval);\n    this._configChangeListener.dispose();\n    this._stopWorker();\n  }\n  _checkIfIdle() {\n    if (!this._worker) {\n      return;\n    }\n    let timePassedSinceLastUsed = Date.now() - this._lastUsedTime;\n    if (timePassedSinceLastUsed > STOP_WHEN_IDLE_FOR) {\n      this._stopWorker();\n    }\n  }\n  _getClient() {\n    this._lastUsedTime = Date.now();\n    if (!this._client) {\n      this._worker = monaco_editor_core_exports.editor.createWebWorker({\n        // module that exports the create() method and returns a `JSONWorker` instance\n        moduleId: \"vs/language/json/jsonWorker\",\n        label: this._defaults.languageId,\n        // passed in to the create() method\n        createData: {\n          languageSettings: this._defaults.diagnosticsOptions,\n          languageId: this._defaults.languageId,\n          enableSchemaRequest: this._defaults.diagnosticsOptions.enableSchemaRequest\n        }\n      });\n      this._client = this._worker.getProxy();\n    }\n    return this._client;\n  }\n  getLanguageServiceWorker(...resources) {\n    let _client;\n    return this._getClient().then((client) => {\n      _client = client;\n    }).then((_) => {\n      if (this._worker) {\n        return this._worker.withSyncedResources(resources);\n      }\n    }).then((_) => _client);\n  }\n};\n\n// node_modules/vscode-languageserver-types/lib/esm/main.js\nvar DocumentUri;\n(function(DocumentUri2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  DocumentUri2.is = is;\n})(DocumentUri || (DocumentUri = {}));\nvar URI;\n(function(URI2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  URI2.is = is;\n})(URI || (URI = {}));\nvar integer;\n(function(integer2) {\n  integer2.MIN_VALUE = -2147483648;\n  integer2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && integer2.MIN_VALUE <= value && value <= integer2.MAX_VALUE;\n  }\n  integer2.is = is;\n})(integer || (integer = {}));\nvar uinteger;\n(function(uinteger2) {\n  uinteger2.MIN_VALUE = 0;\n  uinteger2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && uinteger2.MIN_VALUE <= value && value <= uinteger2.MAX_VALUE;\n  }\n  uinteger2.is = is;\n})(uinteger || (uinteger = {}));\nvar Position;\n(function(Position3) {\n  function create(line, character) {\n    if (line === Number.MAX_VALUE) {\n      line = uinteger.MAX_VALUE;\n    }\n    if (character === Number.MAX_VALUE) {\n      character = uinteger.MAX_VALUE;\n    }\n    return { line, character };\n  }\n  Position3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n  }\n  Position3.is = is;\n})(Position || (Position = {}));\nvar Range;\n(function(Range3) {\n  function create(one, two, three, four) {\n    if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n      return { start: Position.create(one, two), end: Position.create(three, four) };\n    } else if (Position.is(one) && Position.is(two)) {\n      return { start: one, end: two };\n    } else {\n      throw new Error(`Range#create called with invalid arguments[${one}, ${two}, ${three}, ${four}]`);\n    }\n  }\n  Range3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n  }\n  Range3.is = is;\n})(Range || (Range = {}));\nvar Location;\n(function(Location2) {\n  function create(uri, range) {\n    return { uri, range };\n  }\n  Location2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n  }\n  Location2.is = is;\n})(Location || (Location = {}));\nvar LocationLink;\n(function(LocationLink2) {\n  function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n    return { targetUri, targetRange, targetSelectionRange, originSelectionRange };\n  }\n  LocationLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri) && Range.is(candidate.targetSelectionRange) && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n  }\n  LocationLink2.is = is;\n})(LocationLink || (LocationLink = {}));\nvar Color;\n(function(Color2) {\n  function create(red, green, blue, alpha) {\n    return {\n      red,\n      green,\n      blue,\n      alpha\n    };\n  }\n  Color2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.numberRange(candidate.red, 0, 1) && Is.numberRange(candidate.green, 0, 1) && Is.numberRange(candidate.blue, 0, 1) && Is.numberRange(candidate.alpha, 0, 1);\n  }\n  Color2.is = is;\n})(Color || (Color = {}));\nvar ColorInformation;\n(function(ColorInformation2) {\n  function create(range, color) {\n    return {\n      range,\n      color\n    };\n  }\n  ColorInformation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && Color.is(candidate.color);\n  }\n  ColorInformation2.is = is;\n})(ColorInformation || (ColorInformation = {}));\nvar ColorPresentation;\n(function(ColorPresentation2) {\n  function create(label, textEdit, additionalTextEdits) {\n    return {\n      label,\n      textEdit,\n      additionalTextEdits\n    };\n  }\n  ColorPresentation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate)) && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n  }\n  ColorPresentation2.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\nvar FoldingRangeKind;\n(function(FoldingRangeKind2) {\n  FoldingRangeKind2.Comment = \"comment\";\n  FoldingRangeKind2.Imports = \"imports\";\n  FoldingRangeKind2.Region = \"region\";\n})(FoldingRangeKind || (FoldingRangeKind = {}));\nvar FoldingRange;\n(function(FoldingRange2) {\n  function create(startLine, endLine, startCharacter, endCharacter, kind, collapsedText) {\n    const result = {\n      startLine,\n      endLine\n    };\n    if (Is.defined(startCharacter)) {\n      result.startCharacter = startCharacter;\n    }\n    if (Is.defined(endCharacter)) {\n      result.endCharacter = endCharacter;\n    }\n    if (Is.defined(kind)) {\n      result.kind = kind;\n    }\n    if (Is.defined(collapsedText)) {\n      result.collapsedText = collapsedText;\n    }\n    return result;\n  }\n  FoldingRange2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine) && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter)) && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter)) && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n  }\n  FoldingRange2.is = is;\n})(FoldingRange || (FoldingRange = {}));\nvar DiagnosticRelatedInformation;\n(function(DiagnosticRelatedInformation2) {\n  function create(location, message) {\n    return {\n      location,\n      message\n    };\n  }\n  DiagnosticRelatedInformation2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n  }\n  DiagnosticRelatedInformation2.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\nvar DiagnosticSeverity;\n(function(DiagnosticSeverity2) {\n  DiagnosticSeverity2.Error = 1;\n  DiagnosticSeverity2.Warning = 2;\n  DiagnosticSeverity2.Information = 3;\n  DiagnosticSeverity2.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\nvar DiagnosticTag;\n(function(DiagnosticTag2) {\n  DiagnosticTag2.Unnecessary = 1;\n  DiagnosticTag2.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\nvar CodeDescription;\n(function(CodeDescription2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.href);\n  }\n  CodeDescription2.is = is;\n})(CodeDescription || (CodeDescription = {}));\nvar Diagnostic;\n(function(Diagnostic2) {\n  function create(range, message, severity, code, source, relatedInformation) {\n    let result = { range, message };\n    if (Is.defined(severity)) {\n      result.severity = severity;\n    }\n    if (Is.defined(code)) {\n      result.code = code;\n    }\n    if (Is.defined(source)) {\n      result.source = source;\n    }\n    if (Is.defined(relatedInformation)) {\n      result.relatedInformation = relatedInformation;\n    }\n    return result;\n  }\n  Diagnostic2.create = create;\n  function is(value) {\n    var _a;\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && Is.string(candidate.message) && (Is.number(candidate.severity) || Is.undefined(candidate.severity)) && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code)) && (Is.undefined(candidate.codeDescription) || Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)) && (Is.string(candidate.source) || Is.undefined(candidate.source)) && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n  }\n  Diagnostic2.is = is;\n})(Diagnostic || (Diagnostic = {}));\nvar Command;\n(function(Command2) {\n  function create(title, command, ...args) {\n    let result = { title, command };\n    if (Is.defined(args) && args.length > 0) {\n      result.arguments = args;\n    }\n    return result;\n  }\n  Command2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n  }\n  Command2.is = is;\n})(Command || (Command = {}));\nvar TextEdit;\n(function(TextEdit2) {\n  function replace(range, newText) {\n    return { range, newText };\n  }\n  TextEdit2.replace = replace;\n  function insert(position, newText) {\n    return { range: { start: position, end: position }, newText };\n  }\n  TextEdit2.insert = insert;\n  function del(range) {\n    return { range, newText: \"\" };\n  }\n  TextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.newText) && Range.is(candidate.range);\n  }\n  TextEdit2.is = is;\n})(TextEdit || (TextEdit = {}));\nvar ChangeAnnotation;\n(function(ChangeAnnotation2) {\n  function create(label, needsConfirmation, description) {\n    const result = { label };\n    if (needsConfirmation !== void 0) {\n      result.needsConfirmation = needsConfirmation;\n    }\n    if (description !== void 0) {\n      result.description = description;\n    }\n    return result;\n  }\n  ChangeAnnotation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  ChangeAnnotation2.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nvar ChangeAnnotationIdentifier;\n(function(ChangeAnnotationIdentifier2) {\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate);\n  }\n  ChangeAnnotationIdentifier2.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nvar AnnotatedTextEdit;\n(function(AnnotatedTextEdit2) {\n  function replace(range, newText, annotation) {\n    return { range, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.replace = replace;\n  function insert(position, newText, annotation) {\n    return { range: { start: position, end: position }, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.insert = insert;\n  function del(range, annotation) {\n    return { range, newText: \"\", annotationId: annotation };\n  }\n  AnnotatedTextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  AnnotatedTextEdit2.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\nvar TextDocumentEdit;\n(function(TextDocumentEdit2) {\n  function create(textDocument, edits) {\n    return { textDocument, edits };\n  }\n  TextDocumentEdit2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument) && Array.isArray(candidate.edits);\n  }\n  TextDocumentEdit2.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nvar CreateFile;\n(function(CreateFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"create\",\n      uri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  CreateFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"create\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  CreateFile2.is = is;\n})(CreateFile || (CreateFile = {}));\nvar RenameFile;\n(function(RenameFile2) {\n  function create(oldUri, newUri, options, annotation) {\n    let result = {\n      kind: \"rename\",\n      oldUri,\n      newUri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  RenameFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"rename\" && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  RenameFile2.is = is;\n})(RenameFile || (RenameFile = {}));\nvar DeleteFile;\n(function(DeleteFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"delete\",\n      uri\n    };\n    if (options !== void 0 && (options.recursive !== void 0 || options.ignoreIfNotExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  DeleteFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"delete\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.recursive === void 0 || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === void 0 || Is.boolean(candidate.options.ignoreIfNotExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  DeleteFile2.is = is;\n})(DeleteFile || (DeleteFile = {}));\nvar WorkspaceEdit;\n(function(WorkspaceEdit2) {\n  function is(value) {\n    let candidate = value;\n    return candidate && (candidate.changes !== void 0 || candidate.documentChanges !== void 0) && (candidate.documentChanges === void 0 || candidate.documentChanges.every((change) => {\n      if (Is.string(change.kind)) {\n        return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n      } else {\n        return TextDocumentEdit.is(change);\n      }\n    }));\n  }\n  WorkspaceEdit2.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nvar TextDocumentIdentifier;\n(function(TextDocumentIdentifier2) {\n  function create(uri) {\n    return { uri };\n  }\n  TextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri);\n  }\n  TextDocumentIdentifier2.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\nvar VersionedTextDocumentIdentifier;\n(function(VersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  VersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n  }\n  VersionedTextDocumentIdentifier2.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\nvar OptionalVersionedTextDocumentIdentifier;\n(function(OptionalVersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  OptionalVersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n  }\n  OptionalVersionedTextDocumentIdentifier2.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\nvar TextDocumentItem;\n(function(TextDocumentItem2) {\n  function create(uri, languageId, version, text) {\n    return { uri, languageId, version, text };\n  }\n  TextDocumentItem2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n  }\n  TextDocumentItem2.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\nvar MarkupKind;\n(function(MarkupKind2) {\n  MarkupKind2.PlainText = \"plaintext\";\n  MarkupKind2.Markdown = \"markdown\";\n  function is(value) {\n    const candidate = value;\n    return candidate === MarkupKind2.PlainText || candidate === MarkupKind2.Markdown;\n  }\n  MarkupKind2.is = is;\n})(MarkupKind || (MarkupKind = {}));\nvar MarkupContent;\n(function(MarkupContent2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n  }\n  MarkupContent2.is = is;\n})(MarkupContent || (MarkupContent = {}));\nvar CompletionItemKind;\n(function(CompletionItemKind2) {\n  CompletionItemKind2.Text = 1;\n  CompletionItemKind2.Method = 2;\n  CompletionItemKind2.Function = 3;\n  CompletionItemKind2.Constructor = 4;\n  CompletionItemKind2.Field = 5;\n  CompletionItemKind2.Variable = 6;\n  CompletionItemKind2.Class = 7;\n  CompletionItemKind2.Interface = 8;\n  CompletionItemKind2.Module = 9;\n  CompletionItemKind2.Property = 10;\n  CompletionItemKind2.Unit = 11;\n  CompletionItemKind2.Value = 12;\n  CompletionItemKind2.Enum = 13;\n  CompletionItemKind2.Keyword = 14;\n  CompletionItemKind2.Snippet = 15;\n  CompletionItemKind2.Color = 16;\n  CompletionItemKind2.File = 17;\n  CompletionItemKind2.Reference = 18;\n  CompletionItemKind2.Folder = 19;\n  CompletionItemKind2.EnumMember = 20;\n  CompletionItemKind2.Constant = 21;\n  CompletionItemKind2.Struct = 22;\n  CompletionItemKind2.Event = 23;\n  CompletionItemKind2.Operator = 24;\n  CompletionItemKind2.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\nvar InsertTextFormat;\n(function(InsertTextFormat2) {\n  InsertTextFormat2.PlainText = 1;\n  InsertTextFormat2.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\nvar CompletionItemTag;\n(function(CompletionItemTag2) {\n  CompletionItemTag2.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\nvar InsertReplaceEdit;\n(function(InsertReplaceEdit2) {\n  function create(newText, insert, replace) {\n    return { newText, insert, replace };\n  }\n  InsertReplaceEdit2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n  }\n  InsertReplaceEdit2.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\nvar InsertTextMode;\n(function(InsertTextMode2) {\n  InsertTextMode2.asIs = 1;\n  InsertTextMode2.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\nvar CompletionItemLabelDetails;\n(function(CompletionItemLabelDetails2) {\n  function is(value) {\n    const candidate = value;\n    return candidate && (Is.string(candidate.detail) || candidate.detail === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  CompletionItemLabelDetails2.is = is;\n})(CompletionItemLabelDetails || (CompletionItemLabelDetails = {}));\nvar CompletionItem;\n(function(CompletionItem2) {\n  function create(label) {\n    return { label };\n  }\n  CompletionItem2.create = create;\n})(CompletionItem || (CompletionItem = {}));\nvar CompletionList;\n(function(CompletionList2) {\n  function create(items, isIncomplete) {\n    return { items: items ? items : [], isIncomplete: !!isIncomplete };\n  }\n  CompletionList2.create = create;\n})(CompletionList || (CompletionList = {}));\nvar MarkedString;\n(function(MarkedString2) {\n  function fromPlainText(plainText) {\n    return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\");\n  }\n  MarkedString2.fromPlainText = fromPlainText;\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate) || Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value);\n  }\n  MarkedString2.is = is;\n})(MarkedString || (MarkedString = {}));\nvar Hover;\n(function(Hover2) {\n  function is(value) {\n    let candidate = value;\n    return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) || MarkedString.is(candidate.contents) || Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === void 0 || Range.is(value.range));\n  }\n  Hover2.is = is;\n})(Hover || (Hover = {}));\nvar ParameterInformation;\n(function(ParameterInformation2) {\n  function create(label, documentation) {\n    return documentation ? { label, documentation } : { label };\n  }\n  ParameterInformation2.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\nvar SignatureInformation;\n(function(SignatureInformation2) {\n  function create(label, documentation, ...parameters) {\n    let result = { label };\n    if (Is.defined(documentation)) {\n      result.documentation = documentation;\n    }\n    if (Is.defined(parameters)) {\n      result.parameters = parameters;\n    } else {\n      result.parameters = [];\n    }\n    return result;\n  }\n  SignatureInformation2.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\nvar DocumentHighlightKind;\n(function(DocumentHighlightKind2) {\n  DocumentHighlightKind2.Text = 1;\n  DocumentHighlightKind2.Read = 2;\n  DocumentHighlightKind2.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\nvar DocumentHighlight;\n(function(DocumentHighlight2) {\n  function create(range, kind) {\n    let result = { range };\n    if (Is.number(kind)) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  DocumentHighlight2.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\nvar SymbolKind;\n(function(SymbolKind2) {\n  SymbolKind2.File = 1;\n  SymbolKind2.Module = 2;\n  SymbolKind2.Namespace = 3;\n  SymbolKind2.Package = 4;\n  SymbolKind2.Class = 5;\n  SymbolKind2.Method = 6;\n  SymbolKind2.Property = 7;\n  SymbolKind2.Field = 8;\n  SymbolKind2.Constructor = 9;\n  SymbolKind2.Enum = 10;\n  SymbolKind2.Interface = 11;\n  SymbolKind2.Function = 12;\n  SymbolKind2.Variable = 13;\n  SymbolKind2.Constant = 14;\n  SymbolKind2.String = 15;\n  SymbolKind2.Number = 16;\n  SymbolKind2.Boolean = 17;\n  SymbolKind2.Array = 18;\n  SymbolKind2.Object = 19;\n  SymbolKind2.Key = 20;\n  SymbolKind2.Null = 21;\n  SymbolKind2.EnumMember = 22;\n  SymbolKind2.Struct = 23;\n  SymbolKind2.Event = 24;\n  SymbolKind2.Operator = 25;\n  SymbolKind2.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\nvar SymbolTag;\n(function(SymbolTag2) {\n  SymbolTag2.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nvar SymbolInformation;\n(function(SymbolInformation2) {\n  function create(name, kind, range, uri, containerName) {\n    let result = {\n      name,\n      kind,\n      location: { uri, range }\n    };\n    if (containerName) {\n      result.containerName = containerName;\n    }\n    return result;\n  }\n  SymbolInformation2.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nvar WorkspaceSymbol;\n(function(WorkspaceSymbol2) {\n  function create(name, kind, uri, range) {\n    return range !== void 0 ? { name, kind, location: { uri, range } } : { name, kind, location: { uri } };\n  }\n  WorkspaceSymbol2.create = create;\n})(WorkspaceSymbol || (WorkspaceSymbol = {}));\nvar DocumentSymbol;\n(function(DocumentSymbol2) {\n  function create(name, detail, kind, range, selectionRange, children) {\n    let result = {\n      name,\n      detail,\n      kind,\n      range,\n      selectionRange\n    };\n    if (children !== void 0) {\n      result.children = children;\n    }\n    return result;\n  }\n  DocumentSymbol2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.name) && Is.number(candidate.kind) && Range.is(candidate.range) && Range.is(candidate.selectionRange) && (candidate.detail === void 0 || Is.string(candidate.detail)) && (candidate.deprecated === void 0 || Is.boolean(candidate.deprecated)) && (candidate.children === void 0 || Array.isArray(candidate.children)) && (candidate.tags === void 0 || Array.isArray(candidate.tags));\n  }\n  DocumentSymbol2.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\nvar CodeActionKind;\n(function(CodeActionKind2) {\n  CodeActionKind2.Empty = \"\";\n  CodeActionKind2.QuickFix = \"quickfix\";\n  CodeActionKind2.Refactor = \"refactor\";\n  CodeActionKind2.RefactorExtract = \"refactor.extract\";\n  CodeActionKind2.RefactorInline = \"refactor.inline\";\n  CodeActionKind2.RefactorRewrite = \"refactor.rewrite\";\n  CodeActionKind2.Source = \"source\";\n  CodeActionKind2.SourceOrganizeImports = \"source.organizeImports\";\n  CodeActionKind2.SourceFixAll = \"source.fixAll\";\n})(CodeActionKind || (CodeActionKind = {}));\nvar CodeActionTriggerKind;\n(function(CodeActionTriggerKind2) {\n  CodeActionTriggerKind2.Invoked = 1;\n  CodeActionTriggerKind2.Automatic = 2;\n})(CodeActionTriggerKind || (CodeActionTriggerKind = {}));\nvar CodeActionContext;\n(function(CodeActionContext2) {\n  function create(diagnostics, only, triggerKind) {\n    let result = { diagnostics };\n    if (only !== void 0 && only !== null) {\n      result.only = only;\n    }\n    if (triggerKind !== void 0 && triggerKind !== null) {\n      result.triggerKind = triggerKind;\n    }\n    return result;\n  }\n  CodeActionContext2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is) && (candidate.only === void 0 || Is.typedArray(candidate.only, Is.string)) && (candidate.triggerKind === void 0 || candidate.triggerKind === CodeActionTriggerKind.Invoked || candidate.triggerKind === CodeActionTriggerKind.Automatic);\n  }\n  CodeActionContext2.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nvar CodeAction;\n(function(CodeAction2) {\n  function create(title, kindOrCommandOrEdit, kind) {\n    let result = { title };\n    let checkKind = true;\n    if (typeof kindOrCommandOrEdit === \"string\") {\n      checkKind = false;\n      result.kind = kindOrCommandOrEdit;\n    } else if (Command.is(kindOrCommandOrEdit)) {\n      result.command = kindOrCommandOrEdit;\n    } else {\n      result.edit = kindOrCommandOrEdit;\n    }\n    if (checkKind && kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  CodeAction2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.title) && (candidate.diagnostics === void 0 || Is.typedArray(candidate.diagnostics, Diagnostic.is)) && (candidate.kind === void 0 || Is.string(candidate.kind)) && (candidate.edit !== void 0 || candidate.command !== void 0) && (candidate.command === void 0 || Command.is(candidate.command)) && (candidate.isPreferred === void 0 || Is.boolean(candidate.isPreferred)) && (candidate.edit === void 0 || WorkspaceEdit.is(candidate.edit));\n  }\n  CodeAction2.is = is;\n})(CodeAction || (CodeAction = {}));\nvar CodeLens;\n(function(CodeLens2) {\n  function create(range, data) {\n    let result = { range };\n    if (Is.defined(data)) {\n      result.data = data;\n    }\n    return result;\n  }\n  CodeLens2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n  }\n  CodeLens2.is = is;\n})(CodeLens || (CodeLens = {}));\nvar FormattingOptions;\n(function(FormattingOptions2) {\n  function create(tabSize, insertSpaces) {\n    return { tabSize, insertSpaces };\n  }\n  FormattingOptions2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n  }\n  FormattingOptions2.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\nvar DocumentLink;\n(function(DocumentLink2) {\n  function create(range, target, data) {\n    return { range, target, data };\n  }\n  DocumentLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n  }\n  DocumentLink2.is = is;\n})(DocumentLink || (DocumentLink = {}));\nvar SelectionRange;\n(function(SelectionRange2) {\n  function create(range, parent) {\n    return { range, parent };\n  }\n  SelectionRange2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (candidate.parent === void 0 || SelectionRange2.is(candidate.parent));\n  }\n  SelectionRange2.is = is;\n})(SelectionRange || (SelectionRange = {}));\nvar SemanticTokenTypes;\n(function(SemanticTokenTypes2) {\n  SemanticTokenTypes2[\"namespace\"] = \"namespace\";\n  SemanticTokenTypes2[\"type\"] = \"type\";\n  SemanticTokenTypes2[\"class\"] = \"class\";\n  SemanticTokenTypes2[\"enum\"] = \"enum\";\n  SemanticTokenTypes2[\"interface\"] = \"interface\";\n  SemanticTokenTypes2[\"struct\"] = \"struct\";\n  SemanticTokenTypes2[\"typeParameter\"] = \"typeParameter\";\n  SemanticTokenTypes2[\"parameter\"] = \"parameter\";\n  SemanticTokenTypes2[\"variable\"] = \"variable\";\n  SemanticTokenTypes2[\"property\"] = \"property\";\n  SemanticTokenTypes2[\"enumMember\"] = \"enumMember\";\n  SemanticTokenTypes2[\"event\"] = \"event\";\n  SemanticTokenTypes2[\"function\"] = \"function\";\n  SemanticTokenTypes2[\"method\"] = \"method\";\n  SemanticTokenTypes2[\"macro\"] = \"macro\";\n  SemanticTokenTypes2[\"keyword\"] = \"keyword\";\n  SemanticTokenTypes2[\"modifier\"] = \"modifier\";\n  SemanticTokenTypes2[\"comment\"] = \"comment\";\n  SemanticTokenTypes2[\"string\"] = \"string\";\n  SemanticTokenTypes2[\"number\"] = \"number\";\n  SemanticTokenTypes2[\"regexp\"] = \"regexp\";\n  SemanticTokenTypes2[\"operator\"] = \"operator\";\n  SemanticTokenTypes2[\"decorator\"] = \"decorator\";\n})(SemanticTokenTypes || (SemanticTokenTypes = {}));\nvar SemanticTokenModifiers;\n(function(SemanticTokenModifiers2) {\n  SemanticTokenModifiers2[\"declaration\"] = \"declaration\";\n  SemanticTokenModifiers2[\"definition\"] = \"definition\";\n  SemanticTokenModifiers2[\"readonly\"] = \"readonly\";\n  SemanticTokenModifiers2[\"static\"] = \"static\";\n  SemanticTokenModifiers2[\"deprecated\"] = \"deprecated\";\n  SemanticTokenModifiers2[\"abstract\"] = \"abstract\";\n  SemanticTokenModifiers2[\"async\"] = \"async\";\n  SemanticTokenModifiers2[\"modification\"] = \"modification\";\n  SemanticTokenModifiers2[\"documentation\"] = \"documentation\";\n  SemanticTokenModifiers2[\"defaultLibrary\"] = \"defaultLibrary\";\n})(SemanticTokenModifiers || (SemanticTokenModifiers = {}));\nvar SemanticTokens;\n(function(SemanticTokens2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.resultId === void 0 || typeof candidate.resultId === \"string\") && Array.isArray(candidate.data) && (candidate.data.length === 0 || typeof candidate.data[0] === \"number\");\n  }\n  SemanticTokens2.is = is;\n})(SemanticTokens || (SemanticTokens = {}));\nvar InlineValueText;\n(function(InlineValueText2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  InlineValueText2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.string(candidate.text);\n  }\n  InlineValueText2.is = is;\n})(InlineValueText || (InlineValueText = {}));\nvar InlineValueVariableLookup;\n(function(InlineValueVariableLookup2) {\n  function create(range, variableName, caseSensitiveLookup) {\n    return { range, variableName, caseSensitiveLookup };\n  }\n  InlineValueVariableLookup2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.boolean(candidate.caseSensitiveLookup) && (Is.string(candidate.variableName) || candidate.variableName === void 0);\n  }\n  InlineValueVariableLookup2.is = is;\n})(InlineValueVariableLookup || (InlineValueVariableLookup = {}));\nvar InlineValueEvaluatableExpression;\n(function(InlineValueEvaluatableExpression2) {\n  function create(range, expression) {\n    return { range, expression };\n  }\n  InlineValueEvaluatableExpression2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && (Is.string(candidate.expression) || candidate.expression === void 0);\n  }\n  InlineValueEvaluatableExpression2.is = is;\n})(InlineValueEvaluatableExpression || (InlineValueEvaluatableExpression = {}));\nvar InlineValueContext;\n(function(InlineValueContext2) {\n  function create(frameId, stoppedLocation) {\n    return { frameId, stoppedLocation };\n  }\n  InlineValueContext2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.defined(candidate) && Range.is(value.stoppedLocation);\n  }\n  InlineValueContext2.is = is;\n})(InlineValueContext || (InlineValueContext = {}));\nvar InlayHintKind;\n(function(InlayHintKind2) {\n  InlayHintKind2.Type = 1;\n  InlayHintKind2.Parameter = 2;\n  function is(value) {\n    return value === 1 || value === 2;\n  }\n  InlayHintKind2.is = is;\n})(InlayHintKind || (InlayHintKind = {}));\nvar InlayHintLabelPart;\n(function(InlayHintLabelPart2) {\n  function create(value) {\n    return { value };\n  }\n  InlayHintLabelPart2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.location === void 0 || Location.is(candidate.location)) && (candidate.command === void 0 || Command.is(candidate.command));\n  }\n  InlayHintLabelPart2.is = is;\n})(InlayHintLabelPart || (InlayHintLabelPart = {}));\nvar InlayHint;\n(function(InlayHint2) {\n  function create(position, label, kind) {\n    const result = { position, label };\n    if (kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  InlayHint2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.position) && (Is.string(candidate.label) || Is.typedArray(candidate.label, InlayHintLabelPart.is)) && (candidate.kind === void 0 || InlayHintKind.is(candidate.kind)) && candidate.textEdits === void 0 || Is.typedArray(candidate.textEdits, TextEdit.is) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.paddingLeft === void 0 || Is.boolean(candidate.paddingLeft)) && (candidate.paddingRight === void 0 || Is.boolean(candidate.paddingRight));\n  }\n  InlayHint2.is = is;\n})(InlayHint || (InlayHint = {}));\nvar StringValue;\n(function(StringValue2) {\n  function createSnippet(value) {\n    return { kind: \"snippet\", value };\n  }\n  StringValue2.createSnippet = createSnippet;\n})(StringValue || (StringValue = {}));\nvar InlineCompletionItem;\n(function(InlineCompletionItem2) {\n  function create(insertText, filterText, range, command) {\n    return { insertText, filterText, range, command };\n  }\n  InlineCompletionItem2.create = create;\n})(InlineCompletionItem || (InlineCompletionItem = {}));\nvar InlineCompletionList;\n(function(InlineCompletionList2) {\n  function create(items) {\n    return { items };\n  }\n  InlineCompletionList2.create = create;\n})(InlineCompletionList || (InlineCompletionList = {}));\nvar InlineCompletionTriggerKind;\n(function(InlineCompletionTriggerKind2) {\n  InlineCompletionTriggerKind2.Invoked = 0;\n  InlineCompletionTriggerKind2.Automatic = 1;\n})(InlineCompletionTriggerKind || (InlineCompletionTriggerKind = {}));\nvar SelectedCompletionInfo;\n(function(SelectedCompletionInfo2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  SelectedCompletionInfo2.create = create;\n})(SelectedCompletionInfo || (SelectedCompletionInfo = {}));\nvar InlineCompletionContext;\n(function(InlineCompletionContext2) {\n  function create(triggerKind, selectedCompletionInfo) {\n    return { triggerKind, selectedCompletionInfo };\n  }\n  InlineCompletionContext2.create = create;\n})(InlineCompletionContext || (InlineCompletionContext = {}));\nvar WorkspaceFolder;\n(function(WorkspaceFolder2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && URI.is(candidate.uri) && Is.string(candidate.name);\n  }\n  WorkspaceFolder2.is = is;\n})(WorkspaceFolder || (WorkspaceFolder = {}));\nvar TextDocument;\n(function(TextDocument2) {\n  function create(uri, languageId, version, content) {\n    return new FullTextDocument(uri, languageId, version, content);\n  }\n  TextDocument2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount) && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n  }\n  TextDocument2.is = is;\n  function applyEdits(document, edits) {\n    let text = document.getText();\n    let sortedEdits = mergeSort(edits, (a, b) => {\n      let diff = a.range.start.line - b.range.start.line;\n      if (diff === 0) {\n        return a.range.start.character - b.range.start.character;\n      }\n      return diff;\n    });\n    let lastModifiedOffset = text.length;\n    for (let i = sortedEdits.length - 1; i >= 0; i--) {\n      let e = sortedEdits[i];\n      let startOffset = document.offsetAt(e.range.start);\n      let endOffset = document.offsetAt(e.range.end);\n      if (endOffset <= lastModifiedOffset) {\n        text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n      } else {\n        throw new Error(\"Overlapping edit\");\n      }\n      lastModifiedOffset = startOffset;\n    }\n    return text;\n  }\n  TextDocument2.applyEdits = applyEdits;\n  function mergeSort(data, compare) {\n    if (data.length <= 1) {\n      return data;\n    }\n    const p = data.length / 2 | 0;\n    const left = data.slice(0, p);\n    const right = data.slice(p);\n    mergeSort(left, compare);\n    mergeSort(right, compare);\n    let leftIdx = 0;\n    let rightIdx = 0;\n    let i = 0;\n    while (leftIdx < left.length && rightIdx < right.length) {\n      let ret = compare(left[leftIdx], right[rightIdx]);\n      if (ret <= 0) {\n        data[i++] = left[leftIdx++];\n      } else {\n        data[i++] = right[rightIdx++];\n      }\n    }\n    while (leftIdx < left.length) {\n      data[i++] = left[leftIdx++];\n    }\n    while (rightIdx < right.length) {\n      data[i++] = right[rightIdx++];\n    }\n    return data;\n  }\n})(TextDocument || (TextDocument = {}));\nvar FullTextDocument = class {\n  constructor(uri, languageId, version, content) {\n    this._uri = uri;\n    this._languageId = languageId;\n    this._version = version;\n    this._content = content;\n    this._lineOffsets = void 0;\n  }\n  get uri() {\n    return this._uri;\n  }\n  get languageId() {\n    return this._languageId;\n  }\n  get version() {\n    return this._version;\n  }\n  getText(range) {\n    if (range) {\n      let start = this.offsetAt(range.start);\n      let end = this.offsetAt(range.end);\n      return this._content.substring(start, end);\n    }\n    return this._content;\n  }\n  update(event, version) {\n    this._content = event.text;\n    this._version = version;\n    this._lineOffsets = void 0;\n  }\n  getLineOffsets() {\n    if (this._lineOffsets === void 0) {\n      let lineOffsets = [];\n      let text = this._content;\n      let isLineStart = true;\n      for (let i = 0; i < text.length; i++) {\n        if (isLineStart) {\n          lineOffsets.push(i);\n          isLineStart = false;\n        }\n        let ch = text.charAt(i);\n        isLineStart = ch === \"\\r\" || ch === \"\\n\";\n        if (ch === \"\\r\" && i + 1 < text.length && text.charAt(i + 1) === \"\\n\") {\n          i++;\n        }\n      }\n      if (isLineStart && text.length > 0) {\n        lineOffsets.push(text.length);\n      }\n      this._lineOffsets = lineOffsets;\n    }\n    return this._lineOffsets;\n  }\n  positionAt(offset) {\n    offset = Math.max(Math.min(offset, this._content.length), 0);\n    let lineOffsets = this.getLineOffsets();\n    let low = 0, high = lineOffsets.length;\n    if (high === 0) {\n      return Position.create(0, offset);\n    }\n    while (low < high) {\n      let mid = Math.floor((low + high) / 2);\n      if (lineOffsets[mid] > offset) {\n        high = mid;\n      } else {\n        low = mid + 1;\n      }\n    }\n    let line = low - 1;\n    return Position.create(line, offset - lineOffsets[line]);\n  }\n  offsetAt(position) {\n    let lineOffsets = this.getLineOffsets();\n    if (position.line >= lineOffsets.length) {\n      return this._content.length;\n    } else if (position.line < 0) {\n      return 0;\n    }\n    let lineOffset = lineOffsets[position.line];\n    let nextLineOffset = position.line + 1 < lineOffsets.length ? lineOffsets[position.line + 1] : this._content.length;\n    return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n  }\n  get lineCount() {\n    return this.getLineOffsets().length;\n  }\n};\nvar Is;\n(function(Is2) {\n  const toString = Object.prototype.toString;\n  function defined(value) {\n    return typeof value !== \"undefined\";\n  }\n  Is2.defined = defined;\n  function undefined2(value) {\n    return typeof value === \"undefined\";\n  }\n  Is2.undefined = undefined2;\n  function boolean(value) {\n    return value === true || value === false;\n  }\n  Is2.boolean = boolean;\n  function string(value) {\n    return toString.call(value) === \"[object String]\";\n  }\n  Is2.string = string;\n  function number(value) {\n    return toString.call(value) === \"[object Number]\";\n  }\n  Is2.number = number;\n  function numberRange(value, min, max) {\n    return toString.call(value) === \"[object Number]\" && min <= value && value <= max;\n  }\n  Is2.numberRange = numberRange;\n  function integer2(value) {\n    return toString.call(value) === \"[object Number]\" && -2147483648 <= value && value <= 2147483647;\n  }\n  Is2.integer = integer2;\n  function uinteger2(value) {\n    return toString.call(value) === \"[object Number]\" && 0 <= value && value <= 2147483647;\n  }\n  Is2.uinteger = uinteger2;\n  function func(value) {\n    return toString.call(value) === \"[object Function]\";\n  }\n  Is2.func = func;\n  function objectLiteral(value) {\n    return value !== null && typeof value === \"object\";\n  }\n  Is2.objectLiteral = objectLiteral;\n  function typedArray(value, check) {\n    return Array.isArray(value) && value.every(check);\n  }\n  Is2.typedArray = typedArray;\n})(Is || (Is = {}));\n\n// src/language/common/lspLanguageFeatures.ts\nvar DiagnosticsAdapter = class {\n  constructor(_languageId, _worker, configChangeEvent) {\n    this._languageId = _languageId;\n    this._worker = _worker;\n    this._disposables = [];\n    this._listener = /* @__PURE__ */ Object.create(null);\n    const onModelAdd = (model) => {\n      let modeId = model.getLanguageId();\n      if (modeId !== this._languageId) {\n        return;\n      }\n      let handle;\n      this._listener[model.uri.toString()] = model.onDidChangeContent(() => {\n        window.clearTimeout(handle);\n        handle = window.setTimeout(() => this._doValidate(model.uri, modeId), 500);\n      });\n      this._doValidate(model.uri, modeId);\n    };\n    const onModelRemoved = (model) => {\n      monaco_editor_core_exports.editor.setModelMarkers(model, this._languageId, []);\n      let uriStr = model.uri.toString();\n      let listener = this._listener[uriStr];\n      if (listener) {\n        listener.dispose();\n        delete this._listener[uriStr];\n      }\n    };\n    this._disposables.push(monaco_editor_core_exports.editor.onDidCreateModel(onModelAdd));\n    this._disposables.push(monaco_editor_core_exports.editor.onWillDisposeModel(onModelRemoved));\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        onModelRemoved(event.model);\n        onModelAdd(event.model);\n      })\n    );\n    this._disposables.push(\n      configChangeEvent((_) => {\n        monaco_editor_core_exports.editor.getModels().forEach((model) => {\n          if (model.getLanguageId() === this._languageId) {\n            onModelRemoved(model);\n            onModelAdd(model);\n          }\n        });\n      })\n    );\n    this._disposables.push({\n      dispose: () => {\n        monaco_editor_core_exports.editor.getModels().forEach(onModelRemoved);\n        for (let key in this._listener) {\n          this._listener[key].dispose();\n        }\n      }\n    });\n    monaco_editor_core_exports.editor.getModels().forEach(onModelAdd);\n  }\n  dispose() {\n    this._disposables.forEach((d) => d && d.dispose());\n    this._disposables.length = 0;\n  }\n  _doValidate(resource, languageId) {\n    this._worker(resource).then((worker2) => {\n      return worker2.doValidation(resource.toString());\n    }).then((diagnostics) => {\n      const markers = diagnostics.map((d) => toDiagnostics(resource, d));\n      let model = monaco_editor_core_exports.editor.getModel(resource);\n      if (model && model.getLanguageId() === languageId) {\n        monaco_editor_core_exports.editor.setModelMarkers(model, languageId, markers);\n      }\n    }).then(void 0, (err) => {\n      console.error(err);\n    });\n  }\n};\nfunction toSeverity(lsSeverity) {\n  switch (lsSeverity) {\n    case DiagnosticSeverity.Error:\n      return monaco_editor_core_exports.MarkerSeverity.Error;\n    case DiagnosticSeverity.Warning:\n      return monaco_editor_core_exports.MarkerSeverity.Warning;\n    case DiagnosticSeverity.Information:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n    case DiagnosticSeverity.Hint:\n      return monaco_editor_core_exports.MarkerSeverity.Hint;\n    default:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n  }\n}\nfunction toDiagnostics(resource, diag) {\n  let code = typeof diag.code === \"number\" ? String(diag.code) : diag.code;\n  return {\n    severity: toSeverity(diag.severity),\n    startLineNumber: diag.range.start.line + 1,\n    startColumn: diag.range.start.character + 1,\n    endLineNumber: diag.range.end.line + 1,\n    endColumn: diag.range.end.character + 1,\n    message: diag.message,\n    code,\n    source: diag.source\n  };\n}\nvar CompletionAdapter = class {\n  constructor(_worker, _triggerCharacters) {\n    this._worker = _worker;\n    this._triggerCharacters = _triggerCharacters;\n  }\n  get triggerCharacters() {\n    return this._triggerCharacters;\n  }\n  provideCompletionItems(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.doComplete(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      const wordInfo = model.getWordUntilPosition(position);\n      const wordRange = new monaco_editor_core_exports.Range(\n        position.lineNumber,\n        wordInfo.startColumn,\n        position.lineNumber,\n        wordInfo.endColumn\n      );\n      const items = info.items.map((entry) => {\n        const item = {\n          label: entry.label,\n          insertText: entry.insertText || entry.label,\n          sortText: entry.sortText,\n          filterText: entry.filterText,\n          documentation: entry.documentation,\n          detail: entry.detail,\n          command: toCommand(entry.command),\n          range: wordRange,\n          kind: toCompletionItemKind(entry.kind)\n        };\n        if (entry.textEdit) {\n          if (isInsertReplaceEdit(entry.textEdit)) {\n            item.range = {\n              insert: toRange(entry.textEdit.insert),\n              replace: toRange(entry.textEdit.replace)\n            };\n          } else {\n            item.range = toRange(entry.textEdit.range);\n          }\n          item.insertText = entry.textEdit.newText;\n        }\n        if (entry.additionalTextEdits) {\n          item.additionalTextEdits = entry.additionalTextEdits.map(toTextEdit);\n        }\n        if (entry.insertTextFormat === InsertTextFormat.Snippet) {\n          item.insertTextRules = monaco_editor_core_exports.languages.CompletionItemInsertTextRule.InsertAsSnippet;\n        }\n        return item;\n      });\n      return {\n        isIncomplete: info.isIncomplete,\n        suggestions: items\n      };\n    });\n  }\n};\nfunction fromPosition(position) {\n  if (!position) {\n    return void 0;\n  }\n  return { character: position.column - 1, line: position.lineNumber - 1 };\n}\nfunction fromRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return {\n    start: {\n      line: range.startLineNumber - 1,\n      character: range.startColumn - 1\n    },\n    end: { line: range.endLineNumber - 1, character: range.endColumn - 1 }\n  };\n}\nfunction toRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return new monaco_editor_core_exports.Range(\n    range.start.line + 1,\n    range.start.character + 1,\n    range.end.line + 1,\n    range.end.character + 1\n  );\n}\nfunction isInsertReplaceEdit(edit) {\n  return typeof edit.insert !== \"undefined\" && typeof edit.replace !== \"undefined\";\n}\nfunction toCompletionItemKind(kind) {\n  const mItemKind = monaco_editor_core_exports.languages.CompletionItemKind;\n  switch (kind) {\n    case CompletionItemKind.Text:\n      return mItemKind.Text;\n    case CompletionItemKind.Method:\n      return mItemKind.Method;\n    case CompletionItemKind.Function:\n      return mItemKind.Function;\n    case CompletionItemKind.Constructor:\n      return mItemKind.Constructor;\n    case CompletionItemKind.Field:\n      return mItemKind.Field;\n    case CompletionItemKind.Variable:\n      return mItemKind.Variable;\n    case CompletionItemKind.Class:\n      return mItemKind.Class;\n    case CompletionItemKind.Interface:\n      return mItemKind.Interface;\n    case CompletionItemKind.Module:\n      return mItemKind.Module;\n    case CompletionItemKind.Property:\n      return mItemKind.Property;\n    case CompletionItemKind.Unit:\n      return mItemKind.Unit;\n    case CompletionItemKind.Value:\n      return mItemKind.Value;\n    case CompletionItemKind.Enum:\n      return mItemKind.Enum;\n    case CompletionItemKind.Keyword:\n      return mItemKind.Keyword;\n    case CompletionItemKind.Snippet:\n      return mItemKind.Snippet;\n    case CompletionItemKind.Color:\n      return mItemKind.Color;\n    case CompletionItemKind.File:\n      return mItemKind.File;\n    case CompletionItemKind.Reference:\n      return mItemKind.Reference;\n  }\n  return mItemKind.Property;\n}\nfunction toTextEdit(textEdit) {\n  if (!textEdit) {\n    return void 0;\n  }\n  return {\n    range: toRange(textEdit.range),\n    text: textEdit.newText\n  };\n}\nfunction toCommand(c) {\n  return c && c.command === \"editor.action.triggerSuggest\" ? { id: c.command, title: c.title, arguments: c.arguments } : void 0;\n}\nvar HoverAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideHover(model, position, token) {\n    let resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.doHover(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      return {\n        range: toRange(info.range),\n        contents: toMarkedStringArray(info.contents)\n      };\n    });\n  }\n};\nfunction isMarkupContent(thing) {\n  return thing && typeof thing === \"object\" && typeof thing.kind === \"string\";\n}\nfunction toMarkdownString(entry) {\n  if (typeof entry === \"string\") {\n    return {\n      value: entry\n    };\n  }\n  if (isMarkupContent(entry)) {\n    if (entry.kind === \"plaintext\") {\n      return {\n        value: entry.value.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\")\n      };\n    }\n    return {\n      value: entry.value\n    };\n  }\n  return { value: \"```\" + entry.language + \"\\n\" + entry.value + \"\\n```\\n\" };\n}\nfunction toMarkedStringArray(contents) {\n  if (!contents) {\n    return void 0;\n  }\n  if (Array.isArray(contents)) {\n    return contents.map(toMarkdownString);\n  }\n  return [toMarkdownString(contents)];\n}\nvar DocumentHighlightAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentHighlights(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentHighlights(resource.toString(), fromPosition(position))).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map((entry) => {\n        return {\n          range: toRange(entry.range),\n          kind: toDocumentHighlightKind(entry.kind)\n        };\n      });\n    });\n  }\n};\nfunction toDocumentHighlightKind(kind) {\n  switch (kind) {\n    case DocumentHighlightKind.Read:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Read;\n    case DocumentHighlightKind.Write:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Write;\n    case DocumentHighlightKind.Text:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n  }\n  return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n}\nvar DefinitionAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDefinition(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.findDefinition(resource.toString(), fromPosition(position));\n    }).then((definition) => {\n      if (!definition) {\n        return;\n      }\n      return [toLocation(definition)];\n    });\n  }\n};\nfunction toLocation(location) {\n  return {\n    uri: monaco_editor_core_exports.Uri.parse(location.uri),\n    range: toRange(location.range)\n  };\n}\nvar ReferenceAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideReferences(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.findReferences(resource.toString(), fromPosition(position));\n    }).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map(toLocation);\n    });\n  }\n};\nvar RenameAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideRenameEdits(model, position, newName, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.doRename(resource.toString(), fromPosition(position), newName);\n    }).then((edit) => {\n      return toWorkspaceEdit(edit);\n    });\n  }\n};\nfunction toWorkspaceEdit(edit) {\n  if (!edit || !edit.changes) {\n    return void 0;\n  }\n  let resourceEdits = [];\n  for (let uri in edit.changes) {\n    const _uri = monaco_editor_core_exports.Uri.parse(uri);\n    for (let e of edit.changes[uri]) {\n      resourceEdits.push({\n        resource: _uri,\n        versionId: void 0,\n        textEdit: {\n          range: toRange(e.range),\n          text: e.newText\n        }\n      });\n    }\n  }\n  return {\n    edits: resourceEdits\n  };\n}\nvar DocumentSymbolAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentSymbols(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentSymbols(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return items.map((item) => {\n        if (isDocumentSymbol(item)) {\n          return toDocumentSymbol(item);\n        }\n        return {\n          name: item.name,\n          detail: \"\",\n          containerName: item.containerName,\n          kind: toSymbolKind(item.kind),\n          range: toRange(item.location.range),\n          selectionRange: toRange(item.location.range),\n          tags: []\n        };\n      });\n    });\n  }\n};\nfunction isDocumentSymbol(symbol) {\n  return \"children\" in symbol;\n}\nfunction toDocumentSymbol(symbol) {\n  return {\n    name: symbol.name,\n    detail: symbol.detail ?? \"\",\n    kind: toSymbolKind(symbol.kind),\n    range: toRange(symbol.range),\n    selectionRange: toRange(symbol.selectionRange),\n    tags: symbol.tags ?? [],\n    children: (symbol.children ?? []).map((item) => toDocumentSymbol(item))\n  };\n}\nfunction toSymbolKind(kind) {\n  let mKind = monaco_editor_core_exports.languages.SymbolKind;\n  switch (kind) {\n    case SymbolKind.File:\n      return mKind.File;\n    case SymbolKind.Module:\n      return mKind.Module;\n    case SymbolKind.Namespace:\n      return mKind.Namespace;\n    case SymbolKind.Package:\n      return mKind.Package;\n    case SymbolKind.Class:\n      return mKind.Class;\n    case SymbolKind.Method:\n      return mKind.Method;\n    case SymbolKind.Property:\n      return mKind.Property;\n    case SymbolKind.Field:\n      return mKind.Field;\n    case SymbolKind.Constructor:\n      return mKind.Constructor;\n    case SymbolKind.Enum:\n      return mKind.Enum;\n    case SymbolKind.Interface:\n      return mKind.Interface;\n    case SymbolKind.Function:\n      return mKind.Function;\n    case SymbolKind.Variable:\n      return mKind.Variable;\n    case SymbolKind.Constant:\n      return mKind.Constant;\n    case SymbolKind.String:\n      return mKind.String;\n    case SymbolKind.Number:\n      return mKind.Number;\n    case SymbolKind.Boolean:\n      return mKind.Boolean;\n    case SymbolKind.Array:\n      return mKind.Array;\n  }\n  return mKind.Function;\n}\nvar DocumentLinkAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideLinks(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentLinks(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return {\n        links: items.map((item) => ({\n          range: toRange(item.range),\n          url: item.target\n        }))\n      };\n    });\n  }\n};\nvar DocumentFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentFormattingEdits(model, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.format(resource.toString(), null, fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nvar DocumentRangeFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n    this.canFormatMultipleRanges = false;\n  }\n  provideDocumentRangeFormattingEdits(model, range, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => {\n      return worker2.format(resource.toString(), fromRange(range), fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nfunction fromFormattingOptions(options) {\n  return {\n    tabSize: options.tabSize,\n    insertSpaces: options.insertSpaces\n  };\n}\nvar DocumentColorAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentColors(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.findDocumentColors(resource.toString())).then((infos) => {\n      if (!infos) {\n        return;\n      }\n      return infos.map((item) => ({\n        color: item.color,\n        range: toRange(item.range)\n      }));\n    });\n  }\n  provideColorPresentations(model, info, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker2) => worker2.getColorPresentations(resource.toString(), info.color, fromRange(info.range))\n    ).then((presentations) => {\n      if (!presentations) {\n        return;\n      }\n      return presentations.map((presentation) => {\n        let item = {\n          label: presentation.label\n        };\n        if (presentation.textEdit) {\n          item.textEdit = toTextEdit(presentation.textEdit);\n        }\n        if (presentation.additionalTextEdits) {\n          item.additionalTextEdits = presentation.additionalTextEdits.map(toTextEdit);\n        }\n        return item;\n      });\n    });\n  }\n};\nvar FoldingRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideFoldingRanges(model, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker2) => worker2.getFoldingRanges(resource.toString(), context)).then((ranges) => {\n      if (!ranges) {\n        return;\n      }\n      return ranges.map((range) => {\n        const result = {\n          start: range.startLine + 1,\n          end: range.endLine + 1\n        };\n        if (typeof range.kind !== \"undefined\") {\n          result.kind = toFoldingRangeKind(range.kind);\n        }\n        return result;\n      });\n    });\n  }\n};\nfunction toFoldingRangeKind(kind) {\n  switch (kind) {\n    case FoldingRangeKind.Comment:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Comment;\n    case FoldingRangeKind.Imports:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Imports;\n    case FoldingRangeKind.Region:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Region;\n  }\n  return void 0;\n}\nvar SelectionRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideSelectionRanges(model, positions, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker2) => worker2.getSelectionRanges(\n        resource.toString(),\n        positions.map(fromPosition)\n      )\n    ).then((selectionRanges) => {\n      if (!selectionRanges) {\n        return;\n      }\n      return selectionRanges.map((selectionRange) => {\n        const result = [];\n        while (selectionRange) {\n          result.push({ range: toRange(selectionRange.range) });\n          selectionRange = selectionRange.parent;\n        }\n        return result;\n      });\n    });\n  }\n};\n\n// node_modules/jsonc-parser/lib/esm/impl/scanner.js\nfunction createScanner(text, ignoreTrivia = false) {\n  const len = text.length;\n  let pos = 0, value = \"\", tokenOffset = 0, token = 16, lineNumber = 0, lineStartOffset = 0, tokenLineStartOffset = 0, prevTokenLineStartOffset = 0, scanError = 0;\n  function scanHexDigits(count, exact) {\n    let digits = 0;\n    let value2 = 0;\n    while (digits < count || !exact) {\n      let ch = text.charCodeAt(pos);\n      if (ch >= 48 && ch <= 57) {\n        value2 = value2 * 16 + ch - 48;\n      } else if (ch >= 65 && ch <= 70) {\n        value2 = value2 * 16 + ch - 65 + 10;\n      } else if (ch >= 97 && ch <= 102) {\n        value2 = value2 * 16 + ch - 97 + 10;\n      } else {\n        break;\n      }\n      pos++;\n      digits++;\n    }\n    if (digits < count) {\n      value2 = -1;\n    }\n    return value2;\n  }\n  function setPosition(newPosition) {\n    pos = newPosition;\n    value = \"\";\n    tokenOffset = 0;\n    token = 16;\n    scanError = 0;\n  }\n  function scanNumber() {\n    let start = pos;\n    if (text.charCodeAt(pos) === 48) {\n      pos++;\n    } else {\n      pos++;\n      while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n      }\n    }\n    if (pos < text.length && text.charCodeAt(pos) === 46) {\n      pos++;\n      if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n        while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n          pos++;\n        }\n      } else {\n        scanError = 3;\n        return text.substring(start, pos);\n      }\n    }\n    let end = pos;\n    if (pos < text.length && (text.charCodeAt(pos) === 69 || text.charCodeAt(pos) === 101)) {\n      pos++;\n      if (pos < text.length && text.charCodeAt(pos) === 43 || text.charCodeAt(pos) === 45) {\n        pos++;\n      }\n      if (pos < text.length && isDigit(text.charCodeAt(pos))) {\n        pos++;\n        while (pos < text.length && isDigit(text.charCodeAt(pos))) {\n          pos++;\n        }\n        end = pos;\n      } else {\n        scanError = 3;\n      }\n    }\n    return text.substring(start, end);\n  }\n  function scanString() {\n    let result = \"\", start = pos;\n    while (true) {\n      if (pos >= len) {\n        result += text.substring(start, pos);\n        scanError = 2;\n        break;\n      }\n      const ch = text.charCodeAt(pos);\n      if (ch === 34) {\n        result += text.substring(start, pos);\n        pos++;\n        break;\n      }\n      if (ch === 92) {\n        result += text.substring(start, pos);\n        pos++;\n        if (pos >= len) {\n          scanError = 2;\n          break;\n        }\n        const ch2 = text.charCodeAt(pos++);\n        switch (ch2) {\n          case 34:\n            result += '\"';\n            break;\n          case 92:\n            result += \"\\\\\";\n            break;\n          case 47:\n            result += \"/\";\n            break;\n          case 98:\n            result += \"\\b\";\n            break;\n          case 102:\n            result += \"\\f\";\n            break;\n          case 110:\n            result += \"\\n\";\n            break;\n          case 114:\n            result += \"\\r\";\n            break;\n          case 116:\n            result += \"\t\";\n            break;\n          case 117:\n            const ch3 = scanHexDigits(4, true);\n            if (ch3 >= 0) {\n              result += String.fromCharCode(ch3);\n            } else {\n              scanError = 4;\n            }\n            break;\n          default:\n            scanError = 5;\n        }\n        start = pos;\n        continue;\n      }\n      if (ch >= 0 && ch <= 31) {\n        if (isLineBreak(ch)) {\n          result += text.substring(start, pos);\n          scanError = 2;\n          break;\n        } else {\n          scanError = 6;\n        }\n      }\n      pos++;\n    }\n    return result;\n  }\n  function scanNext() {\n    value = \"\";\n    scanError = 0;\n    tokenOffset = pos;\n    lineStartOffset = lineNumber;\n    prevTokenLineStartOffset = tokenLineStartOffset;\n    if (pos >= len) {\n      tokenOffset = len;\n      return token = 17;\n    }\n    let code = text.charCodeAt(pos);\n    if (isWhiteSpace(code)) {\n      do {\n        pos++;\n        value += String.fromCharCode(code);\n        code = text.charCodeAt(pos);\n      } while (isWhiteSpace(code));\n      return token = 15;\n    }\n    if (isLineBreak(code)) {\n      pos++;\n      value += String.fromCharCode(code);\n      if (code === 13 && text.charCodeAt(pos) === 10) {\n        pos++;\n        value += \"\\n\";\n      }\n      lineNumber++;\n      tokenLineStartOffset = pos;\n      return token = 14;\n    }\n    switch (code) {\n      case 123:\n        pos++;\n        return token = 1;\n      case 125:\n        pos++;\n        return token = 2;\n      case 91:\n        pos++;\n        return token = 3;\n      case 93:\n        pos++;\n        return token = 4;\n      case 58:\n        pos++;\n        return token = 6;\n      case 44:\n        pos++;\n        return token = 5;\n      case 34:\n        pos++;\n        value = scanString();\n        return token = 10;\n      case 47:\n        const start = pos - 1;\n        if (text.charCodeAt(pos + 1) === 47) {\n          pos += 2;\n          while (pos < len) {\n            if (isLineBreak(text.charCodeAt(pos))) {\n              break;\n            }\n            pos++;\n          }\n          value = text.substring(start, pos);\n          return token = 12;\n        }\n        if (text.charCodeAt(pos + 1) === 42) {\n          pos += 2;\n          const safeLength = len - 1;\n          let commentClosed = false;\n          while (pos < safeLength) {\n            const ch = text.charCodeAt(pos);\n            if (ch === 42 && text.charCodeAt(pos + 1) === 47) {\n              pos += 2;\n              commentClosed = true;\n              break;\n            }\n            pos++;\n            if (isLineBreak(ch)) {\n              if (ch === 13 && text.charCodeAt(pos) === 10) {\n                pos++;\n              }\n              lineNumber++;\n              tokenLineStartOffset = pos;\n            }\n          }\n          if (!commentClosed) {\n            pos++;\n            scanError = 1;\n          }\n          value = text.substring(start, pos);\n          return token = 13;\n        }\n        value += String.fromCharCode(code);\n        pos++;\n        return token = 16;\n      case 45:\n        value += String.fromCharCode(code);\n        pos++;\n        if (pos === len || !isDigit(text.charCodeAt(pos))) {\n          return token = 16;\n        }\n      case 48:\n      case 49:\n      case 50:\n      case 51:\n      case 52:\n      case 53:\n      case 54:\n      case 55:\n      case 56:\n      case 57:\n        value += scanNumber();\n        return token = 11;\n      default:\n        while (pos < len && isUnknownContentCharacter(code)) {\n          pos++;\n          code = text.charCodeAt(pos);\n        }\n        if (tokenOffset !== pos) {\n          value = text.substring(tokenOffset, pos);\n          switch (value) {\n            case \"true\":\n              return token = 8;\n            case \"false\":\n              return token = 9;\n            case \"null\":\n              return token = 7;\n          }\n          return token = 16;\n        }\n        value += String.fromCharCode(code);\n        pos++;\n        return token = 16;\n    }\n  }\n  function isUnknownContentCharacter(code) {\n    if (isWhiteSpace(code) || isLineBreak(code)) {\n      return false;\n    }\n    switch (code) {\n      case 125:\n      case 93:\n      case 123:\n      case 91:\n      case 34:\n      case 58:\n      case 44:\n      case 47:\n        return false;\n    }\n    return true;\n  }\n  function scanNextNonTrivia() {\n    let result;\n    do {\n      result = scanNext();\n    } while (result >= 12 && result <= 15);\n    return result;\n  }\n  return {\n    setPosition,\n    getPosition: () => pos,\n    scan: ignoreTrivia ? scanNextNonTrivia : scanNext,\n    getToken: () => token,\n    getTokenValue: () => value,\n    getTokenOffset: () => tokenOffset,\n    getTokenLength: () => pos - tokenOffset,\n    getTokenStartLine: () => lineStartOffset,\n    getTokenStartCharacter: () => tokenOffset - prevTokenLineStartOffset,\n    getTokenError: () => scanError\n  };\n}\nfunction isWhiteSpace(ch) {\n  return ch === 32 || ch === 9;\n}\nfunction isLineBreak(ch) {\n  return ch === 10 || ch === 13;\n}\nfunction isDigit(ch) {\n  return ch >= 48 && ch <= 57;\n}\nvar CharacterCodes;\n(function(CharacterCodes2) {\n  CharacterCodes2[CharacterCodes2[\"lineFeed\"] = 10] = \"lineFeed\";\n  CharacterCodes2[CharacterCodes2[\"carriageReturn\"] = 13] = \"carriageReturn\";\n  CharacterCodes2[CharacterCodes2[\"space\"] = 32] = \"space\";\n  CharacterCodes2[CharacterCodes2[\"_0\"] = 48] = \"_0\";\n  CharacterCodes2[CharacterCodes2[\"_1\"] = 49] = \"_1\";\n  CharacterCodes2[CharacterCodes2[\"_2\"] = 50] = \"_2\";\n  CharacterCodes2[CharacterCodes2[\"_3\"] = 51] = \"_3\";\n  CharacterCodes2[CharacterCodes2[\"_4\"] = 52] = \"_4\";\n  CharacterCodes2[CharacterCodes2[\"_5\"] = 53] = \"_5\";\n  CharacterCodes2[CharacterCodes2[\"_6\"] = 54] = \"_6\";\n  CharacterCodes2[CharacterCodes2[\"_7\"] = 55] = \"_7\";\n  CharacterCodes2[CharacterCodes2[\"_8\"] = 56] = \"_8\";\n  CharacterCodes2[CharacterCodes2[\"_9\"] = 57] = \"_9\";\n  CharacterCodes2[CharacterCodes2[\"a\"] = 97] = \"a\";\n  CharacterCodes2[CharacterCodes2[\"b\"] = 98] = \"b\";\n  CharacterCodes2[CharacterCodes2[\"c\"] = 99] = \"c\";\n  CharacterCodes2[CharacterCodes2[\"d\"] = 100] = \"d\";\n  CharacterCodes2[CharacterCodes2[\"e\"] = 101] = \"e\";\n  CharacterCodes2[CharacterCodes2[\"f\"] = 102] = \"f\";\n  CharacterCodes2[CharacterCodes2[\"g\"] = 103] = \"g\";\n  CharacterCodes2[CharacterCodes2[\"h\"] = 104] = \"h\";\n  CharacterCodes2[CharacterCodes2[\"i\"] = 105] = \"i\";\n  CharacterCodes2[CharacterCodes2[\"j\"] = 106] = \"j\";\n  CharacterCodes2[CharacterCodes2[\"k\"] = 107] = \"k\";\n  CharacterCodes2[CharacterCodes2[\"l\"] = 108] = \"l\";\n  CharacterCodes2[CharacterCodes2[\"m\"] = 109] = \"m\";\n  CharacterCodes2[CharacterCodes2[\"n\"] = 110] = \"n\";\n  CharacterCodes2[CharacterCodes2[\"o\"] = 111] = \"o\";\n  CharacterCodes2[CharacterCodes2[\"p\"] = 112] = \"p\";\n  CharacterCodes2[CharacterCodes2[\"q\"] = 113] = \"q\";\n  CharacterCodes2[CharacterCodes2[\"r\"] = 114] = \"r\";\n  CharacterCodes2[CharacterCodes2[\"s\"] = 115] = \"s\";\n  CharacterCodes2[CharacterCodes2[\"t\"] = 116] = \"t\";\n  CharacterCodes2[CharacterCodes2[\"u\"] = 117] = \"u\";\n  CharacterCodes2[CharacterCodes2[\"v\"] = 118] = \"v\";\n  CharacterCodes2[CharacterCodes2[\"w\"] = 119] = \"w\";\n  CharacterCodes2[CharacterCodes2[\"x\"] = 120] = \"x\";\n  CharacterCodes2[CharacterCodes2[\"y\"] = 121] = \"y\";\n  CharacterCodes2[CharacterCodes2[\"z\"] = 122] = \"z\";\n  CharacterCodes2[CharacterCodes2[\"A\"] = 65] = \"A\";\n  CharacterCodes2[CharacterCodes2[\"B\"] = 66] = \"B\";\n  CharacterCodes2[CharacterCodes2[\"C\"] = 67] = \"C\";\n  CharacterCodes2[CharacterCodes2[\"D\"] = 68] = \"D\";\n  CharacterCodes2[CharacterCodes2[\"E\"] = 69] = \"E\";\n  CharacterCodes2[CharacterCodes2[\"F\"] = 70] = \"F\";\n  CharacterCodes2[CharacterCodes2[\"G\"] = 71] = \"G\";\n  CharacterCodes2[CharacterCodes2[\"H\"] = 72] = \"H\";\n  CharacterCodes2[CharacterCodes2[\"I\"] = 73] = \"I\";\n  CharacterCodes2[CharacterCodes2[\"J\"] = 74] = \"J\";\n  CharacterCodes2[CharacterCodes2[\"K\"] = 75] = \"K\";\n  CharacterCodes2[CharacterCodes2[\"L\"] = 76] = \"L\";\n  CharacterCodes2[CharacterCodes2[\"M\"] = 77] = \"M\";\n  CharacterCodes2[CharacterCodes2[\"N\"] = 78] = \"N\";\n  CharacterCodes2[CharacterCodes2[\"O\"] = 79] = \"O\";\n  CharacterCodes2[CharacterCodes2[\"P\"] = 80] = \"P\";\n  CharacterCodes2[CharacterCodes2[\"Q\"] = 81] = \"Q\";\n  CharacterCodes2[CharacterCodes2[\"R\"] = 82] = \"R\";\n  CharacterCodes2[CharacterCodes2[\"S\"] = 83] = \"S\";\n  CharacterCodes2[CharacterCodes2[\"T\"] = 84] = \"T\";\n  CharacterCodes2[CharacterCodes2[\"U\"] = 85] = \"U\";\n  CharacterCodes2[CharacterCodes2[\"V\"] = 86] = \"V\";\n  CharacterCodes2[CharacterCodes2[\"W\"] = 87] = \"W\";\n  CharacterCodes2[CharacterCodes2[\"X\"] = 88] = \"X\";\n  CharacterCodes2[CharacterCodes2[\"Y\"] = 89] = \"Y\";\n  CharacterCodes2[CharacterCodes2[\"Z\"] = 90] = \"Z\";\n  CharacterCodes2[CharacterCodes2[\"asterisk\"] = 42] = \"asterisk\";\n  CharacterCodes2[CharacterCodes2[\"backslash\"] = 92] = \"backslash\";\n  CharacterCodes2[CharacterCodes2[\"closeBrace\"] = 125] = \"closeBrace\";\n  CharacterCodes2[CharacterCodes2[\"closeBracket\"] = 93] = \"closeBracket\";\n  CharacterCodes2[CharacterCodes2[\"colon\"] = 58] = \"colon\";\n  CharacterCodes2[CharacterCodes2[\"comma\"] = 44] = \"comma\";\n  CharacterCodes2[CharacterCodes2[\"dot\"] = 46] = \"dot\";\n  CharacterCodes2[CharacterCodes2[\"doubleQuote\"] = 34] = \"doubleQuote\";\n  CharacterCodes2[CharacterCodes2[\"minus\"] = 45] = \"minus\";\n  CharacterCodes2[CharacterCodes2[\"openBrace\"] = 123] = \"openBrace\";\n  CharacterCodes2[CharacterCodes2[\"openBracket\"] = 91] = \"openBracket\";\n  CharacterCodes2[CharacterCodes2[\"plus\"] = 43] = \"plus\";\n  CharacterCodes2[CharacterCodes2[\"slash\"] = 47] = \"slash\";\n  CharacterCodes2[CharacterCodes2[\"formFeed\"] = 12] = \"formFeed\";\n  CharacterCodes2[CharacterCodes2[\"tab\"] = 9] = \"tab\";\n})(CharacterCodes || (CharacterCodes = {}));\n\n// node_modules/jsonc-parser/lib/esm/impl/string-intern.js\nvar cachedSpaces = new Array(20).fill(0).map((_, index) => {\n  return \" \".repeat(index);\n});\nvar maxCachedValues = 200;\nvar cachedBreakLinesWithSpaces = {\n  \" \": {\n    \"\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\n\" + \" \".repeat(index);\n    }),\n    \"\\r\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\" + \" \".repeat(index);\n    }),\n    \"\\r\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\\n\" + \" \".repeat(index);\n    })\n  },\n  \"\t\": {\n    \"\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\n\" + \"\t\".repeat(index);\n    }),\n    \"\\r\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\" + \"\t\".repeat(index);\n    }),\n    \"\\r\\n\": new Array(maxCachedValues).fill(0).map((_, index) => {\n      return \"\\r\\n\" + \"\t\".repeat(index);\n    })\n  }\n};\n\n// node_modules/jsonc-parser/lib/esm/impl/parser.js\nvar ParseOptions;\n(function(ParseOptions2) {\n  ParseOptions2.DEFAULT = {\n    allowTrailingComma: false\n  };\n})(ParseOptions || (ParseOptions = {}));\n\n// node_modules/jsonc-parser/lib/esm/main.js\nvar createScanner2 = createScanner;\nvar ScanError;\n(function(ScanError2) {\n  ScanError2[ScanError2[\"None\"] = 0] = \"None\";\n  ScanError2[ScanError2[\"UnexpectedEndOfComment\"] = 1] = \"UnexpectedEndOfComment\";\n  ScanError2[ScanError2[\"UnexpectedEndOfString\"] = 2] = \"UnexpectedEndOfString\";\n  ScanError2[ScanError2[\"UnexpectedEndOfNumber\"] = 3] = \"UnexpectedEndOfNumber\";\n  ScanError2[ScanError2[\"InvalidUnicode\"] = 4] = \"InvalidUnicode\";\n  ScanError2[ScanError2[\"InvalidEscapeCharacter\"] = 5] = \"InvalidEscapeCharacter\";\n  ScanError2[ScanError2[\"InvalidCharacter\"] = 6] = \"InvalidCharacter\";\n})(ScanError || (ScanError = {}));\nvar SyntaxKind;\n(function(SyntaxKind2) {\n  SyntaxKind2[SyntaxKind2[\"OpenBraceToken\"] = 1] = \"OpenBraceToken\";\n  SyntaxKind2[SyntaxKind2[\"CloseBraceToken\"] = 2] = \"CloseBraceToken\";\n  SyntaxKind2[SyntaxKind2[\"OpenBracketToken\"] = 3] = \"OpenBracketToken\";\n  SyntaxKind2[SyntaxKind2[\"CloseBracketToken\"] = 4] = \"CloseBracketToken\";\n  SyntaxKind2[SyntaxKind2[\"CommaToken\"] = 5] = \"CommaToken\";\n  SyntaxKind2[SyntaxKind2[\"ColonToken\"] = 6] = \"ColonToken\";\n  SyntaxKind2[SyntaxKind2[\"NullKeyword\"] = 7] = \"NullKeyword\";\n  SyntaxKind2[SyntaxKind2[\"TrueKeyword\"] = 8] = \"TrueKeyword\";\n  SyntaxKind2[SyntaxKind2[\"FalseKeyword\"] = 9] = \"FalseKeyword\";\n  SyntaxKind2[SyntaxKind2[\"StringLiteral\"] = 10] = \"StringLiteral\";\n  SyntaxKind2[SyntaxKind2[\"NumericLiteral\"] = 11] = \"NumericLiteral\";\n  SyntaxKind2[SyntaxKind2[\"LineCommentTrivia\"] = 12] = \"LineCommentTrivia\";\n  SyntaxKind2[SyntaxKind2[\"BlockCommentTrivia\"] = 13] = \"BlockCommentTrivia\";\n  SyntaxKind2[SyntaxKind2[\"LineBreakTrivia\"] = 14] = \"LineBreakTrivia\";\n  SyntaxKind2[SyntaxKind2[\"Trivia\"] = 15] = \"Trivia\";\n  SyntaxKind2[SyntaxKind2[\"Unknown\"] = 16] = \"Unknown\";\n  SyntaxKind2[SyntaxKind2[\"EOF\"] = 17] = \"EOF\";\n})(SyntaxKind || (SyntaxKind = {}));\nvar ParseErrorCode;\n(function(ParseErrorCode2) {\n  ParseErrorCode2[ParseErrorCode2[\"InvalidSymbol\"] = 1] = \"InvalidSymbol\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidNumberFormat\"] = 2] = \"InvalidNumberFormat\";\n  ParseErrorCode2[ParseErrorCode2[\"PropertyNameExpected\"] = 3] = \"PropertyNameExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"ValueExpected\"] = 4] = \"ValueExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"ColonExpected\"] = 5] = \"ColonExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CommaExpected\"] = 6] = \"CommaExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CloseBraceExpected\"] = 7] = \"CloseBraceExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"CloseBracketExpected\"] = 8] = \"CloseBracketExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"EndOfFileExpected\"] = 9] = \"EndOfFileExpected\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidCommentToken\"] = 10] = \"InvalidCommentToken\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfComment\"] = 11] = \"UnexpectedEndOfComment\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfString\"] = 12] = \"UnexpectedEndOfString\";\n  ParseErrorCode2[ParseErrorCode2[\"UnexpectedEndOfNumber\"] = 13] = \"UnexpectedEndOfNumber\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidUnicode\"] = 14] = \"InvalidUnicode\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidEscapeCharacter\"] = 15] = \"InvalidEscapeCharacter\";\n  ParseErrorCode2[ParseErrorCode2[\"InvalidCharacter\"] = 16] = \"InvalidCharacter\";\n})(ParseErrorCode || (ParseErrorCode = {}));\n\n// src/language/json/tokenization.ts\nfunction createTokenizationSupport(supportComments) {\n  return {\n    getInitialState: () => new JSONState(null, null, false, null),\n    tokenize: (line, state) => tokenize(supportComments, line, state)\n  };\n}\nvar TOKEN_DELIM_OBJECT = \"delimiter.bracket.json\";\nvar TOKEN_DELIM_ARRAY = \"delimiter.array.json\";\nvar TOKEN_DELIM_COLON = \"delimiter.colon.json\";\nvar TOKEN_DELIM_COMMA = \"delimiter.comma.json\";\nvar TOKEN_VALUE_BOOLEAN = \"keyword.json\";\nvar TOKEN_VALUE_NULL = \"keyword.json\";\nvar TOKEN_VALUE_STRING = \"string.value.json\";\nvar TOKEN_VALUE_NUMBER = \"number.json\";\nvar TOKEN_PROPERTY_NAME = \"string.key.json\";\nvar TOKEN_COMMENT_BLOCK = \"comment.block.json\";\nvar TOKEN_COMMENT_LINE = \"comment.line.json\";\nvar ParentsStack = class _ParentsStack {\n  constructor(parent, type) {\n    this.parent = parent;\n    this.type = type;\n  }\n  static pop(parents) {\n    if (parents) {\n      return parents.parent;\n    }\n    return null;\n  }\n  static push(parents, type) {\n    return new _ParentsStack(parents, type);\n  }\n  static equals(a, b) {\n    if (!a && !b) {\n      return true;\n    }\n    if (!a || !b) {\n      return false;\n    }\n    while (a && b) {\n      if (a === b) {\n        return true;\n      }\n      if (a.type !== b.type) {\n        return false;\n      }\n      a = a.parent;\n      b = b.parent;\n    }\n    return true;\n  }\n};\nvar JSONState = class _JSONState {\n  constructor(state, scanError, lastWasColon, parents) {\n    this._state = state;\n    this.scanError = scanError;\n    this.lastWasColon = lastWasColon;\n    this.parents = parents;\n  }\n  clone() {\n    return new _JSONState(this._state, this.scanError, this.lastWasColon, this.parents);\n  }\n  equals(other) {\n    if (other === this) {\n      return true;\n    }\n    if (!other || !(other instanceof _JSONState)) {\n      return false;\n    }\n    return this.scanError === other.scanError && this.lastWasColon === other.lastWasColon && ParentsStack.equals(this.parents, other.parents);\n  }\n  getStateData() {\n    return this._state;\n  }\n  setStateData(state) {\n    this._state = state;\n  }\n};\nfunction tokenize(comments, line, state, offsetDelta = 0) {\n  let numberOfInsertedCharacters = 0;\n  let adjustOffset = false;\n  switch (state.scanError) {\n    case 2 /* UnexpectedEndOfString */:\n      line = '\"' + line;\n      numberOfInsertedCharacters = 1;\n      break;\n    case 1 /* UnexpectedEndOfComment */:\n      line = \"/*\" + line;\n      numberOfInsertedCharacters = 2;\n      break;\n  }\n  const scanner = createScanner2(line);\n  let lastWasColon = state.lastWasColon;\n  let parents = state.parents;\n  const ret = {\n    tokens: [],\n    endState: state.clone()\n  };\n  while (true) {\n    let offset = offsetDelta + scanner.getPosition();\n    let type = \"\";\n    const kind = scanner.scan();\n    if (kind === 17 /* EOF */) {\n      break;\n    }\n    if (offset === offsetDelta + scanner.getPosition()) {\n      throw new Error(\n        \"Scanner did not advance, next 3 characters are: \" + line.substr(scanner.getPosition(), 3)\n      );\n    }\n    if (adjustOffset) {\n      offset -= numberOfInsertedCharacters;\n    }\n    adjustOffset = numberOfInsertedCharacters > 0;\n    switch (kind) {\n      case 1 /* OpenBraceToken */:\n        parents = ParentsStack.push(parents, 0 /* Object */);\n        type = TOKEN_DELIM_OBJECT;\n        lastWasColon = false;\n        break;\n      case 2 /* CloseBraceToken */:\n        parents = ParentsStack.pop(parents);\n        type = TOKEN_DELIM_OBJECT;\n        lastWasColon = false;\n        break;\n      case 3 /* OpenBracketToken */:\n        parents = ParentsStack.push(parents, 1 /* Array */);\n        type = TOKEN_DELIM_ARRAY;\n        lastWasColon = false;\n        break;\n      case 4 /* CloseBracketToken */:\n        parents = ParentsStack.pop(parents);\n        type = TOKEN_DELIM_ARRAY;\n        lastWasColon = false;\n        break;\n      case 6 /* ColonToken */:\n        type = TOKEN_DELIM_COLON;\n        lastWasColon = true;\n        break;\n      case 5 /* CommaToken */:\n        type = TOKEN_DELIM_COMMA;\n        lastWasColon = false;\n        break;\n      case 8 /* TrueKeyword */:\n      case 9 /* FalseKeyword */:\n        type = TOKEN_VALUE_BOOLEAN;\n        lastWasColon = false;\n        break;\n      case 7 /* NullKeyword */:\n        type = TOKEN_VALUE_NULL;\n        lastWasColon = false;\n        break;\n      case 10 /* StringLiteral */:\n        const currentParent = parents ? parents.type : 0 /* Object */;\n        const inArray = currentParent === 1 /* Array */;\n        type = lastWasColon || inArray ? TOKEN_VALUE_STRING : TOKEN_PROPERTY_NAME;\n        lastWasColon = false;\n        break;\n      case 11 /* NumericLiteral */:\n        type = TOKEN_VALUE_NUMBER;\n        lastWasColon = false;\n        break;\n    }\n    if (comments) {\n      switch (kind) {\n        case 12 /* LineCommentTrivia */:\n          type = TOKEN_COMMENT_LINE;\n          break;\n        case 13 /* BlockCommentTrivia */:\n          type = TOKEN_COMMENT_BLOCK;\n          break;\n      }\n    }\n    ret.endState = new JSONState(\n      state.getStateData(),\n      scanner.getTokenError(),\n      lastWasColon,\n      parents\n    );\n    ret.tokens.push({\n      startIndex: offset,\n      scopes: type\n    });\n  }\n  return ret;\n}\n\n// src/language/json/jsonMode.ts\nvar worker;\nfunction getWorker() {\n  return new Promise((resolve, reject) => {\n    if (!worker) {\n      return reject(\"JSON not registered!\");\n    }\n    resolve(worker);\n  });\n}\nvar JSONDiagnosticsAdapter = class extends DiagnosticsAdapter {\n  constructor(languageId, worker2, defaults) {\n    super(languageId, worker2, defaults.onDidChange);\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onWillDisposeModel((model) => {\n        this._resetSchema(model.uri);\n      })\n    );\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        this._resetSchema(event.model.uri);\n      })\n    );\n  }\n  _resetSchema(resource) {\n    this._worker().then((worker2) => {\n      worker2.resetSchema(resource.toString());\n    });\n  }\n};\nfunction setupMode(defaults) {\n  const disposables = [];\n  const providers = [];\n  const client = new WorkerManager(defaults);\n  disposables.push(client);\n  worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  function registerProviders() {\n    const { languageId, modeConfiguration: modeConfiguration2 } = defaults;\n    disposeAll(providers);\n    if (modeConfiguration2.documentFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentFormattingEditProvider(\n          languageId,\n          new DocumentFormattingEditProvider(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.documentRangeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(\n          languageId,\n          new DocumentRangeFormattingEditProvider(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.completionItems) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCompletionItemProvider(\n          languageId,\n          new CompletionAdapter(worker, [\" \", \":\", '\"'])\n        )\n      );\n    }\n    if (modeConfiguration2.hovers) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerHoverProvider(languageId, new HoverAdapter(worker))\n      );\n    }\n    if (modeConfiguration2.documentSymbols) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentSymbolProvider(\n          languageId,\n          new DocumentSymbolAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.tokens) {\n      providers.push(monaco_editor_core_exports.languages.setTokensProvider(languageId, createTokenizationSupport(true)));\n    }\n    if (modeConfiguration2.colors) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerColorProvider(\n          languageId,\n          new DocumentColorAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.foldingRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerFoldingRangeProvider(\n          languageId,\n          new FoldingRangeAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration2.diagnostics) {\n      providers.push(new JSONDiagnosticsAdapter(languageId, worker, defaults));\n    }\n    if (modeConfiguration2.selectionRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerSelectionRangeProvider(\n          languageId,\n          new SelectionRangeAdapter(worker)\n        )\n      );\n    }\n  }\n  registerProviders();\n  disposables.push(monaco_editor_core_exports.languages.setLanguageConfiguration(defaults.languageId, richEditConfiguration));\n  let modeConfiguration = defaults.modeConfiguration;\n  defaults.onDidChange((newDefaults) => {\n    if (newDefaults.modeConfiguration !== modeConfiguration) {\n      modeConfiguration = newDefaults.modeConfiguration;\n      registerProviders();\n    }\n  });\n  disposables.push(asDisposable(providers));\n  return asDisposable(disposables);\n}\nfunction asDisposable(disposables) {\n  return { dispose: () => disposeAll(disposables) };\n}\nfunction disposeAll(disposables) {\n  while (disposables.length) {\n    disposables.pop().dispose();\n  }\n}\nvar richEditConfiguration = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\[\\{\\]\\}\\:\\\"\\,\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ]\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/language/json/jsonMode.js\n"));

/***/ })

}]);