"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/AIAssistant/ChatInterface.tsx":
/*!******************************************************!*\
  !*** ./src/components/AIAssistant/ChatInterface.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chatHistoryService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chatHistoryService */ \"(app-pages-browser)/./src/services/chatHistoryService.ts\");\n/* harmony import */ var _services_fileTreeService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\");\n/* harmony import */ var _services_diffToolService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/diffToolService */ \"(app-pages-browser)/./src/services/diffToolService.ts\");\n/* harmony import */ var _services_simpleReplaceService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/simpleReplaceService */ \"(app-pages-browser)/./src/services/simpleReplaceService.ts\");\n/* harmony import */ var _services_aiService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/aiService */ \"(app-pages-browser)/./src/services/aiService.ts\");\n/* harmony import */ var _utils_pathUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/pathUtils */ \"(app-pages-browser)/./src/utils/pathUtils.ts\");\n/* harmony import */ var _utils_SelectionManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/SelectionManager */ \"(app-pages-browser)/./src/utils/SelectionManager.ts\");\n/* harmony import */ var _SessionManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./SessionManager */ \"(app-pages-browser)/./src/components/AIAssistant/SessionManager.tsx\");\n/* harmony import */ var _FileAssociationTreeDialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./FileAssociationTreeDialog */ \"(app-pages-browser)/./src/components/AIAssistant/FileAssociationTreeDialog.tsx\");\n/* harmony import */ var _CompactDiffDisplay__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./CompactDiffDisplay */ \"(app-pages-browser)/./src/components/AIAssistant/CompactDiffDisplay.tsx\");\n/* harmony import */ var _MediaUploader__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./MediaUploader */ \"(app-pages-browser)/./src/components/AIAssistant/MediaUploader.tsx\");\n/* harmony import */ var _HelperResponseLayer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./HelperResponseLayer */ \"(app-pages-browser)/./src/components/AIAssistant/HelperResponseLayer.tsx\");\n/* harmony import */ var _PromptManagerModal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./PromptManagerModal */ \"(app-pages-browser)/./src/components/AIAssistant/PromptManagerModal.tsx\");\n/* harmony import */ var _MultipleComparisonModal__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./MultipleComparisonModal */ \"(app-pages-browser)/./src/components/AIAssistant/MultipleComparisonModal.tsx\");\n/* harmony import */ var _utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/humanBlockParser */ \"(app-pages-browser)/./src/utils/humanBlockParser.ts\");\n/* harmony import */ var _services_promptConfigService__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/services/promptConfigService */ \"(app-pages-browser)/./src/services/promptConfigService.ts\");\n/**\r\n * AI对话界面组件\r\n * 传统的对话形式界面，用于与AI进行实时对话\r\n * 优化版本：解决文本选择被频繁重渲染取消的问题\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n // Renamed to avoid conflict\n\n\n\n\n\n\n\n\n\n\nconst ChatInterface = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c = _s(function ChatInterface(param, ref) {\n    let { currentConfig, onSendMessage, responseContent, isStreaming, isComplete, error, onRetry, onCopy, onStop, onInsertToEditor, onContentInsert, onMessageContentInsert, isLoading, className = \"\", currentPersona, onChatHistoryChange, // 文件关联相关props\n    associatedFiles = [], onFilesChange = ()=>{}, onShowFileAssociation = ()=>{}, // 🔧 AI响应状态清理回调\n    onClearAIResponse, // 🔧 作品ID，用于文件关联\n    artworkId, // 🔧 文件选择回调，用于跳转到编辑器\n    onFileSelect, // 🔧 详细对比回调，用于打开详细差异对比视图\n    onOpenDetailedDiff, // 🔧 聚焦文件状态，由父组件管理\n    focusedFile, // 🔧 媒体文件变化回调\n    onMediaFilesChange, // 🔧 受众设置回调\n    onShowAudienceSettings, // 🔧 会话管理回调\n    onShowSessionManager, // 🔧 多项对比消息构建回调\n    onMultipleComparisonSend } = param;\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [chatHistory, setChatHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSessionManager, setShowSessionManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFileAssociation, setShowFileAssociation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPromptManager, setShowPromptManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activePromptConfig, setActivePromptConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isFileAssociationCollapsed, setIsFileAssociationCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filePaths, setFilePaths] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [showToolsPanel, setShowToolsPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 消息工具面板状态 - 记录哪条消息的工具面板正在显示\n    const [activeMessageTools, setActiveMessageTools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 编辑消息状态\n    const [editingMessageId, setEditingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingContent, setEditingContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 多项对比弹窗状态\n    const [showMultipleComparison, setShowMultipleComparison] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [comparisonMessageContent, setComparisonMessageContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [comparisonTriggerMessageIndex, setComparisonTriggerMessageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    // 共享状态\n    const [availableModels, setAvailableModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingModels, setIsLoadingModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 单模型生成相关状态\n    const [selectedSingleModel, setSelectedSingleModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [singleModelResults, setSingleModelResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [streamingSingleResults, setStreamingSingleResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [generationCount, setGenerationCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3);\n    // 多模型对比相关状态\n    const [selectedMultiModels, setSelectedMultiModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [multiModelResults, setMultiModelResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [streamingMultiModels, setStreamingMultiModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // 🔧 聚焦文件状态现在由父组件管理，通过props传递\n    // 媒体上传相关状态\n    const [uploadedMedia, setUploadedMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showMediaUploader, setShowMediaUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMediaList, setShowMediaList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 消息层状态管理\n    const [messageLayers, setMessageLayers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pendingIntegratedMessage, setPendingIntegratedMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [processedMessages, setProcessedMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // 🔧 调试监听已移除，避免控制台日志过多\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const aiResponseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 🔧 响应内容引用，避免useEffect依赖responseContent导致重复渲染\n    const responseContentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    // 🔧 文本选择管理器\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 初始化选择管理器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!selectionManagerRef.current) {\n            selectionManagerRef.current = new _utils_SelectionManager__WEBPACK_IMPORTED_MODULE_8__.SelectionManager();\n        }\n        return ()=>{\n            if (selectionManagerRef.current && aiResponseRef.current) {\n                selectionManagerRef.current.stopListening(aiResponseRef.current);\n            }\n        };\n    }, []);\n    // 点击外部关闭工具面板\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (showToolsPanel) {\n                const target = event.target;\n                if (!target.closest(\".tools-panel-container\")) {\n                    setShowToolsPanel(false);\n                }\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        showToolsPanel\n    ]);\n    // 暂时禁用选择监听\n    // useEffect(() => {\n    //   if (selectionManagerRef.current && aiResponseRef.current) {\n    //     selectionManagerRef.current.startListening(aiResponseRef.current)\n    //   }\n    //   return () => {\n    //     if (selectionManagerRef.current && aiResponseRef.current) {\n    //       selectionManagerRef.current.stopListening(aiResponseRef.current)\n    //     }\n    //   }\n    // }, [])\n    // 🔧 同步responseContent到ref，避免useEffect直接依赖responseContent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        responseContentRef.current = responseContent;\n    }, [\n        responseContent\n    ]);\n    // 🔧 修复重复渲染问题：使用useMemo确保chatHistoryService引用稳定\n    const chatHistoryService = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>_services_chatHistoryService__WEBPACK_IMPORTED_MODULE_2__.ChatHistoryService.getInstance(), []);\n    // 辅助函数：标准化路径\n    const normalizePath = (path)=>{\n        if (!path) return \"\";\n        // 使用导入的工具函数，避免命名冲突\n        return (0,_utils_pathUtils__WEBPACK_IMPORTED_MODULE_7__.normalizePath)(path).toLowerCase();\n    };\n    // 辅助函数：根据路径查找文件ID\n    const findFileIdByPath = (rootNode, targetPath)=>{\n        // 标准化目标路径，确保相对路径和绝对路径都能正确匹配\n        const normalizedTargetPath = normalizePath(targetPath);\n        const searchNode = (node)=>{\n            // 标准化当前节点路径进行比较\n            if (normalizePath(node.path) === normalizedTargetPath) {\n                return node.id;\n            }\n            if (node.children) {\n                for (const child of node.children){\n                    const foundId = searchNode(child);\n                    if (foundId) {\n                        return foundId;\n                    }\n                }\n            }\n            return null;\n        };\n        return searchNode(rootNode);\n    };\n    // 辅助函数：解析替换内容（仅支持JSON格式）\n    const parseReplaceContent = (content, find)=>{\n        console.log(\"\\uD83D\\uDD0D parseReplaceContent 调用参数:\", {\n            content: content.substring(0, 100) + (content.length > 100 ? \"...\" : \"\"),\n            find: find,\n            findType: typeof find,\n            findLength: find === null || find === void 0 ? void 0 : find.length\n        });\n        // JSON格式：直接使用find和content参数\n        if (find) {\n            console.log(\"✅ 使用JSON格式解析:\", {\n                find,\n                content: content.substring(0, 100) + \"...\"\n            });\n            return [\n                find,\n                content\n            ];\n        }\n        // 如果没有find参数，说明格式不正确\n        console.error(\"❌ 替换操作缺少查找内容\");\n        console.error('\\uD83D\\uDCDD 请使用JSON格式：{\"file\": \"...\", \"type\": \"replace\", \"find\": \"查找内容\", \"content\": \"替换内容\"}');\n        console.error(\"\\uD83D\\uDCDD 实际收到的内容:\", content.substring(0, 200));\n        // 返回错误状态，让调用方处理\n        return [\n            \"\",\n            content\n        ];\n    };\n    // 辅助函数：获取所有文件路径（用于调试）\n    const getAllFilePaths = (rootNode)=>{\n        const paths = [];\n        const collectPaths = (node)=>{\n            if (node.type === \"file\") {\n                paths.push(node.path);\n            }\n            if (node.children) {\n                node.children.forEach(collectPaths);\n            }\n        };\n        collectPaths(rootNode);\n        return paths;\n    };\n    // 辅助函数：根据文件ID查找文件完整路径\n    const findFilePathById = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (fileId1)=>{\n        try {\n            if (!artworkId) return \"未知文件 (\".concat(fileId1.substring(0, 8), \"...)\");\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_3__.FileTreeService.getInstance();\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (fileTreeResult.success && fileTreeResult.data) {\n                const filePath = getFilePath(fileTreeResult.data, fileId1);\n                return filePath || \"未知文件 (\".concat(fileId1.substring(0, 8), \"...)\");\n            }\n            return \"未知文件 (\".concat(fileId1.substring(0, 8), \"...)\");\n        } catch (error) {\n            console.warn(\"获取文件路径失败:\", error);\n            return \"未知文件 (\".concat(fileId1.substring(0, 8), \"...)\");\n        }\n    }, [\n        artworkId\n    ]);\n    // 辅助函数：获取文件的完整路径（不包括作品根节点）\n    const getFilePath = (rootNode, fileId1)=>{\n        // 递归搜索文件树，构建路径\n        const buildPath = function(node, targetId) {\n            let currentPath = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n            // 如果找到目标节点，返回当前路径\n            if (node.id === targetId) {\n                return [\n                    ...currentPath,\n                    node.name\n                ];\n            }\n            // 如果有子节点，递归搜索\n            if (node.children) {\n                for (const child of node.children){\n                    const path = buildPath(child, targetId, [\n                        ...currentPath,\n                        node.name\n                    ]);\n                    if (path) {\n                        return path;\n                    }\n                }\n            }\n            return null;\n        };\n        // 从根节点开始搜索，但不包括根节点名称\n        const path = buildPath(rootNode, fileId1, []);\n        if (path) {\n            // 移除第一个元素（根节点名称）\n            path.shift();\n            return path.join(\"/\");\n        }\n        return fileId1.substring(0, 8) + \"...\"; // 如果找不到路径，返回ID的简短版本\n    };\n    // 递归搜索文件树中的文件名（保留用于兼容性）\n    const searchNodeForName = (node, fileId1)=>{\n        if (node.id === fileId1) {\n            return node.name;\n        }\n        if (node.children) {\n            for (const child of node.children){\n                const result1 = searchNodeForName(child, fileId1);\n                if (result1) return result1;\n            }\n        }\n        return null;\n    };\n    // 🔧 聚焦文件状态现在由父组件统一管理，移除子组件中的事件监听\n    // 组件初始化时加载历史记录和当前会话ID\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeSession = async ()=>{\n            try {\n                // 获取当前会话ID\n                const sessionId = chatHistoryService.getCurrentSessionId();\n                setCurrentSessionId(sessionId);\n                // 加载当前会话的历史记录\n                const storedHistory = await chatHistoryService.getCurrentHistory();\n                if (storedHistory.length > 0) {\n                    setChatHistory(storedHistory);\n                    console.log(\"✅ 已从IndexedDB加载对话历史:\", storedHistory.length, \"条消息\");\n                    // 通知父组件对话历史变化\n                    if (onChatHistoryChange) {\n                        onChatHistoryChange(storedHistory);\n                    }\n                }\n            // 文件关联数据由父组件管理，这里不需要重复加载\n            } catch (error) {\n                console.error(\"❌ 加载对话历史失败:\", error);\n            }\n        };\n        initializeSession();\n    }, [\n        chatHistoryService,\n        onChatHistoryChange\n    ]);\n    // 加载激活的提示词配置\n    const loadActivePromptConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const promptConfigService = _services_promptConfigService__WEBPACK_IMPORTED_MODULE_17__.PromptConfigService.getInstance();\n            const result1 = await promptConfigService.getActiveConfig();\n            if (result1.success && result1.data) {\n                setActivePromptConfig(result1.data);\n            } else {\n                setActivePromptConfig(null);\n            }\n        } catch (error) {\n            console.error(\"加载激活提示词配置失败:\", error);\n            setActivePromptConfig(null);\n        }\n    }, []);\n    // 组件挂载时加载激活配置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadActivePromptConfig();\n    }, [\n        loadActivePromptConfig\n    ]);\n    // 处理提示词配置选择\n    const handlePromptConfigSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setActivePromptConfig(config);\n        console.log(\"\\uD83D\\uDD2E 选择提示词配置:\", config.name);\n        // 重新加载激活配置以确保状态同步\n        setTimeout(()=>{\n            loadActivePromptConfig();\n        }, 100);\n    }, [\n        loadActivePromptConfig\n    ]);\n    // 生成唯一消息ID\n    const generateMessageId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((type)=>{\n        const timestamp = Date.now();\n        const random = Math.random().toString(36).substring(2, 11);\n        return \"\".concat(type, \"-\").concat(timestamp, \"-\").concat(random);\n    }, []);\n    // 添加用户消息到历史记录\n    const addUserMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message)=>{\n        setChatHistory((prev)=>{\n            // 检查消息是否已存在，避免重复添加\n            const exists = prev.some((m)=>m.id === message.id);\n            if (exists) {\n                console.log(\"⚠️ 消息已存在，跳过添加:\", message.content.substring(0, 20));\n                return prev;\n            }\n            const newHistory = [\n                ...prev,\n                message\n            ];\n            console.log(\"✅ 用户消息已添加:\", message.content.substring(0, 20), \"历史长度:\", newHistory.length);\n            // 异步保存到IndexedDB\n            chatHistoryService.saveCurrentHistory(newHistory).catch((error)=>{\n                console.error(\"❌ 保存用户消息失败:\", error);\n            });\n            // 异步通知父组件对话历史变化，避免在渲染过程中同步更新\n            setTimeout(()=>{\n                if (onChatHistoryChange) {\n                    onChatHistoryChange(newHistory);\n                }\n            }, 0);\n            return newHistory;\n        });\n    }, [\n        onChatHistoryChange,\n        chatHistoryService\n    ]);\n    // 处理跳转到编辑器\n    const handleJumpToEditor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filePath)=>{\n        try {\n            if (!artworkId) return;\n            // 通过文件路径查找文件ID\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_3__.FileTreeService.getInstance();\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (fileTreeResult.success && fileTreeResult.data) {\n                const fileId1 = findFileIdByPath(fileTreeResult.data, filePath);\n                if (fileId1) {\n                    // 触发文件选择事件，通知父组件切换到编辑器\n                    onFileSelect === null || onFileSelect === void 0 ? void 0 : onFileSelect(fileId1);\n                    // 可选：设置聚焦状态\n                    const chatHistoryService = _services_chatHistoryService__WEBPACK_IMPORTED_MODULE_2__.ChatHistoryService.getInstance();\n                    await chatHistoryService.setCurrentFocusedFile(fileId1);\n                    console.log(\"\\uD83D\\uDD17 跳转到编辑器:\", filePath);\n                } else {\n                    console.warn(\"⚠️ 未找到文件:\", filePath);\n                }\n            }\n        } catch (error) {\n            console.error(\"❌ 跳转到编辑器失败:\", error);\n        }\n    }, [\n        artworkId,\n        onFileSelect\n    ]);\n    // 处理应用diff修改 - 使用新的简化替换服务\n    const handleApplyDiff = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filePath, content, operation, find)=>{\n        try {\n            var _result;\n            if (!artworkId) return;\n            console.log(\"\\uD83D\\uDE80 开始处理df操作:\", {\n                filePath,\n                operation,\n                contentLength: content.length,\n                hasFindParam: !!find\n            });\n            const simpleReplaceService = _services_simpleReplaceService__WEBPACK_IMPORTED_MODULE_5__.SimpleReplaceService.getInstance();\n            if (operation === \"append\") {\n                // 追加操作\n                const result1 = await simpleReplaceService.appendText(artworkId, filePath, content);\n                if (result1.success) {\n                    var _result_data;\n                    console.log(\"✅ 文本追加成功:\", (_result_data = result1.data) === null || _result_data === void 0 ? void 0 : _result_data.message);\n                } else {\n                    console.error(\"❌ 文本追加失败:\", result1.error);\n                }\n            } else if (operation === \"replace\") {\n                // 替换操作\n                if (!find) {\n                    console.error(\"❌ 替换操作缺少find参数\");\n                    return;\n                }\n                const result1 = await simpleReplaceService.replaceText(artworkId, filePath, find, content);\n                if (result1.success) {\n                    var _result_data1;\n                    console.log(\"✅ 文本替换成功:\", (_result_data1 = result1.data) === null || _result_data1 === void 0 ? void 0 : _result_data1.message);\n                } else {\n                    console.error(\"❌ 文本替换失败:\", result1.error);\n                }\n            }\n            if ((_result = result) === null || _result === void 0 ? void 0 : _result.success) {\n                // 对于append操作，需要应用diff；对于replace操作，已经直接更新了文件\n                if (operation === \"append\" && result.data) {\n                    await diffToolService.applyDiff(result.data);\n                }\n                console.log(\"✅ 文件修改已应用:\", filePath);\n                // 🔧 延迟通知编辑器刷新文件内容，确保文件写入完成\n                if (onFileSelect && fileId) {\n                    setTimeout(()=>{\n                        onFileSelect(fileId);\n                        console.log(\"✅ 已通知编辑器刷新文件:\", filePath);\n                    }, 200) // 200ms延迟确保文件系统完成写入\n                    ;\n                }\n            // 可选：显示成功提示\n            } else {\n                var _result1;\n                console.error(\"❌ 应用修改失败:\", (_result1 = result) === null || _result1 === void 0 ? void 0 : _result1.error);\n            }\n        } catch (error) {\n            console.error(\"❌ 应用差异修改失败:\", error);\n        }\n    }, [\n        artworkId,\n        onFileSelect\n    ]);\n    // 处理媒体文件上传成功\n    const handleMediaUploadSuccess = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((mediaFile)=>{\n        setUploadedMedia((prev)=>{\n            const newMediaFiles = [\n                ...prev,\n                mediaFile\n            ];\n            // 通知父组件媒体文件变化\n            if (onMediaFilesChange) {\n                onMediaFilesChange(newMediaFiles);\n            }\n            return newMediaFiles;\n        });\n        console.log(\"✅ 媒体文件上传成功:\", mediaFile.filename);\n    }, [\n        onMediaFilesChange\n    ]);\n    // 处理媒体文件上传错误\n    const handleMediaUploadError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((error)=>{\n        console.error(\"❌ 媒体文件上传失败:\", error);\n    // 可以在这里添加错误提示UI\n    }, []);\n    // 移除已上传的媒体文件\n    const removeMediaFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((fileId1)=>{\n        setUploadedMedia((prev)=>{\n            const newMediaFiles = prev.filter((file)=>file.id !== fileId1);\n            // 通知父组件媒体文件变化\n            if (onMediaFilesChange) {\n                onMediaFilesChange(newMediaFiles);\n            }\n            return newMediaFiles;\n        });\n    }, [\n        onMediaFilesChange\n    ]);\n    // 处理创建文件\n    const handleCreateFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filePath, content, operation)=>{\n        try {\n            if (!artworkId) {\n                console.error(\"❌ 缺少作品ID\");\n                return;\n            }\n            // 标准化文件路径，确保相对路径和绝对路径都能正确处理\n            const normalizedPath = normalizePath(filePath);\n            console.log(\"\\uD83D\\uDD0D 创建文件:\", {\n                original: filePath,\n                normalized: normalizedPath\n            });\n            const diffToolService1 = _services_diffToolService__WEBPACK_IMPORTED_MODULE_4__.DiffToolService.getInstance();\n            // 使用新的createFileWithPath方法，传入标准化后的路径\n            const result1 = await diffToolService1.createFileWithPath(artworkId, normalizedPath, content, operation);\n            if (result1.success) {\n                // 应用创建结果\n                await diffToolService1.applyDiff(result1.data);\n                console.log(\"✅ 文件创建并应用成功:\", filePath);\n                // 文件树会通过事件系统自动刷新，不再需要手动通知\n                console.log(\"✅ 文件创建完成，文件树将自动刷新\");\n                // 可选：跳转到新创建的文件\n                if (result1.data.fileId) {\n                    handleJumpToEditor(filePath);\n                }\n            } else {\n                // 增强错误日志，提供详细的创建失败调试信息\n                console.error(\"❌ 文件创建失败:\", {\n                    path: normalizedPath,\n                    originalPath: filePath,\n                    error: result1.error,\n                    operation: operation\n                });\n            }\n        } catch (error) {\n            console.error(\"❌ 创建文件失败:\", error);\n        }\n    }, [\n        artworkId,\n        handleJumpToEditor\n    ]);\n    // 🔧 渲染内容缓存，避免重复渲染相同内容\n    const renderedContentCache = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    // 🔧 简化消息渲染函数 - 添加缓存机制\n    const renderMessageContentInternal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((content)=>{\n        // 检查缓存\n        if (renderedContentCache.current.has(content)) {\n            return renderedContentCache.current.get(content);\n        }\n        // 第一步：先识别df代码块，避免被普通代码块处理干扰\n        const dfRegex = /```df\\s*\\n([\\s\\S]*?)```/g;\n        const diffRegex = /<diff>([\\s\\S]*?)<\\/diff>/g;\n        // 添加human代码块正则表达式，用于BrainstormingHelper功能\n        const humanRegex = /```human\\s*\\n\\s*neme:\\s*([^\\n]+)\\s*\\n\\s*currentPersona\\.description:\\s*([^\\n]+)\\s*\\n\\s*问题：\\s*([\\s\\S]*?)```/g;\n        // 第二步：提取普通代码块，但排除df代码块\n        const codeBlockRegex = /```(?!df\\s)(\\w+)?\\n([\\s\\S]*?)```/g;\n        const codeBlocks = [];\n        let processedContent = content;\n        let codeBlockMatch;\n        // 提取普通代码块并替换为占位符（排除df代码块）\n        while((codeBlockMatch = codeBlockRegex.exec(content)) !== null){\n            const placeholder = \"__CODE_BLOCK_\".concat(codeBlocks.length, \"__\");\n            codeBlocks.push({\n                placeholder,\n                content: codeBlockMatch[2],\n                lang: codeBlockMatch[1]\n            });\n            processedContent = processedContent.replace(codeBlockMatch[0], placeholder);\n        }\n        // 第三步：在处理后的内容中识别特殊标记（包括df代码块）\n        const parts = [];\n        let lastIndex = 0;\n        let match;\n        // 创建一个包含所有匹配项的数组，按位置排序\n        const allMatches = [];\n        // 收集diff匹配项\n        diffRegex.lastIndex = 0;\n        while((match = diffRegex.exec(processedContent)) !== null){\n            allMatches.push({\n                type: \"diff\",\n                index: match.index,\n                length: match[0].length,\n                content: match[1]\n            });\n        }\n        // 收集df匹配项（仅支持JSON格式）\n        dfRegex.lastIndex = 0;\n        while((match = dfRegex.exec(content)) !== null){\n            const dfContent = match[1].trim();\n            try {\n                // 只解析JSON格式\n                if (dfContent.startsWith(\"{\") && dfContent.endsWith(\"}\")) {\n                    const parsedDf = JSON.parse(dfContent);\n                    // 验证必需字段\n                    if (!parsedDf.file || !parsedDf.type) {\n                        throw new Error(\"缺少必需字段：file 和 type\");\n                    }\n                    // 验证操作类型\n                    if (![\n                        \"replace\",\n                        \"append\",\n                        \"create\"\n                    ].includes(parsedDf.type)) {\n                        throw new Error(\"不支持的操作类型: \".concat(parsedDf.type));\n                    }\n                    // 对于replace操作，验证find字段\n                    if (parsedDf.type === \"replace\" && !parsedDf.find) {\n                        throw new Error(\"replace操作需要find字段\");\n                    }\n                    allMatches.push({\n                        type: \"df\",\n                        index: match.index,\n                        length: match[0].length,\n                        filePath: parsedDf.file,\n                        operation: parsedDf.type,\n                        content: parsedDf.content || \"\",\n                        find: parsedDf.find || \"\"\n                    });\n                } else {\n                    throw new Error(\"df代码块必须使用JSON格式\");\n                }\n            } catch (error) {\n                console.error(\"❌ 解析df代码块失败:\", error);\n                console.error(\"\\uD83D\\uDCDD 正确格式示例:\");\n                console.error(\"```df\");\n                console.error(\"{\");\n                console.error('  \"file\": \"path/to/file\",');\n                console.error('  \"type\": \"replace\",');\n                console.error('  \"find\": \"要查找的内容\",');\n                console.error('  \"content\": \"替换内容\"');\n                console.error(\"}\");\n                console.error(\"```\");\n                console.error(\"\\uD83D\\uDCDD 实际内容:\", dfContent);\n                // 添加错误项，用于在UI中显示错误信息\n                allMatches.push({\n                    type: \"df\",\n                    index: match.index,\n                    length: match[0].length,\n                    filePath: \"格式错误\",\n                    operation: \"error\",\n                    content: dfContent,\n                    error: error instanceof Error ? error.message : String(error)\n                });\n            }\n        }\n        // 收集human匹配项（用于BrainstormingHelper功能）\n        humanRegex.lastIndex = 0;\n        while((match = humanRegex.exec(content)) !== null){\n            allMatches.push({\n                type: \"human\",\n                index: match.index,\n                length: match[0].length,\n                roleName: match[1].trim(),\n                roleDescription: match[2].trim(),\n                question: match[3].trim()\n            });\n        }\n        // 按位置排序所有匹配项\n        allMatches.sort((a, b)=>a.index - b.index);\n        // 第三步：处理所有匹配项和普通内容\n        for (const matchItem of allMatches){\n            // 添加匹配项前的普通内容\n            if (matchItem.index > lastIndex) {\n                const beforeContent = processedContent.substring(lastIndex, matchItem.index);\n                if (beforeContent.trim()) {\n                    parts.push({\n                        type: \"markdown\",\n                        content: beforeContent\n                    });\n                }\n            }\n            // 添加匹配项内容\n            parts.push(matchItem);\n            lastIndex = matchItem.index + matchItem.length;\n        }\n        // 添加最后剩余的普通内容\n        if (lastIndex < processedContent.length) {\n            const afterContent = processedContent.substring(lastIndex);\n            if (afterContent.trim()) {\n                parts.push({\n                    type: \"markdown\",\n                    content: afterContent\n                });\n            }\n        }\n        // 如果没有特殊内容，直接渲染markdown（恢复代码块）\n        if (parts.length === 0) {\n            const finalContent = restoreCodeBlocks(processedContent, codeBlocks);\n            const result1 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"prose prose-invert max-w-none\",\n                dangerouslySetInnerHTML: {\n                    __html: renderMarkdown(finalContent)\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 816,\n                columnNumber: 9\n            }, this);\n            // 缓存结果\n            renderedContentCache.current.set(content, result1);\n            // 限制缓存大小，避免内存泄漏\n            if (renderedContentCache.current.size > 50) {\n                const firstKey = renderedContentCache.current.keys().next().value;\n                renderedContentCache.current.delete(firstKey);\n            }\n            return result1;\n        }\n        // 第四步：渲染混合内容（恢复代码块）\n        const result1 = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: parts.map((part, index)=>{\n                if (part.type === \"df\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CompactDiffDisplay__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        filePath: part.filePath,\n                        operation: part.operation,\n                        content: part.content,\n                        find: part.find,\n                        onJumpToEditor: handleJumpToEditor,\n                        onApplyChanges: handleApplyDiff,\n                        onCreateFile: handleCreateFile,\n                        onOpenDetailedDiff: onOpenDetailedDiff,\n                        artworkId: artworkId\n                    }, index, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 840,\n                        columnNumber: 15\n                    }, this);\n                } else if (part.type === \"human\") {\n                    // human代码块现在通过handleSendMessage统一处理，这里只显示原始内容\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-purple-900/20 border border-purple-500/30 rounded-lg p-4 my-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-purple-300 text-sm font-handwritten mb-2\",\n                                children: \"\\uD83E\\uDD16 辅助AI协同思考请求\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 857,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-300\",\n                                                children: \"角色：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 24\n                                            }, this),\n                                            part.roleName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 861,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-300\",\n                                                children: \"描述：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 862,\n                                                columnNumber: 24\n                                            }, this),\n                                            part.roleDescription\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 862,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-300\",\n                                                children: \"问题：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 863,\n                                                columnNumber: 24\n                                            }, this),\n                                            part.question\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 863,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 860,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400 mt-2\",\n                                children: \"ℹ️ 此请求将通过消息发送时自动处理\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 865,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 856,\n                        columnNumber: 15\n                    }, this);\n                } else {\n                    // 恢复markdown内容中的代码块\n                    const restoredContent = restoreCodeBlocks(part.content, codeBlocks);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose prose-invert max-w-none\",\n                        dangerouslySetInnerHTML: {\n                            __html: renderMarkdown(restoredContent)\n                        }\n                    }, index, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 874,\n                        columnNumber: 15\n                    }, this);\n                }\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n            lineNumber: 836,\n            columnNumber: 7\n        }, this);\n        // 缓存结果\n        renderedContentCache.current.set(content, result1);\n        // 限制缓存大小，避免内存泄漏\n        if (renderedContentCache.current.size > 50) {\n            const firstKey = renderedContentCache.current.keys().next().value;\n            renderedContentCache.current.delete(firstKey);\n        }\n        return result1;\n    }, [\n        handleJumpToEditor,\n        handleApplyDiff,\n        handleCreateFile,\n        onOpenDetailedDiff,\n        artworkId\n    ]);\n    // 辅助函数：恢复代码块\n    const restoreCodeBlocks = (content, codeBlocks)=>{\n        let restoredContent = content;\n        codeBlocks.forEach((block)=>{\n            const codeBlockHtml = \"```\".concat(block.lang || \"\", \"\\n\").concat(block.content, \"```\");\n            restoredContent = restoredContent.replace(block.placeholder, codeBlockHtml);\n        });\n        return restoredContent;\n    };\n    // Markdown渲染函数（简化版，完全移除diff处理）\n    const renderMarkdown = (text)=>{\n        // 代码块处理\n        const codeBlockRegex = /```(\\w+)?\\n([\\s\\S]*?)```/g;\n        const inlineCodeRegex = /`([^`]+)`/g;\n        // 标题处理\n        const headingRegex = /^(#{1,6})\\s+(.+)$/gm;\n        // 粗体和斜体\n        const boldRegex = /\\*\\*(.*?)\\*\\*/g;\n        const italicRegex = /\\*(.*?)\\*/g;\n        // 链接处理\n        const linkRegex = /\\[([^\\]]+)\\]\\(([^)]+)\\)/g;\n        return text// 代码块\n        .replace(codeBlockRegex, (match, lang, code)=>{\n            return '<div class=\"code-block bg-gray-800 rounded-lg p-3 my-2 overflow-x-auto\">\\n          '.concat(lang ? '<div class=\"text-xs text-gray-400 mb-2\">'.concat(lang, \"</div>\") : \"\", '\\n          <pre class=\"text-sm text-green-300 font-mono whitespace-pre-wrap\">').concat(code.trim(), \"</pre>\\n        </div>\");\n        })// 行内代码\n        .replace(inlineCodeRegex, '<code class=\"bg-gray-800 text-green-300 px-1 py-0.5 rounded text-sm font-mono\">$1</code>')// 标题\n        .replace(headingRegex, (match, hashes, title)=>{\n            const level = hashes.length;\n            const className = level === 1 ? \"text-lg font-bold text-amber-200 mt-4 mb-2\" : level === 2 ? \"text-md font-bold text-amber-200 mt-3 mb-2\" : \"text-sm font-bold text-amber-200 mt-2 mb-1\";\n            return \"<h\".concat(level, ' class=\"').concat(className, '\">').concat(title, \"</h\").concat(level, \">\");\n        })// 粗体\n        .replace(boldRegex, '<strong class=\"font-bold text-amber-100\">$1</strong>')// 斜体\n        .replace(italicRegex, '<em class=\"italic text-amber-100\">$1</em>')// 链接\n        .replace(linkRegex, '<a href=\"$2\" class=\"text-blue-400 hover:text-blue-300 underline\" target=\"_blank\" rel=\"noopener noreferrer\">$1</a>')// 换行\n        .replace(/\\n/g, \"<br>\");\n    };\n    // 自动调整输入框高度\n    const adjustTextareaHeight = ()=>{\n        if (textareaRef.current) {\n            textareaRef.current.style.height = \"auto\";\n            textareaRef.current.style.height = \"\".concat(Math.min(textareaRef.current.scrollHeight, 120), \"px\");\n        }\n    };\n    // 处理输入变化\n    const handleInputChange = (e)=>{\n        setInputMessage(e.target.value);\n        adjustTextareaHeight();\n    };\n    // 发送消息\n    const handleSend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        let messageContent = inputMessage.trim();\n        // 检查是否有激活的提示词配置\n        try {\n            const promptConfigService = _services_promptConfigService__WEBPACK_IMPORTED_MODULE_17__.PromptConfigService.getInstance();\n            const activeConfigResult = await promptConfigService.getActiveConfig();\n            if (activeConfigResult.success && activeConfigResult.data) {\n                const configContentResult = await promptConfigService.getConfigContent(activeConfigResult.data.id);\n                if (configContentResult.success && configContentResult.data) {\n                    // 将提示词内容拼接到用户输入前面\n                    messageContent = \"\".concat(configContentResult.data, \" \").concat(messageContent);\n                    console.log(\"\\uD83D\\uDD2E 应用提示词配置:\", activeConfigResult.data.name, \"内容:\", configContentResult.data);\n                }\n            }\n        } catch (error) {\n            console.error(\"获取提示词配置失败:\", error);\n        // 继续使用原始消息内容\n        }\n        // 检测是否包含```human代码块\n        const hasHumanBlock = (0,_utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_16__.hasHumanBlocks)(messageContent);\n        console.log(\"\\uD83D\\uDD35 开始发送用户消息:\", messageContent.substring(0, 30), hasHumanBlock ? \"(包含Human代码块)\" : \"\");\n        // 创建用户消息对象\n        const userMessage = {\n            id: generateMessageId(\"user\"),\n            type: \"user\",\n            content: messageContent,\n            timestamp: Date.now(),\n            createdAt: Date.now()\n        };\n        // 先清空输入框\n        setInputMessage(\"\");\n        // 重置输入框高度\n        if (textareaRef.current) {\n            textareaRef.current.style.height = \"auto\";\n        }\n        // 添加用户消息到历史记录\n        addUserMessage(userMessage);\n        if (hasHumanBlock) {\n            // 如果包含```human代码块，处理辅助AI调用\n            handleHumanBlockMessage(messageContent);\n        } else {\n            // 正常消息处理\n            setTimeout(()=>{\n                onSendMessage(messageContent);\n            }, 0);\n        }\n    }, [\n        inputMessage,\n        isLoading,\n        generateMessageId,\n        addUserMessage,\n        onSendMessage\n    ]);\n    // 处理包含```human代码块的消息\n    const handleHumanBlockMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message)=>{\n        // 生成消息唯一标识符，防止重复处理\n        const messageHash = btoa(encodeURIComponent(message)).replace(/[^a-zA-Z0-9]/g, \"\").substring(0, 16);\n        if (processedMessages.has(messageHash)) {\n            console.log(\"⚠️ 消息已处理过，跳过重复处理:\", messageHash);\n            return;\n        }\n        // 标记消息为已处理\n        setProcessedMessages((prev)=>new Set([\n                ...prev,\n                messageHash\n            ]));\n        const humanBlocks = (0,_utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_16__.extractHumanBlocks)(message);\n        console.log(\"\\uD83E\\uDD16 检测到Human代码块:\", humanBlocks.length, \"个\", \"消息ID:\", messageHash);\n        // 为每个```human代码块创建HelperResponseLayer\n        humanBlocks.forEach((block, index)=>{\n            const layerId = \"helper-\".concat(messageHash, \"-\").concat(index);\n            const messageLayer = {\n                id: layerId,\n                type: \"helper-response\",\n                content: \"\",\n                timestamp: Date.now(),\n                metadata: {\n                    humanBlock: {\n                        roleName: block.roleName,\n                        roleDescription: block.roleDescription,\n                        question: block.question\n                    },\n                    originalMessage: message,\n                    isExpanded: true,\n                    layerId\n                }\n            };\n            // 添加到消息层列表\n            setMessageLayers((prev)=>[\n                    ...prev,\n                    messageLayer\n                ]);\n        });\n        // 移除```human代码块后的消息内容\n        const cleanMessage = (0,_utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_16__.removeHumanBlocks)(message);\n        // 如果还有其他内容，也发送给主AI\n        if (cleanMessage.trim()) {\n            setTimeout(()=>{\n                onSendMessage(cleanMessage);\n            }, 100);\n        }\n    }, [\n        onSendMessage,\n        processedMessages\n    ]);\n    // 处理辅助AI集成完成\n    const handleIntegrationComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((integratedMessage)=>{\n        console.log(\"\\uD83D\\uDD04 辅助AI集成完成，准备发送给主AI\");\n        setPendingIntegratedMessage(integratedMessage);\n    }, []);\n    // 处理主AI回复\n    const handleMainAIResponse = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((response)=>{\n        console.log(\"✅ 主AI回复完成\");\n    // 这里可以添加额外的处理逻辑\n    }, []);\n    // 处理辅助AI错误\n    const handleHelperError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((error)=>{\n        console.error(\"❌ 辅助AI处理错误:\", error);\n    // 可以显示错误提示\n    }, []);\n    // 发送集成消息给主AI\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingIntegratedMessage) {\n            setTimeout(()=>{\n                onSendMessage(pendingIntegratedMessage);\n                setPendingIntegratedMessage(\"\");\n            }, 500);\n        }\n    }, [\n        pendingIntegratedMessage,\n        onSendMessage\n    ]);\n    // 处理键盘事件\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    // 加载文件路径 - 添加数据完整性验证\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadFilePaths = async ()=>{\n            if (associatedFiles.length === 0) {\n                setFilePaths(new Map());\n                return;\n            }\n            const newFilePaths = new Map();\n            const validFileIds = [];\n            const invalidFileIds = [];\n            for (const fileId1 of associatedFiles){\n                // 数据完整性验证：检查fileId是否有效\n                if (!fileId1 || typeof fileId1 !== \"string\" || fileId1.trim().length === 0) {\n                    console.warn(\"⚠️ 发现无效的文件ID:\", fileId1);\n                    invalidFileIds.push(fileId1);\n                    continue;\n                }\n                try {\n                    const filePath = await findFilePathById(fileId1);\n                    // 验证文件路径是否有效\n                    if (filePath && !filePath.includes(\"未知文件\")) {\n                        newFilePaths.set(fileId1, filePath);\n                        validFileIds.push(fileId1);\n                    } else {\n                        console.warn(\"⚠️ 文件不存在或无法访问:\", fileId1);\n                        invalidFileIds.push(fileId1);\n                    }\n                } catch (error) {\n                    console.error(\"❌ 获取文件路径失败:\", fileId1, error);\n                    invalidFileIds.push(fileId1);\n                }\n            }\n            setFilePaths(newFilePaths);\n            // 如果发现无效文件，自动清理\n            if (invalidFileIds.length > 0) {\n                console.log(\"\\uD83E\\uDDF9 发现\", invalidFileIds.length, \"个无效文件，自动清理:\", invalidFileIds);\n                // 通知父组件更新文件关联，移除无效文件\n                if (validFileIds.length !== associatedFiles.length) {\n                    try {\n                        await chatHistoryService.saveCurrentFileAssociations(validFileIds);\n                        console.log(\"✅ 已自动清理无效文件关联\");\n                    } catch (error) {\n                        console.error(\"❌ 清理无效文件关联失败:\", error);\n                    }\n                }\n            }\n        };\n        loadFilePaths();\n    }, [\n        associatedFiles,\n        findFilePathById,\n        chatHistoryService\n    ]);\n    // 🔧 AI响应完成处理 - 使用防抖机制减少重复触发\n    const lastSavedContentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isComplete && responseContent && responseContent !== lastSavedContentRef.current) {\n            lastSavedContentRef.current = responseContent;\n            console.log(\"\\uD83E\\uDD16 AI响应完成，准备保存消息:\", {\n                isComplete,\n                contentLength: responseContent.length,\n                contentPreview: responseContent.substring(0, 50) + \"...\"\n            });\n            const assistantMessage = {\n                id: generateMessageId(\"assistant\"),\n                type: \"assistant\",\n                content: responseContent,\n                timestamp: Date.now(),\n                createdAt: Date.now()\n            };\n            setChatHistory((prev)=>{\n                console.log(\"\\uD83D\\uDCCB 当前历史记录数量:\", prev.length);\n                // 🔧 改进重复消息检测：检查最近的消息，而不是所有消息\n                // 只检查最近3条assistant消息，避免误判\n                const recentAssistantMessages = prev.filter((m)=>m.type === \"assistant\").slice(-3) // 只检查最近3条\n                ;\n                const duplicateExists = recentAssistantMessages.some((m)=>{\n                    const contentMatch = m.content === responseContent;\n                    const timeMatch = Math.abs(m.timestamp - assistantMessage.timestamp) < 5000 // 5秒内\n                    ;\n                    console.log(\"\\uD83D\\uDD0D 重复检测:\", {\n                        messageId: m.id.substring(0, 8),\n                        contentMatch,\n                        timeMatch,\n                        timeDiff: Math.abs(m.timestamp - assistantMessage.timestamp)\n                    });\n                    return contentMatch && timeMatch;\n                });\n                if (duplicateExists) {\n                    console.log(\"⚠️ 检测到重复消息，跳过添加\");\n                    return prev;\n                }\n                console.log(\"✅ 添加新的AI响应消息:\", assistantMessage.id.substring(0, 8));\n                const newHistory = [\n                    ...prev,\n                    assistantMessage\n                ];\n                // 异步保存历史记录\n                chatHistoryService.saveCurrentHistory(newHistory).catch((error)=>{\n                    console.error(\"❌ 保存AI响应消息失败:\", error);\n                });\n                // 通知父组件历史记录变化\n                if (onChatHistoryChange) {\n                    onChatHistoryChange(newHistory);\n                }\n                return newHistory;\n            });\n        }\n    }, [\n        isComplete,\n        responseContent,\n        generateMessageId,\n        chatHistoryService,\n        onChatHistoryChange\n    ]);\n    // 暂时禁用文本选择保护\n    // useEffect(() => {\n    //   if (selectionManagerRef.current && aiResponseRef.current) {\n    //     if (isStreaming) {\n    //       selectionManagerRef.current.clearSelection()\n    //     } else if (!selectionManagerRef.current.isSelecting()) {\n    //       selectionManagerRef.current.saveSelection(aiResponseRef.current)\n    //     }\n    //   }\n    // }, [isStreaming])\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 在DOM更新后恢复选择状态\n        if (selectionManagerRef.current && aiResponseRef.current && !isStreaming && isComplete) {\n            setTimeout(()=>{\n                var _selectionManagerRef_current;\n                (_selectionManagerRef_current = selectionManagerRef.current) === null || _selectionManagerRef_current === void 0 ? void 0 : _selectionManagerRef_current.restoreSelection(aiResponseRef.current);\n            }, 50) // 短延迟确保DOM更新完成\n            ;\n        }\n    }, [\n        isStreaming,\n        isComplete\n    ]) // 🔧 关键修复：移除responseContent依赖，避免持续重新渲染\n    ;\n    // 🔧 简化的滚动状态管理 - 参考StreamingResponse的简单模式\n    const scrollStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        shouldAutoScroll: true,\n        lastContentLength: 0\n    });\n    // 🔧 简化的滚动控制 - 参考StreamingResponse的直接滚动方式\n    const scrollToBottom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (chatContainerRef.current) {\n            chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n        }\n    }, []);\n    // 🔧 添加流式响应状态监控 - 移除responseContent.length依赖，避免频繁重渲染\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83C\\uDF0A 流式响应状态变化:\", {\n            isStreaming,\n            isComplete,\n            responseContentLength: responseContentRef.current.length,\n            timestamp: Date.now()\n        });\n    }, [\n        isStreaming,\n        isComplete\n    ]);\n    // 🔧 检测用户是否在底部附近\n    const isUserNearBottom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!chatContainerRef.current) return false;\n        const container = chatContainerRef.current;\n        return container.scrollTop + container.clientHeight >= container.scrollHeight - 100;\n    }, []);\n    // 🔧 聊天历史变化时的滚动控制 - 简化版本，参考StreamingResponse\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (chatContainerRef.current && chatHistory.length > 0) {\n            // 简化逻辑：如果用户在底部或启用自动滚动，直接滚动到底部\n            if (isUserNearBottom() || scrollStateRef.current.shouldAutoScroll) {\n                chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n            }\n        }\n    }, [\n        chatHistory.length,\n        isUserNearBottom\n    ]);\n    // 🔧 流式响应时的实时滚动 - 简化版本，参考StreamingResponse的直接模式\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isStreaming && responseContentRef.current && chatContainerRef.current) {\n            const currentLength = responseContentRef.current.length;\n            // 简化逻辑：内容增加时，如果用户在底部就直接滚动\n            if (currentLength > scrollStateRef.current.lastContentLength) {\n                scrollStateRef.current.lastContentLength = currentLength;\n                if (isUserNearBottom() || scrollStateRef.current.shouldAutoScroll) {\n                    // 直接滚动，无需额外的函数调用\n                    chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n                }\n            }\n        }\n    }, [\n        isStreaming,\n        isUserNearBottom\n    ]) // 🔧 简化依赖项\n    ;\n    // 🔧 用户滚动事件处理 - 智能检测用户滚动意图\n    const handleUserScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (chatContainerRef.current) {\n            // 当用户手动滚动时，根据位置决定是否启用自动滚动\n            scrollStateRef.current.shouldAutoScroll = isUserNearBottom();\n        }\n    }, [\n        isUserNearBottom\n    ]);\n    // 处理会话切换\n    const handleSessionSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (sessionId)=>{\n        try {\n            console.log(\"\\uD83D\\uDD04 开始切换会话:\", sessionId);\n            // 🔧 关键修复：在切换会话前，先清理当前AI响应状态\n            // 这是解决会话切换时AI响应残留问题的核心修复\n            console.log(\"\\uD83E\\uDDF9 清理AI响应状态...\");\n            // 通知父组件清理AI响应状态\n            // 这里需要父组件提供清理回调函数\n            if (onClearAIResponse) {\n                onClearAIResponse();\n            }\n            setCurrentSessionId(sessionId);\n            // 加载选中会话的历史记录\n            const sessionHistory = await chatHistoryService.loadHistory(sessionId);\n            setChatHistory(sessionHistory);\n            // 通知父组件对话历史变化\n            if (onChatHistoryChange) {\n                onChatHistoryChange(sessionHistory);\n            }\n            console.log(\"✅ 会话已切换:\", sessionId, \"历史记录:\", sessionHistory.length, \"条\");\n            console.log(\"✅ AI响应状态已清理\");\n        } catch (error) {\n            console.error(\"❌ 切换会话失败:\", error);\n        }\n    }, [\n        chatHistoryService,\n        onChatHistoryChange,\n        onClearAIResponse\n    ]);\n    // 处理文件关联变化\n    const handleFilesChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (newFiles)=>{\n        try {\n            console.log(\"\\uD83D\\uDD25 ChatInterface - 接收到文件关联变化:\", newFiles);\n            console.log(\"\\uD83D\\uDD25 ChatInterface - 当前associatedFiles:\", associatedFiles);\n            // 保存到ChatHistoryService\n            await chatHistoryService.saveCurrentFileAssociations(newFiles);\n            // 通知父组件\n            console.log(\"\\uD83D\\uDD25 ChatInterface - 调用父组件onFilesChange:\", newFiles);\n            onFilesChange(newFiles);\n            console.log(\"✅ 文件关联已更新:\", newFiles.length, \"个文件\");\n        } catch (error) {\n            console.error(\"❌ 保存文件关联失败:\", error);\n        }\n    }, [\n        chatHistoryService,\n        onFilesChange,\n        associatedFiles\n    ]);\n    // 处理单条消息插入到编辑器\n    const handleInsertMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageContent)=>{\n        console.log(\"\\uD83D\\uDD04 尝试插入消息内容到编辑器:\", messageContent.substring(0, 100) + \"...\");\n        if (onMessageContentInsert) {\n            console.log(\"✅ 使用消息内容插入弹窗\");\n            // 使用专门的消息内容插入接口，会显示插入弹窗\n            onMessageContentInsert(messageContent);\n        } else if (onContentInsert) {\n            console.log(\"✅ 直接使用onContentInsert接口插入内容\");\n            // 直接使用onContentInsert插入特定消息内容\n            onContentInsert(messageContent, {\n                position: \"cursor\",\n                addNewlines: true // 添加新行\n            });\n        } else if (onInsertToEditor) {\n            console.log(\"⚠️ 使用降级插入方法\");\n            // 创建一个临时的插入函数，直接操作编辑器\n            try {\n                // 尝试直接访问编辑器并插入内容\n                const activeEditor = document.querySelector('textarea, [contenteditable=\"true\"]');\n                if (activeEditor) {\n                    if (activeEditor instanceof HTMLTextAreaElement) {\n                        // 处理textarea\n                        const start = activeEditor.selectionStart || 0;\n                        const end = activeEditor.selectionEnd || 0;\n                        const currentValue = activeEditor.value;\n                        const newValue = currentValue.slice(0, start) + \"\\n\" + messageContent + \"\\n\" + currentValue.slice(end);\n                        activeEditor.value = newValue;\n                        activeEditor.selectionStart = activeEditor.selectionEnd = start + messageContent.length + 2;\n                        // 触发change事件\n                        const event = new Event(\"input\", {\n                            bubbles: true\n                        });\n                        activeEditor.dispatchEvent(event);\n                        console.log(\"✅ 直接插入到textarea成功\");\n                    } else if (activeEditor.contentEditable === \"true\") {\n                        // 处理contenteditable元素\n                        const selection = window.getSelection();\n                        if (selection && selection.rangeCount > 0) {\n                            const range = selection.getRangeAt(0);\n                            range.deleteContents();\n                            const textNode = document.createTextNode(\"\\n\" + messageContent + \"\\n\");\n                            range.insertNode(textNode);\n                            // 移动光标到插入内容后\n                            range.setStartAfter(textNode);\n                            range.setEndAfter(textNode);\n                            selection.removeAllRanges();\n                            selection.addRange(range);\n                            console.log(\"✅ 直接插入到contenteditable成功\");\n                        }\n                    }\n                } else {\n                    // 如果找不到编辑器，使用原有方法\n                    console.log(\"⚠️ 未找到编辑器元素，使用原有插入方法\");\n                    onInsertToEditor();\n                }\n            } catch (error) {\n                console.error(\"❌ 直接插入失败，使用原有方法:\", error);\n                onInsertToEditor();\n            }\n        } else {\n            console.warn(\"⚠️ 没有可用的插入接口\");\n        }\n    }, [\n        onMessageContentInsert,\n        onContentInsert,\n        onInsertToEditor\n    ]);\n    // 处理重新生成AI消息\n    const handleRegenerateMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageIndex)=>{\n        // 找到该AI消息前面的用户消息\n        const messages = chatHistory.slice(0, messageIndex);\n        const lastUserMessage = messages.reverse().find((msg)=>msg.type === \"user\");\n        if (lastUserMessage) {\n            console.log(\"\\uD83D\\uDD04 重新生成AI消息，基于用户消息:\", lastUserMessage.content.substring(0, 50));\n            console.log(\"\\uD83D\\uDCCA 历史消息限制：只包含索引 0-\".concat(messageIndex - 1, \" 的消息\"));\n            // 删除从当前AI消息开始的所有后续消息\n            const newHistory = chatHistory.slice(0, messageIndex);\n            setChatHistory(newHistory);\n            // 保存更新后的历史记录\n            chatHistoryService.saveCurrentHistory(newHistory);\n            // 重新发送用户消息，传递目标消息索引以限制历史消息范围\n            onSendMessage(lastUserMessage.content, messageIndex);\n            // 关闭工具面板\n            setActiveMessageTools(null);\n        }\n    }, [\n        chatHistory,\n        onSendMessage,\n        chatHistoryService\n    ]);\n    // 处理编辑用户消息\n    const handleEditMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageId, currentContent)=>{\n        setEditingMessageId(messageId);\n        setEditingContent(currentContent);\n        setActiveMessageTools(null);\n    }, []);\n    // 处理保存编辑的消息\n    const handleSaveEditedMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageId, newContent)=>{\n        const messageIndex = chatHistory.findIndex((msg)=>msg.id === messageId);\n        if (messageIndex === -1) return;\n        // 更新消息内容\n        const updatedMessage = {\n            ...chatHistory[messageIndex],\n            content: newContent,\n            updatedAt: Date.now()\n        };\n        // 删除该消息之后的所有消息\n        const newHistory = [\n            ...chatHistory.slice(0, messageIndex),\n            updatedMessage\n        ];\n        setChatHistory(newHistory);\n        // 保存更新后的历史记录\n        chatHistoryService.saveCurrentHistory(newHistory);\n        // 重新发送编辑后的消息\n        onSendMessage(newContent);\n        // 清除编辑状态\n        setEditingMessageId(null);\n        setEditingContent(\"\");\n        console.log(\"✅ 消息已编辑并重发:\", newContent.substring(0, 50));\n    }, [\n        chatHistory,\n        onSendMessage,\n        chatHistoryService\n    ]);\n    // 处理多项对比\n    const handleMultipleComparison = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageIndex)=>{\n        // 找到该AI消息前面的用户消息\n        const messages = chatHistory.slice(0, messageIndex);\n        const lastUserMessage = messages.reverse().find((msg)=>msg.type === \"user\");\n        if (lastUserMessage) {\n            console.log(\"\\uD83D\\uDD04 开启多项对比，基于用户消息:\", lastUserMessage.content.substring(0, 50));\n            console.log(\"\\uD83C\\uDFAF 记录触发消息索引:\", messageIndex);\n            // 设置对比内容并打开弹窗\n            setComparisonMessageContent(lastUserMessage.content);\n            setComparisonTriggerMessageIndex(messageIndex) // 记录触发的消息索引\n            ;\n            setShowMultipleComparison(true);\n            // 关闭工具面板\n            setActiveMessageTools(null);\n        }\n    }, [\n        chatHistory\n    ]);\n    // 处理多项对比结果选择\n    const handleComparisonResultSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((selectedResult)=>{\n        console.log(\"\\uD83C\\uDFAF 多项对比结果选择，替换最后一条AI消息:\", selectedResult.substring(0, 100) + \"...\");\n        // 替换最后一条AI消息的内容\n        setChatHistory((prev)=>{\n            const newHistory = [\n                ...prev\n            ];\n            // 从后往前找最后一条AI消息\n            for(let i = newHistory.length - 1; i >= 0; i--){\n                if (newHistory[i].type === \"assistant\") {\n                    // 替换这条AI消息的内容\n                    newHistory[i] = {\n                        ...newHistory[i],\n                        content: selectedResult,\n                        timestamp: Date.now() // 更新时间戳\n                    };\n                    console.log(\"✅ 已替换AI消息:\", newHistory[i].id);\n                    break;\n                }\n            }\n            // 保存更新后的历史记录\n            chatHistoryService.saveCurrentHistory(newHistory).catch((error)=>{\n                console.error(\"❌ 保存替换后的消息失败:\", error);\n            });\n            return newHistory;\n        });\n        // 关闭弹窗\n        setShowMultipleComparison(false);\n        setComparisonMessageContent(\"\");\n    }, [\n        chatHistoryService\n    ]);\n    // 处理取消编辑\n    const handleCancelEdit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setEditingMessageId(null);\n        setEditingContent(\"\");\n    }, []);\n    // 获取可用模型列表\n    const loadAvailableModels = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!(currentConfig === null || currentConfig === void 0 ? void 0 : currentConfig.apiKey)) {\n            console.warn(\"⚠️ 没有可用的API配置\");\n            return;\n        }\n        setIsLoadingModels(true);\n        try {\n            const aiService = _services_aiService__WEBPACK_IMPORTED_MODULE_6__.AIService.getInstance();\n            const result1 = await aiService.getAvailableModels(currentConfig);\n            if (result1.success && result1.data && result1.data.length > 0) {\n                // 转换为模型对象格式\n                const models = result1.data.map((modelId)=>({\n                        id: modelId,\n                        name: modelId.toUpperCase().replace(/-/g, \" \"),\n                        description: getModelDescription(modelId)\n                    }));\n                setAvailableModels(models);\n                console.log(\"✅ 成功获取模型列表:\", models.length, \"个模型\");\n            } else {\n                console.warn(\"⚠️ 获取模型列表失败\");\n                setAvailableModels([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取模型列表失败:\", error);\n            setAvailableModels([]);\n        } finally{\n            setIsLoadingModels(false);\n        }\n    }, [\n        currentConfig\n    ]);\n    // 获取模型描述\n    const getModelDescription = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const lowerModelId = modelId.toLowerCase();\n        // GPT系列模型\n        if (lowerModelId.includes(\"gpt-4\")) {\n            if (lowerModelId.includes(\"turbo\")) {\n                return \"更快的GPT-4，性价比更高\";\n            }\n            return \"最强大的模型，适合复杂任务\";\n        } else if (lowerModelId.includes(\"gpt-3.5\")) {\n            return \"快速且经济的选择\";\n        } else if (lowerModelId.includes(\"claude\")) {\n            return \"Anthropic Claude，强大的推理能力\";\n        } else if (lowerModelId.includes(\"gemini\")) {\n            return \"Google Gemini，多模态能力强\";\n        }\n        // 其他模型\n        return \"可用的AI模型: \".concat(modelId);\n    }, []);\n    // 点击外部关闭消息工具面板\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (activeMessageTools) {\n                // 检查点击是否在工具面板外部\n                const target = event.target;\n                if (!target.closest(\".message-tools-panel\") && !target.closest(\".message-tools-button\")) {\n                    setActiveMessageTools(null);\n                }\n            }\n            // 点击外部取消编辑状态（除非点击在编辑区域内）\n            if (editingMessageId) {\n                const target = event.target;\n                if (!target.closest(\"textarea\") && !target.closest(\"button\")) {\n                    handleCancelEdit();\n                }\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        activeMessageTools,\n        editingMessageId,\n        handleCancelEdit\n    ]);\n    // 暴露方法给父组件\n    react__WEBPACK_IMPORTED_MODULE_1___default().useImperativeHandle(ref, ()=>({\n            handleSessionSelect\n        }), [\n        handleSessionSelect\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            (associatedFiles.length > 0 || focusedFile) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 bg-gray-800/20 border-b border-amber-500/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2 cursor-pointer hover:bg-gray-700/20 rounded-md p-1 -m-1 transition-all duration-200\",\n                        onClick: ()=>setIsFileAssociationCollapsed(!isFileAssociationCollapsed),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                width: \"14\",\n                                height: \"14\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                className: \"text-amber-400 transition-transform duration-200 \".concat(isFileAssociationCollapsed ? \"rotate-0\" : \"rotate-90\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1681,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1674,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                width: \"14\",\n                                height: \"14\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                className: \"text-amber-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1684,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1683,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-amber-200 font-handwritten\",\n                                children: \"关联文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1686,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-400\",\n                                children: [\n                                    \"(\",\n                                    focusedFile ? associatedFiles.includes(focusedFile) ? associatedFiles.length : associatedFiles.length + 1 : associatedFiles.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1687,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500 ml-auto\",\n                                children: isFileAssociationCollapsed ? \"点击展开\" : \"点击折叠\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1690,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 1670,\n                        columnNumber: 11\n                    }, this),\n                    !isFileAssociationCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2 mt-2\",\n                        children: (()=>{\n                            // 创建文件显示列表，聚焦文件优先\n                            const allFiles = new Set([\n                                ...focusedFile ? [\n                                    focusedFile\n                                ] : [],\n                                ...associatedFiles\n                            ]);\n                            return Array.from(allFiles).map((fileId1, index)=>{\n                                const filePath = filePaths.get(fileId1) || \"加载中... (\".concat(fileId1.substring(0, 8), \"...)\");\n                                const isFocused = focusedFile === fileId1;\n                                const isManuallyAssociated = associatedFiles.includes(fileId1);\n                                // 确定文件类型和样式\n                                const fileType = isFocused ? \"auto\" : \"manual\";\n                                const styleConfig = {\n                                    auto: {\n                                        className: \"bg-blue-500/20 text-blue-200 border-blue-500/50\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"12\",\n                                            height: \"12\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1717,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1716,\n                                            columnNumber: 25\n                                        }, this),\n                                        label: \"自动\",\n                                        title: \"当前编辑文件（自动关联）\"\n                                    },\n                                    manual: {\n                                        className: \"bg-green-500/20 text-green-200 border-green-500/50\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"12\",\n                                            height: \"12\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1727,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1726,\n                                            columnNumber: 25\n                                        }, this),\n                                        label: \"手动\",\n                                        title: \"手动关联文件\"\n                                    }\n                                };\n                                const config = styleConfig[fileType];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 px-2 py-1 \".concat(config.className, \" border rounded-md text-xs\"),\n                                    title: config.title,\n                                    children: [\n                                        config.icon,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs opacity-75 font-handwritten\",\n                                            children: config.label\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1744,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono\",\n                                            title: filePath,\n                                            children: filePath\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1745,\n                                            columnNumber: 23\n                                        }, this),\n                                        isManuallyAssociated && !isFocused && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                const newFiles = associatedFiles.filter((id)=>id !== fileId1);\n                                                handleFilesChange(newFiles);\n                                            },\n                                            className: \"ml-1 p-0.5 text-green-300 hover:text-red-400 hover:bg-red-500/10 rounded transition-all duration-200\",\n                                            title: \"移除手动关联文件\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"10\",\n                                                height: \"10\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 1758,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1757,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1749,\n                                            columnNumber: 25\n                                        }, this),\n                                        isFocused && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-1 px-1 py-0.5 bg-blue-600/30 text-blue-100 rounded text-xs font-handwritten\",\n                                            children: \"当前\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1765,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, fileId1, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1738,\n                                    columnNumber: 21\n                                }, this);\n                            });\n                        })()\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 1696,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 1669,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                className: \"flex-1 overflow-y-auto custom-scrollbar p-4 space-y-4\",\n                onScroll: handleUserScroll,\n                children: [\n                    chatHistory.length === 0 && !isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center h-full text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto mb-4 rounded-lg bg-amber-500/20 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"32\",\n                                    height: \"32\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    className: \"text-amber-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 1788,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1787,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1786,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-handwritten text-amber-200 mb-2\",\n                                children: \"开始对话\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1791,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm font-handwritten\",\n                                children: currentConfig ? \"输入消息开始与AI对话\" : \"请先在配置页面设置API Key\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1794,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 1785,\n                        columnNumber: 11\n                    }, this),\n                    chatHistory.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex \".concat(message.type === \"user\" ? \"justify-end\" : \"justify-start\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-[80%] p-3 rounded-lg \".concat(message.type === \"user\" ? \"bg-blue-500/20 text-blue-200 border border-blue-500/50\" : \"bg-green-500/20 text-green-200 border border-green-500/50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4\",\n                                                        children: message.type === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1817,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1816,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M17.5,12A1.5,1.5 0 0,1 16,10.5A1.5,1.5 0 0,1 17.5,9A1.5,1.5 0 0,1 19,10.5A1.5,1.5 0 0,1 17.5,12M10,10.5C10,11.3 9.3,12 8.5,12S7,11.3 7,10.5C7,9.7 7.7,9 8.5,9S10,9.7 10,10.5M12,14C9.8,14 8,15.8 8,18V20H16V18C16,15.8 14.2,14 12,14M12,4C14.2,4 16,5.8 16,8S14.2,12 12,12S8,10.2 8,8S9.8,4 12,4M12,2A6,6 0 0,0 6,8C6,11.3 8.7,14 12,14S18,11.3 18,8A6,6 0 0,0 12,2Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1821,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1820,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1814,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-handwritten\",\n                                                        children: message.type === \"user\" ? \"你\" : \"AI助手\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1825,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs opacity-60\",\n                                                        children: new Date(message.timestamp).toLocaleTimeString()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1828,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1813,\n                                                columnNumber: 17\n                                            }, this),\n                                            message.type === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveMessageTools(activeMessageTools === message.id ? null : message.id),\n                                                        className: \"message-tools-button p-1 rounded-md bg-blue-400/10 hover:bg-blue-400/20 border border-blue-400/20 hover:border-blue-400/40 transition-all duration-200 text-blue-300 hover:text-blue-200 opacity-90 hover:opacity-100\",\n                                                        title: \"消息工具\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"12\",\n                                                            height: \"12\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1842,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1841,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1836,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    activeMessageTools === message.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"message-tools-panel absolute right-0 top-full mt-1 bg-gray-800 border border-blue-500/30 rounded-lg shadow-lg z-50 min-w-[120px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                handleEditMessage(message.id, message.content);\n                                                            },\n                                                            className: \"w-full px-3 py-2 text-left text-sm text-blue-200 hover:bg-blue-500/20 transition-all duration-200 font-handwritten flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    width: \"14\",\n                                                                    height: \"14\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1856,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 1855,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"编辑重发\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1849,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1848,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1835,\n                                                columnNumber: 19\n                                            }, this),\n                                            message.type === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveMessageTools(activeMessageTools === message.id ? null : message.id),\n                                                        className: \"message-tools-button p-1 rounded-md bg-amber-400/10 hover:bg-amber-400/20 border border-amber-400/20 hover:border-amber-400/40 transition-all duration-200 text-amber-300 hover:text-amber-200 opacity-90 hover:opacity-100\",\n                                                        title: \"消息工具\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"12\",\n                                                            height: \"12\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1874,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1873,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1868,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    activeMessageTools === message.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"message-tools-panel absolute right-0 top-full mt-1 bg-gray-800 border border-green-500/30 rounded-lg shadow-lg z-50 min-w-[140px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    handleInsertMessage(message.content);\n                                                                    setActiveMessageTools(null);\n                                                                },\n                                                                className: \"w-full px-3 py-2 text-left text-sm text-green-200 hover:bg-green-500/20 transition-all duration-200 font-handwritten border-b border-green-500/20 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        width: \"14\",\n                                                                        height: \"14\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 1889,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1888,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"插入到编辑器\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1881,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    const messageIndex = chatHistory.findIndex((msg)=>msg.id === message.id);\n                                                                    if (messageIndex !== -1) {\n                                                                        handleRegenerateMessage(messageIndex);\n                                                                    }\n                                                                },\n                                                                className: \"w-full px-3 py-2 text-left text-sm text-amber-200 hover:bg-amber-500/20 transition-all duration-200 font-handwritten flex items-center gap-2 border-b border-amber-500/20\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        width: \"14\",\n                                                                        height: \"14\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 1903,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1902,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"重新生成\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1893,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    const messageIndex = chatHistory.findIndex((msg)=>msg.id === message.id);\n                                                                    if (messageIndex !== -1) {\n                                                                        handleMultipleComparison(messageIndex);\n                                                                    }\n                                                                },\n                                                                className: \"w-full px-3 py-2 text-left text-sm text-purple-200 hover:bg-purple-500/20 transition-all duration-200 font-handwritten flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        width: \"14\",\n                                                                        height: \"14\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V6.5H5V5H19M19,19H5V8.5H19V19Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 1917,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1916,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"多项对比\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1907,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1880,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1867,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 1812,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: editingMessageId === message.id ? // 编辑模式\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: editingContent,\n                                                    onChange: (e)=>setEditingContent(e.target.value),\n                                                    className: \"w-full p-2 bg-gray-700 border border-blue-500/30 rounded-md text-blue-200 font-handwritten resize-none focus:outline-none focus:border-blue-500/60\",\n                                                    rows: Math.max(2, editingContent.split(\"\\n\").length),\n                                                    placeholder: \"编辑消息内容...\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 1931,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleCancelEdit,\n                                                            className: \"px-3 py-1 text-xs bg-gray-600 hover:bg-gray-500 text-gray-200 rounded-md transition-all duration-200 font-handwritten\",\n                                                            children: \"取消\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1940,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSaveEditedMessage(message.id, editingContent),\n                                                            className: \"px-3 py-1 text-xs bg-blue-600 hover:bg-blue-500 text-white rounded-md transition-all duration-200 font-handwritten\",\n                                                            disabled: !editingContent.trim(),\n                                                            children: \"保存并重发\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1946,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 1939,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1930,\n                                            columnNumber: 19\n                                        }, this) : // 正常显示模式\n                                        renderMessageContentInternal(message.content)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 1927,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1806,\n                                columnNumber: 13\n                            }, this)\n                        }, message.id, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                            lineNumber: 1802,\n                            columnNumber: 11\n                        }, this)),\n                    messageLayers.map((layer)=>{\n                        var _layer_metadata;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full\",\n                            children: layer.type === \"helper-response\" && ((_layer_metadata = layer.metadata) === null || _layer_metadata === void 0 ? void 0 : _layer_metadata.humanBlock) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HelperResponseLayer__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                messageId: layer.id,\n                                humanBlockContent: layer.metadata.humanBlock,\n                                onIntegrationComplete: handleIntegrationComplete,\n                                onMainAIResponse: handleMainAIResponse,\n                                onError: handleHelperError,\n                                className: \"my-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1968,\n                                columnNumber: 15\n                            }, this)\n                        }, layer.id, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                            lineNumber: 1966,\n                            columnNumber: 11\n                        }, this);\n                    }),\n                    (isLoading || isStreaming && !isComplete) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-[80%] p-3 bg-green-500/20 text-green-200 border border-green-500/50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M17.5,12A1.5,1.5 0 0,1 16,10.5A1.5,1.5 0 0,1 17.5,9A1.5,1.5 0 0,1 19,10.5A1.5,1.5 0 0,1 17.5,12M10,10.5C10,11.3 9.3,12 8.5,12S7,11.3 7,10.5C7,9.7 7.7,9 8.5,9S10,9.7 10,10.5M12,14C9.8,14 8,15.8 8,18V20H16V18C16,15.8 14.2,14 12,14M12,4C14.2,4 16,5.8 16,8S14.2,12 12,12S8,10.2 8,8S9.8,4 12,4M12,2A6,6 0 0,0 6,8C6,11.3 8.7,14 12,14S18,11.3 18,8A6,6 0 0,0 12,2Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 1987,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1986,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1985,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-handwritten\",\n                                            children: \"AI助手\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1990,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs opacity-60\",\n                                            children: isLoading && !responseContent ? \"正在思考...\" : isStreaming ? \"正在输入...\" : \"刚刚\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1991,\n                                            columnNumber: 17\n                                        }, this),\n                                        isStreaming && onStop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onStop,\n                                            className: \"ml-auto p-1 text-gray-400 hover:text-red-400 hover:bg-red-500/10 rounded transition-all duration-200\",\n                                            title: \"停止生成\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"12\",\n                                                height: \"12\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M6,6H18V18H6V6Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2002,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2001,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1996,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1984,\n                                    columnNumber: 15\n                                }, this),\n                                responseContent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: aiResponseRef,\n                                    className: \"text-sm\",\n                                    children: [\n                                        renderMessageContentInternal(responseContent),\n                                        isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse text-amber-400 ml-1\",\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2013,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2010,\n                                    columnNumber: 17\n                                }, this) : /* 等待动画 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-amber-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-amber-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"0ms\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2020,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-amber-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"150ms\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2021,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-amber-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"300ms\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2022,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2019,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2018,\n                                    columnNumber: 17\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-2 bg-red-500/20 border border-red-500/50 rounded text-red-200 text-xs\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2031,\n                                                columnNumber: 21\n                                            }, this),\n                                            onRetry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onRetry,\n                                                className: \"ml-2 px-2 py-1 bg-red-500/30 hover:bg-red-500/50 rounded text-xs transition-colors\",\n                                                children: \"重试\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2033,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2030,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2029,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                            lineNumber: 1983,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 1982,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 1779,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-800/30 border-t border-amber-500/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: textareaRef,\n                                        value: inputMessage,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyDown,\n                                        placeholder: currentConfig ? \"输入消息...\" : \"请先配置API Key\",\n                                        disabled: !currentConfig || isLoading,\n                                        className: \"w-full min-h-[40px] max-h-[120px] p-3 pr-12 bg-gray-800 border border-gray-600 rounded-lg text-amber-200 placeholder-gray-500 resize-none focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 disabled:opacity-50 transition-all duration-200\",\n                                        rows: 1\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2052,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSend,\n                                        disabled: !inputMessage.trim() || !currentConfig || isLoading,\n                                        className: \"absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-md transition-all duration-200 \".concat(inputMessage.trim() && currentConfig && !isLoading ? \"text-amber-400 hover:text-amber-300 hover:bg-amber-500/20\" : \"text-gray-500 cursor-not-allowed\"),\n                                        title: isLoading ? \"发送中...\" : \"Enter 发送\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"16\",\n                                            height: \"16\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            className: \"animate-spin\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2075,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2074,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"16\",\n                                            height: \"16\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M2,21L23,12L2,3V10L17,12L2,14V21Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2079,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2078,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2064,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2051,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mt-3 px-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4 text-xs text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"12\",\n                                                        height: \"12\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"currentColor\",\n                                                        className: \"opacity-60\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2090,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M6.25,7.72H10V10.5H14V13.5H10V16.25H6.25V13.5H3.5V10.5H6.25V7.72Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2091,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2089,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Shift+Enter 换行\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2088,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"12\",\n                                                        height: \"12\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"currentColor\",\n                                                        className: \"opacity-60\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2097,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M6.25,7.72H17.75V10.5H6.25V7.72Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2098,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2096,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Enter 发送\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2095,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2087,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: [\n                                                    inputMessage.length,\n                                                    \" 字符\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2105,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative tools-panel-container\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowToolsPanel(!showToolsPanel),\n                                                        className: \"group flex items-center gap-1 px-2 py-1 rounded-md text-xs transition-all duration-200 \".concat(showToolsPanel ? \"text-amber-300 bg-amber-500/20 border border-amber-500/40\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 border border-transparent\"),\n                                                        title: \"工具\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                width: \"12\",\n                                                                height: \"12\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: \"currentColor\",\n                                                                className: \"transition-transform duration-200 group-hover:rotate-12\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 2118,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2117,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"工具\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2120,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2109,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    showToolsPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute bottom-full right-0 mb-3 bg-gray-800/95 backdrop-blur-sm border border-amber-500/30 rounded-xl shadow-2xl p-2 z-50 animate-in fade-in-0 zoom-in-95 duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-amber-200 font-medium mb-2 px-2 py-1\",\n                                                                children: \"快捷工具\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2127,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setShowFileAssociation(true);\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        className: \"p-2 bg-blue-500/20 text-blue-200 border border-blue-500/50 rounded-lg hover:bg-blue-500/30 transition-all duration-200\",\n                                                                        title: \"关联文件\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                lineNumber: 2140,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2139,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2131,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setShowMediaUploader(!showMediaUploader);\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        className: \"p-2 bg-purple-500/20 text-purple-200 border border-purple-500/50 rounded-lg hover:bg-purple-500/30 transition-all duration-200\",\n                                                                        title: \"上传媒体文件\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M9,16V10H5L12,3L19,10H15V16H9M5,20V18H19V20H5Z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                lineNumber: 2154,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2153,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2145,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setShowPromptManager(true);\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        disabled: isLoading,\n                                                                        className: \"p-2 rounded-lg transition-all duration-200 \".concat(activePromptConfig ? \"bg-amber-500/30 text-amber-200 border border-amber-500/70\" : \"bg-amber-500/20 text-amber-200 border border-amber-500/50 hover:bg-amber-500/30\"),\n                                                                        title: activePromptConfig ? \"当前配置: \".concat(activePromptConfig.name) : \"管理提示词模板\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                    lineNumber: 2172,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M12,15.39L8.24,17.66L9.23,13.38L5.91,10.5L10.29,10.13L12,6.09L13.71,10.13L18.09,10.5L14.77,13.38L15.76,17.66M22,9.24L14.81,8.63L12,2L9.19,8.63L2,9.24L7.45,13.97L5.82,21L12,17.27L18.18,21L16.54,13.97L22,9.24Z\",\n                                                                                    opacity: \"0.6\",\n                                                                                    transform: \"scale(0.4) translate(24, 24)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                    lineNumber: 2173,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2171,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2159,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            if (onShowAudienceSettings) {\n                                                                                onShowAudienceSettings();\n                                                                            }\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        className: \"p-2 bg-green-500/20 text-green-200 border border-green-500/50 rounded-lg hover:bg-green-500/30 transition-all duration-200\",\n                                                                        title: \"设置受众\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M16,4C18.11,4 19.81,5.69 19.81,7.8C19.81,9.91 18.11,11.6 16,11.6C13.89,11.6 12.2,9.91 12.2,7.8C12.2,5.69 13.89,4 16,4M16,13.4C18.67,13.4 24,14.73 24,17.4V20H8V17.4C8,14.73 13.33,13.4 16,13.4M8.8,11.6C6.69,11.6 5,9.91 5,7.8C5,5.69 6.69,4 8.8,4C9.13,4 9.45,4.05 9.75,4.14C9.28,5.16 9,6.3 9,7.5C9,8.7 9.28,9.84 9.75,10.86C9.45,10.95 9.13,11 8.8,11.6M8.8,13.4C7.12,13.4 3.5,14.26 3.5,17.4V20H6.5V17.4C6.5,16.55 7.45,15.1 8.8,13.4Z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                lineNumber: 2192,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2191,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2181,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2129,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-full right-4 -mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-gray-800 border-r border-b border-amber-500/30 transform rotate-45\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 2199,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2198,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2125,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2108,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2104,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2086,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 2050,\n                        columnNumber: 9\n                    }, this),\n                    showMediaUploader && artworkId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-4 bg-gray-800/50 border border-purple-500/30 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-purple-200\",\n                                        children: \"媒体文件上传\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2212,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowMediaUploader(false),\n                                        className: \"text-gray-400 hover:text-gray-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"16\",\n                                            height: \"16\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2218,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2217,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2213,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2211,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MediaUploader__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                onUploadSuccess: handleMediaUploadSuccess,\n                                onError: handleMediaUploadError,\n                                artworkId: artworkId,\n                                className: \"mb-4\",\n                                maxFiles: 5\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2222,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 2210,\n                        columnNumber: 11\n                    }, this),\n                    uploadedMedia.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowMediaList(!showMediaList),\n                                className: \"flex items-center gap-2 px-3 py-2 bg-green-500/20 text-green-200 border border-green-500/50 rounded-md hover:bg-green-500/30 transition-all duration-200 font-handwritten text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"16\",\n                                        height: \"16\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2240,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"已上传 \",\n                                            uploadedMedia.length,\n                                            \" 个媒体文件\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2242,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"14\",\n                                        height: \"14\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"currentColor\",\n                                        className: \"transition-transform duration-200 \".concat(showMediaList ? \"rotate-180\" : \"\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2250,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2243,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2235,\n                                columnNumber: 13\n                            }, this),\n                            showMediaList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 p-4 bg-gray-800/50 border border-green-500/30 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-3\",\n                                    children: uploadedMedia.map((mediaFile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 p-3 bg-gray-700/50 rounded-lg border border-gray-600/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: mediaFile.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"20\",\n                                                            height: \"20\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            className: \"text-blue-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2268,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2267,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2266,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"20\",\n                                                            height: \"20\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            className: \"text-purple-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2274,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2273,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2272,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2264,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-200 truncate\",\n                                                            children: mediaFile.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2282,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: [\n                                                                mediaFile.mimeType,\n                                                                \" • \",\n                                                                (mediaFile.size / 1024 / 1024).toFixed(2),\n                                                                \" MB\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2285,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: new Date(mediaFile.uploadedAt).toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2288,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2281,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>removeMediaFile(mediaFile.id),\n                                                        className: \"w-8 h-8 bg-red-500/20 text-red-400 rounded-lg hover:bg-red-500/30 transition-colors flex items-center justify-center\",\n                                                        title: \"删除文件\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"14\",\n                                                            height: \"14\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2301,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2300,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2295,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2294,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, mediaFile.id, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2259,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2257,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2256,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 2234,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2049,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SessionManager__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showSessionManager,\n                onClose: ()=>setShowSessionManager(false),\n                onSessionSelect: handleSessionSelect,\n                currentSessionId: currentSessionId\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2317,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FileAssociationTreeDialog__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: showFileAssociation,\n                onClose: ()=>setShowFileAssociation(false),\n                artworkId: artworkId,\n                onFilesChange: handleFilesChange\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2325,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PromptManagerModal__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                isOpen: showPromptManager,\n                onClose: ()=>setShowPromptManager(false),\n                onConfigSelect: handlePromptConfigSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultipleComparisonModal__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                isOpen: showMultipleComparison,\n                onClose: ()=>setShowMultipleComparison(false),\n                messageContent: comparisonMessageContent,\n                onResultSelect: handleComparisonResultSelect,\n                onSendMessage: onMultipleComparisonSend || onSendMessage,\n                targetMessageIndex: comparisonTriggerMessageIndex\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2340,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n        lineNumber: 1664,\n        columnNumber: 5\n    }, this);\n}, \"RfxeR6O005vLzWT/JUmeZeKO01c=\")), \"RfxeR6O005vLzWT/JUmeZeKO01c=\");\n_c1 = ChatInterface;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ChatInterface);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatInterface$React.forwardRef\");\n$RefreshReg$(_c1, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AIAssistant/ChatInterface.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/simpleReplaceService.ts":
/*!**********************************************!*\
  !*** ./src/services/simpleReplaceService.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleReplaceService: function() { return /* binding */ SimpleReplaceService; }\n/* harmony export */ });\n/* harmony import */ var _fileTreeService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\");\n\n/**\n * 简化的文件替换服务\n * 直接、可靠、无复杂依赖的文件内容替换\n */ class SimpleReplaceService {\n    static getInstance() {\n        if (!SimpleReplaceService.instance) {\n            SimpleReplaceService.instance = new SimpleReplaceService();\n        }\n        return SimpleReplaceService.instance;\n    }\n    /**\n   * 执行简单的文本替换\n   * @param artworkId 作品ID\n   * @param filePath 文件路径\n   * @param searchText 要查找的文本\n   * @param replaceText 替换文本\n   * @returns 替换结果\n   */ async replaceText(artworkId, filePath, searchText, replaceText) {\n        try {\n            console.log(\"\\uD83D\\uDD04 开始简单文本替换:\", {\n                artworkId: artworkId.substring(0, 8) + \"...\",\n                filePath,\n                searchText: searchText.substring(0, 50) + (searchText.length > 50 ? \"...\" : \"\"),\n                replaceText: replaceText.substring(0, 50) + (replaceText.length > 50 ? \"...\" : \"\"),\n                searchLength: searchText.length,\n                replaceLength: replaceText.length\n            });\n            // 1. 获取文件树\n            const fileTreeResult = await this.fileTreeService.getFileTree(artworkId);\n            if (!fileTreeResult.success || !fileTreeResult.data) {\n                return {\n                    success: false,\n                    error: \"获取文件树失败: \" + (fileTreeResult.error || \"未知错误\")\n                };\n            }\n            // 2. 查找文件\n            const fileId = this.findFileIdByPath(fileTreeResult.data, filePath);\n            if (!fileId) {\n                return {\n                    success: false,\n                    error: \"文件不存在: \".concat(filePath)\n                };\n            }\n            // 3. 获取文件内容\n            const fileResult = await this.fileTreeService.getFile(fileId);\n            if (!fileResult.success || !fileResult.data) {\n                return {\n                    success: false,\n                    error: \"获取文件内容失败: \" + (fileResult.error || \"未知错误\")\n                };\n            }\n            const originalContent = fileResult.data.content || \"\";\n            const originalLength = originalContent.length;\n            console.log(\"\\uD83D\\uDCC4 文件内容信息:\", {\n                fileId: fileId.substring(0, 8) + \"...\",\n                originalLength,\n                contentPreview: originalContent.substring(0, 100) + (originalContent.length > 100 ? \"...\" : \"\")\n            });\n            // 4. 检查是否包含要替换的文本\n            if (!originalContent.includes(searchText)) {\n                console.log(\"❌ 未找到要替换的文本\");\n                console.log(\"\\uD83D\\uDD0D 搜索文本:\", JSON.stringify(searchText));\n                console.log(\"\\uD83D\\uDD0D 文件内容前200字符:\", JSON.stringify(originalContent.substring(0, 200)));\n                return {\n                    success: false,\n                    error: '文件中未找到要替换的文本: \"'.concat(searchText, '\"')\n                };\n            }\n            // 5. 执行替换 - 使用安全的字符串替换\n            let modifiedContent = originalContent;\n            let replacementCount = 0;\n            // 计算所有匹配位置\n            const matches = [];\n            let searchIndex = 0;\n            while(true){\n                const index = originalContent.indexOf(searchText, searchIndex);\n                if (index === -1) break;\n                matches.push(index);\n                searchIndex = index + 1;\n            }\n            console.log(\"\\uD83D\\uDD0D 找到匹配项:\", {\n                count: matches.length,\n                positions: matches.slice(0, 5) // 只显示前5个位置\n            });\n            // 从后往前替换，避免位置偏移问题\n            for(let i = matches.length - 1; i >= 0; i--){\n                const index = matches[i];\n                modifiedContent = modifiedContent.substring(0, index) + replaceText + modifiedContent.substring(index + searchText.length);\n                replacementCount++;\n            }\n            const newLength = modifiedContent.length;\n            console.log(\"✅ 替换完成:\", {\n                replacementCount,\n                originalLength,\n                newLength,\n                lengthDiff: newLength - originalLength\n            });\n            // 6. 保存修改后的内容\n            const updateResult = await this.fileTreeService.updateFileContent(fileId, modifiedContent, \"external\" // 标记为外部工具更新\n            );\n            if (!updateResult.success) {\n                return {\n                    success: false,\n                    error: \"保存文件失败: \" + (updateResult.error || \"未知错误\")\n                };\n            }\n            console.log(\"\\uD83D\\uDCBE 文件保存成功\");\n            return {\n                success: true,\n                data: {\n                    success: true,\n                    message: \"成功替换 \".concat(replacementCount, \" 处文本\"),\n                    replacementCount,\n                    originalLength,\n                    newLength\n                }\n            };\n        } catch (error) {\n            const errorMessage = \"文本替换失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\n   * 根据路径查找文件ID\n   * @private\n   */ findFileIdByPath(node, targetPath) {\n        // 标准化路径\n        const normalizedTarget = targetPath.replace(/^\\/+/, \"\").replace(/\\/+$/, \"\");\n        const searchNode = function(currentNode) {\n            let currentPath = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"\";\n            // 构建当前节点的完整路径\n            const nodePath = currentPath ? \"\".concat(currentPath, \"/\").concat(currentNode.name) : currentNode.name;\n            const normalizedNodePath = nodePath.replace(/^\\/+/, \"\").replace(/\\/+$/, \"\");\n            // 检查是否匹配\n            if (normalizedNodePath === normalizedTarget && currentNode.type === \"file\") {\n                return currentNode.id;\n            }\n            // 递归搜索子节点\n            if (currentNode.children) {\n                for (const child of currentNode.children){\n                    const result = searchNode(child, nodePath);\n                    if (result) return result;\n                }\n            }\n            return null;\n        };\n        return searchNode(node);\n    }\n    /**\n   * 追加文本到文件末尾\n   * @param artworkId 作品ID\n   * @param filePath 文件路径\n   * @param content 要追加的内容\n   * @returns 追加结果\n   */ async appendText(artworkId, filePath, content) {\n        try {\n            console.log(\"\\uD83D\\uDCDD 开始追加文本:\", {\n                artworkId: artworkId.substring(0, 8) + \"...\",\n                filePath,\n                contentLength: content.length,\n                contentPreview: content.substring(0, 100) + (content.length > 100 ? \"...\" : \"\")\n            });\n            // 获取文件树和文件ID\n            const fileTreeResult = await this.fileTreeService.getFileTree(artworkId);\n            if (!fileTreeResult.success || !fileTreeResult.data) {\n                return {\n                    success: false,\n                    error: \"获取文件树失败: \" + (fileTreeResult.error || \"未知错误\")\n                };\n            }\n            const fileId = this.findFileIdByPath(fileTreeResult.data, filePath);\n            if (!fileId) {\n                return {\n                    success: false,\n                    error: \"文件不存在: \".concat(filePath)\n                };\n            }\n            // 获取文件内容\n            const fileResult = await this.fileTreeService.getFile(fileId);\n            if (!fileResult.success || !fileResult.data) {\n                return {\n                    success: false,\n                    error: \"获取文件内容失败: \" + (fileResult.error || \"未知错误\")\n                };\n            }\n            const originalContent = fileResult.data.content || \"\";\n            const originalLength = originalContent.length;\n            // 追加内容\n            const modifiedContent = originalContent + \"\\n\" + content;\n            const newLength = modifiedContent.length;\n            // 保存文件\n            const updateResult = await this.fileTreeService.updateFileContent(fileId, modifiedContent, \"external\");\n            if (!updateResult.success) {\n                return {\n                    success: false,\n                    error: \"保存文件失败: \" + (updateResult.error || \"未知错误\")\n                };\n            }\n            console.log(\"✅ 文本追加成功:\", {\n                originalLength,\n                newLength,\n                addedLength: newLength - originalLength\n            });\n            return {\n                success: true,\n                data: {\n                    success: true,\n                    message: \"成功追加 \".concat(content.length, \" 个字符\"),\n                    originalLength,\n                    newLength\n                }\n            };\n        } catch (error) {\n            const errorMessage = \"文本追加失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    constructor(){\n        this.fileTreeService = _fileTreeService__WEBPACK_IMPORTED_MODULE_0__.FileTreeService.getInstance();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/simpleReplaceService.ts\n"));

/***/ })

});