"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_markdown_markdown_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/markdown/markdown.js":
/*!********************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/markdown/markdown.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/markdown/markdown.ts\nvar conf = {\n  comments: {\n    blockComment: [\"<!--\", \"-->\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"(\", close: \")\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*<!--\\\\s*#?region\\\\b.*-->\"),\n      end: new RegExp(\"^\\\\s*<!--\\\\s*#?endregion\\\\b.*-->\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".md\",\n  // escape codes\n  control: /[\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n  noncontrol: /[^\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n  escapes: /\\\\(?:@control)/,\n  // escape codes for javascript/CSS strings\n  jsescapes: /\\\\(?:[btnfr\\\\\"']|[0-7][0-7]?|[0-3][0-7]{2})/,\n  // non matched elements\n  empty: [\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"link\",\n    \"meta\",\n    \"param\"\n  ],\n  tokenizer: {\n    root: [\n      // markdown tables\n      [/^\\s*\\|/, \"@rematch\", \"@table_header\"],\n      // headers (with #)\n      [/^(\\s{0,3})(#+)((?:[^\\\\#]|@escapes)+)((?:#+)?)/, [\"white\", \"keyword\", \"keyword\", \"keyword\"]],\n      // headers (with =)\n      [/^\\s*(=+|\\-+)\\s*$/, \"keyword\"],\n      // headers (with ***)\n      [/^\\s*((\\*[ ]?)+)\\s*$/, \"meta.separator\"],\n      // quote\n      [/^\\s*>+/, \"comment\"],\n      // list (starting with * or number)\n      [/^\\s*([\\*\\-+:]|\\d+\\.)\\s/, \"keyword\"],\n      // code block (4 spaces indent)\n      [/^(\\t|[ ]{4})[^ ].*$/, \"string\"],\n      // code block (3 tilde)\n      [/^\\s*~~~\\s*((?:\\w|[\\/\\-#])+)?\\s*$/, { token: \"string\", next: \"@codeblock\" }],\n      // github style code blocks (with backticks and language)\n      [\n        /^\\s*```\\s*((?:\\w|[\\/\\-#])+).*$/,\n        { token: \"string\", next: \"@codeblockgh\", nextEmbedded: \"$1\" }\n      ],\n      // github style code blocks (with backticks but no language)\n      [/^\\s*```\\s*$/, { token: \"string\", next: \"@codeblock\" }],\n      // markup within lines\n      { include: \"@linecontent\" }\n    ],\n    table_header: [\n      { include: \"@table_common\" },\n      [/[^\\|]+/, \"keyword.table.header\"]\n      // table header\n    ],\n    table_body: [{ include: \"@table_common\" }, { include: \"@linecontent\" }],\n    table_common: [\n      [/\\s*[\\-:]+\\s*/, { token: \"keyword\", switchTo: \"table_body\" }],\n      // header-divider\n      [/^\\s*\\|/, \"keyword.table.left\"],\n      // opening |\n      [/^\\s*[^\\|]/, \"@rematch\", \"@pop\"],\n      // exiting\n      [/^\\s*$/, \"@rematch\", \"@pop\"],\n      // exiting\n      [\n        /\\|/,\n        {\n          cases: {\n            \"@eos\": \"keyword.table.right\",\n            // closing |\n            \"@default\": \"keyword.table.middle\"\n            // inner |\n          }\n        }\n      ]\n    ],\n    codeblock: [\n      [/^\\s*~~~\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/^\\s*```\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    // github style code blocks\n    codeblockgh: [\n      [/```\\s*$/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^`]+/, \"variable.source\"]\n    ],\n    linecontent: [\n      // escapes\n      [/&\\w+;/, \"string.escape\"],\n      [/@escapes/, \"escape\"],\n      // various markup\n      [/\\b__([^\\\\_]|@escapes|_(?!_))+__\\b/, \"strong\"],\n      [/\\*\\*([^\\\\*]|@escapes|\\*(?!\\*))+\\*\\*/, \"strong\"],\n      [/\\b_[^_]+_\\b/, \"emphasis\"],\n      [/\\*([^\\\\*]|@escapes)+\\*/, \"emphasis\"],\n      [/`([^\\\\`]|@escapes)+`/, \"variable\"],\n      // links\n      [/\\{+[^}]+\\}+/, \"string.target\"],\n      [/(!?\\[)((?:[^\\]\\\\]|@escapes)*)(\\]\\([^\\)]+\\))/, [\"string.link\", \"\", \"string.link\"]],\n      [/(!?\\[)((?:[^\\]\\\\]|@escapes)*)(\\])/, \"string.link\"],\n      // or html\n      { include: \"html\" }\n    ],\n    // Note: it is tempting to rather switch to the real HTML mode instead of building our own here\n    // but currently there is a limitation in Monarch that prevents us from doing it: The opening\n    // '<' would start the HTML mode, however there is no way to jump 1 character back to let the\n    // HTML mode also tokenize the opening angle bracket. Thus, even though we could jump to HTML,\n    // we cannot correctly tokenize it in that mode yet.\n    html: [\n      // html tags\n      [/<(\\w+)\\/>/, \"tag\"],\n      [\n        /<(\\w+)(\\-|\\w)*/,\n        {\n          cases: {\n            \"@empty\": { token: \"tag\", next: \"@tag.$1\" },\n            \"@default\": { token: \"tag\", next: \"@tag.$1\" }\n          }\n        }\n      ],\n      [/<\\/(\\w+)(\\-|\\w)*\\s*>/, { token: \"tag\" }],\n      [/<!--/, \"comment\", \"@comment\"]\n    ],\n    comment: [\n      [/[^<\\-]+/, \"comment.content\"],\n      [/-->/, \"comment\", \"@pop\"],\n      [/<!--/, \"comment.content.invalid\"],\n      [/[<\\-]/, \"comment.content\"]\n    ],\n    // Almost full HTML tag matching, complete with embedded scripts & styles\n    tag: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [\n        /(type)(\\s*=\\s*)(\")([^\"]+)(\")/,\n        [\n          \"attribute.name.html\",\n          \"delimiter.html\",\n          \"string.html\",\n          { token: \"string.html\", switchTo: \"@tag.$S2.$4\" },\n          \"string.html\"\n        ]\n      ],\n      [\n        /(type)(\\s*=\\s*)(')([^']+)(')/,\n        [\n          \"attribute.name.html\",\n          \"delimiter.html\",\n          \"string.html\",\n          { token: \"string.html\", switchTo: \"@tag.$S2.$4\" },\n          \"string.html\"\n        ]\n      ],\n      [/(\\w+)(\\s*=\\s*)(\"[^\"]*\"|'[^']*')/, [\"attribute.name.html\", \"delimiter.html\", \"string.html\"]],\n      [/\\w+/, \"attribute.name.html\"],\n      [/\\/>/, \"tag\", \"@pop\"],\n      [\n        />/,\n        {\n          cases: {\n            \"$S2==style\": {\n              token: \"tag\",\n              switchTo: \"embeddedStyle\",\n              nextEmbedded: \"text/css\"\n            },\n            \"$S2==script\": {\n              cases: {\n                $S3: {\n                  token: \"tag\",\n                  switchTo: \"embeddedScript\",\n                  nextEmbedded: \"$S3\"\n                },\n                \"@default\": {\n                  token: \"tag\",\n                  switchTo: \"embeddedScript\",\n                  nextEmbedded: \"text/javascript\"\n                }\n              }\n            },\n            \"@default\": { token: \"tag\", next: \"@pop\" }\n          }\n        }\n      ]\n    ],\n    embeddedStyle: [\n      [/[^<]+/, \"\"],\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/</, \"\"]\n    ],\n    embeddedScript: [\n      [/[^<]+/, \"\"],\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/</, \"\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/markdown/markdown.js\n"));

/***/ })

}]);