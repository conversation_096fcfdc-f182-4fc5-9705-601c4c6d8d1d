"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/services/diffToolService.ts":
/*!*****************************************!*\
  !*** ./src/services/diffToolService.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiffToolService: function() { return /* binding */ DiffToolService; }\n/* harmony export */ });\n/* harmony import */ var _fileTreeService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\");\n/**\r\n * 差异工具服务\r\n * 提供文本追加、正则替换和差异计算功能\r\n */ \nclass DiffToolService {\n    /**\r\n   * 获取差异工具服务单例\r\n   */ static getInstance() {\n        if (!DiffToolService.instance) {\n            DiffToolService.instance = new DiffToolService();\n        }\n        return DiffToolService.instance;\n    }\n    /**\r\n   * 文本追加功能\r\n   * 在文件末尾或开头追加内容\r\n   */ async appendText(fileId, params) {\n        try {\n            // 获取文件内容\n            const fileResult = await this.fileTreeService.getFile(fileId);\n            if (!fileResult.success || !fileResult.data) {\n                return {\n                    success: false,\n                    error: fileResult.error || \"文件不存在\"\n                };\n            }\n            const file = fileResult.data;\n            const originalContent = file.content || \"\";\n            // 根据位置追加内容\n            let modifiedContent;\n            if (params.position === \"start\") {\n                modifiedContent = params.content + (params.addNewline ? \"\\n\" : \"\") + originalContent;\n            } else {\n                // 默认追加到末尾\n                modifiedContent = originalContent + (params.addNewline ? \"\\n\" : \"\") + params.content;\n            }\n            // 计算差异\n            const changes = this.calculateDiff(originalContent, modifiedContent);\n            const stats = this.calculateStats(changes);\n            const diffResult = {\n                fileId,\n                filePath: file.path,\n                operation: \"append\",\n                originalContent,\n                modifiedContent,\n                changes,\n                stats,\n                timestamp: Date.now()\n            };\n            console.log(\"✅ 文本追加操作完成:\", file.path);\n            return {\n                success: true,\n                data: diffResult\n            };\n        } catch (error) {\n            const errorMessage = \"文本追加失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 正则替换功能\r\n   * 使用正则表达式替换文件内容\r\n   */ async regexReplace(fileId, params) {\n        try {\n            // 获取文件内容\n            const fileResult = await this.fileTreeService.getFile(fileId);\n            if (!fileResult.success || !fileResult.data) {\n                return {\n                    success: false,\n                    error: fileResult.error || \"文件不存在\"\n                };\n            }\n            const file = fileResult.data;\n            const originalContent = file.content || \"\";\n            // 构建正则表达式\n            const flags = params.flags || (params.global ? \"gm\" : \"m\"); // 默认添加多行标志\n            let regex;\n            try {\n                regex = new RegExp(params.pattern, flags);\n            } catch (regexError) {\n                return {\n                    success: false,\n                    error: \"正则表达式无效: \".concat(regexError instanceof Error ? regexError.message : String(regexError))\n                };\n            }\n            // 先测试是否有匹配\n            const testRegex = new RegExp(params.pattern, flags);\n            if (!testRegex.test(originalContent)) {\n                return {\n                    success: false,\n                    error: '没有找到匹配的内容进行替换。搜索模式: \"'.concat(params.pattern, '\"')\n                };\n            }\n            // 执行替换\n            const modifiedContent = originalContent.replace(regex, params.replacement);\n            // 检查是否有实际变更\n            if (originalContent === modifiedContent) {\n                return {\n                    success: false,\n                    error: \"替换操作未改变文件内容，可能是替换内容与原内容相同\"\n                };\n            }\n            // 计算差异\n            const changes = this.calculateDiff(originalContent, modifiedContent);\n            const stats = this.calculateStats(changes);\n            const diffResult = {\n                fileId,\n                filePath: file.path,\n                operation: \"replace\",\n                originalContent,\n                modifiedContent,\n                changes,\n                stats,\n                timestamp: Date.now()\n            };\n            console.log(\"✅ 正则替换操作完成:\", file.path);\n            return {\n                success: true,\n                data: diffResult\n            };\n        } catch (error) {\n            const errorMessage = \"正则替换失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 应用差异修改到文件\r\n   * 将差异结果应用到实际文件中\r\n   */ async applyDiff(diffResult) {\n        try {\n            // 🔧 使用 'external' 来源，表示这是外部工具（df）的更新\n            const updateResult = await this.fileTreeService.updateFileContent(diffResult.fileId, diffResult.modifiedContent, \"external\" // 标记为外部工具更新\n            );\n            if (updateResult.success) {\n                console.log(\"✅ 差异修改已应用到文件:\", diffResult.filePath);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: updateResult.error\n            };\n        } catch (error) {\n            const errorMessage = \"应用差异修改失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 计算文本差异\r\n   * 基于行级别的差异算法实现\r\n   */ calculateDiff(original, modified) {\n        const originalLines = original.split(\"\\n\");\n        const modifiedLines = modified.split(\"\\n\");\n        const changes = [];\n        // 简单的行级别差异算法\n        const maxLines = Math.max(originalLines.length, modifiedLines.length);\n        for(let i = 0; i < maxLines; i++){\n            const originalLine = originalLines[i];\n            const modifiedLine = modifiedLines[i];\n            if (originalLine === undefined) {\n                // 新增行\n                changes.push({\n                    type: \"add\",\n                    lineNumber: i + 1,\n                    content: modifiedLine\n                });\n            } else if (modifiedLine === undefined) {\n                // 删除行\n                changes.push({\n                    type: \"delete\",\n                    lineNumber: i + 1,\n                    content: originalLine\n                });\n            } else if (originalLine !== modifiedLine) {\n                // 修改行\n                changes.push({\n                    type: \"modify\",\n                    lineNumber: i + 1,\n                    content: modifiedLine,\n                    originalContent: originalLine\n                });\n            } else {\n                // 未变更行（可选择性包含）\n                changes.push({\n                    type: \"unchanged\",\n                    lineNumber: i + 1,\n                    content: originalLine\n                });\n            }\n        }\n        return changes;\n    }\n    /**\r\n   * 计算差异统计信息\r\n   */ calculateStats(changes) {\n        const stats = {\n            additions: 0,\n            deletions: 0,\n            modifications: 0,\n            totalChanges: 0\n        };\n        changes.forEach((change)=>{\n            switch(change.type){\n                case \"add\":\n                    stats.additions++;\n                    stats.totalChanges++;\n                    break;\n                case \"delete\":\n                    stats.deletions++;\n                    stats.totalChanges++;\n                    break;\n                case \"modify\":\n                    stats.modifications++;\n                    stats.totalChanges++;\n                    break;\n            }\n        });\n        return stats;\n    }\n    /**\r\n   * 预览差异（不实际修改文件）\r\n   * 用于在UI中显示差异预览\r\n   */ async previewAppend(fileId, params) {\n        // 复用appendText逻辑，但不实际修改文件\n        return this.appendText(fileId, params);\n    }\n    /**\r\n   * 预览正则替换（不实际修改文件）\r\n   */ async previewRegexReplace(fileId, params) {\n        // 复用regexReplace逻辑，但不实际修改文件\n        return this.regexReplace(fileId, params);\n    }\n    /**\r\n   * 智能字符串替换功能\r\n   * 使用多种匹配策略进行字符串替换，提供详细的错误信息\r\n   */ async intelligentStringReplace(fileId, findText, replaceText) {\n        try {\n            var _matchResult_matchedText, _matchResult_matchedText1;\n            // 获取文件内容\n            const fileResult = await this.fileTreeService.getFile(fileId);\n            if (!fileResult.success || !fileResult.data) {\n                return {\n                    success: false,\n                    error: fileResult.error || \"文件不存在\"\n                };\n            }\n            const file = fileResult.data;\n            const originalContent = file.content || \"\";\n            // 使用智能匹配查找内容\n            const matchResult = this.intelligentStringMatch(originalContent, findText);\n            if (!matchResult.found) {\n                let errorMessage = matchResult.error || \"未找到匹配内容\";\n                if (matchResult.suggestions && matchResult.suggestions.length > 0) {\n                    errorMessage += \"\\n\\n\\uD83D\\uDD0D 找到以下相似内容：\\n\" + matchResult.suggestions.map((s)=>\"  • \".concat(s)).join(\"\\n\");\n                    errorMessage += \"\\n\\n\\uD83D\\uDCA1 建议：检查搜索内容的大小写、空格或换行符是否正确\";\n                }\n                return {\n                    success: false,\n                    error: errorMessage\n                };\n            }\n            // 执行替换\n            const beforeMatch = originalContent.substring(0, matchResult.startIndex);\n            const afterMatch = originalContent.substring(matchResult.endIndex);\n            const modifiedContent = beforeMatch + replaceText + afterMatch;\n            // 检查是否有实际变更\n            if (originalContent === modifiedContent) {\n                return {\n                    success: false,\n                    error: \"替换操作未改变文件内容，替换内容与原内容相同\"\n                };\n            }\n            // 计算差异\n            const changes = this.calculateDiff(originalContent, modifiedContent);\n            const stats = this.calculateStats(changes);\n            const diffResult = {\n                fileId,\n                filePath: file.path,\n                operation: \"replace\",\n                originalContent,\n                modifiedContent,\n                changes,\n                stats,\n                timestamp: Date.now()\n            };\n            console.log(\"✅ 智能字符串替换完成 (\".concat(matchResult.strategy, \"):\"), file.path);\n            console.log(\"\\uD83D\\uDD0D 匹配策略: \".concat(matchResult.strategy));\n            console.log('\\uD83D\\uDCDD 匹配内容: \"'.concat((_matchResult_matchedText = matchResult.matchedText) === null || _matchResult_matchedText === void 0 ? void 0 : _matchResult_matchedText.substring(0, 50)).concat((((_matchResult_matchedText1 = matchResult.matchedText) === null || _matchResult_matchedText1 === void 0 ? void 0 : _matchResult_matchedText1.length) || 0) > 50 ? \"...\" : \"\", '\"'));\n            return {\n                success: true,\n                data: diffResult\n            };\n        } catch (error) {\n            const errorMessage = \"智能字符串替换失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取文件路径（通过文件ID）\r\n   * 辅助方法，用于其他组件获取文件路径\r\n   */ async getFilePath(fileId) {\n        try {\n            const fileResult = await this.fileTreeService.getFile(fileId);\n            if (fileResult.success && fileResult.data) {\n                return fileResult.data.path;\n            }\n            return null;\n        } catch (error) {\n            console.error(\"❌ 获取文件路径失败:\", error);\n            return null;\n        }\n    }\n    /**\r\n   * 验证正则表达式\r\n   * 辅助方法，用于验证正则表达式的有效性\r\n   */ validateRegex(pattern, flags) {\n        try {\n            new RegExp(pattern, flags);\n            return {\n                valid: true\n            };\n        } catch (error) {\n            return {\n                valid: false,\n                error: error instanceof Error ? error.message : \"正则表达式无效\"\n            };\n        }\n    }\n    /**\r\n   * 智能字符串匹配\r\n   * 尝试多种匹配策略，提供详细的错误信息和建议\r\n   */ intelligentStringMatch(content, pattern) {\n        if (!pattern || !content) {\n            return {\n                found: false,\n                error: \"搜索内容或文件内容为空\"\n            };\n        }\n        // 策略1: 精确匹配\n        let index = content.indexOf(pattern);\n        if (index !== -1) {\n            return {\n                found: true,\n                matchedText: pattern,\n                startIndex: index,\n                endIndex: index + pattern.length,\n                strategy: \"exact\"\n            };\n        }\n        // 策略2: 忽略大小写匹配\n        const lowerContent = content.toLowerCase();\n        const lowerPattern = pattern.toLowerCase();\n        index = lowerContent.indexOf(lowerPattern);\n        if (index !== -1) {\n            const matchedText = content.substring(index, index + pattern.length);\n            return {\n                found: true,\n                matchedText,\n                startIndex: index,\n                endIndex: index + pattern.length,\n                strategy: \"case-insensitive\"\n            };\n        }\n        // 策略3: 标准化空白字符匹配\n        const normalizeWhitespace = (text)=>text.replace(/\\s+/g, \" \").trim();\n        const normalizedContent = normalizeWhitespace(content);\n        const normalizedPattern = normalizeWhitespace(pattern);\n        index = normalizedContent.indexOf(normalizedPattern);\n        if (index !== -1) {\n            // 需要在原始内容中找到对应位置\n            const beforeNormalized = normalizedContent.substring(0, index);\n            const beforeOriginal = this.findOriginalPosition(content, beforeNormalized);\n            const matchLength = this.findOriginalMatchLength(content.substring(beforeOriginal), normalizedPattern);\n            return {\n                found: true,\n                matchedText: content.substring(beforeOriginal, beforeOriginal + matchLength),\n                startIndex: beforeOriginal,\n                endIndex: beforeOriginal + matchLength,\n                strategy: \"whitespace-normalized\"\n            };\n        }\n        // 策略4: 模糊匹配 - 查找包含关键词的行\n        const suggestions = this.findSimilarContent(content, pattern);\n        return {\n            found: false,\n            suggestions,\n            error: '未找到匹配内容。搜索模式: \"'.concat(pattern.substring(0, 100)).concat(pattern.length > 100 ? \"...\" : \"\", '\"')\n        };\n    }\n    /**\r\n   * 查找相似内容，提供替换建议\r\n   */ findSimilarContent(content, pattern) {\n        const lines = content.split(\"\\n\");\n        const patternWords = pattern.toLowerCase().split(/\\s+/).filter((word)=>word.length > 2);\n        const suggestions = [];\n        lines.forEach((line, index)=>{\n            const lowerLine = line.toLowerCase();\n            let score = 0;\n            // 计算匹配分数\n            patternWords.forEach((word)=>{\n                if (lowerLine.includes(word)) {\n                    score += word.length;\n                }\n            });\n            if (score > 0) {\n                suggestions.push({\n                    line: line.trim(),\n                    score,\n                    lineNumber: index + 1\n                });\n            }\n        });\n        // 按分数排序，返回前5个建议\n        return suggestions.sort((a, b)=>b.score - a.score).slice(0, 5).map((s)=>\"第\".concat(s.lineNumber, \"行: \").concat(s.line.substring(0, 100)).concat(s.line.length > 100 ? \"...\" : \"\"));\n    }\n    /**\r\n   * 在原始文本中找到标准化文本的对应位置\r\n   */ findOriginalPosition(original, normalizedPrefix) {\n        let originalPos = 0;\n        let normalizedPos = 0;\n        while(normalizedPos < normalizedPrefix.length && originalPos < original.length){\n            const originalChar = original[originalPos];\n            const normalizedChar = normalizedPrefix[normalizedPos];\n            if (/\\s/.test(originalChar)) {\n                // 跳过原始文本中的空白字符\n                originalPos++;\n            } else if (originalChar === normalizedChar) {\n                originalPos++;\n                normalizedPos++;\n            } else {\n                // 处理多个空白字符被标准化为单个空格的情况\n                if (normalizedChar === \" \") {\n                    normalizedPos++;\n                } else {\n                    originalPos++;\n                    normalizedPos++;\n                }\n            }\n        }\n        return originalPos;\n    }\n    /**\r\n   * 计算原始匹配文本的长度\r\n   */ findOriginalMatchLength(originalText, normalizedPattern) {\n        let originalPos = 0;\n        let normalizedPos = 0;\n        while(normalizedPos < normalizedPattern.length && originalPos < originalText.length){\n            const originalChar = originalText[originalPos];\n            const normalizedChar = normalizedPattern[normalizedPos];\n            if (/\\s/.test(originalChar)) {\n                originalPos++;\n            } else if (originalChar === normalizedChar) {\n                originalPos++;\n                normalizedPos++;\n            } else if (normalizedChar === \" \") {\n                normalizedPos++;\n            } else {\n                originalPos++;\n                normalizedPos++;\n            }\n        }\n        return originalPos;\n    }\n    /**\r\n   * 创建文件及其路径（如果不存在）\r\n   * 支持递归创建文件夹结构\r\n   */ async createFileWithPath(artworkId, filePath) {\n        let content = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"\", operation = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : \"append\";\n        try {\n            // 获取文件树根节点\n            const fileTreeResult = await this.fileTreeService.getFileTree(artworkId);\n            if (!fileTreeResult.success || !fileTreeResult.data) {\n                return {\n                    success: false,\n                    error: \"无法获取文件树\"\n                };\n            }\n            const rootNode = fileTreeResult.data;\n            // 解析文件路径\n            const pathParts = filePath.split(\"/\").filter((part)=>part.trim() !== \"\");\n            if (pathParts.length === 0) {\n                return {\n                    success: false,\n                    error: \"文件路径无效\"\n                };\n            }\n            const fileName = pathParts[pathParts.length - 1];\n            const folderParts = pathParts.slice(0, -1);\n            // 递归创建文件夹结构\n            let currentParentId = rootNode.id;\n            for (const folderName of folderParts){\n                // 检查文件夹是否已存在\n                const existingFolder = await this.findChildByName(artworkId, currentParentId, folderName);\n                if (existingFolder) {\n                    currentParentId = existingFolder.id;\n                } else {\n                    // 创建新文件夹\n                    const folderResult = await this.fileTreeService.createFolder(artworkId, currentParentId, folderName);\n                    if (!folderResult.success) {\n                        return {\n                            success: false,\n                            error: \"创建文件夹失败: \".concat(folderResult.error)\n                        };\n                    }\n                    currentParentId = folderResult.data.id;\n                }\n            }\n            // 检查文件是否已存在\n            const existingFile = await this.findChildByName(artworkId, currentParentId, fileName);\n            if (existingFile) {\n                // 文件已存在，执行原有的追加或替换操作\n                if (operation === \"append\") {\n                    return this.appendText(existingFile.id, {\n                        content,\n                        position: \"end\",\n                        addNewline: true\n                    });\n                } else {\n                    // 对于replace操作，这里简化处理，直接替换整个文件内容\n                    const originalContent = existingFile.content || \"\";\n                    const modifiedContent = content;\n                    const changes = this.calculateDiff(originalContent, modifiedContent);\n                    const stats = this.calculateStats(changes);\n                    const diffResult = {\n                        fileId: existingFile.id,\n                        filePath: existingFile.path,\n                        operation: \"replace\",\n                        originalContent,\n                        modifiedContent,\n                        changes,\n                        stats,\n                        timestamp: Date.now()\n                    };\n                    return {\n                        success: true,\n                        data: diffResult\n                    };\n                }\n            } else {\n                // 文件不存在，创建新文件\n                const fileResult = await this.fileTreeService.createFile(artworkId, currentParentId, fileName, \"text\", content);\n                if (!fileResult.success) {\n                    return {\n                        success: false,\n                        error: \"创建文件失败: \".concat(fileResult.error)\n                    };\n                }\n                const newFile = fileResult.data;\n                // 创建差异结果（新文件创建）\n                const changes = this.calculateDiff(\"\", content);\n                const stats = this.calculateStats(changes);\n                const diffResult = {\n                    fileId: newFile.id,\n                    filePath: newFile.path,\n                    operation: \"create\",\n                    originalContent: \"\",\n                    modifiedContent: content,\n                    changes,\n                    stats,\n                    timestamp: Date.now()\n                };\n                console.log(\"✅ 文件及路径创建成功:\", filePath);\n                return {\n                    success: true,\n                    data: diffResult\n                };\n            }\n        } catch (error) {\n            const errorMessage = \"创建文件及路径失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 根据名称查找子节点（文件或文件夹）\r\n   * 辅助方法，用于检查文件/文件夹是否存在\r\n   */ async findChildByName(artworkId, parentId, name) {\n        try {\n            const allFilesResult = await this.fileTreeService.getFilesByArtwork(artworkId);\n            if (!allFilesResult.success || !allFilesResult.data) {\n                return null;\n            }\n            const files = allFilesResult.data;\n            return files.find((file)=>file.parentId === parentId && file.name === name) || null;\n        } catch (error) {\n            console.error(\"❌ 查找子节点失败:\", error);\n            return null;\n        }\n    }\n    /**\r\n   * 检查文件是否存在\r\n   * 用于UI组件判断显示状态\r\n   */ async checkFileExists(artworkId, filePath) {\n        try {\n            const fileTreeResult = await this.fileTreeService.getFileTree(artworkId);\n            if (!fileTreeResult.success || !fileTreeResult.data) {\n                return false;\n            }\n            const rootNode = fileTreeResult.data;\n            const pathParts = filePath.split(\"/\").filter((part)=>part.trim() !== \"\");\n            let currentParentId = rootNode.id;\n            // 逐级检查路径\n            for (const part of pathParts){\n                const child = await this.findChildByName(artworkId, currentParentId, part);\n                if (!child) {\n                    return false;\n                }\n                currentParentId = child.id;\n            }\n            return true;\n        } catch (error) {\n            console.error(\"❌ 检查文件存在性失败:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.fileTreeService = _fileTreeService__WEBPACK_IMPORTED_MODULE_0__.FileTreeService.getInstance();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/diffToolService.ts\n"));

/***/ })

});