"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_php_php_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/php/php.js":
/*!**********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/php/php.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/php/php.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*(#|//)region\\\\b\"),\n      end: new RegExp(\"^\\\\s*(#|//)endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  // ignoreCase: true,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.root\" }],\n      [/<!DOCTYPE/, \"metatag.html\", \"@doctype\"],\n      [/<!--/, \"comment.html\", \"@comment\"],\n      [/(<)(\\w+)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<)(script)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@script\" }]],\n      [/(<)(style)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@style\" }]],\n      [/(<)([:\\w]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)(\\w+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/[^<]+/]\n      // text\n    ],\n    doctype: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.comment\" }],\n      [/[^>]+/, \"metatag.content.html\"],\n      [/>/, \"metatag.html\", \"@pop\"]\n    ],\n    comment: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.comment\" }],\n      [/-->/, \"comment.html\", \"@pop\"],\n      [/[^-]+/, \"comment.content.html\"],\n      [/./, \"comment.content.html\"]\n    ],\n    otherTag: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.otherTag\" }],\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    // -- BEGIN <script> tags handling\n    // After <script\n    script: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.script\" }],\n      [/type/, \"attribute.name\", \"@scriptAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(script\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <script ... type\n    scriptAfterType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.scriptAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@scriptAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type =\n    scriptAfterTypeEquals: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.scriptAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.text/javascript\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type = $S2\n    scriptWithCustomType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.scriptWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptEmbedded: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInEmbeddedState.scriptEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/script/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    // -- END <script> tags handling\n    // -- BEGIN <style> tags handling\n    // After <style\n    style: [\n      [/<\\?((php)|=)?/, { token: \"@rematch\", switchTo: \"@phpInSimpleState.style\" }],\n      [/type/, \"attribute.name\", \"@styleAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(style\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <style ... type\n    styleAfterType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.styleAfterType\"\n        }\n      ],\n      [/=/, \"delimiter\", \"@styleAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type =\n    styleAfterTypeEquals: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.styleAfterTypeEquals\"\n        }\n      ],\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.text/css\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type = $S2\n    styleWithCustomType: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInSimpleState.styleWithCustomType.$S2\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleEmbedded: [\n      [\n        /<\\?((php)|=)?/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@phpInEmbeddedState.styleEmbedded.$S2\",\n          nextEmbedded: \"@pop\"\n        }\n      ],\n      [/<\\/style/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    // -- END <style> tags handling\n    phpInSimpleState: [\n      [/<\\?((php)|=)?/, \"metatag.php\"],\n      [/\\?>/, { token: \"metatag.php\", switchTo: \"@$S2.$S3\" }],\n      { include: \"phpRoot\" }\n    ],\n    phpInEmbeddedState: [\n      [/<\\?((php)|=)?/, \"metatag.php\"],\n      [\n        /\\?>/,\n        {\n          token: \"metatag.php\",\n          switchTo: \"@$S2.$S3\",\n          nextEmbedded: \"$S3\"\n        }\n      ],\n      { include: \"phpRoot\" }\n    ],\n    phpRoot: [\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@phpKeywords\": { token: \"keyword.php\" },\n            \"@phpCompileTimeConstants\": { token: \"constant.php\" },\n            \"@default\": \"identifier.php\"\n          }\n        }\n      ],\n      [\n        /[$a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@phpPreDefinedVariables\": {\n              token: \"variable.predefined.php\"\n            },\n            \"@default\": \"variable.php\"\n          }\n        }\n      ],\n      // brackets\n      [/[{}]/, \"delimiter.bracket.php\"],\n      [/[\\[\\]]/, \"delimiter.array.php\"],\n      [/[()]/, \"delimiter.parenthesis.php\"],\n      // whitespace\n      [/[ \\t\\r\\n]+/],\n      // comments\n      [/(#|\\/\\/)$/, \"comment.php\"],\n      [/(#|\\/\\/)/, \"comment.php\", \"@phpLineComment\"],\n      // block comments\n      [/\\/\\*/, \"comment.php\", \"@phpComment\"],\n      // strings\n      [/\"/, \"string.php\", \"@phpDoubleQuoteString\"],\n      [/'/, \"string.php\", \"@phpSingleQuoteString\"],\n      // delimiters\n      [/[\\+\\-\\*\\%\\&\\|\\^\\~\\!\\=\\<\\>\\/\\?\\;\\:\\.\\,\\@]/, \"delimiter.php\"],\n      // numbers\n      [/\\d*\\d+[eE]([\\-+]?\\d+)?/, \"number.float.php\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float.php\"],\n      [/0[xX][0-9a-fA-F']*[0-9a-fA-F]/, \"number.hex.php\"],\n      [/0[0-7']*[0-7]/, \"number.octal.php\"],\n      [/0[bB][0-1']*[0-1]/, \"number.binary.php\"],\n      [/\\d[\\d']*/, \"number.php\"],\n      [/\\d/, \"number.php\"]\n    ],\n    phpComment: [\n      [/\\*\\//, \"comment.php\", \"@pop\"],\n      [/[^*]+/, \"comment.php\"],\n      [/./, \"comment.php\"]\n    ],\n    phpLineComment: [\n      [/\\?>/, { token: \"@rematch\", next: \"@pop\" }],\n      [/.$/, \"comment.php\", \"@pop\"],\n      [/[^?]+$/, \"comment.php\", \"@pop\"],\n      [/[^?]+/, \"comment.php\"],\n      [/./, \"comment.php\"]\n    ],\n    phpDoubleQuoteString: [\n      [/[^\\\\\"]+/, \"string.php\"],\n      [/@escapes/, \"string.escape.php\"],\n      [/\\\\./, \"string.escape.invalid.php\"],\n      [/\"/, \"string.php\", \"@pop\"]\n    ],\n    phpSingleQuoteString: [\n      [/[^\\\\']+/, \"string.php\"],\n      [/@escapes/, \"string.escape.php\"],\n      [/\\\\./, \"string.escape.invalid.php\"],\n      [/'/, \"string.php\", \"@pop\"]\n    ]\n  },\n  phpKeywords: [\n    \"abstract\",\n    \"and\",\n    \"array\",\n    \"as\",\n    \"break\",\n    \"callable\",\n    \"case\",\n    \"catch\",\n    \"cfunction\",\n    \"class\",\n    \"clone\",\n    \"const\",\n    \"continue\",\n    \"declare\",\n    \"default\",\n    \"do\",\n    \"else\",\n    \"elseif\",\n    \"enddeclare\",\n    \"endfor\",\n    \"endforeach\",\n    \"endif\",\n    \"endswitch\",\n    \"endwhile\",\n    \"extends\",\n    \"false\",\n    \"final\",\n    \"for\",\n    \"foreach\",\n    \"function\",\n    \"global\",\n    \"goto\",\n    \"if\",\n    \"implements\",\n    \"interface\",\n    \"instanceof\",\n    \"insteadof\",\n    \"namespace\",\n    \"new\",\n    \"null\",\n    \"object\",\n    \"old_function\",\n    \"or\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"resource\",\n    \"static\",\n    \"switch\",\n    \"throw\",\n    \"trait\",\n    \"try\",\n    \"true\",\n    \"use\",\n    \"var\",\n    \"while\",\n    \"xor\",\n    \"die\",\n    \"echo\",\n    \"empty\",\n    \"exit\",\n    \"eval\",\n    \"include\",\n    \"include_once\",\n    \"isset\",\n    \"list\",\n    \"require\",\n    \"require_once\",\n    \"return\",\n    \"print\",\n    \"unset\",\n    \"yield\",\n    \"__construct\"\n  ],\n  phpCompileTimeConstants: [\n    \"__CLASS__\",\n    \"__DIR__\",\n    \"__FILE__\",\n    \"__LINE__\",\n    \"__NAMESPACE__\",\n    \"__METHOD__\",\n    \"__FUNCTION__\",\n    \"__TRAIT__\"\n  ],\n  phpPreDefinedVariables: [\n    \"$GLOBALS\",\n    \"$_SERVER\",\n    \"$_GET\",\n    \"$_POST\",\n    \"$_FILES\",\n    \"$_REQUEST\",\n    \"$_SESSION\",\n    \"$_ENV\",\n    \"$_COOKIE\",\n    \"$php_errormsg\",\n    \"$HTTP_RAW_POST_DATA\",\n    \"$http_response_header\",\n    \"$argc\",\n    \"$argv\"\n  ],\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/php/php.js\n"));

/***/ })

}]);