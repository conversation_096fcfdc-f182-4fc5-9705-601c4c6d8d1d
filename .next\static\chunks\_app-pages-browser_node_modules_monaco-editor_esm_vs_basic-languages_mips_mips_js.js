"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_mips_mips_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/mips/mips.js":
/*!************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/mips/mips.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/mips/mips.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#%\\^\\&\\*\\(\\)\\=\\$\\-\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    blockComment: [\"###\", \"###\"],\n    lineComment: \"#\"\n  },\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  ignoreCase: false,\n  tokenPostfix: \".mips\",\n  regEx: /\\/(?!\\/\\/)(?:[^\\/\\\\]|\\\\.)*\\/[igm]*/,\n  keywords: [\n    \".data\",\n    \".text\",\n    \"syscall\",\n    \"trap\",\n    \"add\",\n    \"addu\",\n    \"addi\",\n    \"addiu\",\n    \"and\",\n    \"andi\",\n    \"div\",\n    \"divu\",\n    \"mult\",\n    \"multu\",\n    \"nor\",\n    \"or\",\n    \"ori\",\n    \"sll\",\n    \"slv\",\n    \"sra\",\n    \"srav\",\n    \"srl\",\n    \"srlv\",\n    \"sub\",\n    \"subu\",\n    \"xor\",\n    \"xori\",\n    \"lhi\",\n    \"lho\",\n    \"lhi\",\n    \"llo\",\n    \"slt\",\n    \"slti\",\n    \"sltu\",\n    \"sltiu\",\n    \"beq\",\n    \"bgtz\",\n    \"blez\",\n    \"bne\",\n    \"j\",\n    \"jal\",\n    \"jalr\",\n    \"jr\",\n    \"lb\",\n    \"lbu\",\n    \"lh\",\n    \"lhu\",\n    \"lw\",\n    \"li\",\n    \"la\",\n    \"sb\",\n    \"sh\",\n    \"sw\",\n    \"mfhi\",\n    \"mflo\",\n    \"mthi\",\n    \"mtlo\",\n    \"move\"\n  ],\n  // we include these common regular expressions\n  symbols: /[\\.,\\:]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"'$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [/\\$[a-zA-Z_]\\w*/, \"variable.predefined\"],\n      [\n        /[.a-zA-Z_]\\w*/,\n        {\n          cases: {\n            this: \"variable.predefined\",\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // whitespace\n      [/[ \\t\\r\\n]+/, \"\"],\n      // Comments\n      [/#.*$/, \"comment\"],\n      // regular expressions\n      [\"///\", { token: \"regexp\", next: \"@hereregexp\" }],\n      [/^(\\s*)(@regEx)/, [\"\", \"regexp\"]],\n      [/(\\,)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\:)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      // delimiters\n      [/@symbols/, \"delimiter\"],\n      // numbers\n      [/\\d+[eE]([\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d+\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/0[0-7]+(?!\\d)/, \"number.octal\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[,.]/, \"delimiter\"],\n      // strings:\n      [/\"\"\"/, \"string\", '@herestring.\"\"\"'],\n      [/'''/, \"string\", \"@herestring.'''\"],\n      [\n        /\"/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: '@string.\"' }\n          }\n        }\n      ],\n      [\n        /'/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: \"@string.'\" }\n          }\n        }\n      ]\n    ],\n    string: [\n      [/[^\"'\\#\\\\]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\./, \"string.escape.invalid\"],\n      [/\\./, \"string.escape.invalid\"],\n      [\n        /#{/,\n        {\n          cases: {\n            '$S2==\"': {\n              token: \"string\",\n              next: \"root.interpolatedstring\"\n            },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/#/, \"string\"]\n    ],\n    herestring: [\n      [\n        /(\"\"\"|''')/,\n        {\n          cases: {\n            \"$1==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/[^#\\\\'\"]+/, \"string\"],\n      [/['\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\./, \"string.escape.invalid\"],\n      [/#{/, { token: \"string.quote\", next: \"root.interpolatedstring\" }],\n      [/#/, \"string\"]\n    ],\n    comment: [\n      [/[^#]+/, \"comment\"],\n      [/#/, \"comment\"]\n    ],\n    hereregexp: [\n      [/[^\\\\\\/#]+/, \"regexp\"],\n      [/\\\\./, \"regexp\"],\n      [/#.*$/, \"comment\"],\n      [\"///[igm]*\", { token: \"regexp\", next: \"@pop\" }],\n      [/\\//, \"regexp\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/mips/mips.js\n"));

/***/ })

}]);