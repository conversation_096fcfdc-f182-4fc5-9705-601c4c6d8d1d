"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_csharp_csharp_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/csharp/csharp.js":
/*!****************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/csharp/csharp.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/csharp/csharp.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\#\\$\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".cs\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  keywords: [\n    \"extern\",\n    \"alias\",\n    \"using\",\n    \"bool\",\n    \"decimal\",\n    \"sbyte\",\n    \"byte\",\n    \"short\",\n    \"ushort\",\n    \"int\",\n    \"uint\",\n    \"long\",\n    \"ulong\",\n    \"char\",\n    \"float\",\n    \"double\",\n    \"object\",\n    \"dynamic\",\n    \"string\",\n    \"assembly\",\n    \"is\",\n    \"as\",\n    \"ref\",\n    \"out\",\n    \"this\",\n    \"base\",\n    \"new\",\n    \"typeof\",\n    \"void\",\n    \"checked\",\n    \"unchecked\",\n    \"default\",\n    \"delegate\",\n    \"var\",\n    \"const\",\n    \"if\",\n    \"else\",\n    \"switch\",\n    \"case\",\n    \"while\",\n    \"do\",\n    \"for\",\n    \"foreach\",\n    \"in\",\n    \"break\",\n    \"continue\",\n    \"goto\",\n    \"return\",\n    \"throw\",\n    \"try\",\n    \"catch\",\n    \"finally\",\n    \"lock\",\n    \"yield\",\n    \"from\",\n    \"let\",\n    \"where\",\n    \"join\",\n    \"on\",\n    \"equals\",\n    \"into\",\n    \"orderby\",\n    \"ascending\",\n    \"descending\",\n    \"select\",\n    \"group\",\n    \"by\",\n    \"namespace\",\n    \"partial\",\n    \"class\",\n    \"field\",\n    \"event\",\n    \"method\",\n    \"param\",\n    \"public\",\n    \"protected\",\n    \"internal\",\n    \"private\",\n    \"abstract\",\n    \"sealed\",\n    \"static\",\n    \"struct\",\n    \"readonly\",\n    \"volatile\",\n    \"virtual\",\n    \"override\",\n    \"params\",\n    \"get\",\n    \"set\",\n    \"add\",\n    \"remove\",\n    \"operator\",\n    \"true\",\n    \"false\",\n    \"implicit\",\n    \"explicit\",\n    \"interface\",\n    \"enum\",\n    \"null\",\n    \"async\",\n    \"await\",\n    \"fixed\",\n    \"sizeof\",\n    \"stackalloc\",\n    \"unsafe\",\n    \"nameof\",\n    \"when\"\n  ],\n  namespaceFollows: [\"namespace\", \"using\"],\n  parenFollows: [\"if\", \"for\", \"while\", \"switch\", \"foreach\", \"using\", \"catch\", \"when\"],\n  operators: [\n    \"=\",\n    \"??\",\n    \"||\",\n    \"&&\",\n    \"|\",\n    \"^\",\n    \"&\",\n    \"==\",\n    \"!=\",\n    \"<=\",\n    \">=\",\n    \"<<\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"!\",\n    \"~\",\n    \"++\",\n    \"--\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"%=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"<<=\",\n    \">>=\",\n    \">>\",\n    \"=>\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  // escape sequences\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [\n        /\\@?[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@namespaceFollows\": {\n              token: \"keyword.$0\",\n              next: \"@namespace\"\n            },\n            \"@keywords\": {\n              token: \"keyword.$0\",\n              next: \"@qualified\"\n            },\n            \"@default\": { token: \"identifier\", next: \"@qualified\" }\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [\n        /}/,\n        {\n          cases: {\n            \"$S2==interpolatedstring\": {\n              token: \"string.quote\",\n              next: \"@pop\"\n            },\n            \"$S2==litinterpstring\": {\n              token: \"string.quote\",\n              next: \"@pop\"\n            },\n            \"@default\": \"@brackets\"\n          }\n        }\n      ],\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/[0-9_]*\\.[0-9_]+([eE][\\-+]?\\d+)?[fFdD]?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F_]+/, \"number.hex\"],\n      [/0[bB][01_]+/, \"number.hex\"],\n      // binary: use same theme style as hex\n      [/[0-9_]+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, { token: \"string.quote\", next: \"@string\" }],\n      [/\\$\\@\"/, { token: \"string.quote\", next: \"@litinterpstring\" }],\n      [/\\@\"/, { token: \"string.quote\", next: \"@litstring\" }],\n      [/\\$\"/, { token: \"string.quote\", next: \"@interpolatedstring\" }],\n      // characters\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    qualified: [\n      [\n        /[a-zA-Z_][\\w]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/\\./, \"delimiter\"],\n      [\"\", \"\", \"@pop\"]\n    ],\n    namespace: [\n      { include: \"@whitespace\" },\n      [/[A-Z]\\w*/, \"namespace\"],\n      [/[\\.=]/, \"delimiter\"],\n      [\"\", \"\", \"@pop\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      // [/\\/\\*/,    'comment', '@push' ],    // no nested comments :-(\n      [\"\\\\*/\", \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    litstring: [\n      [/[^\"]+/, \"string\"],\n      [/\"\"/, \"string.escape\"],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    litinterpstring: [\n      [/[^\"{]+/, \"string\"],\n      [/\"\"/, \"string.escape\"],\n      [/{{/, \"string.escape\"],\n      [/}}/, \"string.escape\"],\n      [/{/, { token: \"string.quote\", next: \"root.litinterpstring\" }],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    interpolatedstring: [\n      [/[^\\\\\"{]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/{{/, \"string.escape\"],\n      [/}}/, \"string.escape\"],\n      [/{/, { token: \"string.quote\", next: \"root.interpolatedstring\" }],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/^[ \\t\\v\\f]*#((r)|(load))(?=\\s)/, \"directive.csx\"],\n      [/^[ \\t\\v\\f]*#\\w.*$/, \"namespace.cpp\"],\n      [/[ \\t\\v\\f\\r\\n]+/, \"\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/csharp/csharp.js\n"));

/***/ })

}]);