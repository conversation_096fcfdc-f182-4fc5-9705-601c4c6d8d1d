"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_redis_redis_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/redis/redis.js":
/*!**************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/redis/redis.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/redis/redis.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".redis\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \"APPEND\",\n    \"AUTH\",\n    \"BGREWRITEAOF\",\n    \"BGSAVE\",\n    \"BITCOUNT\",\n    \"BITFIELD\",\n    \"BITOP\",\n    \"BITPOS\",\n    \"BLPOP\",\n    \"BRPOP\",\n    \"BRPOPLPUSH\",\n    \"CLIENT\",\n    \"KILL\",\n    \"LIST\",\n    \"GETNAME\",\n    \"PAUSE\",\n    \"REPLY\",\n    \"SETNAME\",\n    \"CLUSTER\",\n    \"ADDSLOTS\",\n    \"COUNT-FAILURE-REPORTS\",\n    \"COUNTKEYSINSLOT\",\n    \"DELSLOTS\",\n    \"FAILOVER\",\n    \"FORGET\",\n    \"GETKEYSINSLOT\",\n    \"INFO\",\n    \"KEYSLOT\",\n    \"MEET\",\n    \"NODES\",\n    \"REPLICATE\",\n    \"RESET\",\n    \"SAVECONFIG\",\n    \"SET-CONFIG-EPOCH\",\n    \"SETSLOT\",\n    \"SLAVES\",\n    \"SLOTS\",\n    \"COMMAND\",\n    \"COUNT\",\n    \"GETKEYS\",\n    \"CONFIG\",\n    \"GET\",\n    \"REWRITE\",\n    \"SET\",\n    \"RESETSTAT\",\n    \"DBSIZE\",\n    \"DEBUG\",\n    \"OBJECT\",\n    \"SEGFAULT\",\n    \"DECR\",\n    \"DECRBY\",\n    \"DEL\",\n    \"DISCARD\",\n    \"DUMP\",\n    \"ECHO\",\n    \"EVAL\",\n    \"EVALSHA\",\n    \"EXEC\",\n    \"EXISTS\",\n    \"EXPIRE\",\n    \"EXPIREAT\",\n    \"FLUSHALL\",\n    \"FLUSHDB\",\n    \"GEOADD\",\n    \"GEOHASH\",\n    \"GEOPOS\",\n    \"GEODIST\",\n    \"GEORADIUS\",\n    \"GEORADIUSBYMEMBER\",\n    \"GETBIT\",\n    \"GETRANGE\",\n    \"GETSET\",\n    \"HDEL\",\n    \"HEXISTS\",\n    \"HGET\",\n    \"HGETALL\",\n    \"HINCRBY\",\n    \"HINCRBYFLOAT\",\n    \"HKEYS\",\n    \"HLEN\",\n    \"HMGET\",\n    \"HMSET\",\n    \"HSET\",\n    \"HSETNX\",\n    \"HSTRLEN\",\n    \"HVALS\",\n    \"INCR\",\n    \"INCRBY\",\n    \"INCRBYFLOAT\",\n    \"KEYS\",\n    \"LASTSAVE\",\n    \"LINDEX\",\n    \"LINSERT\",\n    \"LLEN\",\n    \"LPOP\",\n    \"LPUSH\",\n    \"LPUSHX\",\n    \"LRANGE\",\n    \"LREM\",\n    \"LSET\",\n    \"LTRIM\",\n    \"MGET\",\n    \"MIGRATE\",\n    \"MONITOR\",\n    \"MOVE\",\n    \"MSET\",\n    \"MSETNX\",\n    \"MULTI\",\n    \"PERSIST\",\n    \"PEXPIRE\",\n    \"PEXPIREAT\",\n    \"PFADD\",\n    \"PFCOUNT\",\n    \"PFMERGE\",\n    \"PING\",\n    \"PSETEX\",\n    \"PSUBSCRIBE\",\n    \"PUBSUB\",\n    \"PTTL\",\n    \"PUBLISH\",\n    \"PUNSUBSCRIBE\",\n    \"QUIT\",\n    \"RANDOMKEY\",\n    \"READONLY\",\n    \"READWRITE\",\n    \"RENAME\",\n    \"RENAMENX\",\n    \"RESTORE\",\n    \"ROLE\",\n    \"RPOP\",\n    \"RPOPLPUSH\",\n    \"RPUSH\",\n    \"RPUSHX\",\n    \"SADD\",\n    \"SAVE\",\n    \"SCARD\",\n    \"SCRIPT\",\n    \"FLUSH\",\n    \"LOAD\",\n    \"SDIFF\",\n    \"SDIFFSTORE\",\n    \"SELECT\",\n    \"SETBIT\",\n    \"SETEX\",\n    \"SETNX\",\n    \"SETRANGE\",\n    \"SHUTDOWN\",\n    \"SINTER\",\n    \"SINTERSTORE\",\n    \"SISMEMBER\",\n    \"SLAVEOF\",\n    \"SLOWLOG\",\n    \"SMEMBERS\",\n    \"SMOVE\",\n    \"SORT\",\n    \"SPOP\",\n    \"SRANDMEMBER\",\n    \"SREM\",\n    \"STRLEN\",\n    \"SUBSCRIBE\",\n    \"SUNION\",\n    \"SUNIONSTORE\",\n    \"SWAPDB\",\n    \"SYNC\",\n    \"TIME\",\n    \"TOUCH\",\n    \"TTL\",\n    \"TYPE\",\n    \"UNSUBSCRIBE\",\n    \"UNLINK\",\n    \"UNWATCH\",\n    \"WAIT\",\n    \"WATCH\",\n    \"ZADD\",\n    \"ZCARD\",\n    \"ZCOUNT\",\n    \"ZINCRBY\",\n    \"ZINTERSTORE\",\n    \"ZLEXCOUNT\",\n    \"ZRANGE\",\n    \"ZRANGEBYLEX\",\n    \"ZREVRANGEBYLEX\",\n    \"ZRANGEBYSCORE\",\n    \"ZRANK\",\n    \"ZREM\",\n    \"ZREMRANGEBYLEX\",\n    \"ZREMRANGEBYRANK\",\n    \"ZREMRANGEBYSCORE\",\n    \"ZREVRANGE\",\n    \"ZREVRANGEBYSCORE\",\n    \"ZREVRANK\",\n    \"ZSCORE\",\n    \"ZUNIONSTORE\",\n    \"SCAN\",\n    \"SSCAN\",\n    \"HSCAN\",\n    \"ZSCAN\"\n  ],\n  operators: [\n    // NOT SUPPORTED\n  ],\n  builtinFunctions: [\n    // NOT SUPPORTED\n  ],\n  builtinVariables: [\n    // NOT SUPPORTED\n  ],\n  pseudoColumns: [\n    // NOT SUPPORTED\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@pseudoColumns\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@scopes\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [\n        /[\\w@#$]+/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@operators\": \"operator\",\n            \"@builtinVariables\": \"predefined\",\n            \"@builtinFunctions\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    pseudoColumns: [\n      [\n        /[$][A-Za-z_][\\w@#$]*/,\n        {\n          cases: {\n            \"@pseudoColumns\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [\n      [/'/, { token: \"string\", next: \"@string\" }],\n      [/\"/, { token: \"string.double\", next: \"@stringDouble\" }]\n    ],\n    string: [\n      [/[^']+/, \"string\"],\n      [/''/, \"string\"],\n      [/'/, { token: \"string\", next: \"@pop\" }]\n    ],\n    stringDouble: [\n      [/[^\"]+/, \"string.double\"],\n      [/\"\"/, \"string.double\"],\n      [/\"/, { token: \"string.double\", next: \"@pop\" }]\n    ],\n    scopes: [\n      // NOT SUPPORTED\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/redis/redis.js\n"));

/***/ })

}]);