"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_shell_shell_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/shell/shell.js":
/*!**************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/shell/shell.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/shell/shell.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  ignoreCase: true,\n  tokenPostfix: \".shell\",\n  brackets: [\n    { token: \"delimiter.bracket\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  keywords: [\n    \"if\",\n    \"then\",\n    \"do\",\n    \"else\",\n    \"elif\",\n    \"while\",\n    \"until\",\n    \"for\",\n    \"in\",\n    \"esac\",\n    \"fi\",\n    \"fin\",\n    \"fil\",\n    \"done\",\n    \"exit\",\n    \"set\",\n    \"unset\",\n    \"export\",\n    \"function\"\n  ],\n  builtins: [\n    \"ab\",\n    \"awk\",\n    \"bash\",\n    \"beep\",\n    \"cat\",\n    \"cc\",\n    \"cd\",\n    \"chown\",\n    \"chmod\",\n    \"chroot\",\n    \"clear\",\n    \"cp\",\n    \"curl\",\n    \"cut\",\n    \"diff\",\n    \"echo\",\n    \"find\",\n    \"gawk\",\n    \"gcc\",\n    \"get\",\n    \"git\",\n    \"grep\",\n    \"hg\",\n    \"kill\",\n    \"killall\",\n    \"ln\",\n    \"ls\",\n    \"make\",\n    \"mkdir\",\n    \"openssl\",\n    \"mv\",\n    \"nc\",\n    \"node\",\n    \"npm\",\n    \"ping\",\n    \"ps\",\n    \"restart\",\n    \"rm\",\n    \"rmdir\",\n    \"sed\",\n    \"service\",\n    \"sh\",\n    \"shopt\",\n    \"shred\",\n    \"source\",\n    \"sort\",\n    \"sleep\",\n    \"ssh\",\n    \"start\",\n    \"stop\",\n    \"su\",\n    \"sudo\",\n    \"svn\",\n    \"tee\",\n    \"telnet\",\n    \"top\",\n    \"touch\",\n    \"vi\",\n    \"vim\",\n    \"wall\",\n    \"wc\",\n    \"wget\",\n    \"who\",\n    \"write\",\n    \"yes\",\n    \"zsh\"\n  ],\n  startingWithDash: /\\-+\\w+/,\n  identifiersWithDashes: /[a-zA-Z]\\w+(?:@startingWithDash)+/,\n  // we include these common regular expressions\n  symbols: /[=><!~?&|+\\-*\\/\\^;\\.,]+/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      [/@identifiersWithDashes/, \"\"],\n      [/(\\s)((?:@startingWithDash)+)/, [\"white\", \"attribute.name\"]],\n      [\n        /[a-zA-Z]\\w*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"type.identifier\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      { include: \"@strings\" },\n      { include: \"@parameters\" },\n      { include: \"@heredoc\" },\n      [/[{}\\[\\]()]/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      { include: \"@numbers\" },\n      [/[,;]/, \"delimiter\"]\n    ],\n    whitespace: [\n      [/\\s+/, \"white\"],\n      [/(^#!.*$)/, \"metatag\"],\n      [/(^#.*$)/, \"comment\"]\n    ],\n    numbers: [\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F_]*[0-9a-fA-F]/, \"number.hex\"],\n      [/\\d+/, \"number\"]\n    ],\n    // Recognize strings, including those broken across lines\n    strings: [\n      [/'/, \"string\", \"@stringBody\"],\n      [/\"/, \"string\", \"@dblStringBody\"]\n    ],\n    stringBody: [\n      [/'/, \"string\", \"@popall\"],\n      [/./, \"string\"]\n    ],\n    dblStringBody: [\n      [/\"/, \"string\", \"@popall\"],\n      [/./, \"string\"]\n    ],\n    heredoc: [\n      [\n        /(<<[-<]?)(\\s*)(['\"`]?)([\\w\\-]+)(['\"`]?)/,\n        [\n          \"constants\",\n          \"white\",\n          \"string.heredoc.delimiter\",\n          \"string.heredoc\",\n          \"string.heredoc.delimiter\"\n        ]\n      ]\n    ],\n    parameters: [\n      [/\\$\\d+/, \"variable.predefined\"],\n      [/\\$\\w+/, \"variable\"],\n      [/\\$[*@#?\\-$!0_]/, \"variable\"],\n      [/\\$'/, \"variable\", \"@parameterBodyQuote\"],\n      [/\\$\"/, \"variable\", \"@parameterBodyDoubleQuote\"],\n      [/\\$\\(/, \"variable\", \"@parameterBodyParen\"],\n      [/\\$\\{/, \"variable\", \"@parameterBodyCurlyBrace\"]\n    ],\n    parameterBodyQuote: [\n      [/[^#:%*@\\-!_']+/, \"variable\"],\n      [/[#:%*@\\-!_]/, \"delimiter\"],\n      [/[']/, \"variable\", \"@pop\"]\n    ],\n    parameterBodyDoubleQuote: [\n      [/[^#:%*@\\-!_\"]+/, \"variable\"],\n      [/[#:%*@\\-!_]/, \"delimiter\"],\n      [/[\"]/, \"variable\", \"@pop\"]\n    ],\n    parameterBodyParen: [\n      [/[^#:%*@\\-!_)]+/, \"variable\"],\n      [/[#:%*@\\-!_]/, \"delimiter\"],\n      [/[)]/, \"variable\", \"@pop\"]\n    ],\n    parameterBodyCurlyBrace: [\n      [/[^#:%*@\\-!_}]+/, \"variable\"],\n      [/[#:%*@\\-!_]/, \"delimiter\"],\n      [/[}]/, \"variable\", \"@pop\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/shell/shell.js\n"));

/***/ })

}]);