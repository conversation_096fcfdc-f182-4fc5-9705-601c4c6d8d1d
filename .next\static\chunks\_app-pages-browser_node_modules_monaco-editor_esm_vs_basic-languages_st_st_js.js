"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_st_st_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/st/st.js":
/*!********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/st/st.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/st/st.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"(*\", \"*)\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"var\", \"end_var\"],\n    [\"var_input\", \"end_var\"],\n    [\"var_output\", \"end_var\"],\n    [\"var_in_out\", \"end_var\"],\n    [\"var_temp\", \"end_var\"],\n    [\"var_global\", \"end_var\"],\n    [\"var_access\", \"end_var\"],\n    [\"var_external\", \"end_var\"],\n    [\"type\", \"end_type\"],\n    [\"struct\", \"end_struct\"],\n    [\"program\", \"end_program\"],\n    [\"function\", \"end_function\"],\n    [\"function_block\", \"end_function_block\"],\n    [\"action\", \"end_action\"],\n    [\"step\", \"end_step\"],\n    [\"initial_step\", \"end_step\"],\n    [\"transaction\", \"end_transaction\"],\n    [\"configuration\", \"end_configuration\"],\n    [\"tcp\", \"end_tcp\"],\n    [\"recource\", \"end_recource\"],\n    [\"channel\", \"end_channel\"],\n    [\"library\", \"end_library\"],\n    [\"folder\", \"end_folder\"],\n    [\"binaries\", \"end_binaries\"],\n    [\"includes\", \"end_includes\"],\n    [\"sources\", \"end_sources\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"/*\", close: \"*/\" },\n    { open: \"'\", close: \"'\", notIn: [\"string_sq\"] },\n    { open: '\"', close: '\"', notIn: [\"string_dq\"] },\n    { open: \"var_input\", close: \"end_var\" },\n    { open: \"var_output\", close: \"end_var\" },\n    { open: \"var_in_out\", close: \"end_var\" },\n    { open: \"var_temp\", close: \"end_var\" },\n    { open: \"var_global\", close: \"end_var\" },\n    { open: \"var_access\", close: \"end_var\" },\n    { open: \"var_external\", close: \"end_var\" },\n    { open: \"type\", close: \"end_type\" },\n    { open: \"struct\", close: \"end_struct\" },\n    { open: \"program\", close: \"end_program\" },\n    { open: \"function\", close: \"end_function\" },\n    { open: \"function_block\", close: \"end_function_block\" },\n    { open: \"action\", close: \"end_action\" },\n    { open: \"step\", close: \"end_step\" },\n    { open: \"initial_step\", close: \"end_step\" },\n    { open: \"transaction\", close: \"end_transaction\" },\n    { open: \"configuration\", close: \"end_configuration\" },\n    { open: \"tcp\", close: \"end_tcp\" },\n    { open: \"recource\", close: \"end_recource\" },\n    { open: \"channel\", close: \"end_channel\" },\n    { open: \"library\", close: \"end_library\" },\n    { open: \"folder\", close: \"end_folder\" },\n    { open: \"binaries\", close: \"end_binaries\" },\n    { open: \"includes\", close: \"end_includes\" },\n    { open: \"sources\", close: \"end_sources\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"var\", close: \"end_var\" },\n    { open: \"var_input\", close: \"end_var\" },\n    { open: \"var_output\", close: \"end_var\" },\n    { open: \"var_in_out\", close: \"end_var\" },\n    { open: \"var_temp\", close: \"end_var\" },\n    { open: \"var_global\", close: \"end_var\" },\n    { open: \"var_access\", close: \"end_var\" },\n    { open: \"var_external\", close: \"end_var\" },\n    { open: \"type\", close: \"end_type\" },\n    { open: \"struct\", close: \"end_struct\" },\n    { open: \"program\", close: \"end_program\" },\n    { open: \"function\", close: \"end_function\" },\n    { open: \"function_block\", close: \"end_function_block\" },\n    { open: \"action\", close: \"end_action\" },\n    { open: \"step\", close: \"end_step\" },\n    { open: \"initial_step\", close: \"end_step\" },\n    { open: \"transaction\", close: \"end_transaction\" },\n    { open: \"configuration\", close: \"end_configuration\" },\n    { open: \"tcp\", close: \"end_tcp\" },\n    { open: \"recource\", close: \"end_recource\" },\n    { open: \"channel\", close: \"end_channel\" },\n    { open: \"library\", close: \"end_library\" },\n    { open: \"folder\", close: \"end_folder\" },\n    { open: \"binaries\", close: \"end_binaries\" },\n    { open: \"includes\", close: \"end_includes\" },\n    { open: \"sources\", close: \"end_sources\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#pragma\\\\s+region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#pragma\\\\s+endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".st\",\n  ignoreCase: true,\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  keywords: [\n    \"if\",\n    \"end_if\",\n    \"elsif\",\n    \"else\",\n    \"case\",\n    \"of\",\n    \"to\",\n    \"__try\",\n    \"__catch\",\n    \"__finally\",\n    \"do\",\n    \"with\",\n    \"by\",\n    \"while\",\n    \"repeat\",\n    \"end_while\",\n    \"end_repeat\",\n    \"end_case\",\n    \"for\",\n    \"end_for\",\n    \"task\",\n    \"retain\",\n    \"non_retain\",\n    \"constant\",\n    \"with\",\n    \"at\",\n    \"exit\",\n    \"return\",\n    \"interval\",\n    \"priority\",\n    \"address\",\n    \"port\",\n    \"on_channel\",\n    \"then\",\n    \"iec\",\n    \"file\",\n    \"uses\",\n    \"version\",\n    \"packagetype\",\n    \"displayname\",\n    \"copyright\",\n    \"summary\",\n    \"vendor\",\n    \"common_source\",\n    \"from\",\n    \"extends\",\n    \"implements\"\n  ],\n  constant: [\"false\", \"true\", \"null\"],\n  defineKeywords: [\n    \"var\",\n    \"var_input\",\n    \"var_output\",\n    \"var_in_out\",\n    \"var_temp\",\n    \"var_global\",\n    \"var_access\",\n    \"var_external\",\n    \"end_var\",\n    \"type\",\n    \"end_type\",\n    \"struct\",\n    \"end_struct\",\n    \"program\",\n    \"end_program\",\n    \"function\",\n    \"end_function\",\n    \"function_block\",\n    \"end_function_block\",\n    \"interface\",\n    \"end_interface\",\n    \"method\",\n    \"end_method\",\n    \"property\",\n    \"end_property\",\n    \"namespace\",\n    \"end_namespace\",\n    \"configuration\",\n    \"end_configuration\",\n    \"tcp\",\n    \"end_tcp\",\n    \"resource\",\n    \"end_resource\",\n    \"channel\",\n    \"end_channel\",\n    \"library\",\n    \"end_library\",\n    \"folder\",\n    \"end_folder\",\n    \"binaries\",\n    \"end_binaries\",\n    \"includes\",\n    \"end_includes\",\n    \"sources\",\n    \"end_sources\",\n    \"action\",\n    \"end_action\",\n    \"step\",\n    \"initial_step\",\n    \"end_step\",\n    \"transaction\",\n    \"end_transaction\"\n  ],\n  typeKeywords: [\n    \"int\",\n    \"sint\",\n    \"dint\",\n    \"lint\",\n    \"usint\",\n    \"uint\",\n    \"udint\",\n    \"ulint\",\n    \"real\",\n    \"lreal\",\n    \"time\",\n    \"date\",\n    \"time_of_day\",\n    \"date_and_time\",\n    \"string\",\n    \"bool\",\n    \"byte\",\n    \"word\",\n    \"dword\",\n    \"array\",\n    \"pointer\",\n    \"lword\"\n  ],\n  operators: [\n    \"=\",\n    \">\",\n    \"<\",\n    \":\",\n    \":=\",\n    \"<=\",\n    \">=\",\n    \"<>\",\n    \"&\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"**\",\n    \"MOD\",\n    \"^\",\n    \"or\",\n    \"and\",\n    \"not\",\n    \"xor\",\n    \"abs\",\n    \"acos\",\n    \"asin\",\n    \"atan\",\n    \"cos\",\n    \"exp\",\n    \"expt\",\n    \"ln\",\n    \"log\",\n    \"sin\",\n    \"sqrt\",\n    \"tan\",\n    \"sel\",\n    \"max\",\n    \"min\",\n    \"limit\",\n    \"mux\",\n    \"shl\",\n    \"shr\",\n    \"rol\",\n    \"ror\",\n    \"indexof\",\n    \"sizeof\",\n    \"adr\",\n    \"adrinst\",\n    \"bitadr\",\n    \"is_valid\",\n    \"ref\",\n    \"ref_to\"\n  ],\n  builtinVariables: [],\n  builtinFunctions: [\n    \"sr\",\n    \"rs\",\n    \"tp\",\n    \"ton\",\n    \"tof\",\n    \"eq\",\n    \"ge\",\n    \"le\",\n    \"lt\",\n    \"ne\",\n    \"round\",\n    \"trunc\",\n    \"ctd\",\n    \"\\u0441tu\",\n    \"ctud\",\n    \"r_trig\",\n    \"f_trig\",\n    \"move\",\n    \"concat\",\n    \"delete\",\n    \"find\",\n    \"insert\",\n    \"left\",\n    \"len\",\n    \"replace\",\n    \"right\",\n    \"rtc\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  // C# style strings\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      [/(\\.\\.)/, \"delimiter\"],\n      [/\\b(16#[0-9A-Fa-f\\_]*)+\\b/, \"number.hex\"],\n      [/\\b(2#[01\\_]+)+\\b/, \"number.binary\"],\n      [/\\b(8#[0-9\\_]*)+\\b/, \"number.octal\"],\n      [/\\b\\d*\\.\\d+([eE][\\-+]?\\d+)?\\b/, \"number.float\"],\n      [/\\b(L?REAL)#[0-9\\_\\.e]+\\b/, \"number.float\"],\n      [/\\b(BYTE|(?:D|L)?WORD|U?(?:S|D|L)?INT)#[0-9\\_]+\\b/, \"number\"],\n      [/\\d+/, \"number\"],\n      [/\\b(T|DT|TOD)#[0-9:-_shmyd]+\\b/, \"tag\"],\n      [/\\%(I|Q|M)(X|B|W|D|L)[0-9\\.]+/, \"tag\"],\n      [/\\%(I|Q|M)[0-9\\.]*/, \"tag\"],\n      [/\\b[A-Za-z]{1,6}#[0-9]+\\b/, \"tag\"],\n      [/\\b(TO_|CTU_|CTD_|CTUD_|MUX_|SEL_)[A_Za-z]+\\b/, \"predefined\"],\n      [/\\b[A_Za-z]+(_TO_)[A_Za-z]+\\b/, \"predefined\"],\n      [/[;]/, \"delimiter\"],\n      [/[.]/, { token: \"delimiter\", next: \"@params\" }],\n      // identifiers and keywords\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@operators\": \"operators\",\n            \"@keywords\": \"keyword\",\n            \"@typeKeywords\": \"type\",\n            \"@defineKeywords\": \"variable\",\n            \"@constant\": \"constant\",\n            \"@builtinVariables\": \"predefined\",\n            \"@builtinFunctions\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string_dq\" }],\n      [/'/, { token: \"string.quote\", bracket: \"@open\", next: \"@string_sq\" }],\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    params: [\n      [/\\b[A-Za-z0-9_]+\\b(?=\\()/, { token: \"identifier\", next: \"@pop\" }],\n      [/\\b[A-Za-z0-9_]+\\b/, \"variable.name\", \"@pop\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@push\"],\n      // nested comment\n      [\"\\\\*/\", \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    comment2: [\n      [/[^\\(*]+/, \"comment\"],\n      [/\\(\\*/, \"comment\", \"@push\"],\n      // nested comment\n      [\"\\\\*\\\\)\", \"comment\", \"@pop\"],\n      [/[\\(*]/, \"comment\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\/.*$/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\(\\*/, \"comment\", \"@comment2\"]\n    ],\n    string_dq: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    string_sq: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/st/st.js\n"));

/***/ })

}]);