"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/artwork/[id]/edit/page.tsx":
/*!********************************************!*\
  !*** ./src/app/artwork/[id]/edit/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ArtworkEditorPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_artworkService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/artworkService */ \"(app-pages-browser)/./src/services/artworkService.ts\");\n/* harmony import */ var _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\");\n/* harmony import */ var _services_fileTreeEventService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/fileTreeEventService */ \"(app-pages-browser)/./src/services/fileTreeEventService.ts\");\n/* harmony import */ var _services_chatHistoryService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/chatHistoryService */ \"(app-pages-browser)/./src/services/chatHistoryService.ts\");\n/* harmony import */ var _components_FileTreePanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/FileTreePanel */ \"(app-pages-browser)/./src/components/FileTreePanel/index.tsx\");\n/* harmony import */ var _components_EditorPanel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/EditorPanel */ \"(app-pages-browser)/./src/components/EditorPanel/index.tsx\");\n/* harmony import */ var _components_AIAssistant__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/AIAssistant */ \"(app-pages-browser)/./src/components/AIAssistant/index.tsx\");\n/* harmony import */ var _components_ResizableLayout__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ResizableLayout */ \"(app-pages-browser)/./src/components/ResizableLayout/index.tsx\");\n/**\r\n * 作品编辑页面\r\n * 三栏布局的编辑器界面\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// 默认编辑器设置\nconst DEFAULT_EDITOR_SETTINGS = {\n    fontSize: 14,\n    fontWeight: 400,\n    fontFamily: 'Monaco, Consolas, \"Courier New\", monospace',\n    theme: \"light\",\n    wordWrap: true,\n    showLineNumbers: true,\n    enablePreview: true,\n    tabSize: 2,\n    insertSpaces: true,\n    autoSave: true,\n    autoSaveDelay: 1000\n};\n// 默认文件树结构\nconst DEFAULT_FILE_TREE = {\n    id: \"root\",\n    name: \"root\",\n    type: \"folder\",\n    parentId: null,\n    path: \"/\",\n    createdAt: Date.now(),\n    updatedAt: Date.now(),\n    isExpanded: true,\n    children: [\n        {\n            id: \"folder-roles\",\n            name: \"角色\",\n            type: \"folder\",\n            parentId: \"root\",\n            path: \"/角色\",\n            createdAt: Date.now(),\n            updatedAt: Date.now(),\n            isExpanded: false,\n            children: []\n        },\n        {\n            id: \"folder-outline\",\n            name: \"大纲\",\n            type: \"folder\",\n            parentId: \"root\",\n            path: \"/大纲\",\n            createdAt: Date.now(),\n            updatedAt: Date.now(),\n            isExpanded: false,\n            children: []\n        },\n        {\n            id: \"folder-knowledge\",\n            name: \"知识库\",\n            type: \"folder\",\n            parentId: \"root\",\n            path: \"/知识库\",\n            createdAt: Date.now(),\n            updatedAt: Date.now(),\n            isExpanded: false,\n            children: []\n        }\n    ]\n};\nfunction ArtworkEditorPage() {\n    var _editorState_currentFile, _editorState_currentFile1, _editorState_currentFile2;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const artworkId = params === null || params === void 0 ? void 0 : params.id;\n    // 编辑器状态\n    const [editorState, setEditorState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentFile: null,\n        fileTree: DEFAULT_FILE_TREE,\n        aiHistory: [],\n        settings: DEFAULT_EDITOR_SETTINGS,\n        isLoading: true,\n        unsavedChanges: false,\n        error: null\n    });\n    // 文件树刷新状态已移除，现在使用事件系统自动刷新\n    // 作品数据\n    const [artwork, setArtwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 自动关联功能状态\n    const [autoAssociationEnabled, setAutoAssociationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const chatHistoryService = _services_chatHistoryService__WEBPACK_IMPORTED_MODULE_6__.ChatHistoryService.getInstance();\n    // 自动关联状态反馈\n    const [autoAssociationStatus, setAutoAssociationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"idle\",\n        message: \"\"\n    });\n    // 监听当前文件变化，实现自动关联\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (autoAssociationEnabled && editorState.currentFile) {\n            const fileId = editorState.currentFile.id;\n            const fileName = editorState.currentFile.name;\n            console.log(\"\\uD83D\\uDD04 自动关联当前编辑文件:\", fileId);\n            // 🔧 静默关联，不显示处理状态，避免不必要的UI更新\n            // 使用现有的聚焦文件方法实现自动关联\n            chatHistoryService.focusCurrentEditingFile(fileId).then(()=>{\n                console.log(\"✅ 已自动关联文件:\", fileName);\n            // 🔧 移除状态更新，避免触发重新渲染\n            }).catch((error)=>{\n                console.error(\"❌ 自动关联文件失败:\", error);\n            // 🔧 只在控制台记录错误，不更新UI状态\n            });\n        }\n    }, [\n        (_editorState_currentFile = editorState.currentFile) === null || _editorState_currentFile === void 0 ? void 0 : _editorState_currentFile.id,\n        autoAssociationEnabled,\n        chatHistoryService\n    ]) // 🔧 只依赖文件ID，避免不必要的重新执行\n    ;\n    // 处理自动关联开关变化\n    const handleAutoAssociationToggle = (enabled)=>{\n        setAutoAssociationEnabled(enabled);\n        console.log(\"\\uD83C\\uDF9B️ 自动关联功能\", enabled ? \"开启\" : \"关闭\");\n    };\n    // 加载保存的编辑器设置\n    const loadSavedSettings = async ()=>{\n        try {\n            const { DatabaseService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/services/database/DatabaseService */ \"(app-pages-browser)/./src/services/database/DatabaseService.ts\"));\n            const dbService = DatabaseService.getInstance();\n            // 尝试获取保存的设置\n            const result = await dbService.get(\"editor-settings\", artworkId);\n            if (result.success && result.data && result.data.settings) {\n                // 更新编辑器状态\n                setEditorState((prev)=>({\n                        ...prev,\n                        settings: result.data.settings\n                    }));\n                console.log(\"✅ 已加载保存的编辑器设置:\", result.data.settings);\n            } else {\n                console.log(\"\\uD83D\\uDCDD 未找到保存的设置，使用默认设置\");\n            }\n        } catch (error) {\n            console.error(\"❌ 加载编辑器设置失败:\", error);\n        // 使用默认设置\n        }\n    };\n    // 🔧 智能文件内容更新事件监听 - 只响应外部工具更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fileTreeEventService = _services_fileTreeEventService__WEBPACK_IMPORTED_MODULE_5__.FileTreeEventService.getInstance();\n        const handleFileContentUpdate = async (updatedFileId)=>{\n            console.log(\"\\uD83D\\uDD14 收到文件内容更新事件:\", updatedFileId);\n            // 如果更新的文件是当前打开的文件，重新加载文件内容\n            if (editorState.currentFile && editorState.currentFile.id === updatedFileId) {\n                console.log(\"\\uD83D\\uDD04 当前文件内容已被外部工具更新，重新加载编辑器内容\");\n                try {\n                    const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__.FileTreeService.getInstance();\n                    const result = await fileTreeService.getFile(updatedFileId);\n                    if (result.success && result.data) {\n                        const updatedEditorFile = {\n                            id: result.data.id,\n                            name: result.data.name,\n                            content: result.data.content,\n                            type: result.data.type,\n                            path: result.data.path,\n                            lastModified: result.data.updatedAt,\n                            isDirty: false,\n                            isReadOnly: false\n                        };\n                        setEditorState((prev)=>({\n                                ...prev,\n                                currentFile: updatedEditorFile,\n                                unsavedChanges: false\n                            }));\n                        console.log(\"✅ 编辑器内容已自动更新（外部工具）:\", result.data.name);\n                    }\n                } catch (error) {\n                    console.error(\"❌ 重新加载文件内容失败:\", error);\n                }\n            }\n        };\n        // 订阅文件内容更新事件\n        fileTreeEventService.subscribeFileContentUpdate(handleFileContentUpdate);\n        console.log(\"✅ 已订阅文件内容更新事件（仅外部工具）\");\n        return ()=>{\n            // 组件卸载时清理事件监听器\n            fileTreeEventService.unsubscribeFileContentUpdate(handleFileContentUpdate);\n            console.log(\"\\uD83E\\uDDF9 已清理文件内容更新事件监听器\");\n        };\n    }, [\n        (_editorState_currentFile1 = editorState.currentFile) === null || _editorState_currentFile1 === void 0 ? void 0 : _editorState_currentFile1.id\n    ]) // 依赖当前文件ID，确保监听器能正确判断\n    ;\n    // 加载作品数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadArtwork = async ()=>{\n            if (!artworkId) {\n                setEditorState((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: \"无效的作品ID\"\n                    }));\n                return;\n            }\n            try {\n                const artworkService = _services_artworkService__WEBPACK_IMPORTED_MODULE_3__.ArtworkService.getInstance();\n                const result = await artworkService.getArtwork(artworkId);\n                if (result.success && result.data) {\n                    setArtwork(result.data);\n                    setEditorState((prev)=>({\n                            ...prev,\n                            isLoading: false,\n                            error: null\n                        }));\n                    // 加载保存的设置\n                    await loadSavedSettings();\n                    // 恢复字体应用状态（字体持久化功能）\n                    try {\n                        console.log(\"\\uD83D\\uDD04 尝试恢复字体应用状态...\");\n                        const { FontPersistenceService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontPersistenceService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontPersistenceService */ \"(app-pages-browser)/./src/services/fontPersistenceService.ts\"));\n                        const persistenceService = FontPersistenceService.getInstance();\n                        // 延迟一点时间确保DOM完全加载\n                        await new Promise((resolve)=>setTimeout(resolve, 300));\n                        const restoreResult = await persistenceService.restoreFontApplication();\n                        if (restoreResult.success && restoreResult.data) {\n                            console.log(\"✅ 字体应用状态已恢复\");\n                            // 获取当前应用的字体配置\n                            const configResult = await persistenceService.getActiveConfig();\n                            if (configResult.success && configResult.data) {\n                                const fontFamily = configResult.data.fontFamily;\n                                console.log(\"\\uD83C\\uDFAF 当前应用的字体:\", fontFamily);\n                                // 立即更新CSS变量\n                                document.documentElement.style.setProperty(\"--font-family-applied\", \"'\".concat(fontFamily, \"', sans-serif\"));\n                                document.documentElement.style.setProperty(\"--font-family-handwritten\", \"'\".concat(fontFamily, \"', cursive, var(--font-family-primary)\"));\n                                // 强制应用到所有元素\n                                const applyFontToAllElements = ()=>{\n                                    // 应用到body\n                                    document.body.style.setProperty(\"font-family\", \"'\".concat(fontFamily, \"', sans-serif\"), \"important\");\n                                    // 白名单方式：只对指定的元素应用用户字体\n                                    const whitelistSelectors = [\n                                        // 页面标题和导航\n                                        \"header h1, header h2, header p\",\n                                        \"nav, nav *\",\n                                        // 文件树面板\n                                        \".file-tree, .file-tree *\",\n                                        // AI助手面板\n                                        \".ai-assistant, .ai-assistant *\",\n                                        // 按钮和交互元素（非Monaco Editor内）\n                                        \".btn:not(.monaco-editor .btn)\",\n                                        \"button:not(.monaco-editor button)\",\n                                        // 特定的字体应用类\n                                        \".font-applied, .font-handwritten, .font-primary\",\n                                        \".handdrawn-text\",\n                                        // 表单元素（非编辑器内）\n                                        \"input:not(.monaco-editor input)\",\n                                        \"textarea:not(.monaco-editor textarea)\",\n                                        \"label:not(.monaco-editor label)\",\n                                        \"select:not(.monaco-editor select)\",\n                                        // 编辑器工具栏和设置面板\n                                        \".editor-toolbar, .editor-toolbar *\",\n                                        \".editor-settings, .editor-settings *\",\n                                        // 文件名和状态信息\n                                        \".file-name, .file-status\",\n                                        // 侧边栏内容\n                                        \".sidebar, .sidebar *\",\n                                        \".panel-header, .panel-content\"\n                                    ];\n                                    whitelistSelectors.forEach((selector)=>{\n                                        try {\n                                            const elements = document.querySelectorAll(selector);\n                                            elements.forEach((element)=>{\n                                                element.style.setProperty(\"font-family\", \"'\".concat(fontFamily, \"', sans-serif\"), \"important\");\n                                            });\n                                        } catch (e) {\n                                            // 忽略选择器错误\n                                            console.warn(\"字体应用选择器错误:\", selector, e);\n                                        }\n                                    });\n                                    // 强制重排\n                                    document.body.offsetHeight;\n                                };\n                                // 立即应用\n                                applyFontToAllElements();\n                                // 延迟再次应用，确保所有动态加载的元素也能应用字体\n                                setTimeout(applyFontToAllElements, 500);\n                                setTimeout(applyFontToAllElements, 1000);\n                            }\n                        } else {\n                            console.log(\"\\uD83D\\uDCDD 没有需要恢复的字体配置\");\n                        }\n                    } catch (fontError) {\n                        console.warn(\"⚠️ 字体恢复失败，但不影响编辑器启动:\", fontError);\n                    }\n                    // 预加载所有字体，确保字体立即可用\n                    try {\n                        const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n                        const fontService = FontService.getInstance();\n                        await fontService.preloadAllFonts();\n                        console.log(\"✅ 所有字体已预加载\");\n                    } catch (error) {\n                        console.error(\"❌ 预加载字体失败:\", error);\n                    }\n                } else {\n                    setEditorState((prev)=>({\n                            ...prev,\n                            isLoading: false,\n                            error: result.error || \"作品不存在\"\n                        }));\n                }\n            } catch (error) {\n                console.error(\"加载作品失败:\", error);\n                setEditorState((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: \"加载作品失败\"\n                    }));\n            }\n        };\n        loadArtwork();\n    }, [\n        artworkId\n    ]);\n    // 返回主页\n    const handleGoBack = ()=>{\n        if (editorState.unsavedChanges) {\n            const confirmed = window.confirm(\"您有未保存的更改，确定要离开吗？\");\n            if (!confirmed) return;\n        }\n        router.push(\"/\");\n    };\n    // 处理文件内容变化\n    const handleContentChange = async (content)=>{\n        var _editorState_currentFile;\n        if (!editorState.currentFile) return;\n        // 🔧 获取当前文件ID作为基准，防止文件切换时的竞态条件\n        const currentFileId = editorState.currentFile.id;\n        // 更新编辑器状态\n        const updatedFile = {\n            ...editorState.currentFile,\n            content,\n            isDirty: true,\n            lastModified: Date.now()\n        };\n        setEditorState((prev)=>({\n                ...prev,\n                currentFile: updatedFile,\n                unsavedChanges: true\n            }));\n        // 🔧 短暂延迟确保状态同步，然后验证文件ID一致性\n        await new Promise((resolve)=>setTimeout(resolve, 10));\n        // 🔧 验证文件ID一致性，防止文件切换过程中的错误保存\n        if (((_editorState_currentFile = editorState.currentFile) === null || _editorState_currentFile === void 0 ? void 0 : _editorState_currentFile.id) !== currentFileId) {\n            console.log(\"\\uD83D\\uDD12 文件已切换，跳过自动保存，避免内容错乱\");\n            return;\n        }\n        // 自动保存到数据库\n        try {\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__.FileTreeService.getInstance();\n            // 🔧 用户编辑时使用 'user' 来源，不会触发文件内容更新事件\n            await fileTreeService.updateFileContent(updatedFile.id, content, \"user\");\n            // 保存成功后更新状态\n            setEditorState((prev)=>({\n                    ...prev,\n                    currentFile: {\n                        ...updatedFile,\n                        isDirty: false\n                    },\n                    unsavedChanges: false\n                }));\n            console.log(\"✅ 文件自动保存成功\");\n        } catch (error) {\n            console.error(\"❌ 文件保存失败:\", error);\n        }\n    };\n    // 处理编辑器设置变化并持久化\n    const handleSettingsChange = async (newSettings)=>{\n        try {\n            // 更新状态\n            setEditorState((prev)=>({\n                    ...prev,\n                    settings: newSettings\n                }));\n            // 持久化设置到IndexedDB\n            const { DatabaseService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/services/database/DatabaseService */ \"(app-pages-browser)/./src/services/database/DatabaseService.ts\"));\n            const dbService = DatabaseService.getInstance();\n            // 保存设置，使用artworkId作为键\n            await dbService.put(\"editor-settings\", {\n                id: artworkId,\n                settings: newSettings,\n                updatedAt: Date.now()\n            });\n            console.log(\"✅ 编辑器设置已保存\");\n        } catch (error) {\n            console.error(\"❌ 保存编辑器设置失败:\", error);\n        }\n    };\n    // 处理文件选择（从文件树）\n    const handleFileSelect = async (file)=>{\n        if (file.type !== \"file\") {\n            console.log(\"选择的不是文件，跳过:\", file.name);\n            return;\n        }\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始加载文件:\", file.name);\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__.FileTreeService.getInstance();\n            const fileResult = await fileTreeService.getFile(file.id);\n            if (fileResult.success && fileResult.data) {\n                const storedFile = fileResult.data;\n                // 将StoredFile转换为EditorFile\n                const editorFile = {\n                    id: storedFile.id,\n                    name: storedFile.name,\n                    content: storedFile.content || \"\",\n                    type: storedFile.type,\n                    path: storedFile.path,\n                    lastModified: storedFile.updatedAt,\n                    isDirty: false,\n                    isReadOnly: false\n                };\n                // 更新编辑器状态\n                setEditorState((prev)=>({\n                        ...prev,\n                        currentFile: editorFile,\n                        unsavedChanges: false,\n                        error: null\n                    }));\n                console.log(\"✅ 文件选择成功:\", editorFile.name);\n            } else {\n                console.error(\"❌ 文件加载失败:\", fileResult.error);\n                setEditorState((prev)=>({\n                        ...prev,\n                        error: fileResult.error || \"文件加载失败\"\n                    }));\n            }\n        } catch (error) {\n            console.error(\"❌ 文件选择异常:\", error);\n            setEditorState((prev)=>({\n                    ...prev,\n                    error: \"文件选择时发生错误\"\n                }));\n        }\n    };\n    // 根据文件ID查找文件节点的辅助函数\n    const findFileById = (rootNode, targetId)=>{\n        if (rootNode.id === targetId) {\n            return rootNode;\n        }\n        if (rootNode.children) {\n            for (const child of rootNode.children){\n                const found = findFileById(child, targetId);\n                if (found) {\n                    return found;\n                }\n            }\n        }\n        return null;\n    };\n    // 根据目录名查找目录节点的辅助函数\n    const findDirectoryByName = (rootNode, dirName, parentId)=>{\n        // 递归搜索函数\n        const searchNode = (node)=>{\n            // 检查是否是目标目录\n            if (node.type === \"folder\" && node.name === dirName && node.parentId === parentId) {\n                return node;\n            }\n            // 递归检查子节点\n            if (node.children && Array.isArray(node.children)) {\n                for (const child of node.children){\n                    const found = searchNode(child);\n                    if (found) return found;\n                }\n            }\n            return null;\n        };\n        return searchNode(rootNode);\n    };\n    // 根据文件路径查找文件节点的辅助函数\n    const findFileByPath = (rootNode, targetPath)=>{\n        // 增强的路径标准化函数\n        const normalizePath = (path)=>{\n            if (!path) return \"\";\n            // 移除开头的斜杠，统一分隔符，但保持大小写（中文路径敏感）\n            return path.replace(/^\\/+/, \"\").replace(/\\\\/g, \"/\").replace(/\\/+/g, \"/\");\n        };\n        const normalizedTarget = normalizePath(targetPath);\n        // 递归搜索函数\n        const searchNode = (node)=>{\n            const normalizedNodePath = normalizePath(node.path || \"\");\n            const normalizedNodeName = normalizePath(node.name || \"\");\n            console.log(\"\\uD83D\\uDD0D 编辑器页面查找文件:\", {\n                targetPath,\n                normalizedTarget,\n                nodeName: node.name,\n                nodePath: node.path,\n                normalizedNodePath,\n                normalizedNodeName,\n                nodeType: node.type\n            });\n            // 🔧 排除根目录：根目录不应该被匹配为文件\n            if (node.name === \"root\" || node.path === \"/\" || normalizedNodePath === \"\") {\n                console.log(\"⏭️ 跳过根目录，继续搜索子节点\");\n                // 直接搜索子节点，不匹配根目录本身\n                if (node.children && Array.isArray(node.children)) {\n                    for (const child of node.children){\n                        const found = searchNode(child);\n                        if (found) return found;\n                    }\n                }\n                return null;\n            }\n            // 🔧 只匹配文件类型的节点，排除文件夹\n            const isFile = node.type === \"file\" || !node.children || node.children.length === 0;\n            // 1. 精确路径匹配（仅限文件）\n            if (isFile && normalizedNodePath === normalizedTarget) {\n                console.log(\"✅ 编辑器页面精确路径匹配（文件）:\", normalizedNodePath);\n                return node;\n            }\n            // 2. 文件名匹配（仅限文件）\n            if (isFile && normalizedNodeName === normalizedTarget) {\n                console.log(\"✅ 编辑器页面文件名匹配（文件）:\", normalizedNodeName);\n                return node;\n            }\n            // 3. 路径末尾匹配（仅限文件）\n            const targetParts = normalizedTarget.split(\"/\").filter((p)=>p);\n            const nodeParts = normalizedNodePath.split(\"/\").filter((p)=>p);\n            // 检查目标路径是否是节点路径的后缀\n            if (isFile && targetParts.length <= nodeParts.length) {\n                const nodePathSuffix = nodeParts.slice(-targetParts.length).join(\"/\");\n                if (nodePathSuffix === normalizedTarget) {\n                    console.log(\"✅ 编辑器页面路径后缀匹配（文件）:\", {\n                        nodePathSuffix,\n                        normalizedTarget\n                    });\n                    return node;\n                }\n            }\n            // 4. 文件名部分匹配（仅限文件）\n            const targetFileName = targetParts[targetParts.length - 1];\n            if (isFile && targetFileName && normalizedNodeName === targetFileName) {\n                console.log(\"✅ 编辑器页面文件名部分匹配（文件）:\", {\n                    targetFileName,\n                    normalizedNodeName\n                });\n                return node;\n            }\n            // 5. 模糊匹配（仅限文件）\n            if (isFile && (normalizedNodePath.includes(normalizedTarget) || normalizedTarget.includes(normalizedNodePath))) {\n                console.log(\"✅ 编辑器页面模糊路径匹配（文件）:\", {\n                    normalizedNodePath,\n                    normalizedTarget\n                });\n                return node;\n            }\n            // 递归检查子节点\n            if (node.children && Array.isArray(node.children)) {\n                for (const child of node.children){\n                    const found = searchNode(child);\n                    if (found) return found;\n                }\n            }\n            return null;\n        };\n        return searchNode(rootNode);\n    };\n    // 处理文件选择（通过文件ID，用于DF工具跳转）\n    const handleFileSelectById = async (fileId)=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D 根据ID查找文件:\", fileId);\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__.FileTreeService.getInstance();\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (fileTreeResult.success && fileTreeResult.data) {\n                const file = findFileById(fileTreeResult.data, fileId);\n                if (file) {\n                    await handleFileSelect(file);\n                    console.log(\"✅ 通过ID选择文件成功:\", file.name);\n                } else {\n                    console.warn(\"⚠️ 未找到指定ID的文件:\", fileId);\n                    setEditorState((prev)=>({\n                            ...prev,\n                            error: \"未找到指定的文件\"\n                        }));\n                }\n            } else {\n                console.error(\"❌ 获取文件树失败:\", fileTreeResult.error);\n                setEditorState((prev)=>({\n                        ...prev,\n                        error: \"获取文件树失败\"\n                    }));\n            }\n        } catch (error) {\n            console.error(\"❌ 通过ID选择文件异常:\", error);\n            setEditorState((prev)=>({\n                    ...prev,\n                    error: \"文件选择时发生错误\"\n                }));\n        }\n    };\n    // 处理AI内容插入到编辑器\n    const handleContentInsert = async (content, options)=>{\n        if (!editorState.currentFile) {\n            console.warn(\"没有打开的文件，无法插入内容\");\n            return;\n        }\n        try {\n            const currentContent = editorState.currentFile.content;\n            let newContent = \"\";\n            // 根据插入选项处理内容\n            switch(options.position){\n                case \"start\":\n                    newContent = content + (options.addNewlines ? \"\\n\" : \"\") + currentContent;\n                    break;\n                case \"end\":\n                    newContent = currentContent + (options.addNewlines ? \"\\n\" : \"\") + content;\n                    break;\n                case \"replace\":\n                    newContent = content;\n                    break;\n                case \"cursor\":\n                default:\n                    // 对于光标位置，我们暂时插入到文件末尾\n                    // 在实际实现中，这里需要与Monaco编辑器集成获取光标位置\n                    newContent = currentContent + (options.addNewlines ? \"\\n\" : \"\") + content;\n                    break;\n            }\n            // 更新文件内容\n            await handleContentChange(newContent);\n            console.log(\"✅ AI内容已插入到编辑器\");\n        } catch (error) {\n            console.error(\"❌ 插入内容失败:\", error);\n            setEditorState((prev)=>({\n                    ...prev,\n                    error: \"插入内容失败\"\n                }));\n        }\n    };\n    // EditorPanel的ref\n    const editorPanelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = async (diffRequest)=>{\n        console.log(\"\\uD83D\\uDD17 编辑页面收到详细对比请求:\", diffRequest);\n        try {\n            // 1. 首先确保目标文件已打开\n            if (!editorState.currentFile || editorState.currentFile.path !== diffRequest.filePath) {\n                console.log(\"\\uD83D\\uDD04 切换到目标文件:\", diffRequest.filePath);\n                // 根据文件路径查找并打开文件\n                const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__.FileTreeService.getInstance();\n                const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n                if (fileTreeResult.success && fileTreeResult.data) {\n                    const targetFile = findFileByPath(fileTreeResult.data, diffRequest.filePath);\n                    if (targetFile) {\n                        await handleFileSelect(targetFile);\n                        // 等待文件加载完成\n                        await new Promise((resolve)=>setTimeout(resolve, 100));\n                    } else {\n                        console.warn(\"⚠️ 未找到目标文件:\", diffRequest.filePath);\n                        // 🔧 详细对比模式不应该创建文件，只显示错误信息\n                        if (diffRequest.previewMode) {\n                            console.warn(\"⚠️ 详细对比模式：目标文件不存在，无法显示diff\");\n                            return;\n                        }\n                        // 只有在非预览模式下才尝试创建文件\n                        if (diffRequest.operation === \"replace\" || diffRequest.operation === \"append\") {\n                            console.log(\"\\uD83D\\uDD04 尝试创建文件:\", diffRequest.filePath);\n                            // 解析文件路径，获取目录和文件名\n                            const pathParts = diffRequest.filePath.split(\"/\");\n                            const fileName = pathParts.pop() || \"\";\n                            const dirPath = pathParts.join(\"/\");\n                            try {\n                                // 首先确保目录存在\n                                let parentId = \"\".concat(artworkId, \"-root\") // 默认根目录\n                                ;\n                                // 如果有目录路径，需要创建或查找目录\n                                if (dirPath) {\n                                    const dirParts = dirPath.split(\"/\").filter((p)=>p);\n                                    let currentParentId = \"\".concat(artworkId, \"-root\");\n                                    // 逐级创建或查找目录\n                                    for (const dirName of dirParts){\n                                        console.log(\"\\uD83D\\uDD0D 查找或创建目录:\", dirName);\n                                        // 重新获取文件树，查找是否已存在该目录\n                                        const updatedTreeResult = await fileTreeService.getFileTree(artworkId);\n                                        if (updatedTreeResult.success && updatedTreeResult.data) {\n                                            const existingDir = findDirectoryByName(updatedTreeResult.data, dirName, currentParentId);\n                                            if (existingDir) {\n                                                console.log(\"✅ 找到已存在的目录:\", dirName);\n                                                currentParentId = existingDir.id;\n                                            } else {\n                                                console.log(\"\\uD83D\\uDD04 创建新目录:\", dirName);\n                                                const createDirResult = await fileTreeService.createFolder(artworkId, currentParentId, dirName);\n                                                if (createDirResult.success && createDirResult.data) {\n                                                    currentParentId = createDirResult.data.id;\n                                                    console.log(\"✅ 目录创建成功:\", dirName);\n                                                } else {\n                                                    throw new Error(\"创建目录失败: \".concat(createDirResult.error));\n                                                }\n                                            }\n                                        }\n                                    }\n                                    parentId = currentParentId;\n                                }\n                                // 创建文件\n                                console.log(\"\\uD83D\\uDD04 创建文件:\", {\n                                    fileName,\n                                    parentId\n                                });\n                                const createResult = await fileTreeService.createFile(artworkId, parentId, fileName, \"text\", \"\");\n                                if (createResult.success && createResult.data) {\n                                    console.log(\"✅ 文件创建成功:\", createResult.data);\n                                    // 重新加载文件树并选择新创建的文件\n                                    const updatedTreeResult = await fileTreeService.getFileTree(artworkId);\n                                    if (updatedTreeResult.success && updatedTreeResult.data) {\n                                        const newFile = findFileByPath(updatedTreeResult.data, diffRequest.filePath);\n                                        if (newFile) {\n                                            await handleFileSelect(newFile);\n                                            await new Promise((resolve)=>setTimeout(resolve, 300));\n                                        }\n                                    }\n                                } else {\n                                    console.error(\"❌ 文件创建失败:\", createResult.error);\n                                    return;\n                                }\n                            } catch (createError) {\n                                console.error(\"❌ 文件创建异常:\", createError);\n                                return;\n                            }\n                        } else {\n                            return;\n                        }\n                    }\n                }\n            }\n            // 2. 调用EditorPanel的diff方法\n            if (editorPanelRef.current && editorPanelRef.current.handleOpenDetailedDiff) {\n                console.log(\"✅ 调用EditorPanel的diff方法\");\n                editorPanelRef.current.handleOpenDetailedDiff(diffRequest);\n            } else {\n                console.warn(\"⚠️ EditorPanel的diff方法不可用\");\n            }\n        } catch (error) {\n            console.error(\"❌ 打开详细差异对比失败:\", error);\n        }\n    };\n    // 处理创建文件/文件夹\n    const handleCreateFile = async (type)=>{\n        try {\n            var _existingFiles_data;\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__.FileTreeService.getInstance();\n            // 在根目录下创建文件/文件夹\n            const rootFolderId = \"\".concat(artworkId, \"-root\");\n            // 生成唯一名称，避免重名冲突\n            const baseName = type === \"file\" ? \"新文件\" : \"新文件夹\";\n            const extension = type === \"file\" ? \".md\" : \"\";\n            let finalName = \"\".concat(baseName).concat(extension);\n            let counter = 1;\n            // 检查是否存在同名文件，如果存在则添加数字后缀\n            const existingFiles = await fileTreeService.getFileTree(artworkId);\n            if (existingFiles.success && ((_existingFiles_data = existingFiles.data) === null || _existingFiles_data === void 0 ? void 0 : _existingFiles_data.children)) {\n                const existingNames = new Set(existingFiles.data.children.map((child)=>child.name));\n                while(existingNames.has(finalName)){\n                    finalName = \"\".concat(baseName).concat(counter).concat(extension);\n                    counter++;\n                }\n            }\n            if (type === \"file\") {\n                await fileTreeService.createFile(artworkId, rootFolderId, finalName, \"text\");\n            } else {\n                await fileTreeService.createFolder(artworkId, rootFolderId, finalName);\n            }\n            console.log(\"✅ 成功在根目录创建\".concat(type, \":\"), finalName);\n        // 文件树会通过事件自动刷新，不再需要手动触发\n        } catch (error) {\n            console.error(\"❌ 创建\".concat(type, \"失败:\"), error);\n            setEditorState((prev)=>({\n                    ...prev,\n                    error: \"创建\".concat(type, \"失败: \").concat(error instanceof Error ? error.message : String(error))\n                }));\n        }\n    };\n    // 加载状态\n    if (editorState.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4 text-amber-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 901,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg font-handwritten\",\n                        children: \"加载编辑器中...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 902,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 900,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n            lineNumber: 899,\n            columnNumber: 7\n        }, this);\n    }\n    // 错误状态\n    if (editorState.error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 mx-auto mb-6 rounded-lg bg-red-500/20 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"32\",\n                            height: \"32\",\n                            viewBox: \"0 0 24 24\",\n                            fill: \"none\",\n                            className: \"text-red-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"12\",\n                                    cy: \"12\",\n                                    r: \"10\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 915,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                    x1: \"15\",\n                                    y1: \"9\",\n                                    x2: \"9\",\n                                    y2: \"15\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 916,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                    x1: \"9\",\n                                    y1: \"9\",\n                                    x2: \"15\",\n                                    y2: \"15\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 917,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 914,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 913,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-400 text-xl mb-6 font-handwritten\",\n                        children: editorState.error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 920,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleGoBack,\n                        className: \"px-6 py-3 bg-amber-500/20 hover:bg-amber-500/30 text-amber-400 hover:text-amber-300 border border-amber-500/50 rounded-lg transition-all duration-200 font-handwritten\",\n                        children: \"返回主页\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 923,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 912,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n            lineNumber: 911,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gradient-to-r from-gray-900 to-gray-800 backdrop-blur-sm border-b border-amber-500/30 px-6 py-4 shadow-lg shadow-black/20 flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGoBack,\n                                    className: \"p-2 rounded-lg hover:bg-amber-500/20 transition-colors text-amber-400 hover:text-amber-300\",\n                                    title: \"返回主页\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"20\",\n                                        height: \"20\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 946,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 945,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-semibold text-amber-100 font-handwritten\",\n                                            children: (artwork === null || artwork === void 0 ? void 0 : artwork.title) || \"编辑作品\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 950,\n                                            columnNumber: 15\n                                        }, this),\n                                        (artwork === null || artwork === void 0 ? void 0 : artwork.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-300 mt-1\",\n                                            children: artwork.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 954,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 949,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 939,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                editorState.unsavedChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-amber-600 font-medium\",\n                                    children: \"未保存的更改\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 963,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-400 rounded-full\",\n                                    title: \"已连接\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 967,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 961,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 938,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 937,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"h-[calc(100vh-80px)] overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResizableLayout__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    leftPanel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b border-amber-500/30 flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-sm font-semibold text-amber-100 font-handwritten\",\n                                            children: \"文件管理\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 979,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"p-1.5 rounded-md bg-amber-500/20 hover:bg-amber-500/30 transition-colors group\",\n                                                    title: \"新建文件\",\n                                                    onClick: ()=>handleCreateFile(\"file\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"14\",\n                                                        height: \"14\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        className: \"text-amber-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 989,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                points: \"14,2 14,8 20,8\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 990,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"12\",\n                                                                y1: \"18\",\n                                                                x2: \"12\",\n                                                                y2: \"12\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 991,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"9\",\n                                                                y1: \"15\",\n                                                                x2: \"15\",\n                                                                y2: \"15\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 992,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 983,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"p-1.5 rounded-md bg-amber-500/20 hover:bg-amber-500/30 transition-colors group\",\n                                                    title: \"新建文件夹\",\n                                                    onClick: ()=>handleCreateFile(\"folder\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"14\",\n                                                        height: \"14\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        className: \"text-amber-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 1001,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"12\",\n                                                                y1: \"14\",\n                                                                x2: \"12\",\n                                                                y2: \"10\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 1002,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                                x1: \"10\",\n                                                                y1: \"12\",\n                                                                x2: \"14\",\n                                                                y2: \"12\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 1003,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 1000,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 995,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 982,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 978,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 977,\n                                columnNumber: 15\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileTreePanel__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    artworkId: artworkId,\n                                    currentFileId: (_editorState_currentFile2 = editorState.currentFile) === null || _editorState_currentFile2 === void 0 ? void 0 : _editorState_currentFile2.id,\n                                    onFileSelect: handleFileSelect,\n                                    onFileCreate: (parentId, name, type)=>{\n                                        console.log(\"创建文件:\", {\n                                            parentId,\n                                            name,\n                                            type\n                                        });\n                                    },\n                                    onFileDelete: (fileId)=>{\n                                        console.log(\"删除文件:\", fileId);\n                                    },\n                                    onFileRename: (fileId, newName)=>{\n                                        console.log(\"重命名文件:\", {\n                                            fileId,\n                                            newName\n                                        });\n                                    },\n                                    className: \"h-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 1010,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 1009,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 976,\n                        columnNumber: 13\n                    }, void 0),\n                    centerPanel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EditorPanel__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        ref: editorPanelRef,\n                        file: editorState.currentFile,\n                        onContentChange: handleContentChange,\n                        onSettingsChange: handleSettingsChange,\n                        onFileRename: async (fileId, newName)=>{\n                            try {\n                                const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_4__.FileTreeService.getInstance();\n                                await fileTreeService.renameFile(fileId, newName);\n                                // 更新当前文件状态\n                                if (editorState.currentFile && editorState.currentFile.id === fileId) {\n                                    setEditorState((prev)=>({\n                                            ...prev,\n                                            currentFile: {\n                                                ...prev.currentFile,\n                                                name: newName\n                                            }\n                                        }));\n                                }\n                                // 文件树会通过事件自动刷新，不再需要手动触发\n                                console.log(\"✅ 文件重命名成功:\", newName);\n                            } catch (error) {\n                                console.error(\"❌ 文件重命名失败:\", error);\n                                setEditorState((prev)=>({\n                                        ...prev,\n                                        error: \"文件重命名失败\"\n                                    }));\n                            }\n                        },\n                        settings: editorState.settings,\n                        onAutoAssociationToggle: handleAutoAssociationToggle,\n                        autoAssociationEnabled: autoAssociationEnabled,\n                        artworkId: artworkId,\n                        onOpenDetailedDiff: handleOpenDetailedDiff,\n                        className: \"h-full\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 1029,\n                        columnNumber: 13\n                    }, void 0),\n                    rightPanel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AIAssistant__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        onContentInsert: handleContentInsert,\n                        artworkId: artworkId,\n                        onFileSelect: handleFileSelectById,\n                        onOpenDetailedDiff: handleOpenDetailedDiff,\n                        className: \"h-full\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 1070,\n                        columnNumber: 13\n                    }, void 0),\n                    initialLeftWidth: 20,\n                    initialRightWidth: 30,\n                    minLeftWidth: 15,\n                    minRightWidth: 20,\n                    minCenterWidth: 30,\n                    onLayoutChange: (leftWidth, rightWidth, leftCollapsed, rightCollapsed)=>{\n                        var _editorPanelRef_current;\n                        console.log(\"布局变化:\", {\n                            leftWidth,\n                            rightWidth,\n                            leftCollapsed,\n                            rightCollapsed\n                        });\n                        // 通知 EditorPanel 处理布局变化\n                        if ((_editorPanelRef_current = editorPanelRef.current) === null || _editorPanelRef_current === void 0 ? void 0 : _editorPanelRef_current.handleLayoutChange) {\n                            editorPanelRef.current.handleLayoutChange();\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 974,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 973,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\artwork\\\\[id]\\\\edit\\\\page.tsx\",\n        lineNumber: 935,\n        columnNumber: 5\n    }, this);\n}\n_s(ArtworkEditorPage, \"pMbrCOGRuoRcZjaeYkmxq1lVqjI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ArtworkEditorPage;\nvar _c;\n$RefreshReg$(_c, \"ArtworkEditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/artwork/[id]/edit/page.tsx\n"));

/***/ })

});