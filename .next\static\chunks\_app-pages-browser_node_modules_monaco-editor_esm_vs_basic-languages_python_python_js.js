"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_python_python_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/python/python.js":
/*!****************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/python/python.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/editor.api.js?ce9f\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/basic-languages/python/python.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"'''\", \"'''\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(\n        \"^\\\\s*(?:def|class|for|if|elif|else|while|try|with|finally|except|async|match|case).*?:\\\\s*$\"\n      ),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ],\n  folding: {\n    offSide: true,\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".python\",\n  keywords: [\n    // This section is the result of running\n    // `import keyword; for k in sorted(keyword.kwlist + keyword.softkwlist): print(\"  '\" + k + \"',\")`\n    // in a Python REPL,\n    // though note that the output from Python 3 is not a strict superset of the\n    // output from Python 2.\n    \"False\",\n    // promoted to keyword.kwlist in Python 3\n    \"None\",\n    // promoted to keyword.kwlist in Python 3\n    \"True\",\n    // promoted to keyword.kwlist in Python 3\n    \"_\",\n    // new in Python 3.10\n    \"and\",\n    \"as\",\n    \"assert\",\n    \"async\",\n    // new in Python 3\n    \"await\",\n    // new in Python 3\n    \"break\",\n    \"case\",\n    // new in Python 3.10\n    \"class\",\n    \"continue\",\n    \"def\",\n    \"del\",\n    \"elif\",\n    \"else\",\n    \"except\",\n    \"exec\",\n    // Python 2, but not 3.\n    \"finally\",\n    \"for\",\n    \"from\",\n    \"global\",\n    \"if\",\n    \"import\",\n    \"in\",\n    \"is\",\n    \"lambda\",\n    \"match\",\n    // new in Python 3.10\n    \"nonlocal\",\n    // new in Python 3\n    \"not\",\n    \"or\",\n    \"pass\",\n    \"print\",\n    // Python 2, but not 3.\n    \"raise\",\n    \"return\",\n    \"try\",\n    \"type\",\n    // new in Python 3.12\n    \"while\",\n    \"with\",\n    \"yield\",\n    \"int\",\n    \"float\",\n    \"long\",\n    \"complex\",\n    \"hex\",\n    \"abs\",\n    \"all\",\n    \"any\",\n    \"apply\",\n    \"basestring\",\n    \"bin\",\n    \"bool\",\n    \"buffer\",\n    \"bytearray\",\n    \"callable\",\n    \"chr\",\n    \"classmethod\",\n    \"cmp\",\n    \"coerce\",\n    \"compile\",\n    \"complex\",\n    \"delattr\",\n    \"dict\",\n    \"dir\",\n    \"divmod\",\n    \"enumerate\",\n    \"eval\",\n    \"execfile\",\n    \"file\",\n    \"filter\",\n    \"format\",\n    \"frozenset\",\n    \"getattr\",\n    \"globals\",\n    \"hasattr\",\n    \"hash\",\n    \"help\",\n    \"id\",\n    \"input\",\n    \"intern\",\n    \"isinstance\",\n    \"issubclass\",\n    \"iter\",\n    \"len\",\n    \"locals\",\n    \"list\",\n    \"map\",\n    \"max\",\n    \"memoryview\",\n    \"min\",\n    \"next\",\n    \"object\",\n    \"oct\",\n    \"open\",\n    \"ord\",\n    \"pow\",\n    \"print\",\n    \"property\",\n    \"reversed\",\n    \"range\",\n    \"raw_input\",\n    \"reduce\",\n    \"reload\",\n    \"repr\",\n    \"reversed\",\n    \"round\",\n    \"self\",\n    \"set\",\n    \"setattr\",\n    \"slice\",\n    \"sorted\",\n    \"staticmethod\",\n    \"str\",\n    \"sum\",\n    \"super\",\n    \"tuple\",\n    \"type\",\n    \"unichr\",\n    \"unicode\",\n    \"vars\",\n    \"xrange\",\n    \"zip\",\n    \"__dict__\",\n    \"__methods__\",\n    \"__members__\",\n    \"__class__\",\n    \"__bases__\",\n    \"__name__\",\n    \"__mro__\",\n    \"__subclasses__\",\n    \"__init__\",\n    \"__import__\"\n  ],\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      [/[,:;]/, \"delimiter\"],\n      [/[{}\\[\\]()]/, \"@brackets\"],\n      [/@[a-zA-Z_]\\w*/, \"tag\"],\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    // Deal with white space, including single and multi-line comments\n    whitespace: [\n      [/\\s+/, \"white\"],\n      [/(^#.*$)/, \"comment\"],\n      [/'''/, \"string\", \"@endDocString\"],\n      [/\"\"\"/, \"string\", \"@endDblDocString\"]\n    ],\n    endDocString: [\n      [/[^']+/, \"string\"],\n      [/\\\\'/, \"string\"],\n      [/'''/, \"string\", \"@popall\"],\n      [/'/, \"string\"]\n    ],\n    endDblDocString: [\n      [/[^\"]+/, \"string\"],\n      [/\\\\\"/, \"string\"],\n      [/\"\"\"/, \"string\", \"@popall\"],\n      [/\"/, \"string\"]\n    ],\n    // Recognize hex, negatives, decimals, imaginaries, longs, and scientific notation\n    numbers: [\n      [/-?0x([abcdef]|[ABCDEF]|\\d)+[lL]?/, \"number.hex\"],\n      [/-?(\\d*\\.)?\\d+([eE][+\\-]?\\d+)?[jJ]?[lL]?/, \"number\"]\n    ],\n    // Recognize strings, including those broken across lines with \\ (but not without)\n    strings: [\n      [/'$/, \"string.escape\", \"@popall\"],\n      [/f'{1,3}/, \"string.escape\", \"@fStringBody\"],\n      [/'/, \"string.escape\", \"@stringBody\"],\n      [/\"$/, \"string.escape\", \"@popall\"],\n      [/f\"{1,3}/, \"string.escape\", \"@fDblStringBody\"],\n      [/\"/, \"string.escape\", \"@dblStringBody\"]\n    ],\n    fStringBody: [\n      [/[^\\\\'\\{\\}]+$/, \"string\", \"@popall\"],\n      [/[^\\\\'\\{\\}]+/, \"string\"],\n      [/\\{[^\\}':!=]+/, \"identifier\", \"@fStringDetail\"],\n      [/\\\\./, \"string\"],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    stringBody: [\n      [/[^\\\\']+$/, \"string\", \"@popall\"],\n      [/[^\\\\']+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    fDblStringBody: [\n      [/[^\\\\\"\\{\\}]+$/, \"string\", \"@popall\"],\n      [/[^\\\\\"\\{\\}]+/, \"string\"],\n      [/\\{[^\\}':!=]+/, \"identifier\", \"@fStringDetail\"],\n      [/\\\\./, \"string\"],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    dblStringBody: [\n      [/[^\\\\\"]+$/, \"string\", \"@popall\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    fStringDetail: [\n      [/[:][^}]+/, \"string\"],\n      [/[!][ars]/, \"string\"],\n      // only !a, !r, !s are supported by f-strings: https://docs.python.org/3/tutorial/inputoutput.html#formatted-string-literals\n      [/=/, \"string\"],\n      [/\\}/, \"identifier\", \"@pop\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/python/python.js\n"));

/***/ })

}]);