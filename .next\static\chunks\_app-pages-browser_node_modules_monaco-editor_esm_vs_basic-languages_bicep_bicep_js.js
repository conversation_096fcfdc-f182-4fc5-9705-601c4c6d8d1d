"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_bicep_bicep_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/bicep/bicep.js":
/*!**************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/bicep/bicep.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/bicep/bicep.ts\nvar bounded = (text) => `\\\\b${text}\\\\b`;\nvar identifierStart = \"[_a-zA-Z]\";\nvar identifierContinue = \"[_a-zA-Z0-9]\";\nvar identifier = bounded(`${identifierStart}${identifierContinue}*`);\nvar keywords = [\n  \"targetScope\",\n  \"resource\",\n  \"module\",\n  \"param\",\n  \"var\",\n  \"output\",\n  \"for\",\n  \"in\",\n  \"if\",\n  \"existing\"\n];\nvar namedLiterals = [\"true\", \"false\", \"null\"];\nvar nonCommentWs = `[ \\\\t\\\\r\\\\n]`;\nvar numericLiteral = `[0-9]+`;\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\" },\n    { open: \"'''\", close: \"'''\" }\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: \"'''\", close: \"'''\", notIn: [\"string\", \"comment\"] }\n  ],\n  autoCloseBefore: \":.,=}])' \\n\t\",\n  indentationRules: {\n    increaseIndentPattern: new RegExp(\"^((?!\\\\/\\\\/).)*(\\\\{[^}\\\"'`]*|\\\\([^)\\\"'`]*|\\\\[[^\\\\]\\\"'`]*)$\"),\n    decreaseIndentPattern: new RegExp(\"^((?!.*?\\\\/\\\\*).*\\\\*/)?\\\\s*[\\\\}\\\\]].*$\")\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".bicep\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  symbols: /[=><!~?:&|+\\-*/^%]+/,\n  keywords,\n  namedLiterals,\n  escapes: `\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\|'|\\\\\\${)`,\n  tokenizer: {\n    root: [{ include: \"@expression\" }, { include: \"@whitespace\" }],\n    stringVerbatim: [\n      { regex: `(|'|'')[^']`, action: { token: \"string\" } },\n      { regex: `'''`, action: { token: \"string.quote\", next: \"@pop\" } }\n    ],\n    stringLiteral: [\n      { regex: `\\\\\\${`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `[^\\\\\\\\'$]+`, action: { token: \"string\" } },\n      { regex: \"@escapes\", action: { token: \"string.escape\" } },\n      { regex: `\\\\\\\\.`, action: { token: \"string.escape.invalid\" } },\n      { regex: `'`, action: { token: \"string\", next: \"@pop\" } }\n    ],\n    bracketCounting: [\n      { regex: `{`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `}`, action: { token: \"delimiter.bracket\", next: \"@pop\" } },\n      { include: \"expression\" }\n    ],\n    comment: [\n      { regex: `[^\\\\*]+`, action: { token: \"comment\" } },\n      { regex: `\\\\*\\\\/`, action: { token: \"comment\", next: \"@pop\" } },\n      { regex: `[\\\\/*]`, action: { token: \"comment\" } }\n    ],\n    whitespace: [\n      { regex: nonCommentWs },\n      { regex: `\\\\/\\\\*`, action: { token: \"comment\", next: \"@comment\" } },\n      { regex: `\\\\/\\\\/.*$`, action: { token: \"comment\" } }\n    ],\n    expression: [\n      { regex: `'''`, action: { token: \"string.quote\", next: \"@stringVerbatim\" } },\n      { regex: `'`, action: { token: \"string.quote\", next: \"@stringLiteral\" } },\n      { regex: numericLiteral, action: { token: \"number\" } },\n      {\n        regex: identifier,\n        action: {\n          cases: {\n            \"@keywords\": { token: \"keyword\" },\n            \"@namedLiterals\": { token: \"keyword\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      }\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/bicep/bicep.js\n"));

/***/ })

}]);