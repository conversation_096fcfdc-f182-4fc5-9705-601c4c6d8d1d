"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_r_r_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/r/r.js":
/*!******************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/r/r.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/r/r.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".r\",\n  roxygen: [\n    \"@alias\",\n    \"@aliases\",\n    \"@assignee\",\n    \"@author\",\n    \"@backref\",\n    \"@callGraph\",\n    \"@callGraphDepth\",\n    \"@callGraphPrimitives\",\n    \"@concept\",\n    \"@describeIn\",\n    \"@description\",\n    \"@details\",\n    \"@docType\",\n    \"@encoding\",\n    \"@evalNamespace\",\n    \"@evalRd\",\n    \"@example\",\n    \"@examples\",\n    \"@export\",\n    \"@exportClass\",\n    \"@exportMethod\",\n    \"@exportPattern\",\n    \"@family\",\n    \"@field\",\n    \"@formals\",\n    \"@format\",\n    \"@import\",\n    \"@importClassesFrom\",\n    \"@importFrom\",\n    \"@importMethodsFrom\",\n    \"@include\",\n    \"@inherit\",\n    \"@inheritDotParams\",\n    \"@inheritParams\",\n    \"@inheritSection\",\n    \"@keywords\",\n    \"@md\",\n    \"@method\",\n    \"@name\",\n    \"@noMd\",\n    \"@noRd\",\n    \"@note\",\n    \"@param\",\n    \"@rawNamespace\",\n    \"@rawRd\",\n    \"@rdname\",\n    \"@references\",\n    \"@return\",\n    \"@S3method\",\n    \"@section\",\n    \"@seealso\",\n    \"@setClass\",\n    \"@slot\",\n    \"@source\",\n    \"@template\",\n    \"@templateVar\",\n    \"@title\",\n    \"@TODO\",\n    \"@usage\",\n    \"@useDynLib\"\n  ],\n  constants: [\n    \"NULL\",\n    \"FALSE\",\n    \"TRUE\",\n    \"NA\",\n    \"Inf\",\n    \"NaN\",\n    \"NA_integer_\",\n    \"NA_real_\",\n    \"NA_complex_\",\n    \"NA_character_\",\n    \"T\",\n    \"F\",\n    \"LETTERS\",\n    \"letters\",\n    \"month.abb\",\n    \"month.name\",\n    \"pi\",\n    \"R.version.string\"\n  ],\n  keywords: [\n    \"break\",\n    \"next\",\n    \"return\",\n    \"if\",\n    \"else\",\n    \"for\",\n    \"in\",\n    \"repeat\",\n    \"while\",\n    \"array\",\n    \"category\",\n    \"character\",\n    \"complex\",\n    \"double\",\n    \"function\",\n    \"integer\",\n    \"list\",\n    \"logical\",\n    \"matrix\",\n    \"numeric\",\n    \"vector\",\n    \"data.frame\",\n    \"factor\",\n    \"library\",\n    \"require\",\n    \"attach\",\n    \"detach\",\n    \"source\"\n  ],\n  special: [\"\\\\n\", \"\\\\r\", \"\\\\t\", \"\\\\b\", \"\\\\a\", \"\\\\f\", \"\\\\v\", \"\\\\'\", '\\\\\"', \"\\\\\\\\\"],\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      [/[{}\\[\\]()]/, \"@brackets\"],\n      { include: \"@operators\" },\n      [/#'$/, \"comment.doc\"],\n      [/#'/, \"comment.doc\", \"@roxygen\"],\n      [/(^#.*$)/, \"comment\"],\n      [/\\s+/, \"white\"],\n      [/[,:;]/, \"delimiter\"],\n      [/@[a-zA-Z]\\w*/, \"tag\"],\n      [\n        /[a-zA-Z]\\w*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@constants\": \"constant\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    // Recognize Roxygen comments\n    roxygen: [\n      [\n        /@\\w+/,\n        {\n          cases: {\n            \"@roxygen\": \"tag\",\n            \"@eos\": { token: \"comment.doc\", next: \"@pop\" },\n            \"@default\": \"comment.doc\"\n          }\n        }\n      ],\n      [\n        /\\s+/,\n        {\n          cases: {\n            \"@eos\": { token: \"comment.doc\", next: \"@pop\" },\n            \"@default\": \"comment.doc\"\n          }\n        }\n      ],\n      [/.*/, { token: \"comment.doc\", next: \"@pop\" }]\n    ],\n    // Recognize positives, negatives, decimals, imaginaries, and scientific notation\n    numbers: [\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/-?(\\d*\\.)?\\d+([eE][+\\-]?\\d+)?/, \"number\"]\n    ],\n    // Recognize operators\n    operators: [\n      [/<{1,2}-/, \"operator\"],\n      [/->{1,2}/, \"operator\"],\n      [/%[^%\\s]+%/, \"operator\"],\n      [/\\*\\*/, \"operator\"],\n      [/%%/, \"operator\"],\n      [/&&/, \"operator\"],\n      [/\\|\\|/, \"operator\"],\n      [/<</, \"operator\"],\n      [/>>/, \"operator\"],\n      [/[-+=&|!<>^~*/:$]/, \"operator\"]\n    ],\n    // Recognize strings, including those broken across lines\n    strings: [\n      [/'/, \"string.escape\", \"@stringBody\"],\n      [/\"/, \"string.escape\", \"@dblStringBody\"]\n    ],\n    stringBody: [\n      [\n        /\\\\./,\n        {\n          cases: {\n            \"@special\": \"string\",\n            \"@default\": \"error-token\"\n          }\n        }\n      ],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/./, \"string\"]\n    ],\n    dblStringBody: [\n      [\n        /\\\\./,\n        {\n          cases: {\n            \"@special\": \"string\",\n            \"@default\": \"error-token\"\n          }\n        }\n      ],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/./, \"string\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/r/r.js\n"));

/***/ })

}]);