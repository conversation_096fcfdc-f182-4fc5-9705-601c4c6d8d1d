"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/services/diffToolService.ts":
/*!*****************************************!*\
  !*** ./src/services/diffToolService.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiffToolService: function() { return /* binding */ DiffToolService; }\n/* harmony export */ });\n/* harmony import */ var _fileTreeService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\");\n/**\r\n * 差异工具服务\r\n * 提供文本追加、正则替换和差异计算功能\r\n */ \nclass DiffToolService {\n    /**\r\n   * 获取差异工具服务单例\r\n   */ static getInstance() {\n        if (!DiffToolService.instance) {\n            DiffToolService.instance = new DiffToolService();\n        }\n        return DiffToolService.instance;\n    }\n    /**\r\n   * 文本追加功能\r\n   * 在文件末尾或开头追加内容\r\n   */ async appendText(fileId, params) {\n        try {\n            // 获取文件内容\n            const fileResult = await this.fileTreeService.getFile(fileId);\n            if (!fileResult.success || !fileResult.data) {\n                return {\n                    success: false,\n                    error: fileResult.error || \"文件不存在\"\n                };\n            }\n            const file = fileResult.data;\n            const originalContent = file.content || \"\";\n            // 根据位置追加内容\n            let modifiedContent;\n            if (params.position === \"start\") {\n                modifiedContent = params.content + (params.addNewline ? \"\\n\" : \"\") + originalContent;\n            } else {\n                // 默认追加到末尾\n                modifiedContent = originalContent + (params.addNewline ? \"\\n\" : \"\") + params.content;\n            }\n            // 计算差异\n            const changes = this.calculateDiff(originalContent, modifiedContent);\n            const stats = this.calculateStats(changes);\n            const diffResult = {\n                fileId,\n                filePath: file.path,\n                operation: \"append\",\n                originalContent,\n                modifiedContent,\n                changes,\n                stats,\n                timestamp: Date.now()\n            };\n            console.log(\"✅ 文本追加操作完成:\", file.path);\n            return {\n                success: true,\n                data: diffResult\n            };\n        } catch (error) {\n            const errorMessage = \"文本追加失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 正则替换功能\r\n   * 使用正则表达式替换文件内容\r\n   */ async regexReplace(fileId, params) {\n        try {\n            // 获取文件内容\n            const fileResult = await this.fileTreeService.getFile(fileId);\n            if (!fileResult.success || !fileResult.data) {\n                return {\n                    success: false,\n                    error: fileResult.error || \"文件不存在\"\n                };\n            }\n            const file = fileResult.data;\n            const originalContent = file.content || \"\";\n            // 构建正则表达式\n            const flags = params.flags || (params.global ? \"gm\" : \"m\"); // 默认添加多行标志\n            let regex;\n            try {\n                regex = new RegExp(params.pattern, flags);\n            } catch (regexError) {\n                return {\n                    success: false,\n                    error: \"正则表达式无效: \".concat(regexError instanceof Error ? regexError.message : String(regexError))\n                };\n            }\n            // 先测试是否有匹配\n            const testRegex = new RegExp(params.pattern, flags);\n            if (!testRegex.test(originalContent)) {\n                return {\n                    success: false,\n                    error: '没有找到匹配的内容进行替换。搜索模式: \"'.concat(params.pattern, '\"')\n                };\n            }\n            // 执行替换\n            const modifiedContent = originalContent.replace(regex, params.replacement);\n            // 检查是否有实际变更\n            if (originalContent === modifiedContent) {\n                return {\n                    success: false,\n                    error: \"替换操作未改变文件内容，可能是替换内容与原内容相同\"\n                };\n            }\n            // 计算差异\n            const changes = this.calculateDiff(originalContent, modifiedContent);\n            const stats = this.calculateStats(changes);\n            const diffResult = {\n                fileId,\n                filePath: file.path,\n                operation: \"replace\",\n                originalContent,\n                modifiedContent,\n                changes,\n                stats,\n                timestamp: Date.now()\n            };\n            console.log(\"✅ 正则替换操作完成:\", file.path);\n            return {\n                success: true,\n                data: diffResult\n            };\n        } catch (error) {\n            const errorMessage = \"正则替换失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 应用差异修改到文件\r\n   * 将差异结果应用到实际文件中\r\n   */ async applyDiff(diffResult) {\n        try {\n            // 🔧 使用 'external' 来源，表示这是外部工具（df）的更新\n            const updateResult = await this.fileTreeService.updateFileContent(diffResult.fileId, diffResult.modifiedContent, \"external\" // 标记为外部工具更新\n            );\n            if (updateResult.success) {\n                console.log(\"✅ 差异修改已应用到文件:\", diffResult.filePath);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: updateResult.error\n            };\n        } catch (error) {\n            const errorMessage = \"应用差异修改失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 计算文本差异\r\n   * 基于行级别的差异算法实现\r\n   */ calculateDiff(original, modified) {\n        const originalLines = original.split(\"\\n\");\n        const modifiedLines = modified.split(\"\\n\");\n        const changes = [];\n        // 简单的行级别差异算法\n        const maxLines = Math.max(originalLines.length, modifiedLines.length);\n        for(let i = 0; i < maxLines; i++){\n            const originalLine = originalLines[i];\n            const modifiedLine = modifiedLines[i];\n            if (originalLine === undefined) {\n                // 新增行\n                changes.push({\n                    type: \"add\",\n                    lineNumber: i + 1,\n                    content: modifiedLine\n                });\n            } else if (modifiedLine === undefined) {\n                // 删除行\n                changes.push({\n                    type: \"delete\",\n                    lineNumber: i + 1,\n                    content: originalLine\n                });\n            } else if (originalLine !== modifiedLine) {\n                // 修改行\n                changes.push({\n                    type: \"modify\",\n                    lineNumber: i + 1,\n                    content: modifiedLine,\n                    originalContent: originalLine\n                });\n            } else {\n                // 未变更行（可选择性包含）\n                changes.push({\n                    type: \"unchanged\",\n                    lineNumber: i + 1,\n                    content: originalLine\n                });\n            }\n        }\n        return changes;\n    }\n    /**\r\n   * 计算差异统计信息\r\n   */ calculateStats(changes) {\n        const stats = {\n            additions: 0,\n            deletions: 0,\n            modifications: 0,\n            totalChanges: 0\n        };\n        changes.forEach((change)=>{\n            switch(change.type){\n                case \"add\":\n                    stats.additions++;\n                    stats.totalChanges++;\n                    break;\n                case \"delete\":\n                    stats.deletions++;\n                    stats.totalChanges++;\n                    break;\n                case \"modify\":\n                    stats.modifications++;\n                    stats.totalChanges++;\n                    break;\n            }\n        });\n        return stats;\n    }\n    /**\r\n   * 预览差异（不实际修改文件）\r\n   * 用于在UI中显示差异预览\r\n   */ async previewAppend(fileId, params) {\n        // 复用appendText逻辑，但不实际修改文件\n        return this.appendText(fileId, params);\n    }\n    /**\r\n   * 预览正则替换（不实际修改文件）\r\n   */ async previewRegexReplace(fileId, params) {\n        // 复用regexReplace逻辑，但不实际修改文件\n        return this.regexReplace(fileId, params);\n    }\n    /**\r\n   * 获取文件路径（通过文件ID）\r\n   * 辅助方法，用于其他组件获取文件路径\r\n   */ async getFilePath(fileId) {\n        try {\n            const fileResult = await this.fileTreeService.getFile(fileId);\n            if (fileResult.success && fileResult.data) {\n                return fileResult.data.path;\n            }\n            return null;\n        } catch (error) {\n            console.error(\"❌ 获取文件路径失败:\", error);\n            return null;\n        }\n    }\n    /**\r\n   * 验证正则表达式\r\n   * 辅助方法，用于验证正则表达式的有效性\r\n   */ validateRegex(pattern, flags) {\n        try {\n            new RegExp(pattern, flags);\n            return {\n                valid: true\n            };\n        } catch (error) {\n            return {\n                valid: false,\n                error: error instanceof Error ? error.message : \"正则表达式无效\"\n            };\n        }\n    }\n    /**\r\n   * 智能字符串匹配\r\n   * 尝试多种匹配策略，提供详细的错误信息和建议\r\n   */ intelligentStringMatch(content, pattern) {\n        if (!pattern || !content) {\n            return {\n                found: false,\n                error: \"搜索内容或文件内容为空\"\n            };\n        }\n        // 策略1: 精确匹配\n        let index = content.indexOf(pattern);\n        if (index !== -1) {\n            return {\n                found: true,\n                matchedText: pattern,\n                startIndex: index,\n                endIndex: index + pattern.length,\n                strategy: \"exact\"\n            };\n        }\n        // 策略2: 忽略大小写匹配\n        const lowerContent = content.toLowerCase();\n        const lowerPattern = pattern.toLowerCase();\n        index = lowerContent.indexOf(lowerPattern);\n        if (index !== -1) {\n            const matchedText = content.substring(index, index + pattern.length);\n            return {\n                found: true,\n                matchedText,\n                startIndex: index,\n                endIndex: index + pattern.length,\n                strategy: \"case-insensitive\"\n            };\n        }\n        // 策略3: 标准化空白字符匹配\n        const normalizeWhitespace = (text)=>text.replace(/\\s+/g, \" \").trim();\n        const normalizedContent = normalizeWhitespace(content);\n        const normalizedPattern = normalizeWhitespace(pattern);\n        index = normalizedContent.indexOf(normalizedPattern);\n        if (index !== -1) {\n            // 需要在原始内容中找到对应位置\n            const beforeNormalized = normalizedContent.substring(0, index);\n            const beforeOriginal = this.findOriginalPosition(content, beforeNormalized);\n            const matchLength = this.findOriginalMatchLength(content.substring(beforeOriginal), normalizedPattern);\n            return {\n                found: true,\n                matchedText: content.substring(beforeOriginal, beforeOriginal + matchLength),\n                startIndex: beforeOriginal,\n                endIndex: beforeOriginal + matchLength,\n                strategy: \"whitespace-normalized\"\n            };\n        }\n        // 策略4: 模糊匹配 - 查找包含关键词的行\n        const suggestions = this.findSimilarContent(content, pattern);\n        return {\n            found: false,\n            suggestions,\n            error: '未找到匹配内容。搜索模式: \"'.concat(pattern.substring(0, 100)).concat(pattern.length > 100 ? \"...\" : \"\", '\"')\n        };\n    }\n    /**\r\n   * 查找相似内容，提供替换建议\r\n   */ findSimilarContent(content, pattern) {\n        const lines = content.split(\"\\n\");\n        const patternWords = pattern.toLowerCase().split(/\\s+/).filter((word)=>word.length > 2);\n        const suggestions = [];\n        lines.forEach((line, index)=>{\n            const lowerLine = line.toLowerCase();\n            let score = 0;\n            // 计算匹配分数\n            patternWords.forEach((word)=>{\n                if (lowerLine.includes(word)) {\n                    score += word.length;\n                }\n            });\n            if (score > 0) {\n                suggestions.push({\n                    line: line.trim(),\n                    score,\n                    lineNumber: index + 1\n                });\n            }\n        });\n        // 按分数排序，返回前5个建议\n        return suggestions.sort((a, b)=>b.score - a.score).slice(0, 5).map((s)=>\"第\".concat(s.lineNumber, \"行: \").concat(s.line.substring(0, 100)).concat(s.line.length > 100 ? \"...\" : \"\"));\n    }\n    /**\r\n   * 在原始文本中找到标准化文本的对应位置\r\n   */ findOriginalPosition(original, normalizedPrefix) {\n        let originalPos = 0;\n        let normalizedPos = 0;\n        while(normalizedPos < normalizedPrefix.length && originalPos < original.length){\n            const originalChar = original[originalPos];\n            const normalizedChar = normalizedPrefix[normalizedPos];\n            if (/\\s/.test(originalChar)) {\n                // 跳过原始文本中的空白字符\n                originalPos++;\n            } else if (originalChar === normalizedChar) {\n                originalPos++;\n                normalizedPos++;\n            } else {\n                // 处理多个空白字符被标准化为单个空格的情况\n                if (normalizedChar === \" \") {\n                    normalizedPos++;\n                } else {\n                    originalPos++;\n                    normalizedPos++;\n                }\n            }\n        }\n        return originalPos;\n    }\n    /**\r\n   * 计算原始匹配文本的长度\r\n   */ findOriginalMatchLength(originalText, normalizedPattern) {\n        let originalPos = 0;\n        let normalizedPos = 0;\n        while(normalizedPos < normalizedPattern.length && originalPos < originalText.length){\n            const originalChar = originalText[originalPos];\n            const normalizedChar = normalizedPattern[normalizedPos];\n            if (/\\s/.test(originalChar)) {\n                originalPos++;\n            } else if (originalChar === normalizedChar) {\n                originalPos++;\n                normalizedPos++;\n            } else if (normalizedChar === \" \") {\n                normalizedPos++;\n            } else {\n                originalPos++;\n                normalizedPos++;\n            }\n        }\n        return originalPos;\n    }\n    /**\r\n   * 创建文件及其路径（如果不存在）\r\n   * 支持递归创建文件夹结构\r\n   */ async createFileWithPath(artworkId, filePath) {\n        let content = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"\", operation = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : \"append\";\n        try {\n            // 获取文件树根节点\n            const fileTreeResult = await this.fileTreeService.getFileTree(artworkId);\n            if (!fileTreeResult.success || !fileTreeResult.data) {\n                return {\n                    success: false,\n                    error: \"无法获取文件树\"\n                };\n            }\n            const rootNode = fileTreeResult.data;\n            // 解析文件路径\n            const pathParts = filePath.split(\"/\").filter((part)=>part.trim() !== \"\");\n            if (pathParts.length === 0) {\n                return {\n                    success: false,\n                    error: \"文件路径无效\"\n                };\n            }\n            const fileName = pathParts[pathParts.length - 1];\n            const folderParts = pathParts.slice(0, -1);\n            // 递归创建文件夹结构\n            let currentParentId = rootNode.id;\n            for (const folderName of folderParts){\n                // 检查文件夹是否已存在\n                const existingFolder = await this.findChildByName(artworkId, currentParentId, folderName);\n                if (existingFolder) {\n                    currentParentId = existingFolder.id;\n                } else {\n                    // 创建新文件夹\n                    const folderResult = await this.fileTreeService.createFolder(artworkId, currentParentId, folderName);\n                    if (!folderResult.success) {\n                        return {\n                            success: false,\n                            error: \"创建文件夹失败: \".concat(folderResult.error)\n                        };\n                    }\n                    currentParentId = folderResult.data.id;\n                }\n            }\n            // 检查文件是否已存在\n            const existingFile = await this.findChildByName(artworkId, currentParentId, fileName);\n            if (existingFile) {\n                // 文件已存在，执行原有的追加或替换操作\n                if (operation === \"append\") {\n                    return this.appendText(existingFile.id, {\n                        content,\n                        position: \"end\",\n                        addNewline: true\n                    });\n                } else {\n                    // 对于replace操作，这里简化处理，直接替换整个文件内容\n                    const originalContent = existingFile.content || \"\";\n                    const modifiedContent = content;\n                    const changes = this.calculateDiff(originalContent, modifiedContent);\n                    const stats = this.calculateStats(changes);\n                    const diffResult = {\n                        fileId: existingFile.id,\n                        filePath: existingFile.path,\n                        operation: \"replace\",\n                        originalContent,\n                        modifiedContent,\n                        changes,\n                        stats,\n                        timestamp: Date.now()\n                    };\n                    return {\n                        success: true,\n                        data: diffResult\n                    };\n                }\n            } else {\n                // 文件不存在，创建新文件\n                const fileResult = await this.fileTreeService.createFile(artworkId, currentParentId, fileName, \"text\", content);\n                if (!fileResult.success) {\n                    return {\n                        success: false,\n                        error: \"创建文件失败: \".concat(fileResult.error)\n                    };\n                }\n                const newFile = fileResult.data;\n                // 创建差异结果（新文件创建）\n                const changes = this.calculateDiff(\"\", content);\n                const stats = this.calculateStats(changes);\n                const diffResult = {\n                    fileId: newFile.id,\n                    filePath: newFile.path,\n                    operation: \"create\",\n                    originalContent: \"\",\n                    modifiedContent: content,\n                    changes,\n                    stats,\n                    timestamp: Date.now()\n                };\n                console.log(\"✅ 文件及路径创建成功:\", filePath);\n                return {\n                    success: true,\n                    data: diffResult\n                };\n            }\n        } catch (error) {\n            const errorMessage = \"创建文件及路径失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 根据名称查找子节点（文件或文件夹）\r\n   * 辅助方法，用于检查文件/文件夹是否存在\r\n   */ async findChildByName(artworkId, parentId, name) {\n        try {\n            const allFilesResult = await this.fileTreeService.getFilesByArtwork(artworkId);\n            if (!allFilesResult.success || !allFilesResult.data) {\n                return null;\n            }\n            const files = allFilesResult.data;\n            return files.find((file)=>file.parentId === parentId && file.name === name) || null;\n        } catch (error) {\n            console.error(\"❌ 查找子节点失败:\", error);\n            return null;\n        }\n    }\n    /**\r\n   * 检查文件是否存在\r\n   * 用于UI组件判断显示状态\r\n   */ async checkFileExists(artworkId, filePath) {\n        try {\n            const fileTreeResult = await this.fileTreeService.getFileTree(artworkId);\n            if (!fileTreeResult.success || !fileTreeResult.data) {\n                return false;\n            }\n            const rootNode = fileTreeResult.data;\n            const pathParts = filePath.split(\"/\").filter((part)=>part.trim() !== \"\");\n            let currentParentId = rootNode.id;\n            // 逐级检查路径\n            for (const part of pathParts){\n                const child = await this.findChildByName(artworkId, currentParentId, part);\n                if (!child) {\n                    return false;\n                }\n                currentParentId = child.id;\n            }\n            return true;\n        } catch (error) {\n            console.error(\"❌ 检查文件存在性失败:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.fileTreeService = _fileTreeService__WEBPACK_IMPORTED_MODULE_0__.FileTreeService.getInstance();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/diffToolService.ts\n"));

/***/ })

});