"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_redshift_redshift_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/redshift/redshift.js":
/*!********************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/redshift/redshift.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/redshift/redshift.ts\nvar conf = {\n  comments: {\n    lineComment: \"--\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".sql\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \"AES128\",\n    \"AES256\",\n    \"ALL\",\n    \"ALLOWOVERWRITE\",\n    \"ANALYSE\",\n    \"ANALYZE\",\n    \"AND\",\n    \"ANY\",\n    \"ARRAY\",\n    \"AS\",\n    \"ASC\",\n    \"AUTHORIZATION\",\n    \"AZ64\",\n    \"BACKUP\",\n    \"BETWEEN\",\n    \"BINARY\",\n    \"BLANKSASNULL\",\n    \"BOTH\",\n    \"BYTEDICT\",\n    \"BZIP2\",\n    \"CASE\",\n    \"CAST\",\n    \"CHECK\",\n    \"COLLATE\",\n    \"COLUMN\",\n    \"CONSTRAINT\",\n    \"CREATE\",\n    \"CREDENTIALS\",\n    \"CROSS\",\n    \"CURRENT_DATE\",\n    \"CURRENT_TIME\",\n    \"CURRENT_TIMESTAMP\",\n    \"CURRENT_USER\",\n    \"CURRENT_USER_ID\",\n    \"DEFAULT\",\n    \"DEFERRABLE\",\n    \"DEFLATE\",\n    \"DEFRAG\",\n    \"DELTA\",\n    \"DELTA32K\",\n    \"DESC\",\n    \"DISABLE\",\n    \"DISTINCT\",\n    \"DO\",\n    \"ELSE\",\n    \"EMPTYASNULL\",\n    \"ENABLE\",\n    \"ENCODE\",\n    \"ENCRYPT\",\n    \"ENCRYPTION\",\n    \"END\",\n    \"EXCEPT\",\n    \"EXPLICIT\",\n    \"FALSE\",\n    \"FOR\",\n    \"FOREIGN\",\n    \"FREEZE\",\n    \"FROM\",\n    \"FULL\",\n    \"GLOBALDICT256\",\n    \"GLOBALDICT64K\",\n    \"GRANT\",\n    \"GROUP\",\n    \"GZIP\",\n    \"HAVING\",\n    \"IDENTITY\",\n    \"IGNORE\",\n    \"ILIKE\",\n    \"IN\",\n    \"INITIALLY\",\n    \"INNER\",\n    \"INTERSECT\",\n    \"INTO\",\n    \"IS\",\n    \"ISNULL\",\n    \"JOIN\",\n    \"LANGUAGE\",\n    \"LEADING\",\n    \"LEFT\",\n    \"LIKE\",\n    \"LIMIT\",\n    \"LOCALTIME\",\n    \"LOCALTIMESTAMP\",\n    \"LUN\",\n    \"LUNS\",\n    \"LZO\",\n    \"LZOP\",\n    \"MINUS\",\n    \"MOSTLY16\",\n    \"MOSTLY32\",\n    \"MOSTLY8\",\n    \"NATURAL\",\n    \"NEW\",\n    \"NOT\",\n    \"NOTNULL\",\n    \"NULL\",\n    \"NULLS\",\n    \"OFF\",\n    \"OFFLINE\",\n    \"OFFSET\",\n    \"OID\",\n    \"OLD\",\n    \"ON\",\n    \"ONLY\",\n    \"OPEN\",\n    \"OR\",\n    \"ORDER\",\n    \"OUTER\",\n    \"OVERLAPS\",\n    \"PARALLEL\",\n    \"PARTITION\",\n    \"PERCENT\",\n    \"PERMISSIONS\",\n    \"PLACING\",\n    \"PRIMARY\",\n    \"RAW\",\n    \"READRATIO\",\n    \"RECOVER\",\n    \"REFERENCES\",\n    \"RESPECT\",\n    \"REJECTLOG\",\n    \"RESORT\",\n    \"RESTORE\",\n    \"RIGHT\",\n    \"SELECT\",\n    \"SESSION_USER\",\n    \"SIMILAR\",\n    \"SNAPSHOT\",\n    \"SOME\",\n    \"SYSDATE\",\n    \"SYSTEM\",\n    \"TABLE\",\n    \"TAG\",\n    \"TDES\",\n    \"TEXT255\",\n    \"TEXT32K\",\n    \"THEN\",\n    \"TIMESTAMP\",\n    \"TO\",\n    \"TOP\",\n    \"TRAILING\",\n    \"TRUE\",\n    \"TRUNCATECOLUMNS\",\n    \"UNION\",\n    \"UNIQUE\",\n    \"USER\",\n    \"USING\",\n    \"VERBOSE\",\n    \"WALLET\",\n    \"WHEN\",\n    \"WHERE\",\n    \"WITH\",\n    \"WITHOUT\"\n  ],\n  operators: [\n    \"AND\",\n    \"BETWEEN\",\n    \"IN\",\n    \"LIKE\",\n    \"NOT\",\n    \"OR\",\n    \"IS\",\n    \"NULL\",\n    \"INTERSECT\",\n    \"UNION\",\n    \"INNER\",\n    \"JOIN\",\n    \"LEFT\",\n    \"OUTER\",\n    \"RIGHT\"\n  ],\n  builtinFunctions: [\n    \"current_schema\",\n    \"current_schemas\",\n    \"has_database_privilege\",\n    \"has_schema_privilege\",\n    \"has_table_privilege\",\n    \"age\",\n    \"current_time\",\n    \"current_timestamp\",\n    \"localtime\",\n    \"isfinite\",\n    \"now\",\n    \"ascii\",\n    \"get_bit\",\n    \"get_byte\",\n    \"set_bit\",\n    \"set_byte\",\n    \"to_ascii\",\n    \"approximate percentile_disc\",\n    \"avg\",\n    \"count\",\n    \"listagg\",\n    \"max\",\n    \"median\",\n    \"min\",\n    \"percentile_cont\",\n    \"stddev_samp\",\n    \"stddev_pop\",\n    \"sum\",\n    \"var_samp\",\n    \"var_pop\",\n    \"bit_and\",\n    \"bit_or\",\n    \"bool_and\",\n    \"bool_or\",\n    \"cume_dist\",\n    \"first_value\",\n    \"lag\",\n    \"last_value\",\n    \"lead\",\n    \"nth_value\",\n    \"ratio_to_report\",\n    \"dense_rank\",\n    \"ntile\",\n    \"percent_rank\",\n    \"rank\",\n    \"row_number\",\n    \"case\",\n    \"coalesce\",\n    \"decode\",\n    \"greatest\",\n    \"least\",\n    \"nvl\",\n    \"nvl2\",\n    \"nullif\",\n    \"add_months\",\n    \"at time zone\",\n    \"convert_timezone\",\n    \"current_date\",\n    \"date_cmp\",\n    \"date_cmp_timestamp\",\n    \"date_cmp_timestamptz\",\n    \"date_part_year\",\n    \"dateadd\",\n    \"datediff\",\n    \"date_part\",\n    \"date_trunc\",\n    \"extract\",\n    \"getdate\",\n    \"interval_cmp\",\n    \"last_day\",\n    \"months_between\",\n    \"next_day\",\n    \"sysdate\",\n    \"timeofday\",\n    \"timestamp_cmp\",\n    \"timestamp_cmp_date\",\n    \"timestamp_cmp_timestamptz\",\n    \"timestamptz_cmp\",\n    \"timestamptz_cmp_date\",\n    \"timestamptz_cmp_timestamp\",\n    \"timezone\",\n    \"to_timestamp\",\n    \"trunc\",\n    \"abs\",\n    \"acos\",\n    \"asin\",\n    \"atan\",\n    \"atan2\",\n    \"cbrt\",\n    \"ceil\",\n    \"ceiling\",\n    \"checksum\",\n    \"cos\",\n    \"cot\",\n    \"degrees\",\n    \"dexp\",\n    \"dlog1\",\n    \"dlog10\",\n    \"exp\",\n    \"floor\",\n    \"ln\",\n    \"log\",\n    \"mod\",\n    \"pi\",\n    \"power\",\n    \"radians\",\n    \"random\",\n    \"round\",\n    \"sin\",\n    \"sign\",\n    \"sqrt\",\n    \"tan\",\n    \"to_hex\",\n    \"bpcharcmp\",\n    \"btrim\",\n    \"bttext_pattern_cmp\",\n    \"char_length\",\n    \"character_length\",\n    \"charindex\",\n    \"chr\",\n    \"concat\",\n    \"crc32\",\n    \"func_sha1\",\n    \"initcap\",\n    \"left and rights\",\n    \"len\",\n    \"length\",\n    \"lower\",\n    \"lpad and rpads\",\n    \"ltrim\",\n    \"md5\",\n    \"octet_length\",\n    \"position\",\n    \"quote_ident\",\n    \"quote_literal\",\n    \"regexp_count\",\n    \"regexp_instr\",\n    \"regexp_replace\",\n    \"regexp_substr\",\n    \"repeat\",\n    \"replace\",\n    \"replicate\",\n    \"reverse\",\n    \"rtrim\",\n    \"split_part\",\n    \"strpos\",\n    \"strtol\",\n    \"substring\",\n    \"textlen\",\n    \"translate\",\n    \"trim\",\n    \"upper\",\n    \"cast\",\n    \"convert\",\n    \"to_char\",\n    \"to_date\",\n    \"to_number\",\n    \"json_array_length\",\n    \"json_extract_array_element_text\",\n    \"json_extract_path_text\",\n    \"current_setting\",\n    \"pg_cancel_backend\",\n    \"pg_terminate_backend\",\n    \"set_config\",\n    \"current_database\",\n    \"current_user\",\n    \"current_user_id\",\n    \"pg_backend_pid\",\n    \"pg_last_copy_count\",\n    \"pg_last_copy_id\",\n    \"pg_last_query_id\",\n    \"pg_last_unload_count\",\n    \"session_user\",\n    \"slice_num\",\n    \"user\",\n    \"version\",\n    \"abbrev\",\n    \"acosd\",\n    \"any\",\n    \"area\",\n    \"array_agg\",\n    \"array_append\",\n    \"array_cat\",\n    \"array_dims\",\n    \"array_fill\",\n    \"array_length\",\n    \"array_lower\",\n    \"array_ndims\",\n    \"array_position\",\n    \"array_positions\",\n    \"array_prepend\",\n    \"array_remove\",\n    \"array_replace\",\n    \"array_to_json\",\n    \"array_to_string\",\n    \"array_to_tsvector\",\n    \"array_upper\",\n    \"asind\",\n    \"atan2d\",\n    \"atand\",\n    \"bit\",\n    \"bit_length\",\n    \"bound_box\",\n    \"box\",\n    \"brin_summarize_new_values\",\n    \"broadcast\",\n    \"cardinality\",\n    \"center\",\n    \"circle\",\n    \"clock_timestamp\",\n    \"col_description\",\n    \"concat_ws\",\n    \"convert_from\",\n    \"convert_to\",\n    \"corr\",\n    \"cosd\",\n    \"cotd\",\n    \"covar_pop\",\n    \"covar_samp\",\n    \"current_catalog\",\n    \"current_query\",\n    \"current_role\",\n    \"currval\",\n    \"cursor_to_xml\",\n    \"diameter\",\n    \"div\",\n    \"encode\",\n    \"enum_first\",\n    \"enum_last\",\n    \"enum_range\",\n    \"every\",\n    \"family\",\n    \"format\",\n    \"format_type\",\n    \"generate_series\",\n    \"generate_subscripts\",\n    \"get_current_ts_config\",\n    \"gin_clean_pending_list\",\n    \"grouping\",\n    \"has_any_column_privilege\",\n    \"has_column_privilege\",\n    \"has_foreign_data_wrapper_privilege\",\n    \"has_function_privilege\",\n    \"has_language_privilege\",\n    \"has_sequence_privilege\",\n    \"has_server_privilege\",\n    \"has_tablespace_privilege\",\n    \"has_type_privilege\",\n    \"height\",\n    \"host\",\n    \"hostmask\",\n    \"inet_client_addr\",\n    \"inet_client_port\",\n    \"inet_merge\",\n    \"inet_same_family\",\n    \"inet_server_addr\",\n    \"inet_server_port\",\n    \"isclosed\",\n    \"isempty\",\n    \"isopen\",\n    \"json_agg\",\n    \"json_object\",\n    \"json_object_agg\",\n    \"json_populate_record\",\n    \"json_populate_recordset\",\n    \"json_to_record\",\n    \"json_to_recordset\",\n    \"jsonb_agg\",\n    \"jsonb_object_agg\",\n    \"justify_days\",\n    \"justify_hours\",\n    \"justify_interval\",\n    \"lastval\",\n    \"left\",\n    \"line\",\n    \"localtimestamp\",\n    \"lower_inc\",\n    \"lower_inf\",\n    \"lpad\",\n    \"lseg\",\n    \"make_date\",\n    \"make_interval\",\n    \"make_time\",\n    \"make_timestamp\",\n    \"make_timestamptz\",\n    \"masklen\",\n    \"mode\",\n    \"netmask\",\n    \"network\",\n    \"nextval\",\n    \"npoints\",\n    \"num_nonnulls\",\n    \"num_nulls\",\n    \"numnode\",\n    \"obj_description\",\n    \"overlay\",\n    \"parse_ident\",\n    \"path\",\n    \"pclose\",\n    \"percentile_disc\",\n    \"pg_advisory_lock\",\n    \"pg_advisory_lock_shared\",\n    \"pg_advisory_unlock\",\n    \"pg_advisory_unlock_all\",\n    \"pg_advisory_unlock_shared\",\n    \"pg_advisory_xact_lock\",\n    \"pg_advisory_xact_lock_shared\",\n    \"pg_backup_start_time\",\n    \"pg_blocking_pids\",\n    \"pg_client_encoding\",\n    \"pg_collation_is_visible\",\n    \"pg_column_size\",\n    \"pg_conf_load_time\",\n    \"pg_control_checkpoint\",\n    \"pg_control_init\",\n    \"pg_control_recovery\",\n    \"pg_control_system\",\n    \"pg_conversion_is_visible\",\n    \"pg_create_logical_replication_slot\",\n    \"pg_create_physical_replication_slot\",\n    \"pg_create_restore_point\",\n    \"pg_current_xlog_flush_location\",\n    \"pg_current_xlog_insert_location\",\n    \"pg_current_xlog_location\",\n    \"pg_database_size\",\n    \"pg_describe_object\",\n    \"pg_drop_replication_slot\",\n    \"pg_export_snapshot\",\n    \"pg_filenode_relation\",\n    \"pg_function_is_visible\",\n    \"pg_get_constraintdef\",\n    \"pg_get_expr\",\n    \"pg_get_function_arguments\",\n    \"pg_get_function_identity_arguments\",\n    \"pg_get_function_result\",\n    \"pg_get_functiondef\",\n    \"pg_get_indexdef\",\n    \"pg_get_keywords\",\n    \"pg_get_object_address\",\n    \"pg_get_owned_sequence\",\n    \"pg_get_ruledef\",\n    \"pg_get_serial_sequence\",\n    \"pg_get_triggerdef\",\n    \"pg_get_userbyid\",\n    \"pg_get_viewdef\",\n    \"pg_has_role\",\n    \"pg_identify_object\",\n    \"pg_identify_object_as_address\",\n    \"pg_index_column_has_property\",\n    \"pg_index_has_property\",\n    \"pg_indexam_has_property\",\n    \"pg_indexes_size\",\n    \"pg_is_in_backup\",\n    \"pg_is_in_recovery\",\n    \"pg_is_other_temp_schema\",\n    \"pg_is_xlog_replay_paused\",\n    \"pg_last_committed_xact\",\n    \"pg_last_xact_replay_timestamp\",\n    \"pg_last_xlog_receive_location\",\n    \"pg_last_xlog_replay_location\",\n    \"pg_listening_channels\",\n    \"pg_logical_emit_message\",\n    \"pg_logical_slot_get_binary_changes\",\n    \"pg_logical_slot_get_changes\",\n    \"pg_logical_slot_peek_binary_changes\",\n    \"pg_logical_slot_peek_changes\",\n    \"pg_ls_dir\",\n    \"pg_my_temp_schema\",\n    \"pg_notification_queue_usage\",\n    \"pg_opclass_is_visible\",\n    \"pg_operator_is_visible\",\n    \"pg_opfamily_is_visible\",\n    \"pg_options_to_table\",\n    \"pg_postmaster_start_time\",\n    \"pg_read_binary_file\",\n    \"pg_read_file\",\n    \"pg_relation_filenode\",\n    \"pg_relation_filepath\",\n    \"pg_relation_size\",\n    \"pg_reload_conf\",\n    \"pg_replication_origin_create\",\n    \"pg_replication_origin_drop\",\n    \"pg_replication_origin_oid\",\n    \"pg_replication_origin_progress\",\n    \"pg_replication_origin_session_is_setup\",\n    \"pg_replication_origin_session_progress\",\n    \"pg_replication_origin_session_reset\",\n    \"pg_replication_origin_session_setup\",\n    \"pg_replication_origin_xact_reset\",\n    \"pg_replication_origin_xact_setup\",\n    \"pg_rotate_logfile\",\n    \"pg_size_bytes\",\n    \"pg_size_pretty\",\n    \"pg_sleep\",\n    \"pg_sleep_for\",\n    \"pg_sleep_until\",\n    \"pg_start_backup\",\n    \"pg_stat_file\",\n    \"pg_stop_backup\",\n    \"pg_switch_xlog\",\n    \"pg_table_is_visible\",\n    \"pg_table_size\",\n    \"pg_tablespace_databases\",\n    \"pg_tablespace_location\",\n    \"pg_tablespace_size\",\n    \"pg_total_relation_size\",\n    \"pg_trigger_depth\",\n    \"pg_try_advisory_lock\",\n    \"pg_try_advisory_lock_shared\",\n    \"pg_try_advisory_xact_lock\",\n    \"pg_try_advisory_xact_lock_shared\",\n    \"pg_ts_config_is_visible\",\n    \"pg_ts_dict_is_visible\",\n    \"pg_ts_parser_is_visible\",\n    \"pg_ts_template_is_visible\",\n    \"pg_type_is_visible\",\n    \"pg_typeof\",\n    \"pg_xact_commit_timestamp\",\n    \"pg_xlog_location_diff\",\n    \"pg_xlog_replay_pause\",\n    \"pg_xlog_replay_resume\",\n    \"pg_xlogfile_name\",\n    \"pg_xlogfile_name_offset\",\n    \"phraseto_tsquery\",\n    \"plainto_tsquery\",\n    \"point\",\n    \"polygon\",\n    \"popen\",\n    \"pqserverversion\",\n    \"query_to_xml\",\n    \"querytree\",\n    \"quote_nullable\",\n    \"radius\",\n    \"range_merge\",\n    \"regexp_matches\",\n    \"regexp_split_to_array\",\n    \"regexp_split_to_table\",\n    \"regr_avgx\",\n    \"regr_avgy\",\n    \"regr_count\",\n    \"regr_intercept\",\n    \"regr_r2\",\n    \"regr_slope\",\n    \"regr_sxx\",\n    \"regr_sxy\",\n    \"regr_syy\",\n    \"right\",\n    \"row_security_active\",\n    \"row_to_json\",\n    \"rpad\",\n    \"scale\",\n    \"set_masklen\",\n    \"setseed\",\n    \"setval\",\n    \"setweight\",\n    \"shobj_description\",\n    \"sind\",\n    \"sprintf\",\n    \"statement_timestamp\",\n    \"stddev\",\n    \"string_agg\",\n    \"string_to_array\",\n    \"strip\",\n    \"substr\",\n    \"table_to_xml\",\n    \"table_to_xml_and_xmlschema\",\n    \"tand\",\n    \"text\",\n    \"to_json\",\n    \"to_regclass\",\n    \"to_regnamespace\",\n    \"to_regoper\",\n    \"to_regoperator\",\n    \"to_regproc\",\n    \"to_regprocedure\",\n    \"to_regrole\",\n    \"to_regtype\",\n    \"to_tsquery\",\n    \"to_tsvector\",\n    \"transaction_timestamp\",\n    \"ts_debug\",\n    \"ts_delete\",\n    \"ts_filter\",\n    \"ts_headline\",\n    \"ts_lexize\",\n    \"ts_parse\",\n    \"ts_rank\",\n    \"ts_rank_cd\",\n    \"ts_rewrite\",\n    \"ts_stat\",\n    \"ts_token_type\",\n    \"tsquery_phrase\",\n    \"tsvector_to_array\",\n    \"tsvector_update_trigger\",\n    \"tsvector_update_trigger_column\",\n    \"txid_current\",\n    \"txid_current_snapshot\",\n    \"txid_snapshot_xip\",\n    \"txid_snapshot_xmax\",\n    \"txid_snapshot_xmin\",\n    \"txid_visible_in_snapshot\",\n    \"unnest\",\n    \"upper_inc\",\n    \"upper_inf\",\n    \"variance\",\n    \"width\",\n    \"width_bucket\",\n    \"xml_is_well_formed\",\n    \"xml_is_well_formed_content\",\n    \"xml_is_well_formed_document\",\n    \"xmlagg\",\n    \"xmlcomment\",\n    \"xmlconcat\",\n    \"xmlelement\",\n    \"xmlexists\",\n    \"xmlforest\",\n    \"xmlparse\",\n    \"xmlpi\",\n    \"xmlroot\",\n    \"xmlserialize\",\n    \"xpath\",\n    \"xpath_exists\"\n  ],\n  builtinVariables: [\n    // NOT SUPPORTED\n  ],\n  pseudoColumns: [\n    // NOT SUPPORTED\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@pseudoColumns\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@complexIdentifiers\" },\n      { include: \"@scopes\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [\n        /[\\w@#$]+/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@operators\": \"operator\",\n            \"@builtinVariables\": \"predefined\",\n            \"@builtinFunctions\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [/--+.*/, \"comment\"],\n      [/\\/\\*/, { token: \"comment.quote\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^*/]+/, \"comment\"],\n      // Not supporting nested comments, as nested comments seem to not be standard?\n      // i.e. http://stackoverflow.com/questions/728172/are-there-multiline-comment-delimiters-in-sql-that-are-vendor-agnostic\n      // [/\\/\\*/, { token: 'comment.quote', next: '@push' }],    // nested comment not allowed :-(\n      [/\\*\\//, { token: \"comment.quote\", next: \"@pop\" }],\n      [/./, \"comment\"]\n    ],\n    pseudoColumns: [\n      [\n        /[$][A-Za-z_][\\w@#$]*/,\n        {\n          cases: {\n            \"@pseudoColumns\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [[/'/, { token: \"string\", next: \"@string\" }]],\n    string: [\n      [/[^']+/, \"string\"],\n      [/''/, \"string\"],\n      [/'/, { token: \"string\", next: \"@pop\" }]\n    ],\n    complexIdentifiers: [[/\"/, { token: \"identifier.quote\", next: \"@quotedIdentifier\" }]],\n    quotedIdentifier: [\n      [/[^\"]+/, \"identifier\"],\n      [/\"\"/, \"identifier\"],\n      [/\"/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    scopes: [\n      // NOT SUPPORTED\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/redshift/redshift.js\n"));

/***/ })

}]);