"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_services_fontService_ts";
exports.ids = ["_ssr_src_services_fontService_ts"];
exports.modules = {

/***/ "(ssr)/./src/services/fontService.ts":
/*!*************************************!*\
  !*** ./src/services/fontService.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FontService: () => (/* binding */ FontService)\n/* harmony export */ });\n/* harmony import */ var _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database/DatabaseService */ \"(ssr)/./src/services/database/DatabaseService.ts\");\n/**\r\n * 字体数据操作服务\r\n * 提供字体的上传、存储、管理和应用功能\r\n */ \nclass FontService {\n    constructor(){\n        this.storeName = \"fonts\";\n        this.loadedFonts = new Set();\n        // 支持的字体格式\n        this.supportedFormats = [\n            \"woff2\",\n            \"woff\",\n            \"ttf\",\n            \"otf\"\n        ];\n        // 最大文件大小 (5MB)\n        this.maxFileSize = 5 * 1024 * 1024;\n        this.dbService = _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__.DatabaseService.getInstance();\n    }\n    /**\r\n   * 获取字体服务单例\r\n   */ static getInstance() {\n        if (!FontService.instance) {\n            FontService.instance = new FontService();\n        }\n        return FontService.instance;\n    }\n    /**\r\n   * 生成UUID\r\n   */ generateUUID() {\n        return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n            const r = Math.random() * 16 | 0;\n            const v = c === \"x\" ? r : r & 0x3 | 0x8;\n            return v.toString(16);\n        });\n    }\n    /**\r\n   * 验证字体文件\r\n   */ validateFontFile(file) {\n        // 检查文件大小\n        if (file.size > this.maxFileSize) {\n            return {\n                valid: false,\n                error: `文件大小超过限制 (${Math.round(this.maxFileSize / 1024 / 1024)}MB)`\n            };\n        }\n        // 检查文件格式\n        const extension = file.name.split(\".\").pop()?.toLowerCase();\n        if (!extension || !this.supportedFormats.includes(extension)) {\n            return {\n                valid: false,\n                error: `不支持的文件格式，支持格式: ${this.supportedFormats.join(\", \")}`\n            };\n        }\n        return {\n            valid: true\n        };\n    }\n    /**\r\n   * 文件转ArrayBuffer\r\n   */ fileToArrayBuffer(file) {\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = ()=>resolve(reader.result);\n            reader.onerror = ()=>reject(new Error(\"文件读取失败\"));\n            reader.readAsArrayBuffer(file);\n        });\n    }\n    /**\r\n   * 提取字体族名\r\n   */ extractFontFamily(fileName) {\n        // 从文件名提取字体族名，移除扩展名和特殊字符\n        return fileName.replace(/\\.[^/.]+$/, \"\") // 移除扩展名\n        .replace(/[-_]/g, \" \") // 替换连字符和下划线为空格\n        .replace(/\\b\\w/g, (l)=>l.toUpperCase()); // 首字母大写\n    }\n    /**\r\n   * 上传字体文件\r\n   */ async uploadFont(file, customName) {\n        try {\n            // 验证文件\n            const validation = this.validateFontFile(file);\n            if (!validation.valid) {\n                return {\n                    success: false,\n                    error: validation.error\n                };\n            }\n            // 检查是否已存在同名字体\n            const existingFonts = await this.getAllFonts();\n            if (existingFonts.success && existingFonts.data) {\n                const fontName = customName || file.name;\n                const duplicate = existingFonts.data.find((f)=>f.name === fontName);\n                if (duplicate) {\n                    return {\n                        success: false,\n                        error: \"字体名称已存在\"\n                    };\n                }\n            }\n            // 转换文件数据\n            const arrayBuffer = await this.fileToArrayBuffer(file);\n            const extension = file.name.split(\".\").pop()?.toLowerCase() || \"ttf\";\n            const font = {\n                id: this.generateUUID(),\n                name: customName || file.name,\n                family: this.extractFontFamily(file.name),\n                data: arrayBuffer,\n                format: extension,\n                uploadedAt: Date.now(),\n                size: file.size\n            };\n            const result = await this.dbService.add(this.storeName, font);\n            if (result.success) {\n                console.log(\"✅ 字体上传成功:\", font.name);\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = `字体上传失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取字体\r\n   */ async getFont(id) {\n        try {\n            const result = await this.dbService.get(this.storeName, id);\n            if (result.success && !result.data) {\n                return {\n                    success: true,\n                    data: null\n                };\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = `获取字体失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取所有字体\r\n   */ async getAllFonts() {\n        try {\n            const result = await this.dbService.getAll(this.storeName);\n            if (result.success && result.data) {\n                // 按上传时间倒序排列\n                result.data.sort((a, b)=>b.uploadedAt - a.uploadedAt);\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = `获取字体列表失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 删除字体\r\n   */ async deleteFont(id) {\n        try {\n            // 先检查字体是否存在\n            const existingResult = await this.getFont(id);\n            if (!existingResult.success || !existingResult.data) {\n                return {\n                    success: false,\n                    error: \"字体不存在\"\n                };\n            }\n            const result = await this.dbService.delete(this.storeName, id);\n            if (result.success) {\n                // 从已加载字体集合中移除\n                this.loadedFonts.delete(id);\n                // 移除CSS中的字体定义\n                this.removeFontFromCSS(existingResult.data.family);\n                console.log(\"✅ 字体删除成功:\", existingResult.data.name);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = `删除字体失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 加载字体到CSS\r\n   */ async loadFontToCSS(fontId) {\n        try {\n            // 如果已经加载，直接返回成功\n            if (this.loadedFonts.has(fontId)) {\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            const fontResult = await this.getFont(fontId);\n            if (!fontResult.success || !fontResult.data) {\n                return {\n                    success: false,\n                    error: \"字体不存在\"\n                };\n            }\n            const font = fontResult.data;\n            // 创建Blob URL\n            const blob = new Blob([\n                font.data\n            ], {\n                type: this.getMimeType(font.format)\n            });\n            const fontUrl = URL.createObjectURL(blob);\n            // 创建@font-face规则\n            const fontFaceRule = `\r\n        @font-face {\r\n          font-family: '${font.family}';\r\n          src: url('${fontUrl}') format('${this.getFormatName(font.format)}');\r\n          font-display: swap;\r\n        }\r\n      `;\n            // 添加到页面\n            const styleElement = document.createElement(\"style\");\n            styleElement.id = `font-${fontId}`;\n            styleElement.textContent = fontFaceRule;\n            document.head.appendChild(styleElement);\n            // 标记为已加载\n            this.loadedFonts.add(fontId);\n            console.log(\"✅ 字体加载成功:\", font.name);\n            return {\n                success: true,\n                data: true\n            };\n        } catch (error) {\n            const errorMessage = `加载字体失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 从CSS中移除字体\r\n   */ removeFontFromCSS(fontFamily) {\n        const styleElements = document.querySelectorAll('style[id^=\"font-\"]');\n        styleElements.forEach((element)=>{\n            if (element.textContent?.includes(fontFamily)) {\n                element.remove();\n            }\n        });\n    }\n    /**\r\n   * 获取MIME类型\r\n   */ getMimeType(format) {\n        const mimeTypes = {\n            \"woff2\": \"font/woff2\",\n            \"woff\": \"font/woff\",\n            \"ttf\": \"font/ttf\",\n            \"otf\": \"font/otf\"\n        };\n        return mimeTypes[format] || \"font/ttf\";\n    }\n    /**\r\n   * 获取格式名称\r\n   */ getFormatName(format) {\n        const formatNames = {\n            \"woff2\": \"woff2\",\n            \"woff\": \"woff\",\n            \"ttf\": \"truetype\",\n            \"otf\": \"opentype\"\n        };\n        return formatNames[format] || \"truetype\";\n    }\n    /**\r\n   * 预加载所有字体\r\n   */ async preloadAllFonts() {\n        try {\n            const fontsResult = await this.getAllFonts();\n            if (!fontsResult.success || !fontsResult.data) {\n                return {\n                    success: false,\n                    error: fontsResult.error\n                };\n            }\n            let loadedCount = 0;\n            const fonts = fontsResult.data;\n            for (const font of fonts){\n                const loadResult = await this.loadFontToCSS(font.id);\n                if (loadResult.success) {\n                    loadedCount++;\n                }\n            }\n            console.log(`✅ 预加载字体完成: ${loadedCount}/${fonts.length}`);\n            return {\n                success: true,\n                data: loadedCount\n            };\n        } catch (error) {\n            const errorMessage = `预加载字体失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取字体统计信息\r\n   */ async getFontStats() {\n        try {\n            const fontsResult = await this.getAllFonts();\n            if (!fontsResult.success || !fontsResult.data) {\n                return {\n                    success: false,\n                    error: fontsResult.error\n                };\n            }\n            const fonts = fontsResult.data;\n            const stats = {\n                total: fonts.length,\n                totalSize: fonts.reduce((sum, f)=>sum + f.size, 0),\n                formats: {}\n            };\n            // 统计格式\n            fonts.forEach((font)=>{\n                stats.formats[font.format] = (stats.formats[font.format] || 0) + 1;\n            });\n            return {\n                success: true,\n                data: stats\n            };\n        } catch (error) {\n            const errorMessage = `获取字体统计失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 清空所有字体\r\n   */ async clearAllFonts() {\n        try {\n            const result = await this.dbService.clear(this.storeName);\n            if (result.success) {\n                // 清除已加载的字体\n                this.loadedFonts.clear();\n                // 移除所有字体样式\n                const styleElements = document.querySelectorAll('style[id^=\"font-\"]');\n                styleElements.forEach((element)=>element.remove());\n                console.log(\"✅ 所有字体已清空\");\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = `清空字体失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/fontService.ts\n");

/***/ })

};
;