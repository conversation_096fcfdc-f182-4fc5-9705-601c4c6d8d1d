"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_msdax_msdax_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/msdax/msdax.js":
/*!**************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/msdax/msdax.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/msdax/msdax.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"{\", \"}\"]\n  ],\n  autoClosingPairs: [\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".msdax\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.brackets\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    // Query keywords\n    \"VAR\",\n    \"RETURN\",\n    \"NOT\",\n    \"EVALUATE\",\n    \"DATATABLE\",\n    \"ORDER\",\n    \"BY\",\n    \"START\",\n    \"AT\",\n    \"DEFINE\",\n    \"MEASURE\",\n    \"ASC\",\n    \"DESC\",\n    \"IN\",\n    // Datatable types\n    \"BOOLEAN\",\n    \"DOUBLE\",\n    \"INTEGER\",\n    \"DATETIME\",\n    \"CURRENCY\",\n    \"STRING\"\n  ],\n  functions: [\n    // Relational\n    \"CLOSINGBALANCEMONTH\",\n    \"CLOSINGBALANCEQUARTER\",\n    \"CLOSINGBALANCEYEAR\",\n    \"DATEADD\",\n    \"DATESBETWEEN\",\n    \"DATESINPERIOD\",\n    \"DATESMTD\",\n    \"DATESQTD\",\n    \"DATESYTD\",\n    \"ENDOFMONTH\",\n    \"ENDOFQUARTER\",\n    \"ENDOFYEAR\",\n    \"FIRSTDATE\",\n    \"FIRSTNONBLANK\",\n    \"LASTDATE\",\n    \"LASTNONBLANK\",\n    \"NEXTDAY\",\n    \"NEXTMONTH\",\n    \"NEXTQUARTER\",\n    \"NEXTYEAR\",\n    \"OPENINGBALANCEMONTH\",\n    \"OPENINGBALANCEQUARTER\",\n    \"OPENINGBALANCEYEAR\",\n    \"PARALLELPERIOD\",\n    \"PREVIOUSDAY\",\n    \"PREVIOUSMONTH\",\n    \"PREVIOUSQUARTER\",\n    \"PREVIOUSYEAR\",\n    \"SAMEPERIODLASTYEAR\",\n    \"STARTOFMONTH\",\n    \"STARTOFQUARTER\",\n    \"STARTOFYEAR\",\n    \"TOTALMTD\",\n    \"TOTALQTD\",\n    \"TOTALYTD\",\n    \"ADDCOLUMNS\",\n    \"ADDMISSINGITEMS\",\n    \"ALL\",\n    \"ALLEXCEPT\",\n    \"ALLNOBLANKROW\",\n    \"ALLSELECTED\",\n    \"CALCULATE\",\n    \"CALCULATETABLE\",\n    \"CALENDAR\",\n    \"CALENDARAUTO\",\n    \"CROSSFILTER\",\n    \"CROSSJOIN\",\n    \"CURRENTGROUP\",\n    \"DATATABLE\",\n    \"DETAILROWS\",\n    \"DISTINCT\",\n    \"EARLIER\",\n    \"EARLIEST\",\n    \"EXCEPT\",\n    \"FILTER\",\n    \"FILTERS\",\n    \"GENERATE\",\n    \"GENERATEALL\",\n    \"GROUPBY\",\n    \"IGNORE\",\n    \"INTERSECT\",\n    \"ISONORAFTER\",\n    \"KEEPFILTERS\",\n    \"LOOKUPVALUE\",\n    \"NATURALINNERJOIN\",\n    \"NATURALLEFTOUTERJOIN\",\n    \"RELATED\",\n    \"RELATEDTABLE\",\n    \"ROLLUP\",\n    \"ROLLUPADDISSUBTOTAL\",\n    \"ROLLUPGROUP\",\n    \"ROLLUPISSUBTOTAL\",\n    \"ROW\",\n    \"SAMPLE\",\n    \"SELECTCOLUMNS\",\n    \"SUBSTITUTEWITHINDEX\",\n    \"SUMMARIZE\",\n    \"SUMMARIZECOLUMNS\",\n    \"TOPN\",\n    \"TREATAS\",\n    \"UNION\",\n    \"USERELATIONSHIP\",\n    \"VALUES\",\n    \"SUM\",\n    \"SUMX\",\n    \"PATH\",\n    \"PATHCONTAINS\",\n    \"PATHITEM\",\n    \"PATHITEMREVERSE\",\n    \"PATHLENGTH\",\n    \"AVERAGE\",\n    \"AVERAGEA\",\n    \"AVERAGEX\",\n    \"COUNT\",\n    \"COUNTA\",\n    \"COUNTAX\",\n    \"COUNTBLANK\",\n    \"COUNTROWS\",\n    \"COUNTX\",\n    \"DISTINCTCOUNT\",\n    \"DIVIDE\",\n    \"GEOMEAN\",\n    \"GEOMEANX\",\n    \"MAX\",\n    \"MAXA\",\n    \"MAXX\",\n    \"MEDIAN\",\n    \"MEDIANX\",\n    \"MIN\",\n    \"MINA\",\n    \"MINX\",\n    \"PERCENTILE.EXC\",\n    \"PERCENTILE.INC\",\n    \"PERCENTILEX.EXC\",\n    \"PERCENTILEX.INC\",\n    \"PRODUCT\",\n    \"PRODUCTX\",\n    \"RANK.EQ\",\n    \"RANKX\",\n    \"STDEV.P\",\n    \"STDEV.S\",\n    \"STDEVX.P\",\n    \"STDEVX.S\",\n    \"VAR.P\",\n    \"VAR.S\",\n    \"VARX.P\",\n    \"VARX.S\",\n    \"XIRR\",\n    \"XNPV\",\n    // Scalar\n    \"DATE\",\n    \"DATEDIFF\",\n    \"DATEVALUE\",\n    \"DAY\",\n    \"EDATE\",\n    \"EOMONTH\",\n    \"HOUR\",\n    \"MINUTE\",\n    \"MONTH\",\n    \"NOW\",\n    \"SECOND\",\n    \"TIME\",\n    \"TIMEVALUE\",\n    \"TODAY\",\n    \"WEEKDAY\",\n    \"WEEKNUM\",\n    \"YEAR\",\n    \"YEARFRAC\",\n    \"CONTAINS\",\n    \"CONTAINSROW\",\n    \"CUSTOMDATA\",\n    \"ERROR\",\n    \"HASONEFILTER\",\n    \"HASONEVALUE\",\n    \"ISBLANK\",\n    \"ISCROSSFILTERED\",\n    \"ISEMPTY\",\n    \"ISERROR\",\n    \"ISEVEN\",\n    \"ISFILTERED\",\n    \"ISLOGICAL\",\n    \"ISNONTEXT\",\n    \"ISNUMBER\",\n    \"ISODD\",\n    \"ISSUBTOTAL\",\n    \"ISTEXT\",\n    \"USERNAME\",\n    \"USERPRINCIPALNAME\",\n    \"AND\",\n    \"FALSE\",\n    \"IF\",\n    \"IFERROR\",\n    \"NOT\",\n    \"OR\",\n    \"SWITCH\",\n    \"TRUE\",\n    \"ABS\",\n    \"ACOS\",\n    \"ACOSH\",\n    \"ACOT\",\n    \"ACOTH\",\n    \"ASIN\",\n    \"ASINH\",\n    \"ATAN\",\n    \"ATANH\",\n    \"BETA.DIST\",\n    \"BETA.INV\",\n    \"CEILING\",\n    \"CHISQ.DIST\",\n    \"CHISQ.DIST.RT\",\n    \"CHISQ.INV\",\n    \"CHISQ.INV.RT\",\n    \"COMBIN\",\n    \"COMBINA\",\n    \"CONFIDENCE.NORM\",\n    \"CONFIDENCE.T\",\n    \"COS\",\n    \"COSH\",\n    \"COT\",\n    \"COTH\",\n    \"CURRENCY\",\n    \"DEGREES\",\n    \"EVEN\",\n    \"EXP\",\n    \"EXPON.DIST\",\n    \"FACT\",\n    \"FLOOR\",\n    \"GCD\",\n    \"INT\",\n    \"ISO.CEILING\",\n    \"LCM\",\n    \"LN\",\n    \"LOG\",\n    \"LOG10\",\n    \"MOD\",\n    \"MROUND\",\n    \"ODD\",\n    \"PERMUT\",\n    \"PI\",\n    \"POISSON.DIST\",\n    \"POWER\",\n    \"QUOTIENT\",\n    \"RADIANS\",\n    \"RAND\",\n    \"RANDBETWEEN\",\n    \"ROUND\",\n    \"ROUNDDOWN\",\n    \"ROUNDUP\",\n    \"SIGN\",\n    \"SIN\",\n    \"SINH\",\n    \"SQRT\",\n    \"SQRTPI\",\n    \"TAN\",\n    \"TANH\",\n    \"TRUNC\",\n    \"BLANK\",\n    \"CONCATENATE\",\n    \"CONCATENATEX\",\n    \"EXACT\",\n    \"FIND\",\n    \"FIXED\",\n    \"FORMAT\",\n    \"LEFT\",\n    \"LEN\",\n    \"LOWER\",\n    \"MID\",\n    \"REPLACE\",\n    \"REPT\",\n    \"RIGHT\",\n    \"SEARCH\",\n    \"SUBSTITUTE\",\n    \"TRIM\",\n    \"UNICHAR\",\n    \"UNICODE\",\n    \"UPPER\",\n    \"VALUE\"\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@complexIdentifiers\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[({})]/, \"@brackets\"],\n      [\n        /[a-z_][a-zA-Z0-9_]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@functions\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [/\\/\\/+.*/, \"comment\"],\n      [/\\/\\*/, { token: \"comment.quote\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^*/]+/, \"comment\"],\n      [/\\*\\//, { token: \"comment.quote\", next: \"@pop\" }],\n      [/./, \"comment\"]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [\n      [/N\"/, { token: \"string\", next: \"@string\" }],\n      [/\"/, { token: \"string\", next: \"@string\" }]\n    ],\n    string: [\n      [/[^\"]+/, \"string\"],\n      [/\"\"/, \"string\"],\n      [/\"/, { token: \"string\", next: \"@pop\" }]\n    ],\n    complexIdentifiers: [\n      [/\\[/, { token: \"identifier.quote\", next: \"@bracketedIdentifier\" }],\n      [/'/, { token: \"identifier.quote\", next: \"@quotedIdentifier\" }]\n    ],\n    bracketedIdentifier: [\n      [/[^\\]]+/, \"identifier\"],\n      [/]]/, \"identifier\"],\n      [/]/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    quotedIdentifier: [\n      [/[^']+/, \"identifier\"],\n      [/''/, \"identifier\"],\n      [/'/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/msdax/msdax.js\n"));

/***/ })

}]);