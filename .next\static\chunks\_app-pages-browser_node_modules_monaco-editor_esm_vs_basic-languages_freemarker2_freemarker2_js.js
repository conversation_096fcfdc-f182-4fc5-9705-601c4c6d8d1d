"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_freemarker2_freemarker2_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/freemarker2/freemarker2.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/freemarker2/freemarker2.js ***!
  \**************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TagAngleInterpolationBracket: function() { return /* binding */ TagAngleInterpolationBracket; },\n/* harmony export */   TagAngleInterpolationDollar: function() { return /* binding */ TagAngleInterpolationDollar; },\n/* harmony export */   TagAutoInterpolationBracket: function() { return /* binding */ TagAutoInterpolationBracket; },\n/* harmony export */   TagAutoInterpolationDollar: function() { return /* binding */ TagAutoInterpolationDollar; },\n/* harmony export */   TagBracketInterpolationBracket: function() { return /* binding */ TagBracketInterpolationBracket; },\n/* harmony export */   TagBracketInterpolationDollar: function() { return /* binding */ TagBracketInterpolationDollar; }\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/editor.api.js?ce9f\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/basic-languages/freemarker2/freemarker2.ts\nvar EMPTY_ELEMENTS = [\n  \"assign\",\n  \"flush\",\n  \"ftl\",\n  \"return\",\n  \"global\",\n  \"import\",\n  \"include\",\n  \"break\",\n  \"continue\",\n  \"local\",\n  \"nested\",\n  \"nt\",\n  \"setting\",\n  \"stop\",\n  \"t\",\n  \"lt\",\n  \"rt\",\n  \"fallback\"\n];\nvar BLOCK_ELEMENTS = [\n  \"attempt\",\n  \"autoesc\",\n  \"autoEsc\",\n  \"compress\",\n  \"comment\",\n  \"escape\",\n  \"noescape\",\n  \"function\",\n  \"if\",\n  \"list\",\n  \"items\",\n  \"sep\",\n  \"macro\",\n  \"noparse\",\n  \"noParse\",\n  \"noautoesc\",\n  \"noAutoEsc\",\n  \"outputformat\",\n  \"switch\",\n  \"visit\",\n  \"recurse\"\n];\nvar TagSyntaxAngle = {\n  close: \">\",\n  id: \"angle\",\n  open: \"<\"\n};\nvar TagSyntaxBracket = {\n  close: \"\\\\]\",\n  id: \"bracket\",\n  open: \"\\\\[\"\n};\nvar TagSyntaxAuto = {\n  close: \"[>\\\\]]\",\n  id: \"auto\",\n  open: \"[<\\\\[]\"\n};\nvar InterpolationSyntaxDollar = {\n  close: \"\\\\}\",\n  id: \"dollar\",\n  open1: \"\\\\$\",\n  open2: \"\\\\{\"\n};\nvar InterpolationSyntaxBracket = {\n  close: \"\\\\]\",\n  id: \"bracket\",\n  open1: \"\\\\[\",\n  open2: \"=\"\n};\nfunction createLangConfiguration(ts) {\n  return {\n    brackets: [\n      [\"<\", \">\"],\n      [\"[\", \"]\"],\n      [\"(\", \")\"],\n      [\"{\", \"}\"]\n    ],\n    comments: {\n      blockComment: [`${ts.open}--`, `--${ts.close}`]\n    },\n    autoCloseBefore: \"\\n\\r\t }]),.:;=\",\n    autoClosingPairs: [\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: '\"', close: '\"', notIn: [\"string\"] },\n      { open: \"'\", close: \"'\", notIn: [\"string\"] }\n    ],\n    surroundingPairs: [\n      { open: '\"', close: '\"' },\n      { open: \"'\", close: \"'\" },\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: \"<\", close: \">\" }\n    ],\n    folding: {\n      markers: {\n        start: new RegExp(\n          `${ts.open}#(?:${BLOCK_ELEMENTS.join(\"|\")})([^/${ts.close}]*(?!/)${ts.close})[^${ts.open}]*$`\n        ),\n        end: new RegExp(`${ts.open}/#(?:${BLOCK_ELEMENTS.join(\"|\")})[\\\\r\\\\n\\\\t ]*>`)\n      }\n    },\n    onEnterRules: [\n      {\n        beforeText: new RegExp(\n          `${ts.open}#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/${ts.close}]*(?!/)${ts.close})[^${ts.open}]*$`\n        ),\n        afterText: new RegExp(`^${ts.open}/#([a-zA-Z_]+)[\\\\r\\\\n\\\\t ]*${ts.close}$`),\n        action: {\n          indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n        }\n      },\n      {\n        beforeText: new RegExp(\n          `${ts.open}#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/${ts.close}]*(?!/)${ts.close})[^${ts.open}]*$`\n        ),\n        action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n      }\n    ]\n  };\n}\nfunction createLangConfigurationAuto() {\n  return {\n    // Cannot set block comment delimiter in auto mode...\n    // It depends on the content and the cursor position of the file...\n    brackets: [\n      [\"<\", \">\"],\n      [\"[\", \"]\"],\n      [\"(\", \")\"],\n      [\"{\", \"}\"]\n    ],\n    autoCloseBefore: \"\\n\\r\t }]),.:;=\",\n    autoClosingPairs: [\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: '\"', close: '\"', notIn: [\"string\"] },\n      { open: \"'\", close: \"'\", notIn: [\"string\"] }\n    ],\n    surroundingPairs: [\n      { open: '\"', close: '\"' },\n      { open: \"'\", close: \"'\" },\n      { open: \"{\", close: \"}\" },\n      { open: \"[\", close: \"]\" },\n      { open: \"(\", close: \")\" },\n      { open: \"<\", close: \">\" }\n    ],\n    folding: {\n      markers: {\n        start: new RegExp(`[<\\\\[]#(?:${BLOCK_ELEMENTS.join(\"|\")})([^/>\\\\]]*(?!/)[>\\\\]])[^<\\\\[]*$`),\n        end: new RegExp(`[<\\\\[]/#(?:${BLOCK_ELEMENTS.join(\"|\")})[\\\\r\\\\n\\\\t ]*>`)\n      }\n    },\n    onEnterRules: [\n      {\n        beforeText: new RegExp(\n          `[<\\\\[]#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/>\\\\]]*(?!/)[>\\\\]])[^[<\\\\[]]*$`\n        ),\n        afterText: new RegExp(`^[<\\\\[]/#([a-zA-Z_]+)[\\\\r\\\\n\\\\t ]*[>\\\\]]$`),\n        action: {\n          indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n        }\n      },\n      {\n        beforeText: new RegExp(\n          `[<\\\\[]#(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))([a-zA-Z_]+)([^/>\\\\]]*(?!/)[>\\\\]])[^[<\\\\[]]*$`\n        ),\n        action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n      }\n    ]\n  };\n}\nfunction createMonarchLanguage(ts, is) {\n  const id = `_${ts.id}_${is.id}`;\n  const s = (name) => name.replace(/__id__/g, id);\n  const r = (regexp) => {\n    const source = regexp.source.replace(/__id__/g, id);\n    return new RegExp(source, regexp.flags);\n  };\n  return {\n    // Settings\n    unicode: true,\n    includeLF: false,\n    start: s(\"default__id__\"),\n    ignoreCase: false,\n    defaultToken: \"invalid\",\n    tokenPostfix: `.freemarker2`,\n    brackets: [\n      { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n      { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n      { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n      { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n    ],\n    // Dynamic RegExp\n    [s(\"open__id__\")]: new RegExp(ts.open),\n    [s(\"close__id__\")]: new RegExp(ts.close),\n    [s(\"iOpen1__id__\")]: new RegExp(is.open1),\n    [s(\"iOpen2__id__\")]: new RegExp(is.open2),\n    [s(\"iClose__id__\")]: new RegExp(is.close),\n    // <#START_TAG : \"<\" | \"<#\" | \"[#\">\n    // <#END_TAG : \"</\" | \"</#\" | \"[/#\">\n    [s(\"startTag__id__\")]: r(/(@open__id__)(#)/),\n    [s(\"endTag__id__\")]: r(/(@open__id__)(\\/#)/),\n    [s(\"startOrEndTag__id__\")]: r(/(@open__id__)(\\/?#)/),\n    // <#CLOSE_TAG1 : (<BLANK>)* (\">\" | \"]\")>\n    [s(\"closeTag1__id__\")]: r(/((?:@blank)*)(@close__id__)/),\n    // <#CLOSE_TAG2 : (<BLANK>)* (\"/\")? (\">\" | \"]\")>\n    [s(\"closeTag2__id__\")]: r(/((?:@blank)*\\/?)(@close__id__)/),\n    // Static RegExp\n    // <#BLANK : \" \" | \"\\t\" | \"\\n\" | \"\\r\">\n    blank: /[ \\t\\n\\r]/,\n    // <FALSE : \"false\">\n    // <TRUE : \"true\">\n    // <IN : \"in\">\n    // <AS : \"as\">\n    // <USING : \"using\">\n    keywords: [\"false\", \"true\", \"in\", \"as\", \"using\"],\n    // Directive names that cannot have an expression parameters and cannot be self-closing\n    // E.g. <#if id==2> ... </#if>\n    directiveStartCloseTag1: /attempt|recover|sep|auto[eE]sc|no(?:autoe|AutoE)sc|compress|default|no[eE]scape|comment|no[pP]arse/,\n    // Directive names that cannot have an expression parameter and can be self-closing\n    // E.g. <#if> ... <#else>  ... </#if>\n    // E.g. <#if> ... <#else /></#if>\n    directiveStartCloseTag2: /else|break|continue|return|stop|flush|t|lt|rt|nt|nested|recurse|fallback|ftl/,\n    // Directive names that can have an expression parameter and cannot be self-closing\n    // E.g. <#if id==2> ... </#if>\n    directiveStartBlank: /if|else[iI]f|list|for[eE]ach|switch|case|assign|global|local|include|import|function|macro|transform|visit|stop|return|call|setting|output[fF]ormat|nested|recurse|escape|ftl|items/,\n    // Directive names that can have an end tag\n    // E.g. </#if>\n    directiveEndCloseTag1: /if|list|items|sep|recover|attempt|for[eE]ach|local|global|assign|function|macro|output[fF]ormat|auto[eE]sc|no(?:autoe|AutoE)sc|compress|transform|switch|escape|no[eE]scape/,\n    // <#ESCAPED_CHAR :\n    //     \"\\\\\"\n    //     (\n    //         (\"n\" | \"t\" | \"r\" | \"f\" | \"b\" | \"g\" | \"l\" | \"a\" | \"\\\\\" | \"'\" | \"\\\"\" | \"{\" | \"=\")\n    //         |\n    //         (\"x\" [\"0\"-\"9\", \"A\"-\"F\", \"a\"-\"f\"])\n    //     )\n    // >\n    // Note: While the JavaCC tokenizer rule only specifies one hex digit,\n    // FreeMarker actually interprets up to 4 hex digits.\n    escapedChar: /\\\\(?:[ntrfbgla\\\\'\"\\{=]|(?:x[0-9A-Fa-f]{1,4}))/,\n    // <#ASCII_DIGIT: [\"0\" - \"9\"]>\n    asciiDigit: /[0-9]/,\n    // <INTEGER : ([\"0\"-\"9\"])+>\n    integer: /[0-9]+/,\n    // <#NON_ESCAPED_ID_START_CHAR:\n    // [\n    // \t  // This was generated on JDK 1.8.0_20 Win64 with src/main/misc/identifierChars/IdentifierCharGenerator.java\n    //    ...\n    // ]\n    nonEscapedIdStartChar: /[\\$@-Z_a-z\\u00AA\\u00B5\\u00BA\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u1FFF\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183-\\u2184\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3006\\u3031-\\u3035\\u303B-\\u303C\\u3040-\\u318F\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3300-\\u337F\\u3400-\\u4DB5\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66E\\uA67F-\\uA697\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA793\\uA7A0-\\uA7AA\\uA7F8-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8D0-\\uA8D9\\uA8F2-\\uA8F7\\uA8FB\\uA900-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF-\\uA9D9\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A\\uAA80-\\uAAAF\\uAAB1\\uAAB5-\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uABC0-\\uABE2\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40-\\uFB41\\uFB43-\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]/,\n    // <#ESCAPED_ID_CHAR: \"\\\\\" (\"-\" | \".\" | \":\" | \"#\")>\n    escapedIdChar: /\\\\[\\-\\.:#]/,\n    // <#ID_START_CHAR: <NON_ESCAPED_ID_START_CHAR>|<ESCAPED_ID_CHAR>>\n    idStartChar: /(?:@nonEscapedIdStartChar)|(?:@escapedIdChar)/,\n    // <ID: <ID_START_CHAR> (<ID_START_CHAR>|<ASCII_DIGIT>)*>\n    id: /(?:@idStartChar)(?:(?:@idStartChar)|(?:@asciiDigit))*/,\n    // Certain keywords / operators are allowed to index hashes\n    //\n    // Expression DotVariable(Expression exp) :\n    // {\n    // \tToken t;\n    // }\n    // {\n    // \t\t<DOT>\n    // \t\t(\n    // \t\t\tt = <ID> | t = <TIMES> | t = <DOUBLE_STAR>\n    // \t\t\t|\n    // \t\t\t(\n    // \t\t\t\tt = <LESS_THAN>\n    // \t\t\t\t|\n    // \t\t\t\tt = <LESS_THAN_EQUALS>\n    // \t\t\t\t|\n    // \t\t\t\tt = <ESCAPED_GT>\n    // \t\t\t\t|\n    // \t\t\t\tt = <ESCAPED_GTE>\n    // \t\t\t\t|\n    // \t\t\t\tt = <FALSE>\n    // \t\t\t\t|\n    // \t\t\t\tt = <TRUE>\n    // \t\t\t\t|\n    // \t\t\t\tt = <IN>\n    // \t\t\t\t|\n    // \t\t\t\tt = <AS>\n    // \t\t\t\t|\n    // \t\t\t\tt = <USING>\n    // \t\t\t)\n    // \t\t\t{\n    // \t\t\t\tif (!Character.isLetter(t.image.charAt(0))) {\n    // \t\t\t\t\tthrow new ParseException(t.image + \" is not a valid identifier.\", template, t);\n    // \t\t\t\t}\n    // \t\t\t}\n    // \t\t)\n    // \t\t{\n    // \t\t\tnotListLiteral(exp, \"hash\");\n    // \t\t\tnotStringLiteral(exp, \"hash\");\n    // \t\t\tnotBooleanLiteral(exp, \"hash\");\n    // \t\t\tDot dot = new Dot(exp, t.image);\n    // \t\t\tdot.setLocation(template, exp, t);\n    // \t\t\treturn dot;\n    // \t\t}\n    // }\n    specialHashKeys: /\\*\\*|\\*|false|true|in|as|using/,\n    // <DOUBLE_EQUALS : \"==\">\n    // <EQUALS : \"=\">\n    // <NOT_EQUALS : \"!=\">\n    // <PLUS_EQUALS : \"+=\">\n    // <MINUS_EQUALS : \"-=\">\n    // <TIMES_EQUALS : \"*=\">\n    // <DIV_EQUALS : \"/=\">\n    // <MOD_EQUALS : \"%=\">\n    // <PLUS_PLUS : \"++\">\n    // <MINUS_MINUS : \"--\">\n    // <LESS_THAN_EQUALS : \"lte\" | \"\\\\lte\" | \"<=\" | \"&lt;=\">\n    // <LESS_THAN : \"lt\" | \"\\\\lt\" | \"<\" | \"&lt;\">\n    // <ESCAPED_GTE : \"gte\" | \"\\\\gte\" | \"&gt;=\">\n    // <ESCAPED_GT: \"gt\" | \"\\\\gt\" |  \"&gt;\">\n    // <DOUBLE_STAR : \"**\">\n    // <PLUS : \"+\">\n    // <MINUS : \"-\">\n    // <TIMES : \"*\">\n    // <PERCENT : \"%\">\n    // <AND : \"&\" | \"&&\" | \"&amp;&amp;\" | \"\\\\and\" >\n    // <OR : \"|\" | \"||\">\n    // <EXCLAM : \"!\">\n    // <COMMA : \",\">\n    // <SEMICOLON : \";\">\n    // <COLON : \":\">\n    // <ELLIPSIS : \"...\">\n    // <DOT_DOT_ASTERISK : \"..*\" >\n    // <DOT_DOT_LESS : \"..<\" | \"..!\" >\n    // <DOT_DOT : \"..\">\n    // <EXISTS : \"??\">\n    // <BUILT_IN : \"?\">\n    // <LAMBDA_ARROW : \"->\" | \"-&gt;\">\n    namedSymbols: /&lt;=|&gt;=|\\\\lte|\\\\lt|&lt;|\\\\gte|\\\\gt|&gt;|&amp;&amp;|\\\\and|-&gt;|->|==|!=|\\+=|-=|\\*=|\\/=|%=|\\+\\+|--|<=|&&|\\|\\||:|\\.\\.\\.|\\.\\.\\*|\\.\\.<|\\.\\.!|\\?\\?|=|<|\\+|-|\\*|\\/|%|\\||\\.\\.|\\?|!|&|\\.|,|;/,\n    arrows: [\"->\", \"-&gt;\"],\n    delimiters: [\";\", \":\", \",\", \".\"],\n    stringOperators: [\"lte\", \"lt\", \"gte\", \"gt\"],\n    noParseTags: [\"noparse\", \"noParse\", \"comment\"],\n    tokenizer: {\n      // Parser states\n      // Plain text\n      [s(\"default__id__\")]: [\n        { include: s(\"@directive_token__id__\") },\n        { include: s(\"@interpolation_and_text_token__id__\") }\n      ],\n      // A FreeMarker expression inside a directive, e.g. <#if 2<3>\n      [s(\"fmExpression__id__.directive\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@directive_end_token__id__\") },\n        { include: s(\"@expression_token__id__\") }\n      ],\n      // A FreeMarker expression inside an interpolation, e.g. ${2+3}\n      [s(\"fmExpression__id__.interpolation\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@expression_token__id__\") },\n        { include: s(\"@greater_operators_token__id__\") }\n      ],\n      // In an expression and inside a not-yet closed parenthesis / bracket\n      [s(\"inParen__id__.plain\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@directive_end_token__id__\") },\n        { include: s(\"@expression_token__id__\") }\n      ],\n      [s(\"inParen__id__.gt\")]: [\n        { include: s(\"@blank_and_expression_comment_token__id__\") },\n        { include: s(\"@expression_token__id__\") },\n        { include: s(\"@greater_operators_token__id__\") }\n      ],\n      // Expression for the unified call, e.g. <@createMacro() ... >\n      [s(\"noSpaceExpression__id__\")]: [\n        { include: s(\"@no_space_expression_end_token__id__\") },\n        { include: s(\"@directive_end_token__id__\") },\n        { include: s(\"@expression_token__id__\") }\n      ],\n      // For the function of a unified call. Special case for when the\n      // expression is a simple identifier.\n      // <@join [1,2] \",\">\n      // <@null!join [1,2] \",\">\n      [s(\"unifiedCall__id__\")]: [{ include: s(\"@unified_call_token__id__\") }],\n      // For singly and doubly quoted string (that may contain interpolations)\n      [s(\"singleString__id__\")]: [{ include: s(\"@string_single_token__id__\") }],\n      [s(\"doubleString__id__\")]: [{ include: s(\"@string_double_token__id__\") }],\n      // For singly and doubly quoted string (that may not contain interpolations)\n      [s(\"rawSingleString__id__\")]: [{ include: s(\"@string_single_raw_token__id__\") }],\n      [s(\"rawDoubleString__id__\")]: [{ include: s(\"@string_double_raw_token__id__\") }],\n      // For a comment in an expression\n      // ${ 1 + <#-- comment --> 2}\n      [s(\"expressionComment__id__\")]: [{ include: s(\"@expression_comment_token__id__\") }],\n      // For <#noparse> ... </#noparse>\n      // For <#noParse> ... </#noParse>\n      // For <#comment> ... </#comment>\n      [s(\"noParse__id__\")]: [{ include: s(\"@no_parse_token__id__\") }],\n      // For <#-- ... -->\n      [s(\"terseComment__id__\")]: [{ include: s(\"@terse_comment_token__id__\") }],\n      // Common rules\n      [s(\"directive_token__id__\")]: [\n        // <ATTEMPT : <START_TAG> \"attempt\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <RECOVER : <START_TAG> \"recover\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SEP : <START_TAG> \"sep\" <CLOSE_TAG1>>\n        // <AUTOESC : <START_TAG> \"auto\" (\"e\"|\"E\") \"sc\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 4), DEFAULT);\n        // }\n        // <NOAUTOESC : <START_TAG> \"no\" (\"autoe\"|\"AutoE\") \"sc\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        // <COMPRESS : <START_TAG> \"compress\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <DEFAUL : <START_TAG> \"default\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <NOESCAPE : <START_TAG> \"no\" (\"e\" | \"E\") \"scape\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        //\n        // <COMMENT : <START_TAG> \"comment\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, NO_PARSE); noparseTag = \"comment\";\n        // }\n        // <NOPARSE: <START_TAG> \"no\" (\"p\" | \"P\") \"arse\" <CLOSE_TAG1>> {\n        //     int tagNamingConvention = getTagNamingConvention(matchedToken, 2);\n        //     handleTagSyntaxAndSwitch(matchedToken, tagNamingConvention, NO_PARSE);\n        //     noparseTag = tagNamingConvention == Configuration.CAMEL_CASE_NAMING_CONVENTION ? \"noParse\" : \"noparse\";\n        // }\n        [\n          r(/(?:@startTag__id__)(@directiveStartCloseTag1)(?:@closeTag1__id__)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            {\n              cases: {\n                \"@noParseTags\": { token: \"tag\", next: s(\"@noParse__id__.$3\") },\n                \"@default\": { token: \"tag\" }\n              }\n            },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <ELSE : <START_TAG> \"else\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <BREAK : <START_TAG> \"break\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <CONTINUE : <START_TAG> \"continue\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SIMPLE_RETURN : <START_TAG> \"return\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <HALT : <START_TAG> \"stop\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <FLUSH : <START_TAG> \"flush\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <TRIM : <START_TAG> \"t\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <LTRIM : <START_TAG> \"lt\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <RTRIM : <START_TAG> \"rt\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <NOTRIM : <START_TAG> \"nt\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SIMPLE_NESTED : <START_TAG> \"nested\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <SIMPLE_RECURSE : <START_TAG> \"recurse\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <FALLBACK : <START_TAG> \"fallback\" <CLOSE_TAG2>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <TRIVIAL_FTL_HEADER : (\"<#ftl\" | \"[#ftl\") (\"/\")? (\">\" | \"]\")> { ftlHeader(matchedToken); }\n        [\n          r(/(?:@startTag__id__)(@directiveStartCloseTag2)(?:@closeTag2__id__)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <IF : <START_TAG> \"if\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <ELSE_IF : <START_TAG> \"else\" (\"i\" | \"I\") \"f\" <BLANK>> {\n        // \thandleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 4), FM_EXPRESSION);\n        // }\n        // <LIST : <START_TAG> \"list\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <FOREACH : <START_TAG> \"for\" (\"e\" | \"E\") \"ach\" <BLANK>> {\n        //    handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 3), FM_EXPRESSION);\n        // }\n        // <SWITCH : <START_TAG> \"switch\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <CASE : <START_TAG> \"case\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <ASSIGN : <START_TAG> \"assign\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <GLOBALASSIGN : <START_TAG> \"global\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <LOCALASSIGN : <START_TAG> \"local\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <_INCLUDE : <START_TAG> \"include\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <IMPORT : <START_TAG> \"import\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <FUNCTION : <START_TAG> \"function\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <MACRO : <START_TAG> \"macro\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <TRANSFORM : <START_TAG> \"transform\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <VISIT : <START_TAG> \"visit\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <STOP : <START_TAG> \"stop\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <RETURN : <START_TAG> \"return\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <CALL : <START_TAG> \"call\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <SETTING : <START_TAG> \"setting\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <OUTPUTFORMAT : <START_TAG> \"output\" (\"f\"|\"F\") \"ormat\" <BLANK>> {\n        //    handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 6), FM_EXPRESSION);\n        // }\n        // <NESTED : <START_TAG> \"nested\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <RECURSE : <START_TAG> \"recurse\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        // <ESCAPE : <START_TAG> \"escape\" <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        //\n        // Note: FreeMarker grammar appears to treat the FTL header as a special case,\n        // in order to remove new lines after the header (?), but since we only need\n        // to tokenize for highlighting, we can include this directive here.\n        // <FTL_HEADER : (\"<#ftl\" | \"[#ftl\") <BLANK>> { ftlHeader(matchedToken); }\n        //\n        // Note: FreeMarker grammar appears to treat the items directive as a special case for\n        // the AST parsing process, but since we only need to tokenize, we can include this\n        // directive here.\n        // <ITEMS : <START_TAG> \"items\" (<BLANK>)+ <AS> <BLANK>> { handleTagSyntaxAndSwitch(matchedToken, FM_EXPRESSION); }\n        [\n          r(/(?:@startTag__id__)(@directiveStartBlank)(@blank)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"\", next: s(\"@fmExpression__id__.directive\") }\n          ]\n        ],\n        // <END_IF : <END_TAG> \"if\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_LIST : <END_TAG> \"list\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_SEP : <END_TAG> \"sep\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_RECOVER : <END_TAG> \"recover\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_ATTEMPT : <END_TAG> \"attempt\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_FOREACH : <END_TAG> \"for\" (\"e\" | \"E\") \"ach\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 3), DEFAULT);\n        // }\n        // <END_LOCAL : <END_TAG> \"local\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_GLOBAL : <END_TAG> \"global\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_ASSIGN : <END_TAG> \"assign\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_FUNCTION : <END_TAG> \"function\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_MACRO : <END_TAG> \"macro\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_OUTPUTFORMAT : <END_TAG> \"output\" (\"f\" | \"F\") \"ormat\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 6), DEFAULT);\n        // }\n        // <END_AUTOESC : <END_TAG> \"auto\" (\"e\" | \"E\") \"sc\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 4), DEFAULT);\n        // }\n        // <END_NOAUTOESC : <END_TAG> \"no\" (\"autoe\"|\"AutoE\") \"sc\" <CLOSE_TAG1>> {\n        //   handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        // <END_COMPRESS : <END_TAG> \"compress\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_TRANSFORM : <END_TAG> \"transform\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_SWITCH : <END_TAG> \"switch\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_ESCAPE : <END_TAG> \"escape\" <CLOSE_TAG1>> { handleTagSyntaxAndSwitch(matchedToken, DEFAULT); }\n        // <END_NOESCAPE : <END_TAG> \"no\" (\"e\" | \"E\") \"scape\" <CLOSE_TAG1>> {\n        //     handleTagSyntaxAndSwitch(matchedToken, getTagNamingConvention(matchedToken, 2), DEFAULT);\n        // }\n        [\n          r(/(?:@endTag__id__)(@directiveEndCloseTag1)(?:@closeTag1__id__)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <UNIFIED_CALL : \"<@\" | \"[@\" > { unifiedCall(matchedToken); }\n        [\n          r(/(@open__id__)(@)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\", next: s(\"@unifiedCall__id__\") }\n          ]\n        ],\n        // <UNIFIED_CALL_END : (\"<\" | \"[\") \"/@\" ((<ID>) (\".\"<ID>)*)? <CLOSE_TAG1>> { unifiedCallEnd(matchedToken); }\n        [\n          r(/(@open__id__)(\\/@)((?:(?:@id)(?:\\.(?:@id))*)?)(?:@closeTag1__id__)/),\n          [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\" }\n          ]\n        ],\n        // <TERSE_COMMENT : (\"<\" | \"[\") \"#--\" > { noparseTag = \"-->\"; handleTagSyntaxAndSwitch(matchedToken, NO_PARSE); }\n        [\n          r(/(@open__id__)#--/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : { token: \"comment\", next: s(\"@terseComment__id__\") }\n        ],\n        // <UNKNOWN_DIRECTIVE : (\"[#\" | \"[/#\" | \"<#\" | \"</#\") ([\"a\"-\"z\", \"A\"-\"Z\", \"_\"])+>\n        [\n          r(/(?:@startOrEndTag__id__)([a-zA-Z_]+)/),\n          ts.id === \"auto\" ? {\n            cases: {\n              \"$1==<\": { token: \"@rematch\", switchTo: `@default_angle_${is.id}` },\n              \"$1==[\": { token: \"@rematch\", switchTo: `@default_bracket_${is.id}` }\n            }\n          } : [\n            { token: \"@brackets.directive\" },\n            { token: \"delimiter.directive\" },\n            { token: \"tag.invalid\", next: s(\"@fmExpression__id__.directive\") }\n          ]\n        ]\n      ],\n      // <DEFAULT, NO_DIRECTIVE> TOKEN :\n      [s(\"interpolation_and_text_token__id__\")]: [\n        // <DOLLAR_INTERPOLATION_OPENING : \"${\"> { startInterpolation(matchedToken); }\n        // <SQUARE_BRACKET_INTERPOLATION_OPENING : \"[=\"> { startInterpolation(matchedToken); }\n        [\n          r(/(@iOpen1__id__)(@iOpen2__id__)/),\n          [\n            { token: is.id === \"bracket\" ? \"@brackets.interpolation\" : \"delimiter.interpolation\" },\n            {\n              token: is.id === \"bracket\" ? \"delimiter.interpolation\" : \"@brackets.interpolation\",\n              next: s(\"@fmExpression__id__.interpolation\")\n            }\n          ]\n        ],\n        // <STATIC_TEXT_FALSE_ALARM : \"$\" | \"#\" | \"<\" | \"[\" | \"{\"> // to handle a lone dollar sign or \"<\" or \"# or <@ with whitespace after\"\n        // <STATIC_TEXT_WS : (\"\\n\" | \"\\r\" | \"\\t\" | \" \")+>\n        // <STATIC_TEXT_NON_WS : (~[\"$\", \"<\", \"#\", \"[\", \"{\", \"\\n\", \"\\r\", \"\\t\", \" \"])+>\n        [/[\\$#<\\[\\{]|(?:@blank)+|[^\\$<#\\[\\{\\n\\r\\t ]+/, { token: \"source\" }]\n      ],\n      // <STRING_LITERAL :\n      // \t(\n      // \t\t\"\\\"\"\n      // \t\t((~[\"\\\"\", \"\\\\\"]) | <ESCAPED_CHAR>)*\n      // \t\t\"\\\"\"\n      // \t)\n      // \t|\n      // \t(\n      // \t\t\"'\"\n      // \t\t((~[\"'\", \"\\\\\"]) | <ESCAPED_CHAR>)*\n      // \t\t\"'\"\n      // \t)\n      // >\n      [s(\"string_single_token__id__\")]: [\n        [/[^'\\\\]/, { token: \"string\" }],\n        [/@escapedChar/, { token: \"string.escape\" }],\n        [/'/, { token: \"string\", next: \"@pop\" }]\n      ],\n      [s(\"string_double_token__id__\")]: [\n        [/[^\"\\\\]/, { token: \"string\" }],\n        [/@escapedChar/, { token: \"string.escape\" }],\n        [/\"/, { token: \"string\", next: \"@pop\" }]\n      ],\n      // <RAW_STRING : \"r\" ((\"\\\"\" (~[\"\\\"\"])* \"\\\"\") | (\"'\" (~[\"'\"])* \"'\"))>\n      [s(\"string_single_raw_token__id__\")]: [\n        [/[^']+/, { token: \"string.raw\" }],\n        [/'/, { token: \"string.raw\", next: \"@pop\" }]\n      ],\n      [s(\"string_double_raw_token__id__\")]: [\n        [/[^\"]+/, { token: \"string.raw\" }],\n        [/\"/, { token: \"string.raw\", next: \"@pop\" }]\n      ],\n      // <FM_EXPRESSION, IN_PAREN, NO_SPACE_EXPRESSION, NAMED_PARAMETER_EXPRESSION> TOKEN :\n      [s(\"expression_token__id__\")]: [\n        // Strings\n        [\n          /(r?)(['\"])/,\n          {\n            cases: {\n              \"r'\": [\n                { token: \"keyword\" },\n                { token: \"string.raw\", next: s(\"@rawSingleString__id__\") }\n              ],\n              'r\"': [\n                { token: \"keyword\" },\n                { token: \"string.raw\", next: s(\"@rawDoubleString__id__\") }\n              ],\n              \"'\": [{ token: \"source\" }, { token: \"string\", next: s(\"@singleString__id__\") }],\n              '\"': [{ token: \"source\" }, { token: \"string\", next: s(\"@doubleString__id__\") }]\n            }\n          }\n        ],\n        // Numbers\n        // <INTEGER : ([\"0\"-\"9\"])+>\n        // <DECIMAL : <INTEGER> \".\" <INTEGER>>\n        [\n          /(?:@integer)(?:\\.(?:@integer))?/,\n          {\n            cases: {\n              \"(?:@integer)\": { token: \"number\" },\n              \"@default\": { token: \"number.float\" }\n            }\n          }\n        ],\n        // Special hash keys that must not be treated as identifiers\n        // after a period, e.g. a.** is accessing the key \"**\" of a\n        [\n          /(\\.)(@blank*)(@specialHashKeys)/,\n          [{ token: \"delimiter\" }, { token: \"\" }, { token: \"identifier\" }]\n        ],\n        // Symbols / operators\n        [\n          /(?:@namedSymbols)/,\n          {\n            cases: {\n              \"@arrows\": { token: \"meta.arrow\" },\n              \"@delimiters\": { token: \"delimiter\" },\n              \"@default\": { token: \"operators\" }\n            }\n          }\n        ],\n        // Identifiers\n        [\n          /@id/,\n          {\n            cases: {\n              \"@keywords\": { token: \"keyword.$0\" },\n              \"@stringOperators\": { token: \"operators\" },\n              \"@default\": { token: \"identifier\" }\n            }\n          }\n        ],\n        // <OPEN_BRACKET : \"[\">\n        // <CLOSE_BRACKET : \"]\">\n        // <OPEN_PAREN : \"(\">\n        // <CLOSE_PAREN : \")\">\n        // <OPENING_CURLY_BRACKET : \"{\">\n        // <CLOSING_CURLY_BRACKET : \"}\">\n        [\n          /[\\[\\]\\(\\)\\{\\}]/,\n          {\n            cases: {\n              \"\\\\[\": {\n                cases: {\n                  \"$S2==gt\": { token: \"@brackets\", next: s(\"@inParen__id__.gt\") },\n                  \"@default\": { token: \"@brackets\", next: s(\"@inParen__id__.plain\") }\n                }\n              },\n              \"\\\\]\": {\n                cases: {\n                  ...is.id === \"bracket\" ? {\n                    \"$S2==interpolation\": { token: \"@brackets.interpolation\", next: \"@popall\" }\n                  } : {},\n                  // This cannot happen while in auto mode, since this applies only to an\n                  // fmExpression inside a directive. But once we encounter the start of a\n                  // directive, we can establish the tag syntax mode.\n                  ...ts.id === \"bracket\" ? {\n                    \"$S2==directive\": { token: \"@brackets.directive\", next: \"@popall\" }\n                  } : {},\n                  // Ignore mismatched paren\n                  [s(\"$S1==inParen__id__\")]: { token: \"@brackets\", next: \"@pop\" },\n                  \"@default\": { token: \"@brackets\" }\n                }\n              },\n              \"\\\\(\": { token: \"@brackets\", next: s(\"@inParen__id__.gt\") },\n              \"\\\\)\": {\n                cases: {\n                  [s(\"$S1==inParen__id__\")]: { token: \"@brackets\", next: \"@pop\" },\n                  \"@default\": { token: \"@brackets\" }\n                }\n              },\n              \"\\\\{\": {\n                cases: {\n                  \"$S2==gt\": { token: \"@brackets\", next: s(\"@inParen__id__.gt\") },\n                  \"@default\": { token: \"@brackets\", next: s(\"@inParen__id__.plain\") }\n                }\n              },\n              \"\\\\}\": {\n                cases: {\n                  ...is.id === \"bracket\" ? {} : {\n                    \"$S2==interpolation\": { token: \"@brackets.interpolation\", next: \"@popall\" }\n                  },\n                  // Ignore mismatched paren\n                  [s(\"$S1==inParen__id__\")]: { token: \"@brackets\", next: \"@pop\" },\n                  \"@default\": { token: \"@brackets\" }\n                }\n              }\n            }\n          }\n        ],\n        // <OPEN_MISPLACED_INTERPOLATION : \"${\" | \"#{\" | \"[=\">\n        [/\\$\\{/, { token: \"delimiter.invalid\" }]\n      ],\n      // <FM_EXPRESSION, IN_PAREN, NAMED_PARAMETER_EXPRESSION> SKIP :\n      [s(\"blank_and_expression_comment_token__id__\")]: [\n        // < ( \" \" | \"\\t\" | \"\\n\" | \"\\r\" )+ >\n        [/(?:@blank)+/, { token: \"\" }],\n        // < (\"<\" | \"[\") (\"#\" | \"!\") \"--\"> : EXPRESSION_COMMENT\n        [/[<\\[][#!]--/, { token: \"comment\", next: s(\"@expressionComment__id__\") }]\n      ],\n      // <FM_EXPRESSION, NO_SPACE_EXPRESSION, NAMED_PARAMETER_EXPRESSION> TOKEN :\n      [s(\"directive_end_token__id__\")]: [\n        // <DIRECTIVE_END : \">\">\n        // {\n        //     if (inFTLHeader) {\n        //         eatNewline();\n        //         inFTLHeader = false;\n        //     }\n        //     if (squBracTagSyntax || postInterpolationLexState != -1 /* We are in an interpolation */) {\n        //         matchedToken.kind = NATURAL_GT;\n        //     } else {\n        //         SwitchTo(DEFAULT);\n        //     }\n        // }\n        // This cannot happen while in auto mode, since this applies only to an\n        // fmExpression inside a directive. But once we encounter the start of a\n        // directive, we can establish the tag syntax mode.\n        [\n          />/,\n          ts.id === \"bracket\" ? { token: \"operators\" } : { token: \"@brackets.directive\", next: \"@popall\" }\n        ],\n        // <EMPTY_DIRECTIVE_END : \"/>\" | \"/]\">\n        // It is a syntax error to end a tag with the wrong close token\n        // Let's indicate that to the user by not closing the tag\n        [\n          r(/(\\/)(@close__id__)/),\n          [{ token: \"delimiter.directive\" }, { token: \"@brackets.directive\", next: \"@popall\" }]\n        ]\n      ],\n      // <IN_PAREN> TOKEN :\n      [s(\"greater_operators_token__id__\")]: [\n        // <NATURAL_GT : \">\">\n        [/>/, { token: \"operators\" }],\n        // <NATURAL_GTE : \">=\">\n        [/>=/, { token: \"operators\" }]\n      ],\n      // <NO_SPACE_EXPRESSION> TOKEN :\n      [s(\"no_space_expression_end_token__id__\")]: [\n        // <TERMINATING_WHITESPACE :  ([\"\\n\", \"\\r\", \"\\t\", \" \"])+> : FM_EXPRESSION\n        [/(?:@blank)+/, { token: \"\", switchTo: s(\"@fmExpression__id__.directive\") }]\n      ],\n      [s(\"unified_call_token__id__\")]: [\n        // Special case for a call where the expression is just an ID\n        // <UNIFIED_CALL> <ID> <BLANK>+\n        [\n          /(@id)((?:@blank)+)/,\n          [{ token: \"tag\" }, { token: \"\", next: s(\"@fmExpression__id__.directive\") }]\n        ],\n        [\n          r(/(@id)(\\/?)(@close__id__)/),\n          [\n            { token: \"tag\" },\n            { token: \"delimiter.directive\" },\n            { token: \"@brackets.directive\", next: \"@popall\" }\n          ]\n        ],\n        [/./, { token: \"@rematch\", next: s(\"@noSpaceExpression__id__\") }]\n      ],\n      // <NO_PARSE> TOKEN :\n      [s(\"no_parse_token__id__\")]: [\n        // <MAYBE_END :\n        // \t (\"<\" | \"[\")\n        // \t \"/\"\n        // \t (\"#\")?\n        // \t ([\"a\"-\"z\", \"A\"-\"Z\"])+\n        // \t ( \" \" | \"\\t\" | \"\\n\" | \"\\r\" )*\n        // \t (\">\" | \"]\")\n        // >\n        [\n          r(/(@open__id__)(\\/#?)([a-zA-Z]+)((?:@blank)*)(@close__id__)/),\n          {\n            cases: {\n              \"$S2==$3\": [\n                { token: \"@brackets.directive\" },\n                { token: \"delimiter.directive\" },\n                { token: \"tag\" },\n                { token: \"\" },\n                { token: \"@brackets.directive\", next: \"@popall\" }\n              ],\n              \"$S2==comment\": [\n                { token: \"comment\" },\n                { token: \"comment\" },\n                { token: \"comment\" },\n                { token: \"comment\" },\n                { token: \"comment\" }\n              ],\n              \"@default\": [\n                { token: \"source\" },\n                { token: \"source\" },\n                { token: \"source\" },\n                { token: \"source\" },\n                { token: \"source\" }\n              ]\n            }\n          }\n        ],\n        // <KEEP_GOING : (~[\"<\", \"[\", \"-\"])+>\n        // <LONE_LESS_THAN_OR_DASH : [\"<\", \"[\", \"-\"]>\n        [\n          /[^<\\[\\-]+|[<\\[\\-]/,\n          {\n            cases: {\n              \"$S2==comment\": { token: \"comment\" },\n              \"@default\": { token: \"source\" }\n            }\n          }\n        ]\n      ],\n      // <EXPRESSION_COMMENT> SKIP:\n      [s(\"expression_comment_token__id__\")]: [\n        // < \"-->\" | \"--]\">\n        [\n          /--[>\\]]/,\n          {\n            token: \"comment\",\n            next: \"@pop\"\n          }\n        ],\n        // < (~[\"-\", \">\", \"]\"])+ >\n        // < \">\">\n        // < \"]\">\n        // < \"-\">\n        [/[^\\->\\]]+|[>\\]\\-]/, { token: \"comment\" }]\n      ],\n      [s(\"terse_comment_token__id__\")]: [\n        //  <TERSE_COMMENT_END : \"-->\" | \"--]\">\n        [r(/--(?:@close__id__)/), { token: \"comment\", next: \"@popall\" }],\n        // <KEEP_GOING : (~[\"<\", \"[\", \"-\"])+>\n        // <LONE_LESS_THAN_OR_DASH : [\"<\", \"[\", \"-\"]>\n        [/[^<\\[\\-]+|[<\\[\\-]/, { token: \"comment\" }]\n      ]\n    }\n  };\n}\nfunction createMonarchLanguageAuto(is) {\n  const angle = createMonarchLanguage(TagSyntaxAngle, is);\n  const bracket = createMonarchLanguage(TagSyntaxBracket, is);\n  const auto = createMonarchLanguage(TagSyntaxAuto, is);\n  return {\n    // Angle and bracket syntax mode\n    // We switch to one of these once we have determined the mode\n    ...angle,\n    ...bracket,\n    ...auto,\n    // Settings\n    unicode: true,\n    includeLF: false,\n    start: `default_auto_${is.id}`,\n    ignoreCase: false,\n    defaultToken: \"invalid\",\n    tokenPostfix: `.freemarker2`,\n    brackets: [\n      { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n      { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n      { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n      { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n    ],\n    tokenizer: {\n      ...angle.tokenizer,\n      ...bracket.tokenizer,\n      ...auto.tokenizer\n    }\n  };\n}\nvar TagAngleInterpolationDollar = {\n  conf: createLangConfiguration(TagSyntaxAngle),\n  language: createMonarchLanguage(TagSyntaxAngle, InterpolationSyntaxDollar)\n};\nvar TagBracketInterpolationDollar = {\n  conf: createLangConfiguration(TagSyntaxBracket),\n  language: createMonarchLanguage(TagSyntaxBracket, InterpolationSyntaxDollar)\n};\nvar TagAngleInterpolationBracket = {\n  conf: createLangConfiguration(TagSyntaxAngle),\n  language: createMonarchLanguage(TagSyntaxAngle, InterpolationSyntaxBracket)\n};\nvar TagBracketInterpolationBracket = {\n  conf: createLangConfiguration(TagSyntaxBracket),\n  language: createMonarchLanguage(TagSyntaxBracket, InterpolationSyntaxBracket)\n};\nvar TagAutoInterpolationDollar = {\n  conf: createLangConfigurationAuto(),\n  language: createMonarchLanguageAuto(InterpolationSyntaxDollar)\n};\nvar TagAutoInterpolationBracket = {\n  conf: createLangConfigurationAuto(),\n  language: createMonarchLanguageAuto(InterpolationSyntaxBracket)\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/freemarker2/freemarker2.js\n"));

/***/ })

}]);