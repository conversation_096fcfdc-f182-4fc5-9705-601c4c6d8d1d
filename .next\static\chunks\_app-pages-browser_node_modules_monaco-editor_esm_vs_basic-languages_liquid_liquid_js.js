"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_liquid_liquid_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/liquid/liquid.js":
/*!****************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/liquid/liquid.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/editor.api.js?ce9f\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/basic-languages/liquid/liquid.ts\nvar EMPTY_ELEMENTS = [\n  \"area\",\n  \"base\",\n  \"br\",\n  \"col\",\n  \"embed\",\n  \"hr\",\n  \"img\",\n  \"input\",\n  \"keygen\",\n  \"link\",\n  \"menuitem\",\n  \"meta\",\n  \"param\",\n  \"source\",\n  \"track\",\n  \"wbr\"\n];\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n  brackets: [\n    [\"<!--\", \"-->\"],\n    [\"<\", \">\"],\n    [\"{{\", \"}}\"],\n    [\"{%\", \"%}\"],\n    [\"{\", \"}\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"%\", close: \"%\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      afterText: /^<\\/(\\w[\\w\\d]*)\\s*>$/i,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n      }\n    },\n    {\n      beforeText: new RegExp(\n        `<(?!(?:${EMPTY_ELEMENTS.join(\"|\")}))(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`,\n        \"i\"\n      ),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  builtinTags: [\n    \"if\",\n    \"else\",\n    \"elseif\",\n    \"endif\",\n    \"render\",\n    \"assign\",\n    \"capture\",\n    \"endcapture\",\n    \"case\",\n    \"endcase\",\n    \"comment\",\n    \"endcomment\",\n    \"cycle\",\n    \"decrement\",\n    \"for\",\n    \"endfor\",\n    \"include\",\n    \"increment\",\n    \"layout\",\n    \"raw\",\n    \"endraw\",\n    \"render\",\n    \"tablerow\",\n    \"endtablerow\",\n    \"unless\",\n    \"endunless\"\n  ],\n  builtinFilters: [\n    \"abs\",\n    \"append\",\n    \"at_least\",\n    \"at_most\",\n    \"capitalize\",\n    \"ceil\",\n    \"compact\",\n    \"date\",\n    \"default\",\n    \"divided_by\",\n    \"downcase\",\n    \"escape\",\n    \"escape_once\",\n    \"first\",\n    \"floor\",\n    \"join\",\n    \"json\",\n    \"last\",\n    \"lstrip\",\n    \"map\",\n    \"minus\",\n    \"modulo\",\n    \"newline_to_br\",\n    \"plus\",\n    \"prepend\",\n    \"remove\",\n    \"remove_first\",\n    \"replace\",\n    \"replace_first\",\n    \"reverse\",\n    \"round\",\n    \"rstrip\",\n    \"size\",\n    \"slice\",\n    \"sort\",\n    \"sort_natural\",\n    \"split\",\n    \"strip\",\n    \"strip_html\",\n    \"strip_newlines\",\n    \"times\",\n    \"truncate\",\n    \"truncatewords\",\n    \"uniq\",\n    \"upcase\",\n    \"url_decode\",\n    \"url_encode\",\n    \"where\"\n  ],\n  constants: [\"true\", \"false\"],\n  operators: [\"==\", \"!=\", \">\", \"<\", \">=\", \"<=\"],\n  symbol: /[=><!]+/,\n  identifier: /[a-zA-Z_][\\w]*/,\n  tokenizer: {\n    root: [\n      [/\\{\\%\\s*comment\\s*\\%\\}/, \"comment.start.liquid\", \"@comment\"],\n      [/\\{\\{/, { token: \"@rematch\", switchTo: \"@liquidState.root\" }],\n      [/\\{\\%/, { token: \"@rematch\", switchTo: \"@liquidState.root\" }],\n      [/(<)([\\w\\-]+)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"delimiter.html\"]],\n      [/(<)([:\\w]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)([\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/\\{/, \"delimiter.html\"],\n      [/[^<{]+/]\n      // text\n    ],\n    comment: [\n      [/\\{\\%\\s*endcomment\\s*\\%\\}/, \"comment.end.liquid\", \"@pop\"],\n      [/./, \"comment.content.liquid\"]\n    ],\n    otherTag: [\n      [\n        /\\{\\{/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@liquidState.otherTag\"\n        }\n      ],\n      [\n        /\\{\\%/,\n        {\n          token: \"@rematch\",\n          switchTo: \"@liquidState.otherTag\"\n        }\n      ],\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value\"],\n      [/'([^']*)'/, \"attribute.value\"],\n      [/[\\w\\-]+/, \"attribute.name\"],\n      [/=/, \"delimiter\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    liquidState: [\n      [/\\{\\{/, \"delimiter.output.liquid\"],\n      [/\\}\\}/, { token: \"delimiter.output.liquid\", switchTo: \"@$S2.$S3\" }],\n      [/\\{\\%/, \"delimiter.tag.liquid\"],\n      [/raw\\s*\\%\\}/, \"delimiter.tag.liquid\", \"@liquidRaw\"],\n      [/\\%\\}/, { token: \"delimiter.tag.liquid\", switchTo: \"@$S2.$S3\" }],\n      { include: \"liquidRoot\" }\n    ],\n    liquidRaw: [\n      [/^(?!\\{\\%\\s*endraw\\s*\\%\\}).+/],\n      [/\\{\\%/, \"delimiter.tag.liquid\"],\n      [/@identifier/],\n      [/\\%\\}/, { token: \"delimiter.tag.liquid\", next: \"@root\" }]\n    ],\n    liquidRoot: [\n      [/\\d+(\\.\\d+)?/, \"number.liquid\"],\n      [/\"[^\"]*\"/, \"string.liquid\"],\n      [/'[^']*'/, \"string.liquid\"],\n      [/\\s+/],\n      [\n        /@symbol/,\n        {\n          cases: {\n            \"@operators\": \"operator.liquid\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/\\./],\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@constants\": \"keyword.liquid\",\n            \"@builtinFilters\": \"predefined.liquid\",\n            \"@builtinTags\": \"predefined.liquid\",\n            \"@default\": \"variable.liquid\"\n          }\n        }\n      ],\n      [/[^}|%]/, \"variable.liquid\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/liquid/liquid.js\n"));

/***/ })

}]);