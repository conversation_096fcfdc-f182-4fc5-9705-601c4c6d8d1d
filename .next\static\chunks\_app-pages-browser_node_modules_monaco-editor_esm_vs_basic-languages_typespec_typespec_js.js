"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_typespec_typespec_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/typespec/typespec.js":
/*!********************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/typespec/typespec.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/typespec/typespec.ts\nvar bounded = (text) => `\\\\b${text}\\\\b`;\nvar notBefore = (regex) => `(?!${regex})`;\nvar identifierStart = \"[_a-zA-Z]\";\nvar identifierContinue = \"[_a-zA-Z0-9]\";\nvar identifier = bounded(`${identifierStart}${identifierContinue}*`);\nvar directive = bounded(`[_a-zA-Z-0-9]+`);\nvar keywords = [\n  \"import\",\n  \"model\",\n  \"scalar\",\n  \"namespace\",\n  \"op\",\n  \"interface\",\n  \"union\",\n  \"using\",\n  \"is\",\n  \"extends\",\n  \"enum\",\n  \"alias\",\n  \"return\",\n  \"void\",\n  \"if\",\n  \"else\",\n  \"projection\",\n  \"dec\",\n  \"extern\",\n  \"fn\"\n];\nvar namedLiterals = [\"true\", \"false\", \"null\", \"unknown\", \"never\"];\nvar nonCommentWs = `[ \\\\t\\\\r\\\\n]`;\nvar numericLiteral = `[0-9]+`;\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"/**\", close: \" */\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ],\n  indentationRules: {\n    decreaseIndentPattern: new RegExp(\"^((?!.*?/\\\\*).*\\\\*/)?\\\\s*[\\\\}\\\\]].*$\"),\n    increaseIndentPattern: new RegExp(\n      \"^((?!//).)*(\\\\{([^}\\\"'`/]*|(\\\\t|[ ])*//.*)|\\\\([^)\\\"'`/]*|\\\\[[^\\\\]\\\"'`/]*)$\"\n    ),\n    // e.g.  * ...| or */| or *-----*/|\n    unIndentedLinePattern: new RegExp(\n      \"^(\\\\t|[ ])*[ ]\\\\*[^/]*\\\\*/\\\\s*$|^(\\\\t|[ ])*[ ]\\\\*/\\\\s*$|^(\\\\t|[ ])*[ ]\\\\*([ ]([^\\\\*]|\\\\*(?!/))*)?$\"\n    )\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".tsp\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  symbols: /[=:;<>]+/,\n  keywords,\n  namedLiterals,\n  escapes: `\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\|\"|\\\\\\${)`,\n  tokenizer: {\n    root: [{ include: \"@expression\" }, { include: \"@whitespace\" }],\n    stringVerbatim: [\n      { regex: `(|\"|\"\")[^\"]`, action: { token: \"string\" } },\n      { regex: `\"\"\"${notBefore(`\"`)}`, action: { token: \"string\", next: \"@pop\" } }\n    ],\n    stringLiteral: [\n      { regex: `\\\\\\${`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `[^\\\\\\\\\"$]+`, action: { token: \"string\" } },\n      { regex: \"@escapes\", action: { token: \"string.escape\" } },\n      { regex: `\\\\\\\\.`, action: { token: \"string.escape.invalid\" } },\n      { regex: `\"`, action: { token: \"string\", next: \"@pop\" } }\n    ],\n    bracketCounting: [\n      { regex: `{`, action: { token: \"delimiter.bracket\", next: \"@bracketCounting\" } },\n      { regex: `}`, action: { token: \"delimiter.bracket\", next: \"@pop\" } },\n      { include: \"@expression\" }\n    ],\n    comment: [\n      { regex: `[^\\\\*]+`, action: { token: \"comment\" } },\n      { regex: `\\\\*\\\\/`, action: { token: \"comment\", next: \"@pop\" } },\n      { regex: `[\\\\/*]`, action: { token: \"comment\" } }\n    ],\n    whitespace: [\n      { regex: nonCommentWs },\n      { regex: `\\\\/\\\\*`, action: { token: \"comment\", next: \"@comment\" } },\n      { regex: `\\\\/\\\\/.*$`, action: { token: \"comment\" } }\n    ],\n    expression: [\n      { regex: `\"\"\"`, action: { token: \"string\", next: \"@stringVerbatim\" } },\n      { regex: `\"${notBefore(`\"\"`)}`, action: { token: \"string\", next: \"@stringLiteral\" } },\n      { regex: numericLiteral, action: { token: \"number\" } },\n      {\n        regex: identifier,\n        action: {\n          cases: {\n            \"@keywords\": { token: \"keyword\" },\n            \"@namedLiterals\": { token: \"keyword\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      },\n      { regex: `@${identifier}`, action: { token: \"tag\" } },\n      { regex: `#${directive}`, action: { token: \"directive\" } }\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb25hY28tZWRpdG9yL2VzbS92cy9iYXNpYy1sYW5ndWFnZXMvdHlwZXNwZWMvdHlwZXNwZWMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQUdBO0FBQ0EsOEJBQThCLEtBQUs7QUFDbkMsaUNBQWlDLE1BQU07QUFDdkM7QUFDQTtBQUNBLDRCQUE0QixnQkFBZ0IsRUFBRSxtQkFBbUI7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsT0FBTyxLQUFLO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLFFBQVEsWUFBWSxHQUFHO0FBQzdCLE1BQU0sdUJBQXVCO0FBQzdCLE1BQU0sdUJBQXVCO0FBQzdCLE1BQU0sdUJBQXVCO0FBQzdCLE1BQU07QUFDTjtBQUNBO0FBQ0EsTUFBTSxRQUFRLFlBQVksR0FBRztBQUM3QixNQUFNLHVCQUF1QjtBQUM3QixNQUFNLHVCQUF1QjtBQUM3QixNQUFNO0FBQ047QUFDQTtBQUNBLG9FQUFvRTtBQUNwRTtBQUNBLHNCQUFzQixJQUFJO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLFFBQVEsWUFBWSw2QkFBNkI7QUFDdkQsTUFBTSxrREFBa0Q7QUFDeEQsTUFBTTtBQUNOO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQSxtQkFBbUIsYUFBYSxtQkFBbUI7QUFDbkQ7QUFDQSxhQUFhLHdCQUF3QixJQUFJLHdCQUF3QjtBQUNqRTtBQUNBLFFBQVEsZ0NBQWdDLG1CQUFtQjtBQUMzRCxRQUFRLGFBQWEsZUFBZSxhQUFhO0FBQ2pEO0FBQ0E7QUFDQSxRQUFRLGFBQWEsYUFBYSx3REFBd0Q7QUFDMUYsUUFBUSwrQkFBK0IsbUJBQW1CO0FBQzFELFFBQVEsNkJBQTZCLDBCQUEwQjtBQUMvRCxRQUFRLDBCQUEwQixrQ0FBa0M7QUFDcEUsUUFBUSxzQkFBc0I7QUFDOUI7QUFDQTtBQUNBLFFBQVEsU0FBUyxhQUFhLHdEQUF3RDtBQUN0RixRQUFRLFNBQVMsYUFBYSw0Q0FBNEM7QUFDMUUsUUFBUTtBQUNSO0FBQ0E7QUFDQSxRQUFRLDRCQUE0QixvQkFBb0I7QUFDeEQsUUFBUSwyQkFBMkIsa0NBQWtDO0FBQ3JFLFFBQVEsMkJBQTJCO0FBQ25DO0FBQ0E7QUFDQSxRQUFRLHFCQUFxQjtBQUM3QixRQUFRLDJCQUEyQixzQ0FBc0M7QUFDekUsUUFBUSw4QkFBOEI7QUFDdEM7QUFDQTtBQUNBLFFBQVEsd0JBQXdCLDRDQUE0QztBQUM1RSxRQUFRLFdBQVcsZ0JBQWdCLGFBQWEsMkNBQTJDO0FBQzNGLFFBQVEsaUNBQWlDLG1CQUFtQjtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixrQkFBa0I7QUFDN0MsZ0NBQWdDLGtCQUFrQjtBQUNsRCwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBLE9BQU87QUFDUCxRQUFRLFdBQVcsV0FBVyxhQUFhLGdCQUFnQjtBQUMzRCxRQUFRLFdBQVcsVUFBVSxhQUFhO0FBQzFDO0FBQ0E7QUFDQTtBQUlFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tb25hY28tZWRpdG9yL2VzbS92cy9iYXNpYy1sYW5ndWFnZXMvdHlwZXNwZWMvdHlwZXNwZWMuanM/MjFlMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiEtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4gKiBWZXJzaW9uOiAwLjUyLjIoNDA0NTQ1YmRlZDFkZjZmZmE0MWVhMGFmNGU4ZGRiMjE5MDE4YzZjMSlcbiAqIFJlbGVhc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZVxuICogaHR0cHM6Ly9naXRodWIuY29tL21pY3Jvc29mdC9tb25hY28tZWRpdG9yL2Jsb2IvbWFpbi9MSUNFTlNFLnR4dFxuICotLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cblxuLy8gc3JjL2Jhc2ljLWxhbmd1YWdlcy90eXBlc3BlYy90eXBlc3BlYy50c1xudmFyIGJvdW5kZWQgPSAodGV4dCkgPT4gYFxcXFxiJHt0ZXh0fVxcXFxiYDtcbnZhciBub3RCZWZvcmUgPSAocmVnZXgpID0+IGAoPyEke3JlZ2V4fSlgO1xudmFyIGlkZW50aWZpZXJTdGFydCA9IFwiW19hLXpBLVpdXCI7XG52YXIgaWRlbnRpZmllckNvbnRpbnVlID0gXCJbX2EtekEtWjAtOV1cIjtcbnZhciBpZGVudGlmaWVyID0gYm91bmRlZChgJHtpZGVudGlmaWVyU3RhcnR9JHtpZGVudGlmaWVyQ29udGludWV9KmApO1xudmFyIGRpcmVjdGl2ZSA9IGJvdW5kZWQoYFtfYS16QS1aLTAtOV0rYCk7XG52YXIga2V5d29yZHMgPSBbXG4gIFwiaW1wb3J0XCIsXG4gIFwibW9kZWxcIixcbiAgXCJzY2FsYXJcIixcbiAgXCJuYW1lc3BhY2VcIixcbiAgXCJvcFwiLFxuICBcImludGVyZmFjZVwiLFxuICBcInVuaW9uXCIsXG4gIFwidXNpbmdcIixcbiAgXCJpc1wiLFxuICBcImV4dGVuZHNcIixcbiAgXCJlbnVtXCIsXG4gIFwiYWxpYXNcIixcbiAgXCJyZXR1cm5cIixcbiAgXCJ2b2lkXCIsXG4gIFwiaWZcIixcbiAgXCJlbHNlXCIsXG4gIFwicHJvamVjdGlvblwiLFxuICBcImRlY1wiLFxuICBcImV4dGVyblwiLFxuICBcImZuXCJcbl07XG52YXIgbmFtZWRMaXRlcmFscyA9IFtcInRydWVcIiwgXCJmYWxzZVwiLCBcIm51bGxcIiwgXCJ1bmtub3duXCIsIFwibmV2ZXJcIl07XG52YXIgbm9uQ29tbWVudFdzID0gYFsgXFxcXHRcXFxcclxcXFxuXWA7XG52YXIgbnVtZXJpY0xpdGVyYWwgPSBgWzAtOV0rYDtcbnZhciBjb25mID0ge1xuICBjb21tZW50czoge1xuICAgIGxpbmVDb21tZW50OiBcIi8vXCIsXG4gICAgYmxvY2tDb21tZW50OiBbXCIvKlwiLCBcIiovXCJdXG4gIH0sXG4gIGJyYWNrZXRzOiBbXG4gICAgW1wie1wiLCBcIn1cIl0sXG4gICAgW1wiW1wiLCBcIl1cIl0sXG4gICAgW1wiKFwiLCBcIilcIl1cbiAgXSxcbiAgYXV0b0Nsb3NpbmdQYWlyczogW1xuICAgIHsgb3BlbjogXCJ7XCIsIGNsb3NlOiBcIn1cIiB9LFxuICAgIHsgb3BlbjogXCJbXCIsIGNsb3NlOiBcIl1cIiB9LFxuICAgIHsgb3BlbjogXCIoXCIsIGNsb3NlOiBcIilcIiB9LFxuICAgIHsgb3BlbjogJ1wiJywgY2xvc2U6ICdcIicgfSxcbiAgICB7IG9wZW46IFwiLyoqXCIsIGNsb3NlOiBcIiAqL1wiLCBub3RJbjogW1wic3RyaW5nXCJdIH1cbiAgXSxcbiAgc3Vycm91bmRpbmdQYWlyczogW1xuICAgIHsgb3BlbjogXCJ7XCIsIGNsb3NlOiBcIn1cIiB9LFxuICAgIHsgb3BlbjogXCJbXCIsIGNsb3NlOiBcIl1cIiB9LFxuICAgIHsgb3BlbjogXCIoXCIsIGNsb3NlOiBcIilcIiB9LFxuICAgIHsgb3BlbjogJ1wiJywgY2xvc2U6ICdcIicgfVxuICBdLFxuICBpbmRlbnRhdGlvblJ1bGVzOiB7XG4gICAgZGVjcmVhc2VJbmRlbnRQYXR0ZXJuOiBuZXcgUmVnRXhwKFwiXigoPyEuKj8vXFxcXCopLipcXFxcKi8pP1xcXFxzKltcXFxcfVxcXFxdXS4qJFwiKSxcbiAgICBpbmNyZWFzZUluZGVudFBhdHRlcm46IG5ldyBSZWdFeHAoXG4gICAgICBcIl4oKD8hLy8pLikqKFxcXFx7KFtefVxcXCInYC9dKnwoXFxcXHR8WyBdKSovLy4qKXxcXFxcKFteKVxcXCInYC9dKnxcXFxcW1teXFxcXF1cXFwiJ2AvXSopJFwiXG4gICAgKSxcbiAgICAvLyBlLmcuICAqIC4uLnwgb3IgKi98IG9yICotLS0tLSovfFxuICAgIHVuSW5kZW50ZWRMaW5lUGF0dGVybjogbmV3IFJlZ0V4cChcbiAgICAgIFwiXihcXFxcdHxbIF0pKlsgXVxcXFwqW14vXSpcXFxcKi9cXFxccyokfF4oXFxcXHR8WyBdKSpbIF1cXFxcKi9cXFxccyokfF4oXFxcXHR8WyBdKSpbIF1cXFxcKihbIF0oW15cXFxcKl18XFxcXCooPyEvKSkqKT8kXCJcbiAgICApXG4gIH1cbn07XG52YXIgbGFuZ3VhZ2UgPSB7XG4gIGRlZmF1bHRUb2tlbjogXCJcIixcbiAgdG9rZW5Qb3N0Zml4OiBcIi50c3BcIixcbiAgYnJhY2tldHM6IFtcbiAgICB7IG9wZW46IFwie1wiLCBjbG9zZTogXCJ9XCIsIHRva2VuOiBcImRlbGltaXRlci5jdXJseVwiIH0sXG4gICAgeyBvcGVuOiBcIltcIiwgY2xvc2U6IFwiXVwiLCB0b2tlbjogXCJkZWxpbWl0ZXIuc3F1YXJlXCIgfSxcbiAgICB7IG9wZW46IFwiKFwiLCBjbG9zZTogXCIpXCIsIHRva2VuOiBcImRlbGltaXRlci5wYXJlbnRoZXNpc1wiIH1cbiAgXSxcbiAgc3ltYm9sczogL1s9Ojs8Pl0rLyxcbiAga2V5d29yZHMsXG4gIG5hbWVkTGl0ZXJhbHMsXG4gIGVzY2FwZXM6IGBcXFxcXFxcXCh1e1swLTlBLUZhLWZdK318bnxyfHR8XFxcXFxcXFx8XCJ8XFxcXFxcJHspYCxcbiAgdG9rZW5pemVyOiB7XG4gICAgcm9vdDogW3sgaW5jbHVkZTogXCJAZXhwcmVzc2lvblwiIH0sIHsgaW5jbHVkZTogXCJAd2hpdGVzcGFjZVwiIH1dLFxuICAgIHN0cmluZ1ZlcmJhdGltOiBbXG4gICAgICB7IHJlZ2V4OiBgKHxcInxcIlwiKVteXCJdYCwgYWN0aW9uOiB7IHRva2VuOiBcInN0cmluZ1wiIH0gfSxcbiAgICAgIHsgcmVnZXg6IGBcIlwiXCIke25vdEJlZm9yZShgXCJgKX1gLCBhY3Rpb246IHsgdG9rZW46IFwic3RyaW5nXCIsIG5leHQ6IFwiQHBvcFwiIH0gfVxuICAgIF0sXG4gICAgc3RyaW5nTGl0ZXJhbDogW1xuICAgICAgeyByZWdleDogYFxcXFxcXCR7YCwgYWN0aW9uOiB7IHRva2VuOiBcImRlbGltaXRlci5icmFja2V0XCIsIG5leHQ6IFwiQGJyYWNrZXRDb3VudGluZ1wiIH0gfSxcbiAgICAgIHsgcmVnZXg6IGBbXlxcXFxcXFxcXCIkXStgLCBhY3Rpb246IHsgdG9rZW46IFwic3RyaW5nXCIgfSB9LFxuICAgICAgeyByZWdleDogXCJAZXNjYXBlc1wiLCBhY3Rpb246IHsgdG9rZW46IFwic3RyaW5nLmVzY2FwZVwiIH0gfSxcbiAgICAgIHsgcmVnZXg6IGBcXFxcXFxcXC5gLCBhY3Rpb246IHsgdG9rZW46IFwic3RyaW5nLmVzY2FwZS5pbnZhbGlkXCIgfSB9LFxuICAgICAgeyByZWdleDogYFwiYCwgYWN0aW9uOiB7IHRva2VuOiBcInN0cmluZ1wiLCBuZXh0OiBcIkBwb3BcIiB9IH1cbiAgICBdLFxuICAgIGJyYWNrZXRDb3VudGluZzogW1xuICAgICAgeyByZWdleDogYHtgLCBhY3Rpb246IHsgdG9rZW46IFwiZGVsaW1pdGVyLmJyYWNrZXRcIiwgbmV4dDogXCJAYnJhY2tldENvdW50aW5nXCIgfSB9LFxuICAgICAgeyByZWdleDogYH1gLCBhY3Rpb246IHsgdG9rZW46IFwiZGVsaW1pdGVyLmJyYWNrZXRcIiwgbmV4dDogXCJAcG9wXCIgfSB9LFxuICAgICAgeyBpbmNsdWRlOiBcIkBleHByZXNzaW9uXCIgfVxuICAgIF0sXG4gICAgY29tbWVudDogW1xuICAgICAgeyByZWdleDogYFteXFxcXCpdK2AsIGFjdGlvbjogeyB0b2tlbjogXCJjb21tZW50XCIgfSB9LFxuICAgICAgeyByZWdleDogYFxcXFwqXFxcXC9gLCBhY3Rpb246IHsgdG9rZW46IFwiY29tbWVudFwiLCBuZXh0OiBcIkBwb3BcIiB9IH0sXG4gICAgICB7IHJlZ2V4OiBgW1xcXFwvKl1gLCBhY3Rpb246IHsgdG9rZW46IFwiY29tbWVudFwiIH0gfVxuICAgIF0sXG4gICAgd2hpdGVzcGFjZTogW1xuICAgICAgeyByZWdleDogbm9uQ29tbWVudFdzIH0sXG4gICAgICB7IHJlZ2V4OiBgXFxcXC9cXFxcKmAsIGFjdGlvbjogeyB0b2tlbjogXCJjb21tZW50XCIsIG5leHQ6IFwiQGNvbW1lbnRcIiB9IH0sXG4gICAgICB7IHJlZ2V4OiBgXFxcXC9cXFxcLy4qJGAsIGFjdGlvbjogeyB0b2tlbjogXCJjb21tZW50XCIgfSB9XG4gICAgXSxcbiAgICBleHByZXNzaW9uOiBbXG4gICAgICB7IHJlZ2V4OiBgXCJcIlwiYCwgYWN0aW9uOiB7IHRva2VuOiBcInN0cmluZ1wiLCBuZXh0OiBcIkBzdHJpbmdWZXJiYXRpbVwiIH0gfSxcbiAgICAgIHsgcmVnZXg6IGBcIiR7bm90QmVmb3JlKGBcIlwiYCl9YCwgYWN0aW9uOiB7IHRva2VuOiBcInN0cmluZ1wiLCBuZXh0OiBcIkBzdHJpbmdMaXRlcmFsXCIgfSB9LFxuICAgICAgeyByZWdleDogbnVtZXJpY0xpdGVyYWwsIGFjdGlvbjogeyB0b2tlbjogXCJudW1iZXJcIiB9IH0sXG4gICAgICB7XG4gICAgICAgIHJlZ2V4OiBpZGVudGlmaWVyLFxuICAgICAgICBhY3Rpb246IHtcbiAgICAgICAgICBjYXNlczoge1xuICAgICAgICAgICAgXCJAa2V5d29yZHNcIjogeyB0b2tlbjogXCJrZXl3b3JkXCIgfSxcbiAgICAgICAgICAgIFwiQG5hbWVkTGl0ZXJhbHNcIjogeyB0b2tlbjogXCJrZXl3b3JkXCIgfSxcbiAgICAgICAgICAgIFwiQGRlZmF1bHRcIjogeyB0b2tlbjogXCJpZGVudGlmaWVyXCIgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIHsgcmVnZXg6IGBAJHtpZGVudGlmaWVyfWAsIGFjdGlvbjogeyB0b2tlbjogXCJ0YWdcIiB9IH0sXG4gICAgICB7IHJlZ2V4OiBgIyR7ZGlyZWN0aXZlfWAsIGFjdGlvbjogeyB0b2tlbjogXCJkaXJlY3RpdmVcIiB9IH1cbiAgICBdXG4gIH1cbn07XG5leHBvcnQge1xuICBjb25mLFxuICBsYW5ndWFnZVxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/typespec/typespec.js\n"));

/***/ })

}]);