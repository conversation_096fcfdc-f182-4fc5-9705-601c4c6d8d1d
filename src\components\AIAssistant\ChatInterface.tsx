/**
 * AI对话界面组件
 * 传统的对话形式界面，用于与AI进行实时对话
 * 优化版本：解决文本选择被频繁重渲染取消的问题
 */

'use client'

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import { AIMessage, AIConfig, FileTreeNode, MediaFile } from '@/types'
import StreamingResponse from './StreamingResponse'
import { ChatHistoryService } from '@/services/chatHistoryService'
import { FileTreeService } from '@/services/fileTreeService'
import { DiffToolService } from '@/services/diffToolService'
import { AIService } from '@/services/aiService'
import { AIConfigService } from '@/services/aiConfigService'
import { normalizePath as normalizePathUtil } from '@/utils/pathUtils' // Renamed to avoid conflict
import { SelectionManager } from '@/utils/SelectionManager'

import SessionManager from './SessionManager'
import FileAssociationTreeDialog from './FileAssociationTreeDialog'
import CompactDiffDisplay from './CompactDiffDisplay'
import MediaUploader from './MediaUploader'
import HumanBlockProcessor from './HumanBlockProcessor'
import HelperResponseLayer from './HelperResponseLayer'
import PromptButton from './PromptButton'
import PromptManagerModal from './PromptManagerModal'
import MultipleComparisonModal from './MultipleComparisonModal'
import { hasHumanBlocks, extractHumanBlocks, removeHumanBlocks } from '@/utils/humanBlockParser'
import { MessageLayer } from '@/types'
import { PromptConfigService } from '@/services/promptConfigService'
import { PromptTemplateService } from '@/services/promptTemplateService'


interface InsertOptions {
  position: 'cursor' | 'start' | 'end' | 'replace'
  addNewlines?: boolean
  newLine?: boolean // 兼容性属性
}

interface ChatInterfaceProps {
  currentConfig: AIConfig | null
  onSendMessage: (message: string, targetMessageIndex?: number) => void
  responseContent: string
  isStreaming: boolean
  isComplete: boolean
  error: string | null
  onRetry: () => void
  onCopy: (content: string) => void
  onStop: () => void
  onInsertToEditor: () => void
  onContentInsert?: (content: string, options: InsertOptions) => void
  onMessageContentInsert?: (messageContent: string) => void
  isLoading: boolean
  className?: string
  currentPersona?: any
  // 新增：对话历史共享
  onChatHistoryChange?: (history: ChatMessage[]) => void
  // 新增：文件关联功能
  associatedFiles?: string[]
  onFilesChange?: (files: string[]) => void
  onShowFileAssociation?: () => void
  // 🔧 新增：AI响应状态清理回调
  onClearAIResponse?: () => void
  // 🔧 新增：作品ID，用于文件关联
  artworkId?: string
  // 🔧 新增：文件选择回调，用于跳转到编辑器
  onFileSelect?: (fileId: string) => void
  // 🔧 新增：详细对比回调，用于打开详细差异对比视图
  onOpenDetailedDiff?: (diffRequest: { filePath: string; operation: 'append' | 'replace'; content: string; previewMode?: boolean }) => void
  // 🔧 新增：聚焦文件状态，由父组件管理
  focusedFile?: string | null
  // 🔧 新增：媒体文件变化回调
  onMediaFilesChange?: (mediaFiles: MediaFile[]) => void
  // 🔧 新增：受众设置回调
  onShowAudienceSettings?: () => void
  // 🔧 新增：会话管理回调
  onShowSessionManager?: () => void
  // 🔧 新增：多项对比消息构建回调
  onMultipleComparisonSend?: (message: string, modelId: string) => Promise<string>
}

interface ChatMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: number
  sessionId?: string
  createdAt: number
}

const ChatInterface = React.forwardRef<{ handleSessionSelect: (sessionId: string) => Promise<void> }, ChatInterfaceProps>(function ChatInterface({
  currentConfig,
  onSendMessage,
  responseContent,
  isStreaming,
  isComplete,
  error,
  onRetry,
  onCopy,
  onStop,
  onInsertToEditor,
  onContentInsert,
  onMessageContentInsert,
  isLoading,
  className = '',
  currentPersona,
  onChatHistoryChange,
  // 文件关联相关props
  associatedFiles = [],
  onFilesChange = () => { },
  onShowFileAssociation = () => { },
  // 🔧 AI响应状态清理回调
  onClearAIResponse,
  // 🔧 作品ID，用于文件关联
  artworkId,
  // 🔧 文件选择回调，用于跳转到编辑器
  onFileSelect,
  // 🔧 详细对比回调，用于打开详细差异对比视图
  onOpenDetailedDiff,
  // 🔧 聚焦文件状态，由父组件管理
  focusedFile,
  // 🔧 媒体文件变化回调
  onMediaFilesChange,
  // 🔧 受众设置回调
  onShowAudienceSettings,
  // 🔧 会话管理回调
  onShowSessionManager,
  // 🔧 多项对比消息构建回调
  onMultipleComparisonSend,

}: ChatInterfaceProps, ref: React.Ref<{ handleSessionSelect: (sessionId: string) => Promise<void> }>) {
  const [inputMessage, setInputMessage] = useState('')
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([])
  const [showSessionManager, setShowSessionManager] = useState(false)
  const [showFileAssociation, setShowFileAssociation] = useState(false)
  const [showPromptManager, setShowPromptManager] = useState(false)
  const [activePromptConfig, setActivePromptConfig] = useState<any>(null)
  const [currentSessionId, setCurrentSessionId] = useState<string>('')
  const [isFileAssociationCollapsed, setIsFileAssociationCollapsed] = useState(false)
  const [filePaths, setFilePaths] = useState<Map<string, string>>(new Map())
  const [showToolsPanel, setShowToolsPanel] = useState(false)
  // 消息工具面板状态 - 记录哪条消息的工具面板正在显示
  const [activeMessageTools, setActiveMessageTools] = useState<string | null>(null)
  // 编辑消息状态
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null)
  const [editingContent, setEditingContent] = useState('')

  // 多项对比弹窗状态
  const [showMultipleComparison, setShowMultipleComparison] = useState(false)
  const [comparisonMessageContent, setComparisonMessageContent] = useState('')
  const [comparisonTriggerMessageIndex, setComparisonTriggerMessageIndex] = useState<number>(-1)
  
  // 共享状态
  const [availableModels, setAvailableModels] = useState<{id: string, name: string, description: string}[]>([])
  const [isLoadingModels, setIsLoadingModels] = useState(false)
  
  // 单模型生成相关状态
  const [selectedSingleModel, setSelectedSingleModel] = useState<string>('')
  const [singleModelResults, setSingleModelResults] = useState<string[]>([])
  const [streamingSingleResults, setStreamingSingleResults] = useState<Set<number>>(new Set())
  const [generationCount, setGenerationCount] = useState<number>(3)
  
  // 多模型对比相关状态
  const [selectedMultiModels, setSelectedMultiModels] = useState<string[]>([])
  const [multiModelResults, setMultiModelResults] = useState<Record<string, string>>({})
  const [streamingMultiModels, setStreamingMultiModels] = useState<Set<string>>(new Set())
  
  // 🔧 聚焦文件状态现在由父组件管理，通过props传递

  // 媒体上传相关状态
  const [uploadedMedia, setUploadedMedia] = useState<MediaFile[]>([])
  const [showMediaUploader, setShowMediaUploader] = useState(false)
  const [showMediaList, setShowMediaList] = useState(false)

  // 消息层状态管理
  const [messageLayers, setMessageLayers] = useState<MessageLayer[]>([])
  const [pendingIntegratedMessage, setPendingIntegratedMessage] = useState<string>('')
  const [processedMessages, setProcessedMessages] = useState<Set<string>>(new Set())

  // 🔧 调试监听已移除，避免控制台日志过多
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const chatContainerRef = useRef<HTMLDivElement>(null)
  const aiResponseRef = useRef<HTMLDivElement>(null)

  // 🔧 响应内容引用，避免useEffect依赖responseContent导致重复渲染
  const responseContentRef = useRef('')

  // 🔧 文本选择管理器
  const selectionManagerRef = useRef<SelectionManager | null>(null)

  // 初始化选择管理器
  useEffect(() => {
    if (!selectionManagerRef.current) {
      selectionManagerRef.current = new SelectionManager()
    }

    return () => {
      if (selectionManagerRef.current && aiResponseRef.current) {
        selectionManagerRef.current.stopListening(aiResponseRef.current)
      }
    }
  }, [])

  // 点击外部关闭工具面板
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showToolsPanel) {
        const target = event.target as HTMLElement
        if (!target.closest('.tools-panel-container')) {
          setShowToolsPanel(false)
        }
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showToolsPanel])

  // 暂时禁用选择监听
  // useEffect(() => {
  //   if (selectionManagerRef.current && aiResponseRef.current) {
  //     selectionManagerRef.current.startListening(aiResponseRef.current)
  //   }
  //   return () => {
  //     if (selectionManagerRef.current && aiResponseRef.current) {
  //       selectionManagerRef.current.stopListening(aiResponseRef.current)
  //     }
  //   }
  // }, [])

  // 🔧 同步responseContent到ref，避免useEffect直接依赖responseContent
  useEffect(() => {
    responseContentRef.current = responseContent
  }, [responseContent])

  // 🔧 修复重复渲染问题：使用useMemo确保chatHistoryService引用稳定
  const chatHistoryService = useMemo(() => ChatHistoryService.getInstance(), [])

  // 辅助函数：标准化路径
  const normalizePath = (path: string): string => {
    if (!path) return ''
    // 使用导入的工具函数，避免命名冲突
    return normalizePathUtil(path).toLowerCase();
  }

  // 辅助函数：根据路径查找文件ID
  const findFileIdByPath = (rootNode: FileTreeNode, targetPath: string): string | null => {
    // 标准化目标路径，确保相对路径和绝对路径都能正确匹配
    const normalizedTargetPath = normalizePath(targetPath)

    const searchNode = (node: FileTreeNode): string | null => {
      // 标准化当前节点路径进行比较
      if (normalizePath(node.path) === normalizedTargetPath) {
        return node.id
      }

      if (node.children) {
        for (const child of node.children) {
          const foundId = searchNode(child)
          if (foundId) {
            return foundId
          }
        }
      }
      return null
    }

    return searchNode(rootNode)
  }

  // 辅助函数：解析替换内容（仅支持JSON格式）
  const parseReplaceContent = (content: string, find?: string): [string, string] => {
    // JSON格式：直接使用find和content参数
    if (find) {
      console.log('✅ 使用JSON格式解析:', { find, content: content.substring(0, 100) + '...' })
      return [find, content]
    }
    
    // 如果没有find参数，说明格式不正确
    console.error('❌ 替换操作缺少查找内容')
    console.error('📝 请使用JSON格式：{"file": "...", "type": "replace", "find": "查找内容", "content": "替换内容"}')
    console.error('📝 实际收到的内容:', content.substring(0, 200))
    
    // 返回错误状态，让调用方处理
    return ['', content]
  }



  // 辅助函数：获取所有文件路径（用于调试）
  const getAllFilePaths = (rootNode: FileTreeNode): string[] => {
    const paths: string[] = []

    const collectPaths = (node: FileTreeNode) => {
      if (node.type === 'file') {
        paths.push(node.path)
      }
      if (node.children) {
        node.children.forEach(collectPaths)
      }
    }

    collectPaths(rootNode)
    return paths
  }

  // 辅助函数：根据文件ID查找文件完整路径
  const findFilePathById = useCallback(async (fileId: string): Promise<string> => {
    try {
      if (!artworkId) return `未知文件 (${fileId.substring(0, 8)}...)`

      const fileTreeService = FileTreeService.getInstance()
      const fileTreeResult = await fileTreeService.getFileTree(artworkId)

      if (fileTreeResult.success && fileTreeResult.data) {
        const filePath = getFilePath(fileTreeResult.data, fileId)
        return filePath || `未知文件 (${fileId.substring(0, 8)}...)`
      }

      return `未知文件 (${fileId.substring(0, 8)}...)`
    } catch (error) {
      console.warn('获取文件路径失败:', error)
      return `未知文件 (${fileId.substring(0, 8)}...)`
    }
  }, [artworkId])

  // 辅助函数：获取文件的完整路径（不包括作品根节点）
  const getFilePath = (rootNode: FileTreeNode, fileId: string): string => {
    // 递归搜索文件树，构建路径
    const buildPath = (node: FileTreeNode, targetId: string, currentPath: string[] = []): string[] | null => {
      // 如果找到目标节点，返回当前路径
      if (node.id === targetId) {
        return [...currentPath, node.name];
      }
      // 如果有子节点，递归搜索
      if (node.children) {
        for (const child of node.children) {
          const path = buildPath(child, targetId, [...currentPath, node.name]);
          if (path) {
            return path;
          }
        }
      }
      return null;
    };

    // 从根节点开始搜索，但不包括根节点名称
    const path = buildPath(rootNode, fileId, []);
    if (path) {
      // 移除第一个元素（根节点名称）
      path.shift();
      return path.join('/');
    }
    return fileId.substring(0, 8) + '...'; // 如果找不到路径，返回ID的简短版本
  };

  // 递归搜索文件树中的文件名（保留用于兼容性）
  const searchNodeForName = (node: FileTreeNode, fileId: string): string | null => {
    if (node.id === fileId) {
      return node.name
    }

    if (node.children) {
      for (const child of node.children) {
        const result = searchNodeForName(child, fileId)
        if (result) return result
      }
    }

    return null
  }

  // 🔧 聚焦文件状态现在由父组件统一管理，移除子组件中的事件监听



  // 组件初始化时加载历史记录和当前会话ID
  useEffect(() => {
    const initializeSession = async () => {
      try {
        // 获取当前会话ID
        const sessionId = chatHistoryService.getCurrentSessionId()
        setCurrentSessionId(sessionId)

        // 加载当前会话的历史记录
        const storedHistory = await chatHistoryService.getCurrentHistory()
        if (storedHistory.length > 0) {
          setChatHistory(storedHistory)
          console.log('✅ 已从IndexedDB加载对话历史:', storedHistory.length, '条消息')

          // 通知父组件对话历史变化
          if (onChatHistoryChange) {
            onChatHistoryChange(storedHistory)
          }
        }

        // 文件关联数据由父组件管理，这里不需要重复加载
      } catch (error) {
        console.error('❌ 加载对话历史失败:', error)
      }
    }

    initializeSession()
  }, [chatHistoryService, onChatHistoryChange])

  // 加载激活的提示词配置
  const loadActivePromptConfig = useCallback(async () => {
    try {
      const promptConfigService = PromptConfigService.getInstance()
      const result = await promptConfigService.getActiveConfig()
      if (result.success && result.data) {
        setActivePromptConfig(result.data)
      } else {
        setActivePromptConfig(null)
      }
    } catch (error) {
      console.error('加载激活提示词配置失败:', error)
      setActivePromptConfig(null)
    }
  }, [])

  // 组件挂载时加载激活配置
  useEffect(() => {
    loadActivePromptConfig()
  }, [loadActivePromptConfig])

  // 处理提示词配置选择
  const handlePromptConfigSelect = useCallback((config: any) => {
    setActivePromptConfig(config)
    console.log('🔮 选择提示词配置:', config.name)
    // 重新加载激活配置以确保状态同步
    setTimeout(() => {
      loadActivePromptConfig()
    }, 100)
  }, [loadActivePromptConfig])

  // 生成唯一消息ID
  const generateMessageId = useCallback((type: 'user' | 'assistant') => {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 11)
    return `${type}-${timestamp}-${random}`
  }, [])

  // 添加用户消息到历史记录
  const addUserMessage = useCallback((message: ChatMessage) => {
    setChatHistory(prev => {
      // 检查消息是否已存在，避免重复添加
      const exists = prev.some(m => m.id === message.id)
      if (exists) {
        console.log('⚠️ 消息已存在，跳过添加:', message.content.substring(0, 20))
        return prev
      }

      const newHistory = [...prev, message]
      console.log('✅ 用户消息已添加:', message.content.substring(0, 20), '历史长度:', newHistory.length)

      // 异步保存到IndexedDB
      chatHistoryService.saveCurrentHistory(newHistory).catch(error => {
        console.error('❌ 保存用户消息失败:', error)
      })

      // 异步通知父组件对话历史变化，避免在渲染过程中同步更新
      setTimeout(() => {
        if (onChatHistoryChange) {
          onChatHistoryChange(newHistory)
        }
      }, 0)

      return newHistory
    })
  }, [onChatHistoryChange, chatHistoryService])

  // 处理跳转到编辑器
  const handleJumpToEditor = useCallback(async (filePath: string) => {
    try {
      if (!artworkId) return;
      // 通过文件路径查找文件ID
      const fileTreeService = FileTreeService.getInstance()
      const fileTreeResult = await fileTreeService.getFileTree(artworkId)
      if (fileTreeResult.success && fileTreeResult.data) {
        const fileId = findFileIdByPath(fileTreeResult.data, filePath)
        if (fileId) {
          // 触发文件选择事件，通知父组件切换到编辑器
          onFileSelect?.(fileId)
          // 可选：设置聚焦状态
          const chatHistoryService = ChatHistoryService.getInstance()
          await chatHistoryService.setCurrentFocusedFile(fileId)
          console.log('🔗 跳转到编辑器:', filePath)
        } else {
          console.warn('⚠️ 未找到文件:', filePath)
        }
      }
    } catch (error) {
      console.error('❌ 跳转到编辑器失败:', error)
    }
  }, [artworkId, onFileSelect])

  // 处理应用diff修改
  const handleApplyDiff = useCallback(async (filePath: string, content: string, operation: string, find?: string) => {
    try {
      if (!artworkId) return;
      // 标准化文件路径，确保相对路径和绝对路径都能正确处理
      const normalizedPath = normalizePath(filePath)
      console.log('🔍 查找文件:', { original: filePath, normalized: normalizedPath })

      const diffToolService = DiffToolService.getInstance()
      const fileTreeService = FileTreeService.getInstance()
      const fileTreeResult = await fileTreeService.getFileTree(artworkId)
      if (!fileTreeResult.success || !fileTreeResult.data) {
        console.error('❌ 获取文件树失败')
        return
      }

      const fileId = findFileIdByPath(fileTreeResult.data, normalizedPath)
      if (!fileId) {
        // 增强错误日志，提供详细的路径匹配调试信息
        const availablePaths = getAllFilePaths(fileTreeResult.data)
        console.warn('⚠️ 未找到文件:', {
          path: normalizedPath,
          originalPath: filePath,
          availableFiles: availablePaths.slice(0, 10), // 只显示前10个文件，避免日志过长
          totalFiles: availablePaths.length
        })
        return
      }
      let result
      if (operation === 'append') {
        result = await diffToolService.appendText(fileId, { content })
      } else if (operation === 'replace') {
        // 解析替换参数
        const [pattern, replacement] = parseReplaceContent(content, find)

        if (!pattern) {
          console.error('❌ 替换操作缺少查找内容')
          return
        }

        // 🚀 使用新的智能字符串替换功能
        result = await diffToolService.intelligentStringReplace(fileId, pattern, replacement)

        if (result.success) {
          console.log('✅ 智能字符串替换成功')

          // 应用差异修改
          if (result.data) {
            await diffToolService.applyDiff(result.data)
          }
        } else {
          console.error('❌ 智能字符串替换失败:', result.error)

          // 显示详细错误信息（包含建议）
          if (result.error) {
            // 这里可以添加用户友好的错误提示UI
            console.log('💡 替换失败详情:\n', result.error)
          }
          return
        }
      }
      if (result?.success) {
        // 对于append操作，需要应用diff；对于replace操作，智能替换已经处理了应用
        if (operation === 'append' && result.data) {
          await diffToolService.applyDiff(result.data)
        }

        console.log('✅ 文件修改已应用:', filePath)

        // 🔧 延迟通知编辑器刷新文件内容，确保文件写入完成
        if (onFileSelect && fileId) {
          setTimeout(() => {
            onFileSelect(fileId)
            console.log('✅ 已通知编辑器刷新文件:', filePath)
          }, 200) // 200ms延迟确保文件系统完成写入
        }

        // 可选：显示成功提示
      } else {
        console.error('❌ 应用修改失败:', result?.error)
      }
    } catch (error) {
      console.error('❌ 应用差异修改失败:', error)
    }
  }, [artworkId, onFileSelect])

  // 处理媒体文件上传成功
  const handleMediaUploadSuccess = useCallback((mediaFile: MediaFile) => {
    setUploadedMedia(prev => {
      const newMediaFiles = [...prev, mediaFile]
      // 通知父组件媒体文件变化
      if (onMediaFilesChange) {
        onMediaFilesChange(newMediaFiles)
      }
      return newMediaFiles
    })
    console.log('✅ 媒体文件上传成功:', mediaFile.filename)
  }, [onMediaFilesChange])

  // 处理媒体文件上传错误
  const handleMediaUploadError = useCallback((error: string) => {
    console.error('❌ 媒体文件上传失败:', error)
    // 可以在这里添加错误提示UI
  }, [])

  // 移除已上传的媒体文件
  const removeMediaFile = useCallback((fileId: string) => {
    setUploadedMedia(prev => {
      const newMediaFiles = prev.filter(file => file.id !== fileId)
      // 通知父组件媒体文件变化
      if (onMediaFilesChange) {
        onMediaFilesChange(newMediaFiles)
      }
      return newMediaFiles
    })
  }, [onMediaFilesChange])

  // 处理创建文件
  const handleCreateFile = useCallback(async (filePath: string, content: string, operation: string) => {
    try {
      if (!artworkId) {
        console.error('❌ 缺少作品ID')
        return
      }

      // 标准化文件路径，确保相对路径和绝对路径都能正确处理
      const normalizedPath = normalizePath(filePath)
      console.log('🔍 创建文件:', { original: filePath, normalized: normalizedPath })

      const diffToolService = DiffToolService.getInstance()

      // 使用新的createFileWithPath方法，传入标准化后的路径
      const result = await diffToolService.createFileWithPath(
        artworkId,
        normalizedPath,
        content,
        operation as 'append' | 'replace'
      )

      if (result.success && result.data) {
        // 应用创建结果
        await diffToolService.applyDiff(result.data)
        console.log('✅ 文件创建并应用成功:', filePath)

        // 文件树会通过事件系统自动刷新，不再需要手动通知
        console.log('✅ 文件创建完成，文件树将自动刷新')

        // 可选：跳转到新创建的文件
        if (result.data.fileId) {
          handleJumpToEditor(filePath)
        }
      } else {
        // 增强错误日志，提供详细的创建失败调试信息
        console.error('❌ 文件创建失败:', {
          path: normalizedPath,
          originalPath: filePath,
          error: result.error,
          operation: operation
        })
      }
    } catch (error) {
      console.error('❌ 创建文件失败:', error)
    }
  }, [artworkId, handleJumpToEditor])

  // 🔧 渲染内容缓存，避免重复渲染相同内容
  const renderedContentCache = useRef<Map<string, JSX.Element>>(new Map())

  // 🔧 简化消息渲染函数 - 添加缓存机制
  const renderMessageContentInternal = useCallback((content: string) => {
    // 检查缓存
    if (renderedContentCache.current.has(content)) {
      return renderedContentCache.current.get(content)!
    }
    // 第一步：先识别df代码块，避免被普通代码块处理干扰
    const dfRegex = /```df\s*\n([\s\S]*?)```/g
    const diffRegex = /<diff>([\s\S]*?)<\/diff>/g
    // 添加human代码块正则表达式，用于BrainstormingHelper功能
    const humanRegex = /```human\s*\n\s*neme:\s*([^\n]+)\s*\n\s*currentPersona\.description:\s*([^\n]+)\s*\n\s*问题：\s*([\s\S]*?)```/g

    // 第二步：提取普通代码块，但排除df代码块
    const codeBlockRegex = /```(?!df\s)(\w+)?\n([\s\S]*?)```/g
    const codeBlocks: { placeholder: string; content: string; lang?: string }[] = []
    let processedContent = content
    let codeBlockMatch

    // 提取普通代码块并替换为占位符（排除df代码块）
    while ((codeBlockMatch = codeBlockRegex.exec(content)) !== null) {
      const placeholder = `__CODE_BLOCK_${codeBlocks.length}__`
      codeBlocks.push({
        placeholder,
        content: codeBlockMatch[2],
        lang: codeBlockMatch[1]
      })
      processedContent = processedContent.replace(codeBlockMatch[0], placeholder)
    }

    // 第三步：在处理后的内容中识别特殊标记（包括df代码块）
    const parts = []
    let lastIndex = 0
    let match

    // 创建一个包含所有匹配项的数组，按位置排序
    const allMatches: any[] = []

    // 收集diff匹配项
    diffRegex.lastIndex = 0
    while ((match = diffRegex.exec(processedContent)) !== null) {
      allMatches.push({
        type: 'diff',
        index: match.index,
        length: match[0].length,
        content: match[1]
      })
    }

    // 收集df匹配项（仅支持JSON格式）
    dfRegex.lastIndex = 0
    while ((match = dfRegex.exec(content)) !== null) {
      const dfContent = match[1].trim()
      
      try {
        // 只解析JSON格式
        if (dfContent.startsWith('{') && dfContent.endsWith('}')) {
          const parsedDf = JSON.parse(dfContent)
          
          // 验证必需字段
          if (!parsedDf.file || !parsedDf.type) {
            throw new Error('缺少必需字段：file 和 type')
          }
          
          // 验证操作类型
          if (!['replace', 'append', 'create'].includes(parsedDf.type)) {
            throw new Error(`不支持的操作类型: ${parsedDf.type}`)
          }
          
          // 对于replace操作，验证find字段
          if (parsedDf.type === 'replace' && !parsedDf.find) {
            throw new Error('replace操作需要find字段')
          }
          
          allMatches.push({
            type: 'df',
            index: match.index,
            length: match[0].length,
            filePath: parsedDf.file,
            operation: parsedDf.type,
            content: parsedDf.content || '',
            find: parsedDf.find || ''
          })
        } else {
          throw new Error('df代码块必须使用JSON格式')
        }
      } catch (error) {
        console.error('❌ 解析df代码块失败:', error)
        console.error('📝 正确格式示例:')
        console.error('```df')
        console.error('{')
        console.error('  "file": "path/to/file",')
        console.error('  "type": "replace",')
        console.error('  "find": "要查找的内容",')
        console.error('  "content": "替换内容"')
        console.error('}')
        console.error('```')
        console.error('📝 实际内容:', dfContent)
        
        // 添加错误项，用于在UI中显示错误信息
        allMatches.push({
          type: 'df',
          index: match.index,
          length: match[0].length,
          filePath: '格式错误',
          operation: 'error',
          content: dfContent,
          error: error instanceof Error ? error.message : String(error)
        })
      }
    }

    // 收集human匹配项（用于BrainstormingHelper功能）
    humanRegex.lastIndex = 0
    while ((match = humanRegex.exec(content)) !== null) {
      allMatches.push({
        type: 'human',
        index: match.index,
        length: match[0].length,
        roleName: match[1].trim(),
        roleDescription: match[2].trim(),
        question: match[3].trim()
      })
    }

    // 按位置排序所有匹配项
    allMatches.sort((a, b) => a.index - b.index)

    // 第三步：处理所有匹配项和普通内容
    for (const matchItem of allMatches) {
      // 添加匹配项前的普通内容
      if (matchItem.index > lastIndex) {
        const beforeContent = processedContent.substring(lastIndex, matchItem.index)
        if (beforeContent.trim()) {
          parts.push({
            type: 'markdown',
            content: beforeContent
          })
        }
      }

      // 添加匹配项内容
      parts.push(matchItem)

      lastIndex = matchItem.index + matchItem.length
    }

    // 添加最后剩余的普通内容
    if (lastIndex < processedContent.length) {
      const afterContent = processedContent.substring(lastIndex)
      if (afterContent.trim()) {
        parts.push({
          type: 'markdown',
          content: afterContent
        })
      }
    }

    // 如果没有特殊内容，直接渲染markdown（恢复代码块）
    if (parts.length === 0) {
      const finalContent = restoreCodeBlocks(processedContent, codeBlocks)
      const result = (
        <div
          className="prose prose-invert max-w-none"
          dangerouslySetInnerHTML={{ __html: renderMarkdown(finalContent) }}
        />
      )

      // 缓存结果
      renderedContentCache.current.set(content, result)

      // 限制缓存大小，避免内存泄漏
      if (renderedContentCache.current.size > 50) {
        const firstKey = renderedContentCache.current.keys().next().value
        renderedContentCache.current.delete(firstKey)
      }

      return result
    }

    // 第四步：渲染混合内容（恢复代码块）
    const result = (
      <div>
        {parts.map((part: any, index) => {
          if (part.type === 'df') {
            return (
              <CompactDiffDisplay
                key={index}
                filePath={part.filePath}
                operation={part.operation as 'append' | 'replace'}
                content={part.content}
                find={part.find}
                onJumpToEditor={handleJumpToEditor}
                onApplyChanges={handleApplyDiff}
                onCreateFile={handleCreateFile}
                onOpenDetailedDiff={onOpenDetailedDiff}
                artworkId={artworkId}
              />
            )
          } else if (part.type === 'human') {
            // human代码块现在通过handleSendMessage统一处理，这里只显示原始内容
            return (
              <div key={index} className="bg-purple-900/20 border border-purple-500/30 rounded-lg p-4 my-2">
                <div className="text-purple-300 text-sm font-handwritten mb-2">
                  🤖 辅助AI协同思考请求
                </div>
                <div className="space-y-2 text-sm">
                  <div><span className="text-amber-300">角色：</span>{part.roleName}</div>
                  <div><span className="text-amber-300">描述：</span>{part.roleDescription}</div>
                  <div><span className="text-amber-300">问题：</span>{part.question}</div>
                </div>
                <div className="text-xs text-gray-400 mt-2">
                  ℹ️ 此请求将通过消息发送时自动处理
                </div>
              </div>
            )
          } else {
            // 恢复markdown内容中的代码块
            const restoredContent = restoreCodeBlocks(part.content, codeBlocks)
            return (
              <div
                key={index}
                className="prose prose-invert max-w-none"
                dangerouslySetInnerHTML={{ __html: renderMarkdown(restoredContent) }}
              />
            )
          }
        })}
      </div>
    )

    // 缓存结果
    renderedContentCache.current.set(content, result)

    // 限制缓存大小，避免内存泄漏
    if (renderedContentCache.current.size > 50) {
      const firstKey = renderedContentCache.current.keys().next().value
      renderedContentCache.current.delete(firstKey)
    }

    return result
  }, [handleJumpToEditor, handleApplyDiff, handleCreateFile, onOpenDetailedDiff, artworkId])

  // 辅助函数：恢复代码块
  const restoreCodeBlocks = (content: string, codeBlocks: { placeholder: string; content: string; lang?: string }[]) => {
    let restoredContent = content
    codeBlocks.forEach(block => {
      const codeBlockHtml = `\`\`\`${block.lang || ''}\n${block.content}\`\`\``
      restoredContent = restoredContent.replace(block.placeholder, codeBlockHtml)
    })
    return restoredContent
  }

  // Markdown渲染函数（简化版，完全移除diff处理）
  const renderMarkdown = (text: string) => {
    // 代码块处理
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g
    const inlineCodeRegex = /`([^`]+)`/g

    // 标题处理
    const headingRegex = /^(#{1,6})\s+(.+)$/gm

    // 粗体和斜体
    const boldRegex = /\*\*(.*?)\*\*/g
    const italicRegex = /\*(.*?)\*/g

    // 链接处理
    const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g

    return text
      // 代码块
      .replace(codeBlockRegex, (match, lang, code) => {
        return `<div class="code-block bg-gray-800 rounded-lg p-3 my-2 overflow-x-auto">
          ${lang ? `<div class="text-xs text-gray-400 mb-2">${lang}</div>` : ''}
          <pre class="text-sm text-green-300 font-mono whitespace-pre-wrap">${code.trim()}</pre>
        </div>`
      })
      // 行内代码
      .replace(inlineCodeRegex, '<code class="bg-gray-800 text-green-300 px-1 py-0.5 rounded text-sm font-mono">$1</code>')
      // 标题
      .replace(headingRegex, (match, hashes, title) => {
        const level = hashes.length
        const className = level === 1 ? 'text-lg font-bold text-amber-200 mt-4 mb-2' :
          level === 2 ? 'text-md font-bold text-amber-200 mt-3 mb-2' :
            'text-sm font-bold text-amber-200 mt-2 mb-1'
        return `<h${level} class="${className}">${title}</h${level}>`
      })
      // 粗体
      .replace(boldRegex, '<strong class="font-bold text-amber-100">$1</strong>')
      // 斜体
      .replace(italicRegex, '<em class="italic text-amber-100">$1</em>')
      // 链接
      .replace(linkRegex, '<a href="$2" class="text-blue-400 hover:text-blue-300 underline" target="_blank" rel="noopener noreferrer">$1</a>')
      // 换行
      .replace(/\n/g, '<br>')
  }

  // 自动调整输入框高度
  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`
    }
  }

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputMessage(e.target.value)
    adjustTextareaHeight()
  }

  // 发送消息
  const handleSend = useCallback(async () => {
    if (!inputMessage.trim() || isLoading) return

    let messageContent = inputMessage.trim()

    // 检查是否有激活的提示词配置
    try {
      const promptConfigService = PromptConfigService.getInstance()
      const activeConfigResult = await promptConfigService.getActiveConfig()

      if (activeConfigResult.success && activeConfigResult.data) {
        const configContentResult = await promptConfigService.getConfigContent(activeConfigResult.data.id)
        if (configContentResult.success && configContentResult.data) {
          // 将提示词内容拼接到用户输入前面
          messageContent = `${configContentResult.data} ${messageContent}`
          console.log('🔮 应用提示词配置:', activeConfigResult.data.name, '内容:', configContentResult.data)
        }
      }
    } catch (error) {
      console.error('获取提示词配置失败:', error)
      // 继续使用原始消息内容
    }

    // 检测是否包含```human代码块
    const hasHumanBlock = hasHumanBlocks(messageContent)

    console.log('🔵 开始发送用户消息:', messageContent.substring(0, 30), hasHumanBlock ? '(包含Human代码块)' : '')

    // 创建用户消息对象
    const userMessage: ChatMessage = {
      id: generateMessageId('user'),
      type: 'user',
      content: messageContent,
      timestamp: Date.now(),
      createdAt: Date.now()
    }

    // 先清空输入框
    setInputMessage('')

    // 重置输入框高度
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
    }

    // 添加用户消息到历史记录
    addUserMessage(userMessage)

    if (hasHumanBlock) {
      // 如果包含```human代码块，处理辅助AI调用
      handleHumanBlockMessage(messageContent)
    } else {
      // 正常消息处理
      setTimeout(() => {
        onSendMessage(messageContent)
      }, 0)
    }
  }, [inputMessage, isLoading, generateMessageId, addUserMessage, onSendMessage])

  // 处理包含```human代码块的消息
  const handleHumanBlockMessage = useCallback((message: string) => {
    // 生成消息唯一标识符，防止重复处理
    const messageHash = btoa(encodeURIComponent(message)).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16)

    if (processedMessages.has(messageHash)) {
      console.log('⚠️ 消息已处理过，跳过重复处理:', messageHash)
      return
    }

    // 标记消息为已处理
    setProcessedMessages(prev => new Set([...prev, messageHash]))

    const humanBlocks = extractHumanBlocks(message)

    console.log('🤖 检测到Human代码块:', humanBlocks.length, '个', '消息ID:', messageHash)

    // 为每个```human代码块创建HelperResponseLayer
    humanBlocks.forEach((block, index) => {
      const layerId = `helper-${messageHash}-${index}`

      const messageLayer: MessageLayer = {
        id: layerId,
        type: 'helper-response',
        content: '',
        timestamp: Date.now(),
        metadata: {
          humanBlock: {
            roleName: block.roleName,
            roleDescription: block.roleDescription,
            question: block.question
          },
          originalMessage: message,
          isExpanded: true,
          layerId
        }
      }

      // 添加到消息层列表
      setMessageLayers(prev => [...prev, messageLayer])
    })

    // 移除```human代码块后的消息内容
    const cleanMessage = removeHumanBlocks(message)

    // 如果还有其他内容，也发送给主AI
    if (cleanMessage.trim()) {
      setTimeout(() => {
        onSendMessage(cleanMessage)
      }, 100)
    }
  }, [onSendMessage, processedMessages])

  // 处理辅助AI集成完成
  const handleIntegrationComplete = useCallback((integratedMessage: string) => {
    console.log('🔄 辅助AI集成完成，准备发送给主AI')
    setPendingIntegratedMessage(integratedMessage)
  }, [])

  // 处理主AI回复
  const handleMainAIResponse = useCallback((response: string) => {
    console.log('✅ 主AI回复完成')
    // 这里可以添加额外的处理逻辑
  }, [])

  // 处理辅助AI错误
  const handleHelperError = useCallback((error: string) => {
    console.error('❌ 辅助AI处理错误:', error)
    // 可以显示错误提示
  }, [])

  // 发送集成消息给主AI
  useEffect(() => {
    if (pendingIntegratedMessage) {
      setTimeout(() => {
        onSendMessage(pendingIntegratedMessage)
        setPendingIntegratedMessage('')
      }, 500)
    }
  }, [pendingIntegratedMessage, onSendMessage])

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  // 加载文件路径 - 添加数据完整性验证
  useEffect(() => {
    const loadFilePaths = async () => {
      if (associatedFiles.length === 0) {
        setFilePaths(new Map())
        return
      }

      const newFilePaths = new Map<string, string>()
      const validFileIds: string[] = []
      const invalidFileIds: string[] = []

      for (const fileId of associatedFiles) {
        // 数据完整性验证：检查fileId是否有效
        if (!fileId || typeof fileId !== 'string' || fileId.trim().length === 0) {
          console.warn('⚠️ 发现无效的文件ID:', fileId)
          invalidFileIds.push(fileId)
          continue
        }

        try {
          const filePath = await findFilePathById(fileId)

          // 验证文件路径是否有效
          if (filePath && !filePath.includes('未知文件')) {
            newFilePaths.set(fileId, filePath)
            validFileIds.push(fileId)
          } else {
            console.warn('⚠️ 文件不存在或无法访问:', fileId)
            invalidFileIds.push(fileId)
          }
        } catch (error) {
          console.error('❌ 获取文件路径失败:', fileId, error)
          invalidFileIds.push(fileId)
        }
      }

      setFilePaths(newFilePaths)

      // 如果发现无效文件，自动清理
      if (invalidFileIds.length > 0) {
        console.log('🧹 发现', invalidFileIds.length, '个无效文件，自动清理:', invalidFileIds)

        // 通知父组件更新文件关联，移除无效文件
        if (validFileIds.length !== associatedFiles.length) {
          try {
            await chatHistoryService.saveCurrentFileAssociations(validFileIds)
            console.log('✅ 已自动清理无效文件关联')
          } catch (error) {
            console.error('❌ 清理无效文件关联失败:', error)
          }
        }
      }
    }

    loadFilePaths()
  }, [associatedFiles, findFilePathById, chatHistoryService])

  // 🔧 AI响应完成处理 - 使用防抖机制减少重复触发
  const lastSavedContentRef = useRef('')

  useEffect(() => {
    if (isComplete && responseContent && responseContent !== lastSavedContentRef.current) {
      lastSavedContentRef.current = responseContent

      console.log('🤖 AI响应完成，准备保存消息:', {
        isComplete,
        contentLength: responseContent.length,
        contentPreview: responseContent.substring(0, 50) + '...'
      })

      const assistantMessage: ChatMessage = {
        id: generateMessageId('assistant'),
        type: 'assistant',
        content: responseContent,
        timestamp: Date.now(),
        createdAt: Date.now()
      }

      setChatHistory(prev => {
        console.log('📋 当前历史记录数量:', prev.length)

        // 🔧 改进重复消息检测：检查最近的消息，而不是所有消息
        // 只检查最近3条assistant消息，避免误判
        const recentAssistantMessages = prev
          .filter(m => m.type === 'assistant')
          .slice(-3) // 只检查最近3条

        const duplicateExists = recentAssistantMessages.some(m => {
          const contentMatch = m.content === responseContent
          const timeMatch = Math.abs(m.timestamp - assistantMessage.timestamp) < 5000 // 5秒内

          console.log('🔍 重复检测:', {
            messageId: m.id.substring(0, 8),
            contentMatch,
            timeMatch,
            timeDiff: Math.abs(m.timestamp - assistantMessage.timestamp)
          })

          return contentMatch && timeMatch
        })

        if (duplicateExists) {
          console.log('⚠️ 检测到重复消息，跳过添加')
          return prev
        }

        console.log('✅ 添加新的AI响应消息:', assistantMessage.id.substring(0, 8))
        const newHistory = [...prev, assistantMessage]

        // 异步保存历史记录
        chatHistoryService.saveCurrentHistory(newHistory).catch(error => {
          console.error('❌ 保存AI响应消息失败:', error)
        })

        // 通知父组件历史记录变化
        if (onChatHistoryChange) {
          onChatHistoryChange(newHistory)
        }

        return newHistory
      })
    }
  }, [isComplete, responseContent, generateMessageId, chatHistoryService, onChatHistoryChange])

  // 暂时禁用文本选择保护
  // useEffect(() => {
  //   if (selectionManagerRef.current && aiResponseRef.current) {
  //     if (isStreaming) {
  //       selectionManagerRef.current.clearSelection()
  //     } else if (!selectionManagerRef.current.isSelecting()) {
  //       selectionManagerRef.current.saveSelection(aiResponseRef.current)
  //     }
  //   }
  // }, [isStreaming])

  useEffect(() => {
    // 在DOM更新后恢复选择状态
    if (selectionManagerRef.current && aiResponseRef.current && !isStreaming && isComplete) {
      setTimeout(() => {
        selectionManagerRef.current?.restoreSelection(aiResponseRef.current!)
      }, 50) // 短延迟确保DOM更新完成
    }
  }, [isStreaming, isComplete]) // 🔧 关键修复：移除responseContent依赖，避免持续重新渲染

  // 🔧 简化的滚动状态管理 - 参考StreamingResponse的简单模式
  const scrollStateRef = useRef({
    shouldAutoScroll: true,
    lastContentLength: 0
  })

  // 🔧 简化的滚动控制 - 参考StreamingResponse的直接滚动方式
  const scrollToBottom = useCallback(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
    }
  }, [])

  // 🔧 添加流式响应状态监控 - 移除responseContent.length依赖，避免频繁重渲染
  useEffect(() => {
    console.log('🌊 流式响应状态变化:', {
      isStreaming,
      isComplete,
      responseContentLength: responseContentRef.current.length,
      timestamp: Date.now()
    })
  }, [isStreaming, isComplete])

  // 🔧 检测用户是否在底部附近
  const isUserNearBottom = useCallback(() => {
    if (!chatContainerRef.current) return false
    const container = chatContainerRef.current
    return container.scrollTop + container.clientHeight >= container.scrollHeight - 100
  }, [])

  // 🔧 聊天历史变化时的滚动控制 - 简化版本，参考StreamingResponse
  useEffect(() => {
    if (chatContainerRef.current && chatHistory.length > 0) {
      // 简化逻辑：如果用户在底部或启用自动滚动，直接滚动到底部
      if (isUserNearBottom() || scrollStateRef.current.shouldAutoScroll) {
        chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
      }
    }
  }, [chatHistory.length, isUserNearBottom])

  // 🔧 流式响应时的实时滚动 - 简化版本，参考StreamingResponse的直接模式
  useEffect(() => {
    if (isStreaming && responseContentRef.current && chatContainerRef.current) {
      const currentLength = responseContentRef.current.length

      // 简化逻辑：内容增加时，如果用户在底部就直接滚动
      if (currentLength > scrollStateRef.current.lastContentLength) {
        scrollStateRef.current.lastContentLength = currentLength

        if (isUserNearBottom() || scrollStateRef.current.shouldAutoScroll) {
          // 直接滚动，无需额外的函数调用
          chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
        }
      }
    }
  }, [isStreaming, isUserNearBottom]) // 🔧 简化依赖项

  // 🔧 用户滚动事件处理 - 智能检测用户滚动意图
  const handleUserScroll = useCallback(() => {
    if (chatContainerRef.current) {
      // 当用户手动滚动时，根据位置决定是否启用自动滚动
      scrollStateRef.current.shouldAutoScroll = isUserNearBottom()
    }
  }, [isUserNearBottom])

  // 处理会话切换
  const handleSessionSelect = useCallback(async (sessionId: string) => {
    try {
      console.log('🔄 开始切换会话:', sessionId)

      // 🔧 关键修复：在切换会话前，先清理当前AI响应状态
      // 这是解决会话切换时AI响应残留问题的核心修复
      console.log('🧹 清理AI响应状态...')

      // 通知父组件清理AI响应状态
      // 这里需要父组件提供清理回调函数
      if (onClearAIResponse) {
        onClearAIResponse()
      }

      setCurrentSessionId(sessionId)

      // 加载选中会话的历史记录
      const sessionHistory = await chatHistoryService.loadHistory(sessionId)
      setChatHistory(sessionHistory)

      // 通知父组件对话历史变化
      if (onChatHistoryChange) {
        onChatHistoryChange(sessionHistory)
      }

      console.log('✅ 会话已切换:', sessionId, '历史记录:', sessionHistory.length, '条')
      console.log('✅ AI响应状态已清理')
    } catch (error) {
      console.error('❌ 切换会话失败:', error)
    }
  }, [chatHistoryService, onChatHistoryChange, onClearAIResponse])

  // 处理文件关联变化
  const handleFilesChange = useCallback(async (newFiles: string[]) => {
    try {
      console.log('🔥 ChatInterface - 接收到文件关联变化:', newFiles)
      console.log('🔥 ChatInterface - 当前associatedFiles:', associatedFiles)

      // 保存到ChatHistoryService
      await chatHistoryService.saveCurrentFileAssociations(newFiles)

      // 通知父组件
      console.log('🔥 ChatInterface - 调用父组件onFilesChange:', newFiles)
      onFilesChange(newFiles)

      console.log('✅ 文件关联已更新:', newFiles.length, '个文件')
    } catch (error) {
      console.error('❌ 保存文件关联失败:', error)
    }
  }, [chatHistoryService, onFilesChange, associatedFiles])

  // 处理单条消息插入到编辑器
  const handleInsertMessage = useCallback((messageContent: string) => {
    console.log('🔄 尝试插入消息内容到编辑器:', messageContent.substring(0, 100) + '...')

    if (onMessageContentInsert) {
      console.log('✅ 使用消息内容插入弹窗')
      // 使用专门的消息内容插入接口，会显示插入弹窗
      onMessageContentInsert(messageContent)
    } else if (onContentInsert) {
      console.log('✅ 直接使用onContentInsert接口插入内容')
      // 直接使用onContentInsert插入特定消息内容
      onContentInsert(messageContent, {
        position: 'cursor', // 在光标位置插入
        addNewlines: true // 添加新行
      })
    } else if (onInsertToEditor) {
      console.log('⚠️ 使用降级插入方法')

      // 创建一个临时的插入函数，直接操作编辑器
      try {
        // 尝试直接访问编辑器并插入内容
        const activeEditor = document.querySelector('textarea, [contenteditable="true"]') as HTMLTextAreaElement | HTMLElement

        if (activeEditor) {
          if (activeEditor instanceof HTMLTextAreaElement) {
            // 处理textarea
            const start = activeEditor.selectionStart || 0
            const end = activeEditor.selectionEnd || 0
            const currentValue = activeEditor.value
            const newValue = currentValue.slice(0, start) + '\n' + messageContent + '\n' + currentValue.slice(end)

            activeEditor.value = newValue
            activeEditor.selectionStart = activeEditor.selectionEnd = start + messageContent.length + 2

            // 触发change事件
            const event = new Event('input', { bubbles: true })
            activeEditor.dispatchEvent(event)

            console.log('✅ 直接插入到textarea成功')
          } else if (activeEditor.contentEditable === 'true') {
            // 处理contenteditable元素
            const selection = window.getSelection()
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0)
              range.deleteContents()

              const textNode = document.createTextNode('\n' + messageContent + '\n')
              range.insertNode(textNode)

              // 移动光标到插入内容后
              range.setStartAfter(textNode)
              range.setEndAfter(textNode)
              selection.removeAllRanges()
              selection.addRange(range)

              console.log('✅ 直接插入到contenteditable成功')
            }
          }
        } else {
          // 如果找不到编辑器，使用原有方法
          console.log('⚠️ 未找到编辑器元素，使用原有插入方法')
          onInsertToEditor()
        }
      } catch (error) {
        console.error('❌ 直接插入失败，使用原有方法:', error)
        onInsertToEditor()
      }
    } else {
      console.warn('⚠️ 没有可用的插入接口')
    }
  }, [onMessageContentInsert, onContentInsert, onInsertToEditor])

  // 处理重新生成AI消息
  const handleRegenerateMessage = useCallback((messageIndex: number) => {
    // 找到该AI消息前面的用户消息
    const messages = chatHistory.slice(0, messageIndex)
    const lastUserMessage = messages.reverse().find(msg => msg.type === 'user')

    if (lastUserMessage) {
      console.log('🔄 重新生成AI消息，基于用户消息:', lastUserMessage.content.substring(0, 50))
      console.log(`📊 历史消息限制：只包含索引 0-${messageIndex-1} 的消息`)

      // 删除从当前AI消息开始的所有后续消息
      const newHistory = chatHistory.slice(0, messageIndex)
      setChatHistory(newHistory)

      // 保存更新后的历史记录
      chatHistoryService.saveCurrentHistory(newHistory)

      // 重新发送用户消息，传递目标消息索引以限制历史消息范围
      onSendMessage(lastUserMessage.content, messageIndex)

      // 关闭工具面板
      setActiveMessageTools(null)
    }
  }, [chatHistory, onSendMessage, chatHistoryService])

  // 处理编辑用户消息
  const handleEditMessage = useCallback((messageId: string, currentContent: string) => {
    setEditingMessageId(messageId)
    setEditingContent(currentContent)
    setActiveMessageTools(null)
  }, [])

  // 处理保存编辑的消息
  const handleSaveEditedMessage = useCallback((messageId: string, newContent: string) => {
    const messageIndex = chatHistory.findIndex(msg => msg.id === messageId)
    if (messageIndex === -1) return

    // 更新消息内容
    const updatedMessage = { ...chatHistory[messageIndex], content: newContent, updatedAt: Date.now() }

    // 删除该消息之后的所有消息
    const newHistory = [...chatHistory.slice(0, messageIndex), updatedMessage]
    setChatHistory(newHistory)

    // 保存更新后的历史记录
    chatHistoryService.saveCurrentHistory(newHistory)

    // 重新发送编辑后的消息
    onSendMessage(newContent)

    // 清除编辑状态
    setEditingMessageId(null)
    setEditingContent('')

    console.log('✅ 消息已编辑并重发:', newContent.substring(0, 50))
  }, [chatHistory, onSendMessage, chatHistoryService])

  // 处理多项对比
  const handleMultipleComparison = useCallback((messageIndex: number) => {
    // 找到该AI消息前面的用户消息
    const messages = chatHistory.slice(0, messageIndex)
    const lastUserMessage = messages.reverse().find(msg => msg.type === 'user')

    if (lastUserMessage) {
      console.log('🔄 开启多项对比，基于用户消息:', lastUserMessage.content.substring(0, 50))
      console.log('🎯 记录触发消息索引:', messageIndex)
      
      // 设置对比内容并打开弹窗
      setComparisonMessageContent(lastUserMessage.content)
      setComparisonTriggerMessageIndex(messageIndex) // 记录触发的消息索引
      setShowMultipleComparison(true)
      
      // 关闭工具面板
      setActiveMessageTools(null)
    }
  }, [chatHistory])

  // 处理多项对比结果选择
  const handleComparisonResultSelect = useCallback((selectedResult: string) => {
    console.log('🎯 多项对比结果选择，替换最后一条AI消息:', selectedResult.substring(0, 100) + '...')
    
    // 替换最后一条AI消息的内容
    setChatHistory(prev => {
      const newHistory = [...prev]
      
      // 从后往前找最后一条AI消息
      for (let i = newHistory.length - 1; i >= 0; i--) {
        if (newHistory[i].type === 'assistant') {
          // 替换这条AI消息的内容
          newHistory[i] = {
            ...newHistory[i],
            content: selectedResult,
            timestamp: Date.now() // 更新时间戳
          }
          
          console.log('✅ 已替换AI消息:', newHistory[i].id)
          break
        }
      }
      
      // 保存更新后的历史记录
      chatHistoryService.saveCurrentHistory(newHistory).catch(error => {
        console.error('❌ 保存替换后的消息失败:', error)
      })
      
      return newHistory
    })
    
    // 关闭弹窗
    setShowMultipleComparison(false)
    setComparisonMessageContent('')
  }, [chatHistoryService])



  // 处理取消编辑
  const handleCancelEdit = useCallback(() => {
    setEditingMessageId(null)
    setEditingContent('')
  }, [])

  // 获取可用模型列表
  const loadAvailableModels = useCallback(async () => {
    if (!currentConfig?.apiKey) {
      console.warn('⚠️ 没有可用的API配置')
      return
    }

    setIsLoadingModels(true)
    try {
      const aiService = AIService.getInstance()
      const result = await aiService.getAvailableModels(currentConfig)

      if (result.success && result.data && result.data.length > 0) {
        // 转换为模型对象格式
        const models = result.data.map(modelId => ({
          id: modelId,
          name: modelId.toUpperCase().replace(/-/g, ' '),
          description: getModelDescription(modelId)
        }))
        setAvailableModels(models)
        console.log('✅ 成功获取模型列表:', models.length, '个模型')
      } else {
        console.warn('⚠️ 获取模型列表失败')
        setAvailableModels([])
      }
    } catch (error) {
      console.error('❌ 获取模型列表失败:', error)
      setAvailableModels([])
    } finally {
      setIsLoadingModels(false)
    }
  }, [currentConfig])

  // 获取模型描述
  const getModelDescription = useCallback((modelId: string): string => {
    const lowerModelId = modelId.toLowerCase()

    // GPT系列模型
    if (lowerModelId.includes('gpt-4')) {
      if (lowerModelId.includes('turbo')) {
        return '更快的GPT-4，性价比更高'
      }
      return '最强大的模型，适合复杂任务'
    } else if (lowerModelId.includes('gpt-3.5')) {
      return '快速且经济的选择'
    }

    // Claude系列模型
    else if (lowerModelId.includes('claude')) {
      return 'Anthropic Claude，强大的推理能力'
    }

    // Gemini系列模型
    else if (lowerModelId.includes('gemini')) {
      return 'Google Gemini，多模态能力强'
    }

    // 其他模型
    return `可用的AI模型: ${modelId}`
  }, [])



  // 点击外部关闭消息工具面板
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (activeMessageTools) {
        // 检查点击是否在工具面板外部
        const target = event.target as Element
        if (!target.closest('.message-tools-panel') && !target.closest('.message-tools-button')) {
          setActiveMessageTools(null)
        }
      }

      // 点击外部取消编辑状态（除非点击在编辑区域内）
      if (editingMessageId) {
        const target = event.target as Element
        if (!target.closest('textarea') && !target.closest('button')) {
          handleCancelEdit()
        }
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [activeMessageTools, editingMessageId, handleCancelEdit])

  // 暴露方法给父组件
  React.useImperativeHandle(ref, () => ({
    handleSessionSelect
  }), [handleSessionSelect])

  return (
    <div className={`flex flex-col h-full ${className}`}>


      {/* 文件关联显示区域 */}
      {(associatedFiles.length > 0 || focusedFile) && (
        <div className="px-4 py-2 bg-gray-800/20 border-b border-amber-500/10">
          <div
            className="flex items-center gap-2 mb-2 cursor-pointer hover:bg-gray-700/20 rounded-md p-1 -m-1 transition-all duration-200"
            onClick={() => setIsFileAssociationCollapsed(!isFileAssociationCollapsed)}
          >
            <svg
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="currentColor"
              className={`text-amber-400 transition-transform duration-200 ${isFileAssociationCollapsed ? 'rotate-0' : 'rotate-90'}`}
            >
              <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
            </svg>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor" className="text-amber-400">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            </svg>
            <span className="text-xs text-amber-200 font-handwritten">关联文件</span>
            <span className="text-xs text-gray-400">
              ({focusedFile ? (associatedFiles.includes(focusedFile) ? associatedFiles.length : associatedFiles.length + 1) : associatedFiles.length})
            </span>
            <span className="text-xs text-gray-500 ml-auto">
              {isFileAssociationCollapsed ? '点击展开' : '点击折叠'}
            </span>
          </div>

          {!isFileAssociationCollapsed && (
            <div className="flex flex-wrap gap-2 mt-2">
              {/* 创建包含聚焦文件和关联文件的合并列表，聚焦文件优先显示 */}
              {(() => {
                // 创建文件显示列表，聚焦文件优先
                const allFiles = new Set([
                  ...(focusedFile ? [focusedFile] : []),
                  ...associatedFiles
                ])

                return Array.from(allFiles).map((fileId, index) => {
                  const filePath = filePaths.get(fileId) || `加载中... (${fileId.substring(0, 8)}...)`
                  const isFocused = focusedFile === fileId
                  const isManuallyAssociated = associatedFiles.includes(fileId)

                  // 确定文件类型和样式
                  const fileType = isFocused ? 'auto' : 'manual'
                  const styleConfig = {
                    auto: {
                      className: 'bg-blue-500/20 text-blue-200 border-blue-500/50',
                      icon: (
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z" />
                        </svg>
                      ),
                      label: '自动',
                      title: '当前编辑文件（自动关联）'
                    },
                    manual: {
                      className: 'bg-green-500/20 text-green-200 border-green-500/50',
                      icon: (
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                        </svg>
                      ),
                      label: '手动',
                      title: '手动关联文件'
                    }
                  }

                  const config = styleConfig[fileType]

                  return (
                    <div
                      key={fileId}
                      className={`flex items-center gap-2 px-2 py-1 ${config.className} border rounded-md text-xs`}
                      title={config.title}
                    >
                      {config.icon}
                      <span className="text-xs opacity-75 font-handwritten">{config.label}</span>
                      <span className="font-mono" title={filePath}>{filePath}</span>

                      {/* 只有手动关联的文件才显示移除按钮 */}
                      {isManuallyAssociated && !isFocused && (
                        <button
                          onClick={() => {
                            const newFiles = associatedFiles.filter(id => id !== fileId)
                            handleFilesChange(newFiles)
                          }}
                          className="ml-1 p-0.5 text-green-300 hover:text-red-400 hover:bg-red-500/10 rounded transition-all duration-200"
                          title="移除手动关联文件"
                        >
                          <svg width="10" height="10" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                          </svg>
                        </button>
                      )}

                      {/* 自动关联文件显示状态指示 */}
                      {isFocused && (
                        <div className="ml-1 px-1 py-0.5 bg-blue-600/30 text-blue-100 rounded text-xs font-handwritten">
                          当前
                        </div>
                      )}
                    </div>
                  )
                })
              })()}
            </div>
          )}
        </div>
      )}

      {/* 对话历史区域 */}
      <div
        ref={chatContainerRef}
        className="flex-1 overflow-y-auto custom-scrollbar p-4 space-y-4"
        onScroll={handleUserScroll}
      >
        {chatHistory.length === 0 && !isStreaming && (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-lg bg-amber-500/20 flex items-center justify-center">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor" className="text-amber-400">
                <path d="M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2Z" />
              </svg>
            </div>
            <h4 className="text-lg font-handwritten text-amber-200 mb-2">
              开始对话
            </h4>
            <p className="text-gray-400 text-sm font-handwritten">
              {currentConfig ? '输入消息开始与AI对话' : '请先在配置页面设置API Key'}
            </p>
          </div>
        )}

        {/* 渲染对话历史 */}
        {chatHistory.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] p-3 rounded-lg ${message.type === 'user'
                ? 'bg-blue-500/20 text-blue-200 border border-blue-500/50'
                : 'bg-green-500/20 text-green-200 border border-green-500/50'
                }`}
            >
              <div className="flex items-center justify-between mb-1">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4">
                    {message.type === 'user' ? (
                      <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z" />
                      </svg>
                    ) : (
                      <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.5,12A1.5,1.5 0 0,1 16,10.5A1.5,1.5 0 0,1 17.5,9A1.5,1.5 0 0,1 19,10.5A1.5,1.5 0 0,1 17.5,12M10,10.5C10,11.3 9.3,12 8.5,12S7,11.3 7,10.5C7,9.7 7.7,9 8.5,9S10,9.7 10,10.5M12,14C9.8,14 8,15.8 8,18V20H16V18C16,15.8 14.2,14 12,14M12,4C14.2,4 16,5.8 16,8S14.2,12 12,12S8,10.2 8,8S9.8,4 12,4M12,2A6,6 0 0,0 6,8C6,11.3 8.7,14 12,14S18,11.3 18,8A6,6 0 0,0 12,2Z" />
                      </svg>
                    )}
                  </div>
                  <span className="text-xs font-handwritten">
                    {message.type === 'user' ? '你' : 'AI助手'}
                  </span>
                  <span className="text-xs opacity-60">
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </span>
                </div>

                {/* 用户消息工具按钮 */}
                {message.type === 'user' && (
                  <div className="relative">
                    <button
                      onClick={() => setActiveMessageTools(activeMessageTools === message.id ? null : message.id)}
                      className="message-tools-button p-1 rounded-md bg-blue-400/10 hover:bg-blue-400/20 border border-blue-400/20 hover:border-blue-400/40 transition-all duration-200 text-blue-300 hover:text-blue-200 opacity-90 hover:opacity-100"
                      title="消息工具"
                    >
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z" />
                      </svg>
                    </button>

                    {/* 用户消息悬浮工具面板 */}
                    {activeMessageTools === message.id && (
                      <div className="message-tools-panel absolute right-0 top-full mt-1 bg-gray-800 border border-blue-500/30 rounded-lg shadow-lg z-50 min-w-[120px]">
                        <button
                          onClick={() => {
                            handleEditMessage(message.id, message.content)
                          }}
                          className="w-full px-3 py-2 text-left text-sm text-blue-200 hover:bg-blue-500/20 transition-all duration-200 font-handwritten flex items-center gap-2"
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z" />
                          </svg>
                          编辑重发
                        </button>
                      </div>
                    )}
                  </div>
                )}

                {/* AI助手消息工具按钮 */}
                {message.type === 'assistant' && (
                  <div className="relative">
                    <button
                      onClick={() => setActiveMessageTools(activeMessageTools === message.id ? null : message.id)}
                      className="message-tools-button p-1 rounded-md bg-amber-400/10 hover:bg-amber-400/20 border border-amber-400/20 hover:border-amber-400/40 transition-all duration-200 text-amber-300 hover:text-amber-200 opacity-90 hover:opacity-100"
                      title="消息工具"
                    >
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z" />
                      </svg>
                    </button>

                    {/* 悬浮工具面板 */}
                    {activeMessageTools === message.id && (
                      <div className="message-tools-panel absolute right-0 top-full mt-1 bg-gray-800 border border-green-500/30 rounded-lg shadow-lg z-50 min-w-[140px]">
                        <button
                          onClick={() => {
                            handleInsertMessage(message.content)
                            setActiveMessageTools(null)
                          }}
                          className="w-full px-3 py-2 text-left text-sm text-green-200 hover:bg-green-500/20 transition-all duration-200 font-handwritten border-b border-green-500/20 flex items-center gap-2"
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                          </svg>
                          插入到编辑器
                        </button>
                        <button
                          onClick={() => {
                            const messageIndex = chatHistory.findIndex(msg => msg.id === message.id)
                            if (messageIndex !== -1) {
                              handleRegenerateMessage(messageIndex)
                            }
                          }}
                          className="w-full px-3 py-2 text-left text-sm text-amber-200 hover:bg-amber-500/20 transition-all duration-200 font-handwritten flex items-center gap-2 border-b border-amber-500/20"
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
                          </svg>
                          重新生成
                        </button>
                        <button
                          onClick={() => {
                            const messageIndex = chatHistory.findIndex(msg => msg.id === message.id)
                            if (messageIndex !== -1) {
                              handleMultipleComparison(messageIndex)
                            }
                          }}
                          className="w-full px-3 py-2 text-left text-sm text-purple-200 hover:bg-purple-500/20 transition-all duration-200 font-handwritten flex items-center gap-2"
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V6.5H5V5H19M19,19H5V8.5H19V19Z" />
                          </svg>
                          多项对比
                        </button>

                      </div>
                    )}
                  </div>
                )}
              </div>
              <div className="text-sm">
                {editingMessageId === message.id ? (
                  // 编辑模式
                  <div className="space-y-2">
                    <textarea
                      value={editingContent}
                      onChange={(e) => setEditingContent(e.target.value)}
                      className="w-full p-2 bg-gray-700 border border-blue-500/30 rounded-md text-blue-200 font-handwritten resize-none focus:outline-none focus:border-blue-500/60"
                      rows={Math.max(2, editingContent.split('\n').length)}
                      placeholder="编辑消息内容..."
                      autoFocus
                    />
                    <div className="flex gap-2 justify-end">
                      <button
                        onClick={handleCancelEdit}
                        className="px-3 py-1 text-xs bg-gray-600 hover:bg-gray-500 text-gray-200 rounded-md transition-all duration-200 font-handwritten"
                      >
                        取消
                      </button>
                      <button
                        onClick={() => handleSaveEditedMessage(message.id, editingContent)}
                        className="px-3 py-1 text-xs bg-blue-600 hover:bg-blue-500 text-white rounded-md transition-all duration-200 font-handwritten"
                        disabled={!editingContent.trim()}
                      >
                        保存并重发
                      </button>
                    </div>
                  </div>
                ) : (
                  // 正常显示模式
                  renderMessageContentInternal(message.content)
                )}
              </div>
            </div>
          </div>
        ))}

        {/* 渲染辅助AI回复层 */}
        {messageLayers.map((layer) => (
          <div key={layer.id} className="w-full">
            {layer.type === 'helper-response' && layer.metadata?.humanBlock && (
              <HelperResponseLayer
                messageId={layer.id}
                humanBlockContent={layer.metadata.humanBlock}
                onIntegrationComplete={handleIntegrationComplete}
                onMainAIResponse={handleMainAIResponse}
                onError={handleHelperError}
                className="my-4"
              />
            )}
          </div>
        ))}

        {/* AI响应气泡 - 简化显示条件，流式完成后立即隐藏 */}
        {(isLoading || (isStreaming && !isComplete)) && (
          <div className="flex justify-start">
            <div className="max-w-[80%] p-3 bg-green-500/20 text-green-200 border border-green-500/50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-4 h-4">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M17.5,12A1.5,1.5 0 0,1 16,10.5A1.5,1.5 0 0,1 17.5,9A1.5,1.5 0 0,1 19,10.5A1.5,1.5 0 0,1 17.5,12M10,10.5C10,11.3 9.3,12 8.5,12S7,11.3 7,10.5C7,9.7 7.7,9 8.5,9S10,9.7 10,10.5M12,14C9.8,14 8,15.8 8,18V20H16V18C16,15.8 14.2,14 12,14M12,4C14.2,4 16,5.8 16,8S14.2,12 12,12S8,10.2 8,8S9.8,4 12,4M12,2A6,6 0 0,0 6,8C6,11.3 8.7,14 12,14S18,11.3 18,8A6,6 0 0,0 12,2Z" />
                  </svg>
                </div>
                <span className="text-xs font-handwritten">AI助手</span>
                <span className="text-xs opacity-60">
                  {isLoading && !responseContent ? '正在思考...' :
                    isStreaming ? '正在输入...' : '刚刚'}
                </span>
                {isStreaming && onStop && (
                  <button
                    onClick={onStop}
                    className="ml-auto p-1 text-gray-400 hover:text-red-400 hover:bg-red-500/10 rounded transition-all duration-200"
                    title="停止生成"
                  >
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M6,6H18V18H6V6Z" />
                    </svg>
                  </button>
                )}
              </div>

              {/* 内容显示 */}
              {responseContent ? (
                <div ref={aiResponseRef} className="text-sm">
                  {renderMessageContentInternal(responseContent)}
                  {isStreaming && (
                    <span className="animate-pulse text-amber-400 ml-1">|</span>
                  )}
                </div>
              ) : (
                /* 等待动画 */
                <div className="flex items-center gap-2 text-amber-400">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-amber-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                    <div className="w-2 h-2 bg-amber-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                    <div className="w-2 h-2 bg-amber-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                  </div>
                </div>
              )}

              {/* 错误显示 */}
              {error && (
                <div className="mt-2 p-2 bg-red-500/20 border border-red-500/50 rounded text-red-200 text-xs">
                  <div className="flex items-center justify-between">
                    <span>{error}</span>
                    {onRetry && (
                      <button
                        onClick={onRetry}
                        className="ml-2 px-2 py-1 bg-red-500/30 hover:bg-red-500/50 rounded text-xs transition-colors"
                      >
                        重试
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 输入区域 */}
      <div className="p-4 bg-gray-800/30 border-t border-amber-500/10">
        <div className="relative">
          <div className="relative">
            <textarea
              ref={textareaRef}
              value={inputMessage}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder={currentConfig ? "输入消息..." : "请先配置API Key"}
              disabled={!currentConfig || isLoading}
              className="w-full min-h-[40px] max-h-[120px] p-3 pr-12 bg-gray-800 border border-gray-600 rounded-lg text-amber-200 placeholder-gray-500 resize-none focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 disabled:opacity-50 transition-all duration-200"
              rows={1}
            />

            {/* 输入框内的发送按钮 */}
            <button
              onClick={handleSend}
              disabled={!inputMessage.trim() || !currentConfig || isLoading}
              className={`absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-md transition-all duration-200 ${inputMessage.trim() && currentConfig && !isLoading
                  ? 'text-amber-400 hover:text-amber-300 hover:bg-amber-500/20'
                  : 'text-gray-500 cursor-not-allowed'
                }`}
              title={isLoading ? '发送中...' : 'Enter 发送'}
            >
              {isLoading ? (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="animate-spin">
                  <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z" />
                </svg>
              ) : (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z" />
                </svg>
              )}
            </button>
          </div>

          {/* 底部信息栏 */}
          <div className="flex items-center justify-between mt-3 px-1">
            <div className="flex items-center gap-4 text-xs text-gray-400">
              <span className="flex items-center gap-1">
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" className="opacity-60">
                  <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z" />
                  <path d="M6.25,7.72H10V10.5H14V13.5H10V16.25H6.25V13.5H3.5V10.5H6.25V7.72Z" />
                </svg>
                Shift+Enter 换行
              </span>
              <span className="flex items-center gap-1">
                <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" className="opacity-60">
                  <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z" />
                  <path d="M6.25,7.72H17.75V10.5H6.25V7.72Z" />
                </svg>
                Enter 发送
              </span>
            </div>

            <div className="flex items-center gap-3">
              <span className="text-xs text-gray-400">{inputMessage.length} 字符</span>

              {/* 美化的工具按钮 */}
              <div className="relative tools-panel-container">
                <button
                  onClick={() => setShowToolsPanel(!showToolsPanel)}
                  className={`group flex items-center gap-1 px-2 py-1 rounded-md text-xs transition-all duration-200 ${showToolsPanel
                      ? 'text-amber-300 bg-amber-500/20 border border-amber-500/40'
                      : 'text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 border border-transparent'
                    }`}
                  title="工具"
                >
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" className="transition-transform duration-200 group-hover:rotate-12">
                    <path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z" />
                  </svg>
                  <span className="font-medium">工具</span>
                </button>

                {/* 美化的浮动工具面板 */}
                {showToolsPanel && (
                  <div className="absolute bottom-full right-0 mb-3 bg-gray-800/95 backdrop-blur-sm border border-amber-500/30 rounded-xl shadow-2xl p-2 z-50 animate-in fade-in-0 zoom-in-95 duration-200">
                    {/* 面板标题 */}
                    <div className="text-xs text-amber-200 font-medium mb-2 px-2 py-1">快捷工具</div>

                    <div className="flex gap-1">
                      {/* 关联文件 */}
                      <button
                        onClick={() => {
                          setShowFileAssociation(true)
                          setShowToolsPanel(false)
                        }}
                        className="p-2 bg-blue-500/20 text-blue-200 border border-blue-500/50 rounded-lg hover:bg-blue-500/30 transition-all duration-200"
                        title="关联文件"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                        </svg>
                      </button>

                      {/* 上传媒体 */}
                      <button
                        onClick={() => {
                          setShowMediaUploader(!showMediaUploader)
                          setShowToolsPanel(false)
                        }}
                        className="p-2 bg-purple-500/20 text-purple-200 border border-purple-500/50 rounded-lg hover:bg-purple-500/30 transition-all duration-200"
                        title="上传媒体文件"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M9,16V10H5L12,3L19,10H15V16H9M5,20V18H19V20H5Z" />
                        </svg>
                      </button>

                      {/* 提示词管理 */}
                      <button
                        onClick={() => {
                          setShowPromptManager(true)
                          setShowToolsPanel(false)
                        }}
                        disabled={isLoading}
                        className={`p-2 rounded-lg transition-all duration-200 ${activePromptConfig
                            ? 'bg-amber-500/30 text-amber-200 border border-amber-500/70'
                            : 'bg-amber-500/20 text-amber-200 border border-amber-500/50 hover:bg-amber-500/30'
                          }`}
                        title={activePromptConfig ? `当前配置: ${activePromptConfig.name}` : '管理提示词模板'}
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                          <path d="M12,15.39L8.24,17.66L9.23,13.38L5.91,10.5L10.29,10.13L12,6.09L13.71,10.13L18.09,10.5L14.77,13.38L15.76,17.66M22,9.24L14.81,8.63L12,2L9.19,8.63L2,9.24L7.45,13.97L5.82,21L12,17.27L18.18,21L16.54,13.97L22,9.24Z"
                            opacity="0.6"
                            transform="scale(0.4) translate(24, 24)"
                          />
                        </svg>
                      </button>

                      {/* 受众设置 */}
                      <button
                        onClick={() => {
                          if (onShowAudienceSettings) {
                            onShowAudienceSettings()
                          }
                          setShowToolsPanel(false)
                        }}
                        className="p-2 bg-green-500/20 text-green-200 border border-green-500/50 rounded-lg hover:bg-green-500/30 transition-all duration-200"
                        title="设置受众"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M16,4C18.11,4 19.81,5.69 19.81,7.8C19.81,9.91 18.11,11.6 16,11.6C13.89,11.6 12.2,9.91 12.2,7.8C12.2,5.69 13.89,4 16,4M16,13.4C18.67,13.4 24,14.73 24,17.4V20H8V17.4C8,14.73 13.33,13.4 16,13.4M8.8,11.6C6.69,11.6 5,9.91 5,7.8C5,5.69 6.69,4 8.8,4C9.13,4 9.45,4.05 9.75,4.14C9.28,5.16 9,6.3 9,7.5C9,8.7 9.28,9.84 9.75,10.86C9.45,10.95 9.13,11 8.8,11.6M8.8,13.4C7.12,13.4 3.5,14.26 3.5,17.4V20H6.5V17.4C6.5,16.55 7.45,15.1 8.8,13.4Z" />
                        </svg>
                      </button>
                    </div>

                    {/* 小箭头指示器 */}
                    <div className="absolute top-full right-4 -mt-1">
                      <div className="w-2 h-2 bg-gray-800 border-r border-b border-amber-500/30 transform rotate-45"></div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 媒体上传器 */}
        {showMediaUploader && artworkId && (
          <div className="mt-3 p-4 bg-gray-800/50 border border-purple-500/30 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-purple-200">媒体文件上传</h3>
              <button
                onClick={() => setShowMediaUploader(false)}
                className="text-gray-400 hover:text-gray-200 transition-colors"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                </svg>
              </button>
            </div>
            <MediaUploader
              onUploadSuccess={handleMediaUploadSuccess}
              onError={handleMediaUploadError}
              artworkId={artworkId}
              className="mb-4"
              maxFiles={5}
            />
          </div>
        )}

        {/* 已上传媒体文件 - 紧凑显示 */}
        {uploadedMedia.length > 0 && (
          <div className="mt-3">
            <button
              onClick={() => setShowMediaList(!showMediaList)}
              className="flex items-center gap-2 px-3 py-2 bg-green-500/20 text-green-200 border border-green-500/50 rounded-md hover:bg-green-500/30 transition-all duration-200 font-handwritten text-sm"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z" />
              </svg>
              <span>已上传 {uploadedMedia.length} 个媒体文件</span>
              <svg
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="currentColor"
                className={`transition-transform duration-200 ${showMediaList ? 'rotate-180' : ''}`}
              >
                <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z" />
              </svg>
            </button>

            {/* 可折叠的媒体文件列表 */}
            {showMediaList && (
              <div className="mt-2 p-4 bg-gray-800/50 border border-green-500/30 rounded-lg">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {uploadedMedia.map((mediaFile) => (
                    <div
                      key={mediaFile.id}
                      className="flex items-center gap-3 p-3 bg-gray-700/50 rounded-lg border border-gray-600/50"
                    >
                      {/* 文件类型图标 */}
                      <div className="flex-shrink-0">
                        {mediaFile.type === 'image' ? (
                          <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" className="text-blue-400">
                              <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z" />
                            </svg>
                          </div>
                        ) : (
                          <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" className="text-purple-400">
                              <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z" />
                            </svg>
                          </div>
                        )}
                      </div>

                      {/* 文件信息 */}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-200 truncate">
                          {mediaFile.name}
                        </p>
                        <p className="text-xs text-gray-400">
                          {mediaFile.mimeType} • {(mediaFile.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(mediaFile.uploadedAt).toLocaleString()}
                        </p>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex-shrink-0">
                        <button
                          onClick={() => removeMediaFile(mediaFile.id)}
                          className="w-8 h-8 bg-red-500/20 text-red-400 rounded-lg hover:bg-red-500/30 transition-colors flex items-center justify-center"
                          title="删除文件"
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}


      </div>

      {/* 会话管理弹窗 */}
      <SessionManager
        isOpen={showSessionManager}
        onClose={() => setShowSessionManager(false)}
        onSessionSelect={handleSessionSelect}
        currentSessionId={currentSessionId}
      />

      {/* 文件关联树形对话框 */}
      <FileAssociationTreeDialog
        isOpen={showFileAssociation}
        onClose={() => setShowFileAssociation(false)}
        artworkId={artworkId} // 使用正确的作品ID而不是会话ID
        onFilesChange={handleFilesChange}
      />

      {/* 提示词管理弹窗 */}
      <PromptManagerModal
        isOpen={showPromptManager}
        onClose={() => setShowPromptManager(false)}
        onConfigSelect={handlePromptConfigSelect}
      />

      {/* 多项对比弹窗 */}
      <MultipleComparisonModal
        isOpen={showMultipleComparison}
        onClose={() => setShowMultipleComparison(false)}
        messageContent={comparisonMessageContent}
        onResultSelect={handleComparisonResultSelect}
        onSendMessage={onMultipleComparisonSend || onSendMessage}
        targetMessageIndex={comparisonTriggerMessageIndex}
      />

    </div>
  );
});

export default ChatInterface;