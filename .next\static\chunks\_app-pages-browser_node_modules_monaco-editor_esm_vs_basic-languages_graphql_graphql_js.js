"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_graphql_graphql_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/graphql/graphql.js":
/*!******************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/graphql/graphql.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/graphql/graphql.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"\"\"', close: '\"\"\"', notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"\"\"', close: '\"\"\"' },\n    { open: '\"', close: '\"' }\n  ],\n  folding: {\n    offSide: true\n  }\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  defaultToken: \"invalid\",\n  tokenPostfix: \".gql\",\n  keywords: [\n    \"null\",\n    \"true\",\n    \"false\",\n    \"query\",\n    \"mutation\",\n    \"subscription\",\n    \"extend\",\n    \"schema\",\n    \"directive\",\n    \"scalar\",\n    \"type\",\n    \"interface\",\n    \"union\",\n    \"enum\",\n    \"input\",\n    \"implements\",\n    \"fragment\",\n    \"on\"\n  ],\n  typeKeywords: [\"Int\", \"Float\", \"String\", \"Boolean\", \"ID\"],\n  directiveLocations: [\n    \"SCHEMA\",\n    \"SCALAR\",\n    \"OBJECT\",\n    \"FIELD_DEFINITION\",\n    \"ARGUMENT_DEFINITION\",\n    \"INTERFACE\",\n    \"UNION\",\n    \"ENUM\",\n    \"ENUM_VALUE\",\n    \"INPUT_OBJECT\",\n    \"INPUT_FIELD_DEFINITION\",\n    \"QUERY\",\n    \"MUTATION\",\n    \"SUBSCRIPTION\",\n    \"FIELD\",\n    \"FRAGMENT_DEFINITION\",\n    \"FRAGMENT_SPREAD\",\n    \"INLINE_FRAGMENT\",\n    \"VARIABLE_DEFINITION\"\n  ],\n  operators: [\"=\", \"!\", \"?\", \":\", \"&\", \"|\"],\n  // we include these common regular expressions\n  symbols: /[=!?:&|]+/,\n  // https://facebook.github.io/graphql/draft/#sec-String-Value\n  escapes: /\\\\(?:[\"\\\\\\/bfnrt]|u[0-9A-Fa-f]{4})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // fields and argument names\n      [\n        /[a-z_][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"key.identifier\"\n          }\n        }\n      ],\n      // identify typed input variables\n      [\n        /[$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"argument.identifier\"\n          }\n        }\n      ],\n      // to show class names nicely\n      [\n        /[A-Z][\\w\\$]*/,\n        {\n          cases: {\n            \"@typeKeywords\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, { cases: { \"@operators\": \"operator\", \"@default\": \"\" } }],\n      // @ annotations.\n      // As an example, we emit a debugging log message on these tokens.\n      // Note: message are supressed during the first load -- change some lines to see them.\n      [/@\\s*[a-zA-Z_\\$][\\w\\$]*/, { token: \"annotation\", log: \"annotation token: $0\" }],\n      // numbers\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      [/\"\"\"/, { token: \"string\", next: \"@mlstring\", nextEmbedded: \"markdown\" }],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }]\n    ],\n    mlstring: [\n      [/[^\"]+/, \"string\"],\n      ['\"\"\"', { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/#.*$/, \"comment\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/graphql/graphql.js\n"));

/***/ })

}]);