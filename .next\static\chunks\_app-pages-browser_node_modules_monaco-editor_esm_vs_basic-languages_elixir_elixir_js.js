"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_elixir_elixir_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/elixir/elixir.js":
/*!****************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/elixir/elixir.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/elixir/elixir.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  autoClosingPairs: [\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"comment\"] },\n    { open: '\"\"\"', close: '\"\"\"' },\n    { open: \"`\", close: \"`\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"<<\", close: \">>\" }\n  ],\n  indentationRules: {\n    increaseIndentPattern: /^\\s*(after|else|catch|rescue|fn|[^#]*(do|<\\-|\\->|\\{|\\[|\\=))\\s*$/,\n    decreaseIndentPattern: /^\\s*((\\}|\\])\\s*$|(after|else|catch|rescue|end)\\b)/\n  }\n};\nvar language = {\n  defaultToken: \"source\",\n  tokenPostfix: \".elixir\",\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"<<\", close: \">>\", token: \"delimiter.angle.special\" }\n  ],\n  // Below are lists/regexps to which we reference later.\n  declarationKeywords: [\n    \"def\",\n    \"defp\",\n    \"defn\",\n    \"defnp\",\n    \"defguard\",\n    \"defguardp\",\n    \"defmacro\",\n    \"defmacrop\",\n    \"defdelegate\",\n    \"defcallback\",\n    \"defmacrocallback\",\n    \"defmodule\",\n    \"defprotocol\",\n    \"defexception\",\n    \"defimpl\",\n    \"defstruct\"\n  ],\n  operatorKeywords: [\"and\", \"in\", \"not\", \"or\", \"when\"],\n  namespaceKeywords: [\"alias\", \"import\", \"require\", \"use\"],\n  otherKeywords: [\n    \"after\",\n    \"case\",\n    \"catch\",\n    \"cond\",\n    \"do\",\n    \"else\",\n    \"end\",\n    \"fn\",\n    \"for\",\n    \"if\",\n    \"quote\",\n    \"raise\",\n    \"receive\",\n    \"rescue\",\n    \"super\",\n    \"throw\",\n    \"try\",\n    \"unless\",\n    \"unquote_splicing\",\n    \"unquote\",\n    \"with\"\n  ],\n  constants: [\"true\", \"false\", \"nil\"],\n  nameBuiltin: [\"__MODULE__\", \"__DIR__\", \"__ENV__\", \"__CALLER__\", \"__STACKTRACE__\"],\n  // Matches any of the operator names:\n  // <<< >>> ||| &&& ^^^ ~~~ === !== ~>> <~> |~> <|> == != <= >= && || \\\\ <> ++ -- |> =~ -> <- ~> <~ :: .. = < > + - * / | . ^ & !\n  operator: /-[->]?|!={0,2}|\\*{1,2}|\\/|\\\\\\\\|&{1,3}|\\.\\.?|\\^(?:\\^\\^)?|\\+\\+?|<(?:-|<<|=|>|\\|>|~>?)?|=~|={1,3}|>(?:=|>>)?|\\|~>|\\|>|\\|{1,3}|~>>?|~~~|::/,\n  // See https://hexdocs.pm/elixir/syntax-reference.html#variables\n  variableName: /[a-z_][a-zA-Z0-9_]*[?!]?/,\n  // See https://hexdocs.pm/elixir/syntax-reference.html#atoms\n  atomName: /[a-zA-Z_][a-zA-Z0-9_@]*[?!]?|@specialAtomName|@operator/,\n  specialAtomName: /\\.\\.\\.|<<>>|%\\{\\}|%|\\{\\}/,\n  aliasPart: /[A-Z][a-zA-Z0-9_]*/,\n  moduleName: /@aliasPart(?:\\.@aliasPart)*/,\n  // Sigil pairs are: \"\"\" \"\"\", ''' ''', \" \", ' ', / /, | |, < >, { }, [ ], ( )\n  sigilSymmetricDelimiter: /\"\"\"|'''|\"|'|\\/|\\|/,\n  sigilStartDelimiter: /@sigilSymmetricDelimiter|<|\\{|\\[|\\(/,\n  sigilEndDelimiter: /@sigilSymmetricDelimiter|>|\\}|\\]|\\)/,\n  sigilModifiers: /[a-zA-Z0-9]*/,\n  decimal: /\\d(?:_?\\d)*/,\n  hex: /[0-9a-fA-F](_?[0-9a-fA-F])*/,\n  octal: /[0-7](_?[0-7])*/,\n  binary: /[01](_?[01])*/,\n  // See https://hexdocs.pm/elixir/master/String.html#module-escape-characters\n  escape: /\\\\u[0-9a-fA-F]{4}|\\\\x[0-9a-fA-F]{2}|\\\\./,\n  // The keys below correspond to tokenizer states.\n  // We start from the root state and match against its rules\n  // until we explicitly transition into another state.\n  // The `include` simply brings in all operations from the given state\n  // and is useful for improving readability.\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comments\" },\n      // Keywords start as either an identifier or a string,\n      // but end with a : so it's important to match this first.\n      { include: \"@keywordsShorthand\" },\n      { include: \"@numbers\" },\n      { include: \"@identifiers\" },\n      { include: \"@strings\" },\n      { include: \"@atoms\" },\n      { include: \"@sigils\" },\n      { include: \"@attributes\" },\n      { include: \"@symbols\" }\n    ],\n    // Whitespace\n    whitespace: [[/\\s+/, \"white\"]],\n    // Comments\n    comments: [[/(#)(.*)/, [\"comment.punctuation\", \"comment\"]]],\n    // Keyword list shorthand\n    keywordsShorthand: [\n      [/(@atomName)(:)(\\s+)/, [\"constant\", \"constant.punctuation\", \"white\"]],\n      // Use positive look-ahead to ensure the string is followed by :\n      // and should be considered a keyword.\n      [\n        /\"(?=([^\"]|#\\{.*?\\}|\\\\\")*\":)/,\n        { token: \"constant.delimiter\", next: \"@doubleQuotedStringKeyword\" }\n      ],\n      [\n        /'(?=([^']|#\\{.*?\\}|\\\\')*':)/,\n        { token: \"constant.delimiter\", next: \"@singleQuotedStringKeyword\" }\n      ]\n    ],\n    doubleQuotedStringKeyword: [\n      [/\":/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    singleQuotedStringKeyword: [\n      [/':/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    // Numbers\n    numbers: [\n      [/0b@binary/, \"number.binary\"],\n      [/0o@octal/, \"number.octal\"],\n      [/0x@hex/, \"number.hex\"],\n      [/@decimal\\.@decimal([eE]-?@decimal)?/, \"number.float\"],\n      [/@decimal/, \"number\"]\n    ],\n    // Identifiers\n    identifiers: [\n      // Tokenize identifier name in function-like definitions.\n      // Note: given `def a + b, do: nil`, `a` is not a function name,\n      // so we use negative look-ahead to ensure there's no operator.\n      [\n        /\\b(defp?|defnp?|defmacrop?|defguardp?|defdelegate)(\\s+)(@variableName)(?!\\s+@operator)/,\n        [\n          \"keyword.declaration\",\n          \"white\",\n          {\n            cases: {\n              unquote: \"keyword\",\n              \"@default\": \"function\"\n            }\n          }\n        ]\n      ],\n      // Tokenize function calls\n      [\n        // In-scope call - an identifier followed by ( or .(\n        /(@variableName)(?=\\s*\\.?\\s*\\()/,\n        {\n          cases: {\n            // Tokenize as keyword in cases like `if(..., do: ..., else: ...)`\n            \"@declarationKeywords\": \"keyword.declaration\",\n            \"@namespaceKeywords\": \"keyword\",\n            \"@otherKeywords\": \"keyword\",\n            \"@default\": \"function.call\"\n          }\n        }\n      ],\n      [\n        // Referencing function in a module\n        /(@moduleName)(\\s*)(\\.)(\\s*)(@variableName)/,\n        [\"type.identifier\", \"white\", \"operator\", \"white\", \"function.call\"]\n      ],\n      [\n        // Referencing function in an Erlang module\n        /(:)(@atomName)(\\s*)(\\.)(\\s*)(@variableName)/,\n        [\"constant.punctuation\", \"constant\", \"white\", \"operator\", \"white\", \"function.call\"]\n      ],\n      [\n        // Piping into a function (tokenized separately as it may not have parentheses)\n        /(\\|>)(\\s*)(@variableName)/,\n        [\n          \"operator\",\n          \"white\",\n          {\n            cases: {\n              \"@otherKeywords\": \"keyword\",\n              \"@default\": \"function.call\"\n            }\n          }\n        ]\n      ],\n      [\n        // Function reference passed to another function\n        /(&)(\\s*)(@variableName)/,\n        [\"operator\", \"white\", \"function.call\"]\n      ],\n      // Language keywords, builtins, constants and variables\n      [\n        /@variableName/,\n        {\n          cases: {\n            \"@declarationKeywords\": \"keyword.declaration\",\n            \"@operatorKeywords\": \"keyword.operator\",\n            \"@namespaceKeywords\": \"keyword\",\n            \"@otherKeywords\": \"keyword\",\n            \"@constants\": \"constant.language\",\n            \"@nameBuiltin\": \"variable.language\",\n            \"_.*\": \"comment.unused\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // Module names\n      [/@moduleName/, \"type.identifier\"]\n    ],\n    // Strings\n    strings: [\n      [/\"\"\"/, { token: \"string.delimiter\", next: \"@doubleQuotedHeredoc\" }],\n      [/'''/, { token: \"string.delimiter\", next: \"@singleQuotedHeredoc\" }],\n      [/\"/, { token: \"string.delimiter\", next: \"@doubleQuotedString\" }],\n      [/'/, { token: \"string.delimiter\", next: \"@singleQuotedString\" }]\n    ],\n    doubleQuotedHeredoc: [\n      [/\"\"\"/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    singleQuotedHeredoc: [\n      [/'''/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    doubleQuotedString: [\n      [/\"/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    singleQuotedString: [\n      [/'/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    // Atoms\n    atoms: [\n      [/(:)(@atomName)/, [\"constant.punctuation\", \"constant\"]],\n      [/:\"/, { token: \"constant.delimiter\", next: \"@doubleQuotedStringAtom\" }],\n      [/:'/, { token: \"constant.delimiter\", next: \"@singleQuotedStringAtom\" }]\n    ],\n    doubleQuotedStringAtom: [\n      [/\"/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    singleQuotedStringAtom: [\n      [/'/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    // Sigils\n    // See https://elixir-lang.org/getting-started/sigils.html\n    // Sigils allow for typing values using their textual representation.\n    // All sigils start with ~ followed by a letter or\n    // multi-letter uppercase starting at Elixir v1.15.0, indicating sigil type\n    // and then a delimiter pair enclosing the textual representation.\n    // Optional modifiers are allowed after the closing delimiter.\n    // For instance a regular expressions can be written as:\n    // ~r/foo|bar/ ~r{foo|bar} ~r/foo|bar/g\n    //\n    // In general lowercase sigils allow for interpolation\n    // and escaped characters, whereas uppercase sigils don't\n    //\n    // During tokenization we want to distinguish some\n    // specific sigil types, namely string and regexp,\n    // so that they cen be themed separately.\n    //\n    // To reasonably handle all those combinations we leverage\n    // dot-separated states, so if we transition to @sigilStart.interpol.s.{.}\n    // then \"sigilStart.interpol.s\" state will match and also all\n    // the individual dot-separated parameters can be accessed.\n    sigils: [\n      [/~[a-z]@sigilStartDelimiter/, { token: \"@rematch\", next: \"@sigil.interpol\" }],\n      [/~([A-Z]+)@sigilStartDelimiter/, { token: \"@rematch\", next: \"@sigil.noInterpol\" }]\n    ],\n    sigil: [\n      [/~([a-z]|[A-Z]+)\\{/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.{.}\" }],\n      [/~([a-z]|[A-Z]+)\\[/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.[.]\" }],\n      [/~([a-z]|[A-Z]+)\\(/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.(.)\" }],\n      [/~([a-z]|[A-Z]+)\\</, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.<.>\" }],\n      [\n        /~([a-z]|[A-Z]+)(@sigilSymmetricDelimiter)/,\n        { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.$2.$2\" }\n      ]\n    ],\n    // The definitions below expect states to be of the form:\n    //\n    // sigilStart.<interpol-or-noInterpol>.<sigil-letter>.<start-delimiter>.<end-delimiter>\n    // sigilContinue.<interpol-or-noInterpol>.<sigil-letter>.<start-delimiter>.<end-delimiter>\n    //\n    // The sigilStart state is used only to properly classify the token (as string/regex/sigil)\n    // and immediately switches to the sigilContinue sate, which handles the actual content\n    // and waits for the corresponding end delimiter.\n    \"sigilStart.interpol.s\": [\n      [\n        /~s@sigilStartDelimiter/,\n        {\n          token: \"string.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol.s\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"string.delimiter\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      { include: \"@stringContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol.S\": [\n      [\n        /~S@sigilStartDelimiter/,\n        {\n          token: \"string.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol.S\": [\n      // Ignore escaped sigil end\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"string\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"string.delimiter\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      { include: \"@stringContent\" }\n    ],\n    \"sigilStart.interpol.r\": [\n      [\n        /~r@sigilStartDelimiter/,\n        {\n          token: \"regexp.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol.r\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"regexp.delimiter\", next: \"@pop\" },\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexpContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol.R\": [\n      [\n        /~R@sigilStartDelimiter/,\n        {\n          token: \"regexp.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol.R\": [\n      // Ignore escaped sigil end\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"regexp\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"regexp.delimiter\", next: \"@pop\" },\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexpContent\" }\n    ],\n    // Fallback to the generic sigil by default\n    \"sigilStart.interpol\": [\n      [\n        /~([a-z]|[A-Z]+)@sigilStartDelimiter/,\n        {\n          token: \"sigil.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"sigil.delimiter\", next: \"@pop\" },\n            \"@default\": \"sigil\"\n          }\n        }\n      ],\n      { include: \"@sigilContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol\": [\n      [\n        /~([a-z]|[A-Z]+)@sigilStartDelimiter/,\n        {\n          token: \"sigil.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol\": [\n      // Ignore escaped sigil end\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"sigil\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"sigil.delimiter\", next: \"@pop\" },\n            \"@default\": \"sigil\"\n          }\n        }\n      ],\n      { include: \"@sigilContent\" }\n    ],\n    // Attributes\n    attributes: [\n      // Module @doc* attributes - tokenized as comments\n      [\n        /\\@(module|type)?doc (~[sS])?\"\"\"/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@doubleQuotedHeredocDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?'''/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@singleQuotedHeredocDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?\"/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@doubleQuotedStringDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?'/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@singleQuotedStringDocstring\"\n        }\n      ],\n      [/\\@(module|type)?doc false/, \"comment.block.documentation\"],\n      // Module attributes\n      [/\\@(@variableName)/, \"variable\"]\n    ],\n    doubleQuotedHeredocDocstring: [\n      [/\"\"\"/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    singleQuotedHeredocDocstring: [\n      [/'''/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    doubleQuotedStringDocstring: [\n      [/\"/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    singleQuotedStringDocstring: [\n      [/'/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    // Operators, punctuation, brackets\n    symbols: [\n      // Code point operator (either with regular character ?a or an escaped one ?\\n)\n      [/\\?(\\\\.|[^\\\\\\s])/, \"number.constant\"],\n      // Anonymous function arguments\n      [/&\\d+/, \"operator\"],\n      // Bitshift operators (must go before delimiters, so that << >> don't match first)\n      [/<<<|>>>/, \"operator\"],\n      // Delimiter pairs\n      [/[()\\[\\]\\{\\}]|<<|>>/, \"@brackets\"],\n      // Triple dot is a valid name (must go before operators, so that .. doesn't match instead)\n      [/\\.\\.\\./, \"identifier\"],\n      // Punctuation => (must go before operators, so it's not tokenized as = then >)\n      [/=>/, \"punctuation\"],\n      // Operators\n      [/@operator/, \"operator\"],\n      // Punctuation\n      [/[:;,.%]/, \"punctuation\"]\n    ],\n    // Generic helpers\n    stringContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@stringContent\" }\n    ],\n    stringContent: [[/./, \"string\"]],\n    stringConstantContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@stringConstantContent\" }\n    ],\n    stringConstantContent: [[/./, \"constant\"]],\n    regexpContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@regexpContent\" }\n    ],\n    regexpContent: [\n      // # may be a regular regexp char, so we use a heuristic\n      // assuming a # surrounded by whitespace is actually a comment.\n      [/(\\s)(#)(\\s.*)$/, [\"white\", \"comment.punctuation\", \"comment\"]],\n      [/./, \"regexp\"]\n    ],\n    sigilContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@sigilContent\" }\n    ],\n    sigilContent: [[/./, \"sigil\"]],\n    docstringContent: [[/./, \"comment.block.documentation\"]],\n    escapeChar: [[/@escape/, \"constant.character.escape\"]],\n    interpolation: [[/#{/, { token: \"delimiter.bracket.embed\", next: \"@interpolationContinue\" }]],\n    interpolationContinue: [\n      [/}/, { token: \"delimiter.bracket.embed\", next: \"@pop\" }],\n      // Interpolation brackets may contain arbitrary code,\n      // so we simply match against all the root rules,\n      // until we reach interpolation end (the above matches).\n      { include: \"@root\" }\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/elixir/elixir.js\n"));

/***/ })

}]);