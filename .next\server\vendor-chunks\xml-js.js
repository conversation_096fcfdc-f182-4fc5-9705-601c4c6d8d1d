/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xml-js";
exports.ids = ["vendor-chunks/xml-js"];
exports.modules = {

/***/ "(ssr)/./node_modules/xml-js/lib/array-helper.js":
/*!*************************************************!*\
  !*** ./node_modules/xml-js/lib/array-helper.js ***!
  \*************************************************/
/***/ ((module) => {

eval("module.exports = {\r\n\r\n  isArray: function(value) {\r\n    if (Array.isArray) {\r\n      return Array.isArray(value);\r\n    }\r\n    // fallback for older browsers like  IE 8\r\n    return Object.prototype.toString.call( value ) === '[object Array]';\r\n  }\r\n\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMveG1sLWpzL2xpYi9hcnJheS1oZWxwZXIuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FydHdvcmstcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMveG1sLWpzL2xpYi9hcnJheS1oZWxwZXIuanM/NThjNiJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHtcclxuXHJcbiAgaXNBcnJheTogZnVuY3Rpb24odmFsdWUpIHtcclxuICAgIGlmIChBcnJheS5pc0FycmF5KSB7XHJcbiAgICAgIHJldHVybiBBcnJheS5pc0FycmF5KHZhbHVlKTtcclxuICAgIH1cclxuICAgIC8vIGZhbGxiYWNrIGZvciBvbGRlciBicm93c2VycyBsaWtlICBJRSA4XHJcbiAgICByZXR1cm4gT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKCB2YWx1ZSApID09PSAnW29iamVjdCBBcnJheV0nO1xyXG4gIH1cclxuXHJcbn07XHJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xml-js/lib/array-helper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xml-js/lib/index.js":
/*!******************************************!*\
  !*** ./node_modules/xml-js/lib/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*jslint node:true */\r\n\r\nvar xml2js = __webpack_require__(/*! ./xml2js */ \"(ssr)/./node_modules/xml-js/lib/xml2js.js\");\r\nvar xml2json = __webpack_require__(/*! ./xml2json */ \"(ssr)/./node_modules/xml-js/lib/xml2json.js\");\r\nvar js2xml = __webpack_require__(/*! ./js2xml */ \"(ssr)/./node_modules/xml-js/lib/js2xml.js\");\r\nvar json2xml = __webpack_require__(/*! ./json2xml */ \"(ssr)/./node_modules/xml-js/lib/json2xml.js\");\r\n\r\nmodule.exports = {\r\n  xml2js: xml2js,\r\n  xml2json: xml2json,\r\n  js2xml: js2xml,\r\n  json2xml: json2xml\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMveG1sLWpzL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0EsYUFBYSxtQkFBTyxDQUFDLDJEQUFVO0FBQy9CLGVBQWUsbUJBQU8sQ0FBQywrREFBWTtBQUNuQyxhQUFhLG1CQUFPLENBQUMsMkRBQVU7QUFDL0IsZUFBZSxtQkFBTyxDQUFDLCtEQUFZO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXJ0d29yay1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy94bWwtanMvbGliL2luZGV4LmpzPzM1ZTYiXSwic291cmNlc0NvbnRlbnQiOlsiLypqc2xpbnQgbm9kZTp0cnVlICovXHJcblxyXG52YXIgeG1sMmpzID0gcmVxdWlyZSgnLi94bWwyanMnKTtcclxudmFyIHhtbDJqc29uID0gcmVxdWlyZSgnLi94bWwyanNvbicpO1xyXG52YXIganMyeG1sID0gcmVxdWlyZSgnLi9qczJ4bWwnKTtcclxudmFyIGpzb24yeG1sID0gcmVxdWlyZSgnLi9qc29uMnhtbCcpO1xyXG5cclxubW9kdWxlLmV4cG9ydHMgPSB7XHJcbiAgeG1sMmpzOiB4bWwyanMsXHJcbiAgeG1sMmpzb246IHhtbDJqc29uLFxyXG4gIGpzMnhtbDoganMyeG1sLFxyXG4gIGpzb24yeG1sOiBqc29uMnhtbFxyXG59O1xyXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xml-js/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xml-js/lib/js2xml.js":
/*!*******************************************!*\
  !*** ./node_modules/xml-js/lib/js2xml.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var helper = __webpack_require__(/*! ./options-helper */ \"(ssr)/./node_modules/xml-js/lib/options-helper.js\");\nvar isArray = (__webpack_require__(/*! ./array-helper */ \"(ssr)/./node_modules/xml-js/lib/array-helper.js\").isArray);\n\nvar currentElement, currentElementName;\n\nfunction validateOptions(userOptions) {\n  var options = helper.copyOptions(userOptions);\n  helper.ensureFlagExists('ignoreDeclaration', options);\n  helper.ensureFlagExists('ignoreInstruction', options);\n  helper.ensureFlagExists('ignoreAttributes', options);\n  helper.ensureFlagExists('ignoreText', options);\n  helper.ensureFlagExists('ignoreComment', options);\n  helper.ensureFlagExists('ignoreCdata', options);\n  helper.ensureFlagExists('ignoreDoctype', options);\n  helper.ensureFlagExists('compact', options);\n  helper.ensureFlagExists('indentText', options);\n  helper.ensureFlagExists('indentCdata', options);\n  helper.ensureFlagExists('indentAttributes', options);\n  helper.ensureFlagExists('indentInstruction', options);\n  helper.ensureFlagExists('fullTagEmptyElement', options);\n  helper.ensureFlagExists('noQuotesForNativeAttributes', options);\n  helper.ensureSpacesExists(options);\n  if (typeof options.spaces === 'number') {\n    options.spaces = Array(options.spaces + 1).join(' ');\n  }\n  helper.ensureKeyExists('declaration', options);\n  helper.ensureKeyExists('instruction', options);\n  helper.ensureKeyExists('attributes', options);\n  helper.ensureKeyExists('text', options);\n  helper.ensureKeyExists('comment', options);\n  helper.ensureKeyExists('cdata', options);\n  helper.ensureKeyExists('doctype', options);\n  helper.ensureKeyExists('type', options);\n  helper.ensureKeyExists('name', options);\n  helper.ensureKeyExists('elements', options);\n  helper.checkFnExists('doctype', options);\n  helper.checkFnExists('instruction', options);\n  helper.checkFnExists('cdata', options);\n  helper.checkFnExists('comment', options);\n  helper.checkFnExists('text', options);\n  helper.checkFnExists('instructionName', options);\n  helper.checkFnExists('elementName', options);\n  helper.checkFnExists('attributeName', options);\n  helper.checkFnExists('attributeValue', options);\n  helper.checkFnExists('attributes', options);\n  helper.checkFnExists('fullTagEmptyElement', options);\n  return options;\n}\n\nfunction writeIndentation(options, depth, firstLine) {\n  return (!firstLine && options.spaces ? '\\n' : '') + Array(depth + 1).join(options.spaces);\n}\n\nfunction writeAttributes(attributes, options, depth) {\n  if (options.ignoreAttributes) {\n    return '';\n  }\n  if ('attributesFn' in options) {\n    attributes = options.attributesFn(attributes, currentElementName, currentElement);\n  }\n  var key, attr, attrName, quote, result = [];\n  for (key in attributes) {\n    if (attributes.hasOwnProperty(key) && attributes[key] !== null && attributes[key] !== undefined) {\n      quote = options.noQuotesForNativeAttributes && typeof attributes[key] !== 'string' ? '' : '\"';\n      attr = '' + attributes[key]; // ensure number and boolean are converted to String\n      attr = attr.replace(/\"/g, '&quot;');\n      attrName = 'attributeNameFn' in options ? options.attributeNameFn(key, attr, currentElementName, currentElement) : key;\n      result.push((options.spaces && options.indentAttributes? writeIndentation(options, depth+1, false) : ' '));\n      result.push(attrName + '=' + quote + ('attributeValueFn' in options ? options.attributeValueFn(attr, key, currentElementName, currentElement) : attr) + quote);\n    }\n  }\n  if (attributes && Object.keys(attributes).length && options.spaces && options.indentAttributes) {\n    result.push(writeIndentation(options, depth, false));\n  }\n  return result.join('');\n}\n\nfunction writeDeclaration(declaration, options, depth) {\n  currentElement = declaration;\n  currentElementName = 'xml';\n  return options.ignoreDeclaration ? '' :  '<?' + 'xml' + writeAttributes(declaration[options.attributesKey], options, depth) + '?>';\n}\n\nfunction writeInstruction(instruction, options, depth) {\n  if (options.ignoreInstruction) {\n    return '';\n  }\n  var key;\n  for (key in instruction) {\n    if (instruction.hasOwnProperty(key)) {\n      break;\n    }\n  }\n  var instructionName = 'instructionNameFn' in options ? options.instructionNameFn(key, instruction[key], currentElementName, currentElement) : key;\n  if (typeof instruction[key] === 'object') {\n    currentElement = instruction;\n    currentElementName = instructionName;\n    return '<?' + instructionName + writeAttributes(instruction[key][options.attributesKey], options, depth) + '?>';\n  } else {\n    var instructionValue = instruction[key] ? instruction[key] : '';\n    if ('instructionFn' in options) instructionValue = options.instructionFn(instructionValue, key, currentElementName, currentElement);\n    return '<?' + instructionName + (instructionValue ? ' ' + instructionValue : '') + '?>';\n  }\n}\n\nfunction writeComment(comment, options) {\n  return options.ignoreComment ? '' : '<!--' + ('commentFn' in options ? options.commentFn(comment, currentElementName, currentElement) : comment) + '-->';\n}\n\nfunction writeCdata(cdata, options) {\n  return options.ignoreCdata ? '' : '<![CDATA[' + ('cdataFn' in options ? options.cdataFn(cdata, currentElementName, currentElement) : cdata.replace(']]>', ']]]]><![CDATA[>')) + ']]>';\n}\n\nfunction writeDoctype(doctype, options) {\n  return options.ignoreDoctype ? '' : '<!DOCTYPE ' + ('doctypeFn' in options ? options.doctypeFn(doctype, currentElementName, currentElement) : doctype) + '>';\n}\n\nfunction writeText(text, options) {\n  if (options.ignoreText) return '';\n  text = '' + text; // ensure Number and Boolean are converted to String\n  text = text.replace(/&amp;/g, '&'); // desanitize to avoid double sanitization\n  text = text.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n  return 'textFn' in options ? options.textFn(text, currentElementName, currentElement) : text;\n}\n\nfunction hasContent(element, options) {\n  var i;\n  if (element.elements && element.elements.length) {\n    for (i = 0; i < element.elements.length; ++i) {\n      switch (element.elements[i][options.typeKey]) {\n      case 'text':\n        if (options.indentText) {\n          return true;\n        }\n        break; // skip to next key\n      case 'cdata':\n        if (options.indentCdata) {\n          return true;\n        }\n        break; // skip to next key\n      case 'instruction':\n        if (options.indentInstruction) {\n          return true;\n        }\n        break; // skip to next key\n      case 'doctype':\n      case 'comment':\n      case 'element':\n        return true;\n      default:\n        return true;\n      }\n    }\n  }\n  return false;\n}\n\nfunction writeElement(element, options, depth) {\n  currentElement = element;\n  currentElementName = element.name;\n  var xml = [], elementName = 'elementNameFn' in options ? options.elementNameFn(element.name, element) : element.name;\n  xml.push('<' + elementName);\n  if (element[options.attributesKey]) {\n    xml.push(writeAttributes(element[options.attributesKey], options, depth));\n  }\n  var withClosingTag = element[options.elementsKey] && element[options.elementsKey].length || element[options.attributesKey] && element[options.attributesKey]['xml:space'] === 'preserve';\n  if (!withClosingTag) {\n    if ('fullTagEmptyElementFn' in options) {\n      withClosingTag = options.fullTagEmptyElementFn(element.name, element);\n    } else {\n      withClosingTag = options.fullTagEmptyElement;\n    }\n  }\n  if (withClosingTag) {\n    xml.push('>');\n    if (element[options.elementsKey] && element[options.elementsKey].length) {\n      xml.push(writeElements(element[options.elementsKey], options, depth + 1));\n      currentElement = element;\n      currentElementName = element.name;\n    }\n    xml.push(options.spaces && hasContent(element, options) ? '\\n' + Array(depth + 1).join(options.spaces) : '');\n    xml.push('</' + elementName + '>');\n  } else {\n    xml.push('/>');\n  }\n  return xml.join('');\n}\n\nfunction writeElements(elements, options, depth, firstLine) {\n  return elements.reduce(function (xml, element) {\n    var indent = writeIndentation(options, depth, firstLine && !xml);\n    switch (element.type) {\n    case 'element': return xml + indent + writeElement(element, options, depth);\n    case 'comment': return xml + indent + writeComment(element[options.commentKey], options);\n    case 'doctype': return xml + indent + writeDoctype(element[options.doctypeKey], options);\n    case 'cdata': return xml + (options.indentCdata ? indent : '') + writeCdata(element[options.cdataKey], options);\n    case 'text': return xml + (options.indentText ? indent : '') + writeText(element[options.textKey], options);\n    case 'instruction':\n      var instruction = {};\n      instruction[element[options.nameKey]] = element[options.attributesKey] ? element : element[options.instructionKey];\n      return xml + (options.indentInstruction ? indent : '') + writeInstruction(instruction, options, depth);\n    }\n  }, '');\n}\n\nfunction hasContentCompact(element, options, anyContent) {\n  var key;\n  for (key in element) {\n    if (element.hasOwnProperty(key)) {\n      switch (key) {\n      case options.parentKey:\n      case options.attributesKey:\n        break; // skip to next key\n      case options.textKey:\n        if (options.indentText || anyContent) {\n          return true;\n        }\n        break; // skip to next key\n      case options.cdataKey:\n        if (options.indentCdata || anyContent) {\n          return true;\n        }\n        break; // skip to next key\n      case options.instructionKey:\n        if (options.indentInstruction || anyContent) {\n          return true;\n        }\n        break; // skip to next key\n      case options.doctypeKey:\n      case options.commentKey:\n        return true;\n      default:\n        return true;\n      }\n    }\n  }\n  return false;\n}\n\nfunction writeElementCompact(element, name, options, depth, indent) {\n  currentElement = element;\n  currentElementName = name;\n  var elementName = 'elementNameFn' in options ? options.elementNameFn(name, element) : name;\n  if (typeof element === 'undefined' || element === null || element === '') {\n    return 'fullTagEmptyElementFn' in options && options.fullTagEmptyElementFn(name, element) || options.fullTagEmptyElement ? '<' + elementName + '></' + elementName + '>' : '<' + elementName + '/>';\n  }\n  var xml = [];\n  if (name) {\n    xml.push('<' + elementName);\n    if (typeof element !== 'object') {\n      xml.push('>' + writeText(element,options) + '</' + elementName + '>');\n      return xml.join('');\n    }\n    if (element[options.attributesKey]) {\n      xml.push(writeAttributes(element[options.attributesKey], options, depth));\n    }\n    var withClosingTag = hasContentCompact(element, options, true) || element[options.attributesKey] && element[options.attributesKey]['xml:space'] === 'preserve';\n    if (!withClosingTag) {\n      if ('fullTagEmptyElementFn' in options) {\n        withClosingTag = options.fullTagEmptyElementFn(name, element);\n      } else {\n        withClosingTag = options.fullTagEmptyElement;\n      }\n    }\n    if (withClosingTag) {\n      xml.push('>');\n    } else {\n      xml.push('/>');\n      return xml.join('');\n    }\n  }\n  xml.push(writeElementsCompact(element, options, depth + 1, false));\n  currentElement = element;\n  currentElementName = name;\n  if (name) {\n    xml.push((indent ? writeIndentation(options, depth, false) : '') + '</' + elementName + '>');\n  }\n  return xml.join('');\n}\n\nfunction writeElementsCompact(element, options, depth, firstLine) {\n  var i, key, nodes, xml = [];\n  for (key in element) {\n    if (element.hasOwnProperty(key)) {\n      nodes = isArray(element[key]) ? element[key] : [element[key]];\n      for (i = 0; i < nodes.length; ++i) {\n        switch (key) {\n        case options.declarationKey: xml.push(writeDeclaration(nodes[i], options, depth)); break;\n        case options.instructionKey: xml.push((options.indentInstruction ? writeIndentation(options, depth, firstLine) : '') + writeInstruction(nodes[i], options, depth)); break;\n        case options.attributesKey: case options.parentKey: break; // skip\n        case options.textKey: xml.push((options.indentText ? writeIndentation(options, depth, firstLine) : '') + writeText(nodes[i], options)); break;\n        case options.cdataKey: xml.push((options.indentCdata ? writeIndentation(options, depth, firstLine) : '') + writeCdata(nodes[i], options)); break;\n        case options.doctypeKey: xml.push(writeIndentation(options, depth, firstLine) + writeDoctype(nodes[i], options)); break;\n        case options.commentKey: xml.push(writeIndentation(options, depth, firstLine) + writeComment(nodes[i], options)); break;\n        default: xml.push(writeIndentation(options, depth, firstLine) + writeElementCompact(nodes[i], key, options, depth, hasContentCompact(nodes[i], options)));\n        }\n        firstLine = firstLine && !xml.length;\n      }\n    }\n  }\n  return xml.join('');\n}\n\nmodule.exports = function (js, options) {\n  options = validateOptions(options);\n  var xml = [];\n  currentElement = js;\n  currentElementName = '_root_';\n  if (options.compact) {\n    xml.push(writeElementsCompact(js, options, 0, true));\n  } else {\n    if (js[options.declarationKey]) {\n      xml.push(writeDeclaration(js[options.declarationKey], options, 0));\n    }\n    if (js[options.elementsKey] && js[options.elementsKey].length) {\n      xml.push(writeElements(js[options.elementsKey], options, 0, !xml.length));\n    }\n  }\n  return xml.join('');\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xml-js/lib/js2xml.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xml-js/lib/json2xml.js":
/*!*********************************************!*\
  !*** ./node_modules/xml-js/lib/json2xml.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var js2xml = __webpack_require__(/*! ./js2xml.js */ \"(ssr)/./node_modules/xml-js/lib/js2xml.js\");\r\n\r\nmodule.exports = function (json, options) {\r\n  if (json instanceof Buffer) {\r\n    json = json.toString();\r\n  }\r\n  var js = null;\r\n  if (typeof (json) === 'string') {\r\n    try {\r\n      js = JSON.parse(json);\r\n    } catch (e) {\r\n      throw new Error('The JSON structure is invalid');\r\n    }\r\n  } else {\r\n    js = json;\r\n  }\r\n  return js2xml(js, options);\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMveG1sLWpzL2xpYi9qc29uMnhtbC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxhQUFhLG1CQUFPLENBQUMsOERBQWE7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcnR3b3JrLXBsYXRmb3JtLy4vbm9kZV9tb2R1bGVzL3htbC1qcy9saWIvanNvbjJ4bWwuanM/ZDkyYSJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIganMyeG1sID0gcmVxdWlyZSgnLi9qczJ4bWwuanMnKTtcclxuXHJcbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKGpzb24sIG9wdGlvbnMpIHtcclxuICBpZiAoanNvbiBpbnN0YW5jZW9mIEJ1ZmZlcikge1xyXG4gICAganNvbiA9IGpzb24udG9TdHJpbmcoKTtcclxuICB9XHJcbiAgdmFyIGpzID0gbnVsbDtcclxuICBpZiAodHlwZW9mIChqc29uKSA9PT0gJ3N0cmluZycpIHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGpzID0gSlNPTi5wYXJzZShqc29uKTtcclxuICAgIH0gY2F0Y2ggKGUpIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdUaGUgSlNPTiBzdHJ1Y3R1cmUgaXMgaW52YWxpZCcpO1xyXG4gICAgfVxyXG4gIH0gZWxzZSB7XHJcbiAgICBqcyA9IGpzb247XHJcbiAgfVxyXG4gIHJldHVybiBqczJ4bWwoanMsIG9wdGlvbnMpO1xyXG59O1xyXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xml-js/lib/json2xml.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xml-js/lib/options-helper.js":
/*!***************************************************!*\
  !*** ./node_modules/xml-js/lib/options-helper.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var isArray = (__webpack_require__(/*! ./array-helper */ \"(ssr)/./node_modules/xml-js/lib/array-helper.js\").isArray);\r\n\r\nmodule.exports = {\r\n\r\n  copyOptions: function (options) {\r\n    var key, copy = {};\r\n    for (key in options) {\r\n      if (options.hasOwnProperty(key)) {\r\n        copy[key] = options[key];\r\n      }\r\n    }\r\n    return copy;\r\n  },\r\n\r\n  ensureFlagExists: function (item, options) {\r\n    if (!(item in options) || typeof options[item] !== 'boolean') {\r\n      options[item] = false;\r\n    }\r\n  },\r\n\r\n  ensureSpacesExists: function (options) {\r\n    if (!('spaces' in options) || (typeof options.spaces !== 'number' && typeof options.spaces !== 'string')) {\r\n      options.spaces = 0;\r\n    }\r\n  },\r\n\r\n  ensureAlwaysArrayExists: function (options) {\r\n    if (!('alwaysArray' in options) || (typeof options.alwaysArray !== 'boolean' && !isArray(options.alwaysArray))) {\r\n      options.alwaysArray = false;\r\n    }\r\n  },\r\n\r\n  ensureKeyExists: function (key, options) {\r\n    if (!(key + 'Key' in options) || typeof options[key + 'Key'] !== 'string') {\r\n      options[key + 'Key'] = options.compact ? '_' + key : key;\r\n    }\r\n  },\r\n\r\n  checkFnExists: function (key, options) {\r\n    return key + 'Fn' in options;\r\n  }\r\n\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xml-js/lib/options-helper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xml-js/lib/xml2js.js":
/*!*******************************************!*\
  !*** ./node_modules/xml-js/lib/xml2js.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var sax = __webpack_require__(/*! sax */ \"(ssr)/./node_modules/sax/lib/sax.js\");\r\nvar expat /*= require('node-expat');*/ = { on: function () { }, parse: function () { } };\r\nvar helper = __webpack_require__(/*! ./options-helper */ \"(ssr)/./node_modules/xml-js/lib/options-helper.js\");\r\nvar isArray = (__webpack_require__(/*! ./array-helper */ \"(ssr)/./node_modules/xml-js/lib/array-helper.js\").isArray);\r\n\r\nvar options;\r\nvar pureJsParser = true;\r\nvar currentElement;\r\n\r\nfunction validateOptions(userOptions) {\r\n  options = helper.copyOptions(userOptions);\r\n  helper.ensureFlagExists('ignoreDeclaration', options);\r\n  helper.ensureFlagExists('ignoreInstruction', options);\r\n  helper.ensureFlagExists('ignoreAttributes', options);\r\n  helper.ensureFlagExists('ignoreText', options);\r\n  helper.ensureFlagExists('ignoreComment', options);\r\n  helper.ensureFlagExists('ignoreCdata', options);\r\n  helper.ensureFlagExists('ignoreDoctype', options);\r\n  helper.ensureFlagExists('compact', options);\r\n  helper.ensureFlagExists('alwaysChildren', options);\r\n  helper.ensureFlagExists('addParent', options);\r\n  helper.ensureFlagExists('trim', options);\r\n  helper.ensureFlagExists('nativeType', options);\r\n  helper.ensureFlagExists('nativeTypeAttributes', options);\r\n  helper.ensureFlagExists('sanitize', options);\r\n  helper.ensureFlagExists('instructionHasAttributes', options);\r\n  helper.ensureFlagExists('captureSpacesBetweenElements', options);\r\n  helper.ensureAlwaysArrayExists(options);\r\n  helper.ensureKeyExists('declaration', options);\r\n  helper.ensureKeyExists('instruction', options);\r\n  helper.ensureKeyExists('attributes', options);\r\n  helper.ensureKeyExists('text', options);\r\n  helper.ensureKeyExists('comment', options);\r\n  helper.ensureKeyExists('cdata', options);\r\n  helper.ensureKeyExists('doctype', options);\r\n  helper.ensureKeyExists('type', options);\r\n  helper.ensureKeyExists('name', options);\r\n  helper.ensureKeyExists('elements', options);\r\n  helper.ensureKeyExists('parent', options);\r\n  helper.checkFnExists('doctype', options);\r\n  helper.checkFnExists('instruction', options);\r\n  helper.checkFnExists('cdata', options);\r\n  helper.checkFnExists('comment', options);\r\n  helper.checkFnExists('text', options);\r\n  helper.checkFnExists('instructionName', options);\r\n  helper.checkFnExists('elementName', options);\r\n  helper.checkFnExists('attributeName', options);\r\n  helper.checkFnExists('attributeValue', options);\r\n  helper.checkFnExists('attributes', options);\r\n  return options;\r\n}\r\n\r\nfunction nativeType(value) {\r\n  var nValue = Number(value);\r\n  if (!isNaN(nValue)) {\r\n    return nValue;\r\n  }\r\n  var bValue = value.toLowerCase();\r\n  if (bValue === 'true') {\r\n    return true;\r\n  } else if (bValue === 'false') {\r\n    return false;\r\n  }\r\n  return value;\r\n}\r\n\r\nfunction addField(type, value) {\r\n  var key;\r\n  if (options.compact) {\r\n    if (\r\n      !currentElement[options[type + 'Key']] &&\r\n      (isArray(options.alwaysArray) ? options.alwaysArray.indexOf(options[type + 'Key']) !== -1 : options.alwaysArray)\r\n    ) {\r\n      currentElement[options[type + 'Key']] = [];\r\n    }\r\n    if (currentElement[options[type + 'Key']] && !isArray(currentElement[options[type + 'Key']])) {\r\n      currentElement[options[type + 'Key']] = [currentElement[options[type + 'Key']]];\r\n    }\r\n    if (type + 'Fn' in options && typeof value === 'string') {\r\n      value = options[type + 'Fn'](value, currentElement);\r\n    }\r\n    if (type === 'instruction' && ('instructionFn' in options || 'instructionNameFn' in options)) {\r\n      for (key in value) {\r\n        if (value.hasOwnProperty(key)) {\r\n          if ('instructionFn' in options) {\r\n            value[key] = options.instructionFn(value[key], key, currentElement);\r\n          } else {\r\n            var temp = value[key];\r\n            delete value[key];\r\n            value[options.instructionNameFn(key, temp, currentElement)] = temp;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    if (isArray(currentElement[options[type + 'Key']])) {\r\n      currentElement[options[type + 'Key']].push(value);\r\n    } else {\r\n      currentElement[options[type + 'Key']] = value;\r\n    }\r\n  } else {\r\n    if (!currentElement[options.elementsKey]) {\r\n      currentElement[options.elementsKey] = [];\r\n    }\r\n    var element = {};\r\n    element[options.typeKey] = type;\r\n    if (type === 'instruction') {\r\n      for (key in value) {\r\n        if (value.hasOwnProperty(key)) {\r\n          break;\r\n        }\r\n      }\r\n      element[options.nameKey] = 'instructionNameFn' in options ? options.instructionNameFn(key, value, currentElement) : key;\r\n      if (options.instructionHasAttributes) {\r\n        element[options.attributesKey] = value[key][options.attributesKey];\r\n        if ('instructionFn' in options) {\r\n          element[options.attributesKey] = options.instructionFn(element[options.attributesKey], key, currentElement);\r\n        }\r\n      } else {\r\n        if ('instructionFn' in options) {\r\n          value[key] = options.instructionFn(value[key], key, currentElement);\r\n        }\r\n        element[options.instructionKey] = value[key];\r\n      }\r\n    } else {\r\n      if (type + 'Fn' in options) {\r\n        value = options[type + 'Fn'](value, currentElement);\r\n      }\r\n      element[options[type + 'Key']] = value;\r\n    }\r\n    if (options.addParent) {\r\n      element[options.parentKey] = currentElement;\r\n    }\r\n    currentElement[options.elementsKey].push(element);\r\n  }\r\n}\r\n\r\nfunction manipulateAttributes(attributes) {\r\n  if ('attributesFn' in options && attributes) {\r\n    attributes = options.attributesFn(attributes, currentElement);\r\n  }\r\n  if ((options.trim || 'attributeValueFn' in options || 'attributeNameFn' in options || options.nativeTypeAttributes) && attributes) {\r\n    var key;\r\n    for (key in attributes) {\r\n      if (attributes.hasOwnProperty(key)) {\r\n        if (options.trim) attributes[key] = attributes[key].trim();\r\n        if (options.nativeTypeAttributes) {\r\n          attributes[key] = nativeType(attributes[key]);\r\n        }\r\n        if ('attributeValueFn' in options) attributes[key] = options.attributeValueFn(attributes[key], key, currentElement);\r\n        if ('attributeNameFn' in options) {\r\n          var temp = attributes[key];\r\n          delete attributes[key];\r\n          attributes[options.attributeNameFn(key, attributes[key], currentElement)] = temp;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  return attributes;\r\n}\r\n\r\nfunction onInstruction(instruction) {\r\n  var attributes = {};\r\n  if (instruction.body && (instruction.name.toLowerCase() === 'xml' || options.instructionHasAttributes)) {\r\n    var attrsRegExp = /([\\w:-]+)\\s*=\\s*(?:\"([^\"]*)\"|'([^']*)'|(\\w+))\\s*/g;\r\n    var match;\r\n    while ((match = attrsRegExp.exec(instruction.body)) !== null) {\r\n      attributes[match[1]] = match[2] || match[3] || match[4];\r\n    }\r\n    attributes = manipulateAttributes(attributes);\r\n  }\r\n  if (instruction.name.toLowerCase() === 'xml') {\r\n    if (options.ignoreDeclaration) {\r\n      return;\r\n    }\r\n    currentElement[options.declarationKey] = {};\r\n    if (Object.keys(attributes).length) {\r\n      currentElement[options.declarationKey][options.attributesKey] = attributes;\r\n    }\r\n    if (options.addParent) {\r\n      currentElement[options.declarationKey][options.parentKey] = currentElement;\r\n    }\r\n  } else {\r\n    if (options.ignoreInstruction) {\r\n      return;\r\n    }\r\n    if (options.trim) {\r\n      instruction.body = instruction.body.trim();\r\n    }\r\n    var value = {};\r\n    if (options.instructionHasAttributes && Object.keys(attributes).length) {\r\n      value[instruction.name] = {};\r\n      value[instruction.name][options.attributesKey] = attributes;\r\n    } else {\r\n      value[instruction.name] = instruction.body;\r\n    }\r\n    addField('instruction', value);\r\n  }\r\n}\r\n\r\nfunction onStartElement(name, attributes) {\r\n  var element;\r\n  if (typeof name === 'object') {\r\n    attributes = name.attributes;\r\n    name = name.name;\r\n  }\r\n  attributes = manipulateAttributes(attributes);\r\n  if ('elementNameFn' in options) {\r\n    name = options.elementNameFn(name, currentElement);\r\n  }\r\n  if (options.compact) {\r\n    element = {};\r\n    if (!options.ignoreAttributes && attributes && Object.keys(attributes).length) {\r\n      element[options.attributesKey] = {};\r\n      var key;\r\n      for (key in attributes) {\r\n        if (attributes.hasOwnProperty(key)) {\r\n          element[options.attributesKey][key] = attributes[key];\r\n        }\r\n      }\r\n    }\r\n    if (\r\n      !(name in currentElement) &&\r\n      (isArray(options.alwaysArray) ? options.alwaysArray.indexOf(name) !== -1 : options.alwaysArray)\r\n    ) {\r\n      currentElement[name] = [];\r\n    }\r\n    if (currentElement[name] && !isArray(currentElement[name])) {\r\n      currentElement[name] = [currentElement[name]];\r\n    }\r\n    if (isArray(currentElement[name])) {\r\n      currentElement[name].push(element);\r\n    } else {\r\n      currentElement[name] = element;\r\n    }\r\n  } else {\r\n    if (!currentElement[options.elementsKey]) {\r\n      currentElement[options.elementsKey] = [];\r\n    }\r\n    element = {};\r\n    element[options.typeKey] = 'element';\r\n    element[options.nameKey] = name;\r\n    if (!options.ignoreAttributes && attributes && Object.keys(attributes).length) {\r\n      element[options.attributesKey] = attributes;\r\n    }\r\n    if (options.alwaysChildren) {\r\n      element[options.elementsKey] = [];\r\n    }\r\n    currentElement[options.elementsKey].push(element);\r\n  }\r\n  element[options.parentKey] = currentElement; // will be deleted in onEndElement() if !options.addParent\r\n  currentElement = element;\r\n}\r\n\r\nfunction onText(text) {\r\n  if (options.ignoreText) {\r\n    return;\r\n  }\r\n  if (!text.trim() && !options.captureSpacesBetweenElements) {\r\n    return;\r\n  }\r\n  if (options.trim) {\r\n    text = text.trim();\r\n  }\r\n  if (options.nativeType) {\r\n    text = nativeType(text);\r\n  }\r\n  if (options.sanitize) {\r\n    text = text.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\r\n  }\r\n  addField('text', text);\r\n}\r\n\r\nfunction onComment(comment) {\r\n  if (options.ignoreComment) {\r\n    return;\r\n  }\r\n  if (options.trim) {\r\n    comment = comment.trim();\r\n  }\r\n  addField('comment', comment);\r\n}\r\n\r\nfunction onEndElement(name) {\r\n  var parentElement = currentElement[options.parentKey];\r\n  if (!options.addParent) {\r\n    delete currentElement[options.parentKey];\r\n  }\r\n  currentElement = parentElement;\r\n}\r\n\r\nfunction onCdata(cdata) {\r\n  if (options.ignoreCdata) {\r\n    return;\r\n  }\r\n  if (options.trim) {\r\n    cdata = cdata.trim();\r\n  }\r\n  addField('cdata', cdata);\r\n}\r\n\r\nfunction onDoctype(doctype) {\r\n  if (options.ignoreDoctype) {\r\n    return;\r\n  }\r\n  doctype = doctype.replace(/^ /, '');\r\n  if (options.trim) {\r\n    doctype = doctype.trim();\r\n  }\r\n  addField('doctype', doctype);\r\n}\r\n\r\nfunction onError(error) {\r\n  error.note = error; //console.error(error);\r\n}\r\n\r\nmodule.exports = function (xml, userOptions) {\r\n\r\n  var parser = pureJsParser ? sax.parser(true, {}) : parser = new expat.Parser('UTF-8');\r\n  var result = {};\r\n  currentElement = result;\r\n\r\n  options = validateOptions(userOptions);\r\n\r\n  if (pureJsParser) {\r\n    parser.opt = {strictEntities: true};\r\n    parser.onopentag = onStartElement;\r\n    parser.ontext = onText;\r\n    parser.oncomment = onComment;\r\n    parser.onclosetag = onEndElement;\r\n    parser.onerror = onError;\r\n    parser.oncdata = onCdata;\r\n    parser.ondoctype = onDoctype;\r\n    parser.onprocessinginstruction = onInstruction;\r\n  } else {\r\n    parser.on('startElement', onStartElement);\r\n    parser.on('text', onText);\r\n    parser.on('comment', onComment);\r\n    parser.on('endElement', onEndElement);\r\n    parser.on('error', onError);\r\n    //parser.on('startCdata', onStartCdata);\r\n    //parser.on('endCdata', onEndCdata);\r\n    //parser.on('entityDecl', onEntityDecl);\r\n  }\r\n\r\n  if (pureJsParser) {\r\n    parser.write(xml).close();\r\n  } else {\r\n    if (!parser.parse(xml)) {\r\n      throw new Error('XML parsing error: ' + parser.getError());\r\n    }\r\n  }\r\n\r\n  if (result[options.elementsKey]) {\r\n    var temp = result[options.elementsKey];\r\n    delete result[options.elementsKey];\r\n    result[options.elementsKey] = temp;\r\n    delete result.text;\r\n  }\r\n\r\n  return result;\r\n\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xml-js/lib/xml2js.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xml-js/lib/xml2json.js":
/*!*********************************************!*\
  !*** ./node_modules/xml-js/lib/xml2json.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var helper = __webpack_require__(/*! ./options-helper */ \"(ssr)/./node_modules/xml-js/lib/options-helper.js\");\r\nvar xml2js = __webpack_require__(/*! ./xml2js */ \"(ssr)/./node_modules/xml-js/lib/xml2js.js\");\r\n\r\nfunction validateOptions (userOptions) {\r\n  var options = helper.copyOptions(userOptions);\r\n  helper.ensureSpacesExists(options);\r\n  return options;\r\n}\r\n\r\nmodule.exports = function(xml, userOptions) {\r\n  var options, js, json, parentKey;\r\n  options = validateOptions(userOptions);\r\n  js = xml2js(xml, options);\r\n  parentKey = 'compact' in options && options.compact ? '_parent' : 'parent';\r\n  // parentKey = ptions.compact ? '_parent' : 'parent'; // consider this\r\n  if ('addParent' in options && options.addParent) {\r\n    json = JSON.stringify(js, function (k, v) { return k === parentKey? '_' : v; }, options.spaces);\r\n  } else {\r\n    json = JSON.stringify(js, null, options.spaces);\r\n  }\r\n  return json.replace(/\\u2028/g, '\\\\u2028').replace(/\\u2029/g, '\\\\u2029');\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMveG1sLWpzL2xpYi94bWwyanNvbi5qcyIsIm1hcHBpbmdzIjoiQUFBQSxhQUFhLG1CQUFPLENBQUMsMkVBQWtCO0FBQ3ZDLGFBQWEsbUJBQU8sQ0FBQywyREFBVTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3REFBd0Q7QUFDeEQ7QUFDQSxnREFBZ0Qsa0NBQWtDO0FBQ2xGLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FydHdvcmstcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMveG1sLWpzL2xpYi94bWwyanNvbi5qcz9kMTQwIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBoZWxwZXIgPSByZXF1aXJlKCcuL29wdGlvbnMtaGVscGVyJyk7XHJcbnZhciB4bWwyanMgPSByZXF1aXJlKCcuL3htbDJqcycpO1xyXG5cclxuZnVuY3Rpb24gdmFsaWRhdGVPcHRpb25zICh1c2VyT3B0aW9ucykge1xyXG4gIHZhciBvcHRpb25zID0gaGVscGVyLmNvcHlPcHRpb25zKHVzZXJPcHRpb25zKTtcclxuICBoZWxwZXIuZW5zdXJlU3BhY2VzRXhpc3RzKG9wdGlvbnMpO1xyXG4gIHJldHVybiBvcHRpb25zO1xyXG59XHJcblxyXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uKHhtbCwgdXNlck9wdGlvbnMpIHtcclxuICB2YXIgb3B0aW9ucywganMsIGpzb24sIHBhcmVudEtleTtcclxuICBvcHRpb25zID0gdmFsaWRhdGVPcHRpb25zKHVzZXJPcHRpb25zKTtcclxuICBqcyA9IHhtbDJqcyh4bWwsIG9wdGlvbnMpO1xyXG4gIHBhcmVudEtleSA9ICdjb21wYWN0JyBpbiBvcHRpb25zICYmIG9wdGlvbnMuY29tcGFjdCA/ICdfcGFyZW50JyA6ICdwYXJlbnQnO1xyXG4gIC8vIHBhcmVudEtleSA9IHB0aW9ucy5jb21wYWN0ID8gJ19wYXJlbnQnIDogJ3BhcmVudCc7IC8vIGNvbnNpZGVyIHRoaXNcclxuICBpZiAoJ2FkZFBhcmVudCcgaW4gb3B0aW9ucyAmJiBvcHRpb25zLmFkZFBhcmVudCkge1xyXG4gICAganNvbiA9IEpTT04uc3RyaW5naWZ5KGpzLCBmdW5jdGlvbiAoaywgdikgeyByZXR1cm4gayA9PT0gcGFyZW50S2V5PyAnXycgOiB2OyB9LCBvcHRpb25zLnNwYWNlcyk7XHJcbiAgfSBlbHNlIHtcclxuICAgIGpzb24gPSBKU09OLnN0cmluZ2lmeShqcywgbnVsbCwgb3B0aW9ucy5zcGFjZXMpO1xyXG4gIH1cclxuICByZXR1cm4ganNvbi5yZXBsYWNlKC9cXHUyMDI4L2csICdcXFxcdTIwMjgnKS5yZXBsYWNlKC9cXHUyMDI5L2csICdcXFxcdTIwMjknKTtcclxufTtcclxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xml-js/lib/xml2json.js\n");

/***/ })

};
;