"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_pascal_pascal_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/pascal/pascal.js":
/*!****************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/pascal/pascal.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/pascal/pascal.ts\nvar conf = {\n  // the default separators except `@$`\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"{\", \"}\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"<\", \">\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\{\\\\$REGION(\\\\s\\\\'.*\\\\')?\\\\}\"),\n      end: new RegExp(\"^\\\\s*\\\\{\\\\$ENDREGION\\\\}\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".pascal\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  keywords: [\n    \"absolute\",\n    \"abstract\",\n    \"all\",\n    \"and_then\",\n    \"array\",\n    \"as\",\n    \"asm\",\n    \"attribute\",\n    \"begin\",\n    \"bindable\",\n    \"case\",\n    \"class\",\n    \"const\",\n    \"contains\",\n    \"default\",\n    \"div\",\n    \"else\",\n    \"end\",\n    \"except\",\n    \"exports\",\n    \"external\",\n    \"far\",\n    \"file\",\n    \"finalization\",\n    \"finally\",\n    \"forward\",\n    \"generic\",\n    \"goto\",\n    \"if\",\n    \"implements\",\n    \"import\",\n    \"in\",\n    \"index\",\n    \"inherited\",\n    \"initialization\",\n    \"interrupt\",\n    \"is\",\n    \"label\",\n    \"library\",\n    \"mod\",\n    \"module\",\n    \"name\",\n    \"near\",\n    \"not\",\n    \"object\",\n    \"of\",\n    \"on\",\n    \"only\",\n    \"operator\",\n    \"or_else\",\n    \"otherwise\",\n    \"override\",\n    \"package\",\n    \"packed\",\n    \"pow\",\n    \"private\",\n    \"program\",\n    \"protected\",\n    \"public\",\n    \"published\",\n    \"interface\",\n    \"implementation\",\n    \"qualified\",\n    \"read\",\n    \"record\",\n    \"resident\",\n    \"requires\",\n    \"resourcestring\",\n    \"restricted\",\n    \"segment\",\n    \"set\",\n    \"shl\",\n    \"shr\",\n    \"specialize\",\n    \"stored\",\n    \"strict\",\n    \"then\",\n    \"threadvar\",\n    \"to\",\n    \"try\",\n    \"type\",\n    \"unit\",\n    \"uses\",\n    \"var\",\n    \"view\",\n    \"virtual\",\n    \"dynamic\",\n    \"overload\",\n    \"reintroduce\",\n    \"with\",\n    \"write\",\n    \"xor\",\n    \"true\",\n    \"false\",\n    \"procedure\",\n    \"function\",\n    \"constructor\",\n    \"destructor\",\n    \"property\",\n    \"break\",\n    \"continue\",\n    \"exit\",\n    \"abort\",\n    \"while\",\n    \"do\",\n    \"for\",\n    \"raise\",\n    \"repeat\",\n    \"until\"\n  ],\n  typeKeywords: [\n    \"boolean\",\n    \"double\",\n    \"byte\",\n    \"integer\",\n    \"shortint\",\n    \"char\",\n    \"longint\",\n    \"float\",\n    \"string\"\n  ],\n  operators: [\n    \"=\",\n    \">\",\n    \"<\",\n    \"<=\",\n    \">=\",\n    \"<>\",\n    \":\",\n    \":=\",\n    \"and\",\n    \"or\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"@\",\n    \"&\",\n    \"^\",\n    \"%\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><:@\\^&|+\\-*\\/\\^%]+/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [\n        /[a-zA-Z_][\\w]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/\\$[0-9a-fA-F]{1,16}/, \"number.hex\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'/, \"string\", \"@string\"],\n      // characters\n      [/'[^\\\\']'/, \"string\"],\n      [/'/, \"string.invalid\"],\n      [/\\#\\d+/, \"string\"]\n    ],\n    comment: [\n      [/[^\\*\\}]+/, \"comment\"],\n      //[/\\(\\*/,    'comment', '@push' ],    // nested comment  not allowed :-(\n      [/\\}/, \"comment\", \"@pop\"],\n      [/[\\{]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\']+/, \"string\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\{/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/pascal/pascal.js\n"));

/***/ })

}]);