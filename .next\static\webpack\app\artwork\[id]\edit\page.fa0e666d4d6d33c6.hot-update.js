"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/AIAssistant/ChatInterface.tsx":
/*!******************************************************!*\
  !*** ./src/components/AIAssistant/ChatInterface.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chatHistoryService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chatHistoryService */ \"(app-pages-browser)/./src/services/chatHistoryService.ts\");\n/* harmony import */ var _services_fileTreeService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\");\n/* harmony import */ var _services_diffToolService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/diffToolService */ \"(app-pages-browser)/./src/services/diffToolService.ts\");\n/* harmony import */ var _services_aiService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/aiService */ \"(app-pages-browser)/./src/services/aiService.ts\");\n/* harmony import */ var _utils_pathUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/pathUtils */ \"(app-pages-browser)/./src/utils/pathUtils.ts\");\n/* harmony import */ var _utils_SelectionManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/SelectionManager */ \"(app-pages-browser)/./src/utils/SelectionManager.ts\");\n/* harmony import */ var _SessionManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./SessionManager */ \"(app-pages-browser)/./src/components/AIAssistant/SessionManager.tsx\");\n/* harmony import */ var _FileAssociationTreeDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./FileAssociationTreeDialog */ \"(app-pages-browser)/./src/components/AIAssistant/FileAssociationTreeDialog.tsx\");\n/* harmony import */ var _CompactDiffDisplay__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./CompactDiffDisplay */ \"(app-pages-browser)/./src/components/AIAssistant/CompactDiffDisplay.tsx\");\n/* harmony import */ var _MediaUploader__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./MediaUploader */ \"(app-pages-browser)/./src/components/AIAssistant/MediaUploader.tsx\");\n/* harmony import */ var _HelperResponseLayer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./HelperResponseLayer */ \"(app-pages-browser)/./src/components/AIAssistant/HelperResponseLayer.tsx\");\n/* harmony import */ var _PromptManagerModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./PromptManagerModal */ \"(app-pages-browser)/./src/components/AIAssistant/PromptManagerModal.tsx\");\n/* harmony import */ var _MultipleComparisonModal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./MultipleComparisonModal */ \"(app-pages-browser)/./src/components/AIAssistant/MultipleComparisonModal.tsx\");\n/* harmony import */ var _utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/humanBlockParser */ \"(app-pages-browser)/./src/utils/humanBlockParser.ts\");\n/* harmony import */ var _services_promptConfigService__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/services/promptConfigService */ \"(app-pages-browser)/./src/services/promptConfigService.ts\");\n/**\r\n * AI对话界面组件\r\n * 传统的对话形式界面，用于与AI进行实时对话\r\n * 优化版本：解决文本选择被频繁重渲染取消的问题\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n // Renamed to avoid conflict\n\n\n\n\n\n\n\n\n\n\nconst ChatInterface = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c = _s(function ChatInterface(param, ref) {\n    let { currentConfig, onSendMessage, responseContent, isStreaming, isComplete, error, onRetry, onCopy, onStop, onInsertToEditor, onContentInsert, onMessageContentInsert, isLoading, className = \"\", currentPersona, onChatHistoryChange, // 文件关联相关props\n    associatedFiles = [], onFilesChange = ()=>{}, onShowFileAssociation = ()=>{}, // 🔧 AI响应状态清理回调\n    onClearAIResponse, // 🔧 作品ID，用于文件关联\n    artworkId, // 🔧 文件选择回调，用于跳转到编辑器\n    onFileSelect, // 🔧 详细对比回调，用于打开详细差异对比视图\n    onOpenDetailedDiff, // 🔧 聚焦文件状态，由父组件管理\n    focusedFile, // 🔧 媒体文件变化回调\n    onMediaFilesChange, // 🔧 受众设置回调\n    onShowAudienceSettings, // 🔧 会话管理回调\n    onShowSessionManager, // 🔧 多项对比消息构建回调\n    onMultipleComparisonSend } = param;\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [chatHistory, setChatHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSessionManager, setShowSessionManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFileAssociation, setShowFileAssociation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPromptManager, setShowPromptManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activePromptConfig, setActivePromptConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isFileAssociationCollapsed, setIsFileAssociationCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filePaths, setFilePaths] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [showToolsPanel, setShowToolsPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 消息工具面板状态 - 记录哪条消息的工具面板正在显示\n    const [activeMessageTools, setActiveMessageTools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 编辑消息状态\n    const [editingMessageId, setEditingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingContent, setEditingContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 多项对比弹窗状态\n    const [showMultipleComparison, setShowMultipleComparison] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [comparisonMessageContent, setComparisonMessageContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [comparisonTriggerMessageIndex, setComparisonTriggerMessageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    // 共享状态\n    const [availableModels, setAvailableModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingModels, setIsLoadingModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 单模型生成相关状态\n    const [selectedSingleModel, setSelectedSingleModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [singleModelResults, setSingleModelResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [streamingSingleResults, setStreamingSingleResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [generationCount, setGenerationCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3);\n    // 多模型对比相关状态\n    const [selectedMultiModels, setSelectedMultiModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [multiModelResults, setMultiModelResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [streamingMultiModels, setStreamingMultiModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // 🔧 聚焦文件状态现在由父组件管理，通过props传递\n    // 媒体上传相关状态\n    const [uploadedMedia, setUploadedMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showMediaUploader, setShowMediaUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMediaList, setShowMediaList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 消息层状态管理\n    const [messageLayers, setMessageLayers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pendingIntegratedMessage, setPendingIntegratedMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [processedMessages, setProcessedMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // 🔧 调试监听已移除，避免控制台日志过多\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const aiResponseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 🔧 响应内容引用，避免useEffect依赖responseContent导致重复渲染\n    const responseContentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    // 🔧 文本选择管理器\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 初始化选择管理器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!selectionManagerRef.current) {\n            selectionManagerRef.current = new _utils_SelectionManager__WEBPACK_IMPORTED_MODULE_7__.SelectionManager();\n        }\n        return ()=>{\n            if (selectionManagerRef.current && aiResponseRef.current) {\n                selectionManagerRef.current.stopListening(aiResponseRef.current);\n            }\n        };\n    }, []);\n    // 点击外部关闭工具面板\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (showToolsPanel) {\n                const target = event.target;\n                if (!target.closest(\".tools-panel-container\")) {\n                    setShowToolsPanel(false);\n                }\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        showToolsPanel\n    ]);\n    // 暂时禁用选择监听\n    // useEffect(() => {\n    //   if (selectionManagerRef.current && aiResponseRef.current) {\n    //     selectionManagerRef.current.startListening(aiResponseRef.current)\n    //   }\n    //   return () => {\n    //     if (selectionManagerRef.current && aiResponseRef.current) {\n    //       selectionManagerRef.current.stopListening(aiResponseRef.current)\n    //     }\n    //   }\n    // }, [])\n    // 🔧 同步responseContent到ref，避免useEffect直接依赖responseContent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        responseContentRef.current = responseContent;\n    }, [\n        responseContent\n    ]);\n    // 🔧 修复重复渲染问题：使用useMemo确保chatHistoryService引用稳定\n    const chatHistoryService = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>_services_chatHistoryService__WEBPACK_IMPORTED_MODULE_2__.ChatHistoryService.getInstance(), []);\n    // 辅助函数：标准化路径\n    const normalizePath = (path)=>{\n        if (!path) return \"\";\n        // 使用导入的工具函数，避免命名冲突\n        return (0,_utils_pathUtils__WEBPACK_IMPORTED_MODULE_6__.normalizePath)(path).toLowerCase();\n    };\n    // 辅助函数：根据路径查找文件ID\n    const findFileIdByPath = (rootNode, targetPath)=>{\n        // 标准化目标路径，确保相对路径和绝对路径都能正确匹配\n        const normalizedTargetPath = normalizePath(targetPath);\n        const searchNode = (node)=>{\n            // 标准化当前节点路径进行比较\n            if (normalizePath(node.path) === normalizedTargetPath) {\n                return node.id;\n            }\n            if (node.children) {\n                for (const child of node.children){\n                    const foundId = searchNode(child);\n                    if (foundId) {\n                        return foundId;\n                    }\n                }\n            }\n            return null;\n        };\n        return searchNode(rootNode);\n    };\n    // 辅助函数：解析替换内容（仅支持JSON格式）\n    const parseReplaceContent = (content, find)=>{\n        // JSON格式：直接使用find和content参数\n        if (find) {\n            console.log(\"✅ 使用JSON格式解析:\", {\n                find,\n                content: content.substring(0, 100) + \"...\"\n            });\n            return [\n                find,\n                content\n            ];\n        }\n        // 如果没有find参数，说明格式不正确\n        console.error(\"❌ 替换操作缺少查找内容\");\n        console.error('\\uD83D\\uDCDD 请使用JSON格式：{\"file\": \"...\", \"type\": \"replace\", \"find\": \"查找内容\", \"content\": \"替换内容\"}');\n        console.error(\"\\uD83D\\uDCDD 实际收到的内容:\", content.substring(0, 200));\n        // 返回错误状态，让调用方处理\n        return [\n            \"\",\n            content\n        ];\n    };\n    // 辅助函数：获取所有文件路径（用于调试）\n    const getAllFilePaths = (rootNode)=>{\n        const paths = [];\n        const collectPaths = (node)=>{\n            if (node.type === \"file\") {\n                paths.push(node.path);\n            }\n            if (node.children) {\n                node.children.forEach(collectPaths);\n            }\n        };\n        collectPaths(rootNode);\n        return paths;\n    };\n    // 辅助函数：根据文件ID查找文件完整路径\n    const findFilePathById = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (fileId)=>{\n        try {\n            if (!artworkId) return \"未知文件 (\".concat(fileId.substring(0, 8), \"...)\");\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_3__.FileTreeService.getInstance();\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (fileTreeResult.success && fileTreeResult.data) {\n                const filePath = getFilePath(fileTreeResult.data, fileId);\n                return filePath || \"未知文件 (\".concat(fileId.substring(0, 8), \"...)\");\n            }\n            return \"未知文件 (\".concat(fileId.substring(0, 8), \"...)\");\n        } catch (error) {\n            console.warn(\"获取文件路径失败:\", error);\n            return \"未知文件 (\".concat(fileId.substring(0, 8), \"...)\");\n        }\n    }, [\n        artworkId\n    ]);\n    // 辅助函数：获取文件的完整路径（不包括作品根节点）\n    const getFilePath = (rootNode, fileId)=>{\n        // 递归搜索文件树，构建路径\n        const buildPath = function(node, targetId) {\n            let currentPath = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n            // 如果找到目标节点，返回当前路径\n            if (node.id === targetId) {\n                return [\n                    ...currentPath,\n                    node.name\n                ];\n            }\n            // 如果有子节点，递归搜索\n            if (node.children) {\n                for (const child of node.children){\n                    const path = buildPath(child, targetId, [\n                        ...currentPath,\n                        node.name\n                    ]);\n                    if (path) {\n                        return path;\n                    }\n                }\n            }\n            return null;\n        };\n        // 从根节点开始搜索，但不包括根节点名称\n        const path = buildPath(rootNode, fileId, []);\n        if (path) {\n            // 移除第一个元素（根节点名称）\n            path.shift();\n            return path.join(\"/\");\n        }\n        return fileId.substring(0, 8) + \"...\"; // 如果找不到路径，返回ID的简短版本\n    };\n    // 递归搜索文件树中的文件名（保留用于兼容性）\n    const searchNodeForName = (node, fileId)=>{\n        if (node.id === fileId) {\n            return node.name;\n        }\n        if (node.children) {\n            for (const child of node.children){\n                const result = searchNodeForName(child, fileId);\n                if (result) return result;\n            }\n        }\n        return null;\n    };\n    // 🔧 聚焦文件状态现在由父组件统一管理，移除子组件中的事件监听\n    // 组件初始化时加载历史记录和当前会话ID\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeSession = async ()=>{\n            try {\n                // 获取当前会话ID\n                const sessionId = chatHistoryService.getCurrentSessionId();\n                setCurrentSessionId(sessionId);\n                // 加载当前会话的历史记录\n                const storedHistory = await chatHistoryService.getCurrentHistory();\n                if (storedHistory.length > 0) {\n                    setChatHistory(storedHistory);\n                    console.log(\"✅ 已从IndexedDB加载对话历史:\", storedHistory.length, \"条消息\");\n                    // 通知父组件对话历史变化\n                    if (onChatHistoryChange) {\n                        onChatHistoryChange(storedHistory);\n                    }\n                }\n            // 文件关联数据由父组件管理，这里不需要重复加载\n            } catch (error) {\n                console.error(\"❌ 加载对话历史失败:\", error);\n            }\n        };\n        initializeSession();\n    }, [\n        chatHistoryService,\n        onChatHistoryChange\n    ]);\n    // 加载激活的提示词配置\n    const loadActivePromptConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const promptConfigService = _services_promptConfigService__WEBPACK_IMPORTED_MODULE_16__.PromptConfigService.getInstance();\n            const result = await promptConfigService.getActiveConfig();\n            if (result.success && result.data) {\n                setActivePromptConfig(result.data);\n            } else {\n                setActivePromptConfig(null);\n            }\n        } catch (error) {\n            console.error(\"加载激活提示词配置失败:\", error);\n            setActivePromptConfig(null);\n        }\n    }, []);\n    // 组件挂载时加载激活配置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadActivePromptConfig();\n    }, [\n        loadActivePromptConfig\n    ]);\n    // 处理提示词配置选择\n    const handlePromptConfigSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setActivePromptConfig(config);\n        console.log(\"\\uD83D\\uDD2E 选择提示词配置:\", config.name);\n        // 重新加载激活配置以确保状态同步\n        setTimeout(()=>{\n            loadActivePromptConfig();\n        }, 100);\n    }, [\n        loadActivePromptConfig\n    ]);\n    // 生成唯一消息ID\n    const generateMessageId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((type)=>{\n        const timestamp = Date.now();\n        const random = Math.random().toString(36).substring(2, 11);\n        return \"\".concat(type, \"-\").concat(timestamp, \"-\").concat(random);\n    }, []);\n    // 添加用户消息到历史记录\n    const addUserMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message)=>{\n        setChatHistory((prev)=>{\n            // 检查消息是否已存在，避免重复添加\n            const exists = prev.some((m)=>m.id === message.id);\n            if (exists) {\n                console.log(\"⚠️ 消息已存在，跳过添加:\", message.content.substring(0, 20));\n                return prev;\n            }\n            const newHistory = [\n                ...prev,\n                message\n            ];\n            console.log(\"✅ 用户消息已添加:\", message.content.substring(0, 20), \"历史长度:\", newHistory.length);\n            // 异步保存到IndexedDB\n            chatHistoryService.saveCurrentHistory(newHistory).catch((error)=>{\n                console.error(\"❌ 保存用户消息失败:\", error);\n            });\n            // 异步通知父组件对话历史变化，避免在渲染过程中同步更新\n            setTimeout(()=>{\n                if (onChatHistoryChange) {\n                    onChatHistoryChange(newHistory);\n                }\n            }, 0);\n            return newHistory;\n        });\n    }, [\n        onChatHistoryChange,\n        chatHistoryService\n    ]);\n    // 处理跳转到编辑器\n    const handleJumpToEditor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filePath)=>{\n        try {\n            if (!artworkId) return;\n            // 通过文件路径查找文件ID\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_3__.FileTreeService.getInstance();\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (fileTreeResult.success && fileTreeResult.data) {\n                const fileId = findFileIdByPath(fileTreeResult.data, filePath);\n                if (fileId) {\n                    // 触发文件选择事件，通知父组件切换到编辑器\n                    onFileSelect === null || onFileSelect === void 0 ? void 0 : onFileSelect(fileId);\n                    // 可选：设置聚焦状态\n                    const chatHistoryService = _services_chatHistoryService__WEBPACK_IMPORTED_MODULE_2__.ChatHistoryService.getInstance();\n                    await chatHistoryService.setCurrentFocusedFile(fileId);\n                    console.log(\"\\uD83D\\uDD17 跳转到编辑器:\", filePath);\n                } else {\n                    console.warn(\"⚠️ 未找到文件:\", filePath);\n                }\n            }\n        } catch (error) {\n            console.error(\"❌ 跳转到编辑器失败:\", error);\n        }\n    }, [\n        artworkId,\n        onFileSelect\n    ]);\n    // 处理应用diff修改\n    const handleApplyDiff = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filePath, content, operation, find)=>{\n        try {\n            if (!artworkId) return;\n            // 标准化文件路径，确保相对路径和绝对路径都能正确处理\n            const normalizedPath = normalizePath(filePath);\n            console.log(\"\\uD83D\\uDD0D 查找文件:\", {\n                original: filePath,\n                normalized: normalizedPath\n            });\n            const diffToolService = _services_diffToolService__WEBPACK_IMPORTED_MODULE_4__.DiffToolService.getInstance();\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_3__.FileTreeService.getInstance();\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (!fileTreeResult.success || !fileTreeResult.data) {\n                console.error(\"❌ 获取文件树失败\");\n                return;\n            }\n            const fileId = findFileIdByPath(fileTreeResult.data, normalizedPath);\n            if (!fileId) {\n                // 增强错误日志，提供详细的路径匹配调试信息\n                const availablePaths = getAllFilePaths(fileTreeResult.data);\n                console.warn(\"⚠️ 未找到文件:\", {\n                    path: normalizedPath,\n                    originalPath: filePath,\n                    availableFiles: availablePaths.slice(0, 10),\n                    totalFiles: availablePaths.length\n                });\n                return;\n            }\n            let result;\n            if (operation === \"append\") {\n                result = await diffToolService.appendText(fileId, {\n                    content\n                });\n            } else if (operation === \"replace\") {\n                // 解析替换参数\n                const [pattern, replacement] = parseReplaceContent(content, find);\n                // 🔧 使用字面量字符串替换而不是正则表达式\n                // 获取文件内容并进行字面量替换\n                const fileResult = await fileTreeService.getFile(fileId);\n                if (fileResult.success && fileResult.data) {\n                    const originalContent = fileResult.data.content || \"\";\n                    // 检查是否包含要替换的内容\n                    if (!originalContent.includes(pattern)) {\n                        console.error(\"❌ 文件中未找到要替换的内容:\", pattern);\n                        console.log(\"\\uD83D\\uDD0D 文件内容预览:\", originalContent.substring(0, 500));\n                        console.log(\"\\uD83D\\uDD0D 搜索模式长度:\", pattern.length);\n                        console.log(\"\\uD83D\\uDD0D 搜索模式:\", JSON.stringify(pattern));\n                        console.log(\"\\uD83D\\uDD0D 文件内容长度:\", originalContent.length);\n                        // 尝试查找相似内容\n                        const lines = originalContent.split(\"\\n\");\n                        const patternLines = pattern.split(\"\\n\");\n                        console.log(\"\\uD83D\\uDD0D 搜索模式行数:\", patternLines.length);\n                        console.log(\"\\uD83D\\uDD0D 文件总行数:\", lines.length);\n                        // 查找包含搜索模式第一行的行\n                        if (patternLines.length > 0) {\n                            const firstLine = patternLines[0].trim();\n                            const matchingLines = lines.filter((line, index)=>line.includes(firstLine)).map((line, index)=>({\n                                    line: line.trim(),\n                                    index\n                                }));\n                            if (matchingLines.length > 0) {\n                                console.log(\"\\uD83D\\uDD0D 找到包含首行的相似内容:\", matchingLines.slice(0, 3));\n                            } else {\n                                console.log(\"\\uD83D\\uDD0D 未找到包含首行的内容:\", firstLine);\n                            }\n                        }\n                        return;\n                    }\n                    // 🔧 安全的字面量替换 - 只替换第一个匹配项，避免split().join()的问题\n                    const firstIndex = originalContent.indexOf(pattern);\n                    if (firstIndex === -1) {\n                        console.error(\"❌ 意外错误：includes检查通过但indexOf失败\");\n                        return;\n                    }\n                    const modifiedContent = originalContent.substring(0, firstIndex) + replacement + originalContent.substring(firstIndex + pattern.length);\n                    console.log(\"\\uD83D\\uDD27 替换详情:\", {\n                        原文长度: originalContent.length,\n                        搜索内容: JSON.stringify(pattern),\n                        替换内容: JSON.stringify(replacement),\n                        匹配位置: firstIndex,\n                        修改后长度: modifiedContent.length\n                    });\n                    // 直接更新文件内容\n                    const updateResult = await fileTreeService.updateFileContent(fileId, modifiedContent, \"external\");\n                    if (updateResult.success) {\n                        console.log(\"✅ 字面量替换成功:\", pattern, \"->\", replacement);\n                        // 🔧 设置result以便后续处理\n                        result = {\n                            success: true,\n                            data: null\n                        };\n                    } else {\n                        console.error(\"❌ 更新文件内容失败:\", updateResult.error);\n                        return;\n                    }\n                } else {\n                    console.error(\"❌ 获取文件内容失败\");\n                    return;\n                }\n            }\n            if (result === null || result === void 0 ? void 0 : result.success) {\n                // 对于append操作，需要应用diff；对于replace操作，已经直接更新了文件\n                if (operation === \"append\" && result.data) {\n                    await diffToolService.applyDiff(result.data);\n                }\n                console.log(\"✅ 文件修改已应用:\", filePath);\n                // 🔧 延迟通知编辑器刷新文件内容，确保文件写入完成\n                if (onFileSelect && fileId) {\n                    setTimeout(()=>{\n                        onFileSelect(fileId);\n                        console.log(\"✅ 已通知编辑器刷新文件:\", filePath);\n                    }, 200) // 200ms延迟确保文件系统完成写入\n                    ;\n                }\n            // 可选：显示成功提示\n            } else {\n                console.error(\"❌ 应用修改失败:\", result === null || result === void 0 ? void 0 : result.error);\n            }\n        } catch (error) {\n            console.error(\"❌ 应用差异修改失败:\", error);\n        }\n    }, [\n        artworkId,\n        onFileSelect\n    ]);\n    // 处理媒体文件上传成功\n    const handleMediaUploadSuccess = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((mediaFile)=>{\n        setUploadedMedia((prev)=>{\n            const newMediaFiles = [\n                ...prev,\n                mediaFile\n            ];\n            // 通知父组件媒体文件变化\n            if (onMediaFilesChange) {\n                onMediaFilesChange(newMediaFiles);\n            }\n            return newMediaFiles;\n        });\n        console.log(\"✅ 媒体文件上传成功:\", mediaFile.filename);\n    }, [\n        onMediaFilesChange\n    ]);\n    // 处理媒体文件上传错误\n    const handleMediaUploadError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((error)=>{\n        console.error(\"❌ 媒体文件上传失败:\", error);\n    // 可以在这里添加错误提示UI\n    }, []);\n    // 移除已上传的媒体文件\n    const removeMediaFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((fileId)=>{\n        setUploadedMedia((prev)=>{\n            const newMediaFiles = prev.filter((file)=>file.id !== fileId);\n            // 通知父组件媒体文件变化\n            if (onMediaFilesChange) {\n                onMediaFilesChange(newMediaFiles);\n            }\n            return newMediaFiles;\n        });\n    }, [\n        onMediaFilesChange\n    ]);\n    // 处理创建文件\n    const handleCreateFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filePath, content, operation)=>{\n        try {\n            if (!artworkId) {\n                console.error(\"❌ 缺少作品ID\");\n                return;\n            }\n            // 标准化文件路径，确保相对路径和绝对路径都能正确处理\n            const normalizedPath = normalizePath(filePath);\n            console.log(\"\\uD83D\\uDD0D 创建文件:\", {\n                original: filePath,\n                normalized: normalizedPath\n            });\n            const diffToolService = _services_diffToolService__WEBPACK_IMPORTED_MODULE_4__.DiffToolService.getInstance();\n            // 使用新的createFileWithPath方法，传入标准化后的路径\n            const result = await diffToolService.createFileWithPath(artworkId, normalizedPath, content, operation);\n            if (result.success) {\n                // 应用创建结果\n                await diffToolService.applyDiff(result.data);\n                console.log(\"✅ 文件创建并应用成功:\", filePath);\n                // 文件树会通过事件系统自动刷新，不再需要手动通知\n                console.log(\"✅ 文件创建完成，文件树将自动刷新\");\n                // 可选：跳转到新创建的文件\n                if (result.data.fileId) {\n                    handleJumpToEditor(filePath);\n                }\n            } else {\n                // 增强错误日志，提供详细的创建失败调试信息\n                console.error(\"❌ 文件创建失败:\", {\n                    path: normalizedPath,\n                    originalPath: filePath,\n                    error: result.error,\n                    operation: operation\n                });\n            }\n        } catch (error) {\n            console.error(\"❌ 创建文件失败:\", error);\n        }\n    }, [\n        artworkId,\n        handleJumpToEditor\n    ]);\n    // 🔧 渲染内容缓存，避免重复渲染相同内容\n    const renderedContentCache = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    // 🔧 简化消息渲染函数 - 添加缓存机制\n    const renderMessageContentInternal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((content)=>{\n        // 检查缓存\n        if (renderedContentCache.current.has(content)) {\n            return renderedContentCache.current.get(content);\n        }\n        // 第一步：先识别df代码块，避免被普通代码块处理干扰\n        const dfRegex = /```df\\s*\\n([\\s\\S]*?)```/g;\n        const diffRegex = /<diff>([\\s\\S]*?)<\\/diff>/g;\n        // 添加human代码块正则表达式，用于BrainstormingHelper功能\n        const humanRegex = /```human\\s*\\n\\s*neme:\\s*([^\\n]+)\\s*\\n\\s*currentPersona\\.description:\\s*([^\\n]+)\\s*\\n\\s*问题：\\s*([\\s\\S]*?)```/g;\n        // 第二步：提取普通代码块，但排除df代码块\n        const codeBlockRegex = /```(?!df\\s)(\\w+)?\\n([\\s\\S]*?)```/g;\n        const codeBlocks = [];\n        let processedContent = content;\n        let codeBlockMatch;\n        // 提取普通代码块并替换为占位符（排除df代码块）\n        while((codeBlockMatch = codeBlockRegex.exec(content)) !== null){\n            const placeholder = \"__CODE_BLOCK_\".concat(codeBlocks.length, \"__\");\n            codeBlocks.push({\n                placeholder,\n                content: codeBlockMatch[2],\n                lang: codeBlockMatch[1]\n            });\n            processedContent = processedContent.replace(codeBlockMatch[0], placeholder);\n        }\n        // 第三步：在处理后的内容中识别特殊标记（包括df代码块）\n        const parts = [];\n        let lastIndex = 0;\n        let match;\n        // 创建一个包含所有匹配项的数组，按位置排序\n        const allMatches = [];\n        // 收集diff匹配项\n        diffRegex.lastIndex = 0;\n        while((match = diffRegex.exec(processedContent)) !== null){\n            allMatches.push({\n                type: \"diff\",\n                index: match.index,\n                length: match[0].length,\n                content: match[1]\n            });\n        }\n        // 收集df匹配项（仅支持JSON格式）\n        dfRegex.lastIndex = 0;\n        while((match = dfRegex.exec(content)) !== null){\n            const dfContent = match[1].trim();\n            try {\n                // 只解析JSON格式\n                if (dfContent.startsWith(\"{\") && dfContent.endsWith(\"}\")) {\n                    const parsedDf = JSON.parse(dfContent);\n                    // 验证必需字段\n                    if (!parsedDf.file || !parsedDf.type) {\n                        throw new Error(\"缺少必需字段：file 和 type\");\n                    }\n                    // 验证操作类型\n                    if (![\n                        \"replace\",\n                        \"append\",\n                        \"create\"\n                    ].includes(parsedDf.type)) {\n                        throw new Error(\"不支持的操作类型: \".concat(parsedDf.type));\n                    }\n                    // 对于replace操作，验证find字段\n                    if (parsedDf.type === \"replace\" && !parsedDf.find) {\n                        throw new Error(\"replace操作需要find字段\");\n                    }\n                    allMatches.push({\n                        type: \"df\",\n                        index: match.index,\n                        length: match[0].length,\n                        filePath: parsedDf.file,\n                        operation: parsedDf.type,\n                        content: parsedDf.content || \"\",\n                        find: parsedDf.find || \"\"\n                    });\n                } else {\n                    throw new Error(\"df代码块必须使用JSON格式\");\n                }\n            } catch (error) {\n                console.error(\"❌ 解析df代码块失败:\", error);\n                console.error(\"\\uD83D\\uDCDD 正确格式示例:\");\n                console.error(\"```df\");\n                console.error(\"{\");\n                console.error('  \"file\": \"path/to/file\",');\n                console.error('  \"type\": \"replace\",');\n                console.error('  \"find\": \"要查找的内容\",');\n                console.error('  \"content\": \"替换内容\"');\n                console.error(\"}\");\n                console.error(\"```\");\n                console.error(\"\\uD83D\\uDCDD 实际内容:\", dfContent);\n                // 添加错误项，用于在UI中显示错误信息\n                allMatches.push({\n                    type: \"df\",\n                    index: match.index,\n                    length: match[0].length,\n                    filePath: \"格式错误\",\n                    operation: \"error\",\n                    content: dfContent,\n                    error: error instanceof Error ? error.message : String(error)\n                });\n            }\n        }\n        // 收集human匹配项（用于BrainstormingHelper功能）\n        humanRegex.lastIndex = 0;\n        while((match = humanRegex.exec(content)) !== null){\n            allMatches.push({\n                type: \"human\",\n                index: match.index,\n                length: match[0].length,\n                roleName: match[1].trim(),\n                roleDescription: match[2].trim(),\n                question: match[3].trim()\n            });\n        }\n        // 按位置排序所有匹配项\n        allMatches.sort((a, b)=>a.index - b.index);\n        // 第三步：处理所有匹配项和普通内容\n        for (const matchItem of allMatches){\n            // 添加匹配项前的普通内容\n            if (matchItem.index > lastIndex) {\n                const beforeContent = processedContent.substring(lastIndex, matchItem.index);\n                if (beforeContent.trim()) {\n                    parts.push({\n                        type: \"markdown\",\n                        content: beforeContent\n                    });\n                }\n            }\n            // 添加匹配项内容\n            parts.push(matchItem);\n            lastIndex = matchItem.index + matchItem.length;\n        }\n        // 添加最后剩余的普通内容\n        if (lastIndex < processedContent.length) {\n            const afterContent = processedContent.substring(lastIndex);\n            if (afterContent.trim()) {\n                parts.push({\n                    type: \"markdown\",\n                    content: afterContent\n                });\n            }\n        }\n        // 如果没有特殊内容，直接渲染markdown（恢复代码块）\n        if (parts.length === 0) {\n            const finalContent = restoreCodeBlocks(processedContent, codeBlocks);\n            const result = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"prose prose-invert max-w-none\",\n                dangerouslySetInnerHTML: {\n                    __html: renderMarkdown(finalContent)\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 876,\n                columnNumber: 9\n            }, this);\n            // 缓存结果\n            renderedContentCache.current.set(content, result);\n            // 限制缓存大小，避免内存泄漏\n            if (renderedContentCache.current.size > 50) {\n                const firstKey = renderedContentCache.current.keys().next().value;\n                renderedContentCache.current.delete(firstKey);\n            }\n            return result;\n        }\n        // 第四步：渲染混合内容（恢复代码块）\n        const result = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: parts.map((part, index)=>{\n                if (part.type === \"df\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CompactDiffDisplay__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        filePath: part.filePath,\n                        operation: part.operation,\n                        content: part.content,\n                        find: part.find,\n                        onJumpToEditor: handleJumpToEditor,\n                        onApplyChanges: handleApplyDiff,\n                        onCreateFile: handleCreateFile,\n                        onOpenDetailedDiff: onOpenDetailedDiff,\n                        artworkId: artworkId\n                    }, index, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 900,\n                        columnNumber: 15\n                    }, this);\n                } else if (part.type === \"human\") {\n                    // human代码块现在通过handleSendMessage统一处理，这里只显示原始内容\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-purple-900/20 border border-purple-500/30 rounded-lg p-4 my-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-purple-300 text-sm font-handwritten mb-2\",\n                                children: \"\\uD83E\\uDD16 辅助AI协同思考请求\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 917,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-300\",\n                                                children: \"角色：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 24\n                                            }, this),\n                                            part.roleName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 921,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-300\",\n                                                children: \"描述：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 24\n                                            }, this),\n                                            part.roleDescription\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-300\",\n                                                children: \"问题：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 923,\n                                                columnNumber: 24\n                                            }, this),\n                                            part.question\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 923,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 920,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400 mt-2\",\n                                children: \"ℹ️ 此请求将通过消息发送时自动处理\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 925,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 916,\n                        columnNumber: 15\n                    }, this);\n                } else {\n                    // 恢复markdown内容中的代码块\n                    const restoredContent = restoreCodeBlocks(part.content, codeBlocks);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose prose-invert max-w-none\",\n                        dangerouslySetInnerHTML: {\n                            __html: renderMarkdown(restoredContent)\n                        }\n                    }, index, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 934,\n                        columnNumber: 15\n                    }, this);\n                }\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n            lineNumber: 896,\n            columnNumber: 7\n        }, this);\n        // 缓存结果\n        renderedContentCache.current.set(content, result);\n        // 限制缓存大小，避免内存泄漏\n        if (renderedContentCache.current.size > 50) {\n            const firstKey = renderedContentCache.current.keys().next().value;\n            renderedContentCache.current.delete(firstKey);\n        }\n        return result;\n    }, [\n        handleJumpToEditor,\n        handleApplyDiff,\n        handleCreateFile,\n        onOpenDetailedDiff,\n        artworkId\n    ]);\n    // 辅助函数：恢复代码块\n    const restoreCodeBlocks = (content, codeBlocks)=>{\n        let restoredContent = content;\n        codeBlocks.forEach((block)=>{\n            const codeBlockHtml = \"```\".concat(block.lang || \"\", \"\\n\").concat(block.content, \"```\");\n            restoredContent = restoredContent.replace(block.placeholder, codeBlockHtml);\n        });\n        return restoredContent;\n    };\n    // Markdown渲染函数（简化版，完全移除diff处理）\n    const renderMarkdown = (text)=>{\n        // 代码块处理\n        const codeBlockRegex = /```(\\w+)?\\n([\\s\\S]*?)```/g;\n        const inlineCodeRegex = /`([^`]+)`/g;\n        // 标题处理\n        const headingRegex = /^(#{1,6})\\s+(.+)$/gm;\n        // 粗体和斜体\n        const boldRegex = /\\*\\*(.*?)\\*\\*/g;\n        const italicRegex = /\\*(.*?)\\*/g;\n        // 链接处理\n        const linkRegex = /\\[([^\\]]+)\\]\\(([^)]+)\\)/g;\n        return text// 代码块\n        .replace(codeBlockRegex, (match, lang, code)=>{\n            return '<div class=\"code-block bg-gray-800 rounded-lg p-3 my-2 overflow-x-auto\">\\n          '.concat(lang ? '<div class=\"text-xs text-gray-400 mb-2\">'.concat(lang, \"</div>\") : \"\", '\\n          <pre class=\"text-sm text-green-300 font-mono whitespace-pre-wrap\">').concat(code.trim(), \"</pre>\\n        </div>\");\n        })// 行内代码\n        .replace(inlineCodeRegex, '<code class=\"bg-gray-800 text-green-300 px-1 py-0.5 rounded text-sm font-mono\">$1</code>')// 标题\n        .replace(headingRegex, (match, hashes, title)=>{\n            const level = hashes.length;\n            const className = level === 1 ? \"text-lg font-bold text-amber-200 mt-4 mb-2\" : level === 2 ? \"text-md font-bold text-amber-200 mt-3 mb-2\" : \"text-sm font-bold text-amber-200 mt-2 mb-1\";\n            return \"<h\".concat(level, ' class=\"').concat(className, '\">').concat(title, \"</h\").concat(level, \">\");\n        })// 粗体\n        .replace(boldRegex, '<strong class=\"font-bold text-amber-100\">$1</strong>')// 斜体\n        .replace(italicRegex, '<em class=\"italic text-amber-100\">$1</em>')// 链接\n        .replace(linkRegex, '<a href=\"$2\" class=\"text-blue-400 hover:text-blue-300 underline\" target=\"_blank\" rel=\"noopener noreferrer\">$1</a>')// 换行\n        .replace(/\\n/g, \"<br>\");\n    };\n    // 自动调整输入框高度\n    const adjustTextareaHeight = ()=>{\n        if (textareaRef.current) {\n            textareaRef.current.style.height = \"auto\";\n            textareaRef.current.style.height = \"\".concat(Math.min(textareaRef.current.scrollHeight, 120), \"px\");\n        }\n    };\n    // 处理输入变化\n    const handleInputChange = (e)=>{\n        setInputMessage(e.target.value);\n        adjustTextareaHeight();\n    };\n    // 发送消息\n    const handleSend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        let messageContent = inputMessage.trim();\n        // 检查是否有激活的提示词配置\n        try {\n            const promptConfigService = _services_promptConfigService__WEBPACK_IMPORTED_MODULE_16__.PromptConfigService.getInstance();\n            const activeConfigResult = await promptConfigService.getActiveConfig();\n            if (activeConfigResult.success && activeConfigResult.data) {\n                const configContentResult = await promptConfigService.getConfigContent(activeConfigResult.data.id);\n                if (configContentResult.success && configContentResult.data) {\n                    // 将提示词内容拼接到用户输入前面\n                    messageContent = \"\".concat(configContentResult.data, \" \").concat(messageContent);\n                    console.log(\"\\uD83D\\uDD2E 应用提示词配置:\", activeConfigResult.data.name, \"内容:\", configContentResult.data);\n                }\n            }\n        } catch (error) {\n            console.error(\"获取提示词配置失败:\", error);\n        // 继续使用原始消息内容\n        }\n        // 检测是否包含```human代码块\n        const hasHumanBlock = (0,_utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_15__.hasHumanBlocks)(messageContent);\n        console.log(\"\\uD83D\\uDD35 开始发送用户消息:\", messageContent.substring(0, 30), hasHumanBlock ? \"(包含Human代码块)\" : \"\");\n        // 创建用户消息对象\n        const userMessage = {\n            id: generateMessageId(\"user\"),\n            type: \"user\",\n            content: messageContent,\n            timestamp: Date.now(),\n            createdAt: Date.now()\n        };\n        // 先清空输入框\n        setInputMessage(\"\");\n        // 重置输入框高度\n        if (textareaRef.current) {\n            textareaRef.current.style.height = \"auto\";\n        }\n        // 添加用户消息到历史记录\n        addUserMessage(userMessage);\n        if (hasHumanBlock) {\n            // 如果包含```human代码块，处理辅助AI调用\n            handleHumanBlockMessage(messageContent);\n        } else {\n            // 正常消息处理\n            setTimeout(()=>{\n                onSendMessage(messageContent);\n            }, 0);\n        }\n    }, [\n        inputMessage,\n        isLoading,\n        generateMessageId,\n        addUserMessage,\n        onSendMessage\n    ]);\n    // 处理包含```human代码块的消息\n    const handleHumanBlockMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message)=>{\n        // 生成消息唯一标识符，防止重复处理\n        const messageHash = btoa(encodeURIComponent(message)).replace(/[^a-zA-Z0-9]/g, \"\").substring(0, 16);\n        if (processedMessages.has(messageHash)) {\n            console.log(\"⚠️ 消息已处理过，跳过重复处理:\", messageHash);\n            return;\n        }\n        // 标记消息为已处理\n        setProcessedMessages((prev)=>new Set([\n                ...prev,\n                messageHash\n            ]));\n        const humanBlocks = (0,_utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_15__.extractHumanBlocks)(message);\n        console.log(\"\\uD83E\\uDD16 检测到Human代码块:\", humanBlocks.length, \"个\", \"消息ID:\", messageHash);\n        // 为每个```human代码块创建HelperResponseLayer\n        humanBlocks.forEach((block, index)=>{\n            const layerId = \"helper-\".concat(messageHash, \"-\").concat(index);\n            const messageLayer = {\n                id: layerId,\n                type: \"helper-response\",\n                content: \"\",\n                timestamp: Date.now(),\n                metadata: {\n                    humanBlock: {\n                        roleName: block.roleName,\n                        roleDescription: block.roleDescription,\n                        question: block.question\n                    },\n                    originalMessage: message,\n                    isExpanded: true,\n                    layerId\n                }\n            };\n            // 添加到消息层列表\n            setMessageLayers((prev)=>[\n                    ...prev,\n                    messageLayer\n                ]);\n        });\n        // 移除```human代码块后的消息内容\n        const cleanMessage = (0,_utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_15__.removeHumanBlocks)(message);\n        // 如果还有其他内容，也发送给主AI\n        if (cleanMessage.trim()) {\n            setTimeout(()=>{\n                onSendMessage(cleanMessage);\n            }, 100);\n        }\n    }, [\n        onSendMessage,\n        processedMessages\n    ]);\n    // 处理辅助AI集成完成\n    const handleIntegrationComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((integratedMessage)=>{\n        console.log(\"\\uD83D\\uDD04 辅助AI集成完成，准备发送给主AI\");\n        setPendingIntegratedMessage(integratedMessage);\n    }, []);\n    // 处理主AI回复\n    const handleMainAIResponse = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((response)=>{\n        console.log(\"✅ 主AI回复完成\");\n    // 这里可以添加额外的处理逻辑\n    }, []);\n    // 处理辅助AI错误\n    const handleHelperError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((error)=>{\n        console.error(\"❌ 辅助AI处理错误:\", error);\n    // 可以显示错误提示\n    }, []);\n    // 发送集成消息给主AI\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingIntegratedMessage) {\n            setTimeout(()=>{\n                onSendMessage(pendingIntegratedMessage);\n                setPendingIntegratedMessage(\"\");\n            }, 500);\n        }\n    }, [\n        pendingIntegratedMessage,\n        onSendMessage\n    ]);\n    // 处理键盘事件\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    // 加载文件路径 - 添加数据完整性验证\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadFilePaths = async ()=>{\n            if (associatedFiles.length === 0) {\n                setFilePaths(new Map());\n                return;\n            }\n            const newFilePaths = new Map();\n            const validFileIds = [];\n            const invalidFileIds = [];\n            for (const fileId of associatedFiles){\n                // 数据完整性验证：检查fileId是否有效\n                if (!fileId || typeof fileId !== \"string\" || fileId.trim().length === 0) {\n                    console.warn(\"⚠️ 发现无效的文件ID:\", fileId);\n                    invalidFileIds.push(fileId);\n                    continue;\n                }\n                try {\n                    const filePath = await findFilePathById(fileId);\n                    // 验证文件路径是否有效\n                    if (filePath && !filePath.includes(\"未知文件\")) {\n                        newFilePaths.set(fileId, filePath);\n                        validFileIds.push(fileId);\n                    } else {\n                        console.warn(\"⚠️ 文件不存在或无法访问:\", fileId);\n                        invalidFileIds.push(fileId);\n                    }\n                } catch (error) {\n                    console.error(\"❌ 获取文件路径失败:\", fileId, error);\n                    invalidFileIds.push(fileId);\n                }\n            }\n            setFilePaths(newFilePaths);\n            // 如果发现无效文件，自动清理\n            if (invalidFileIds.length > 0) {\n                console.log(\"\\uD83E\\uDDF9 发现\", invalidFileIds.length, \"个无效文件，自动清理:\", invalidFileIds);\n                // 通知父组件更新文件关联，移除无效文件\n                if (validFileIds.length !== associatedFiles.length) {\n                    try {\n                        await chatHistoryService.saveCurrentFileAssociations(validFileIds);\n                        console.log(\"✅ 已自动清理无效文件关联\");\n                    } catch (error) {\n                        console.error(\"❌ 清理无效文件关联失败:\", error);\n                    }\n                }\n            }\n        };\n        loadFilePaths();\n    }, [\n        associatedFiles,\n        findFilePathById,\n        chatHistoryService\n    ]);\n    // 🔧 AI响应完成处理 - 使用防抖机制减少重复触发\n    const lastSavedContentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isComplete && responseContent && responseContent !== lastSavedContentRef.current) {\n            lastSavedContentRef.current = responseContent;\n            console.log(\"\\uD83E\\uDD16 AI响应完成，准备保存消息:\", {\n                isComplete,\n                contentLength: responseContent.length,\n                contentPreview: responseContent.substring(0, 50) + \"...\"\n            });\n            const assistantMessage = {\n                id: generateMessageId(\"assistant\"),\n                type: \"assistant\",\n                content: responseContent,\n                timestamp: Date.now(),\n                createdAt: Date.now()\n            };\n            setChatHistory((prev)=>{\n                console.log(\"\\uD83D\\uDCCB 当前历史记录数量:\", prev.length);\n                // 🔧 改进重复消息检测：检查最近的消息，而不是所有消息\n                // 只检查最近3条assistant消息，避免误判\n                const recentAssistantMessages = prev.filter((m)=>m.type === \"assistant\").slice(-3) // 只检查最近3条\n                ;\n                const duplicateExists = recentAssistantMessages.some((m)=>{\n                    const contentMatch = m.content === responseContent;\n                    const timeMatch = Math.abs(m.timestamp - assistantMessage.timestamp) < 5000 // 5秒内\n                    ;\n                    console.log(\"\\uD83D\\uDD0D 重复检测:\", {\n                        messageId: m.id.substring(0, 8),\n                        contentMatch,\n                        timeMatch,\n                        timeDiff: Math.abs(m.timestamp - assistantMessage.timestamp)\n                    });\n                    return contentMatch && timeMatch;\n                });\n                if (duplicateExists) {\n                    console.log(\"⚠️ 检测到重复消息，跳过添加\");\n                    return prev;\n                }\n                console.log(\"✅ 添加新的AI响应消息:\", assistantMessage.id.substring(0, 8));\n                const newHistory = [\n                    ...prev,\n                    assistantMessage\n                ];\n                // 异步保存历史记录\n                chatHistoryService.saveCurrentHistory(newHistory).catch((error)=>{\n                    console.error(\"❌ 保存AI响应消息失败:\", error);\n                });\n                // 通知父组件历史记录变化\n                if (onChatHistoryChange) {\n                    onChatHistoryChange(newHistory);\n                }\n                return newHistory;\n            });\n        }\n    }, [\n        isComplete,\n        responseContent,\n        generateMessageId,\n        chatHistoryService,\n        onChatHistoryChange\n    ]);\n    // 暂时禁用文本选择保护\n    // useEffect(() => {\n    //   if (selectionManagerRef.current && aiResponseRef.current) {\n    //     if (isStreaming) {\n    //       selectionManagerRef.current.clearSelection()\n    //     } else if (!selectionManagerRef.current.isSelecting()) {\n    //       selectionManagerRef.current.saveSelection(aiResponseRef.current)\n    //     }\n    //   }\n    // }, [isStreaming])\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 在DOM更新后恢复选择状态\n        if (selectionManagerRef.current && aiResponseRef.current && !isStreaming && isComplete) {\n            setTimeout(()=>{\n                var _selectionManagerRef_current;\n                (_selectionManagerRef_current = selectionManagerRef.current) === null || _selectionManagerRef_current === void 0 ? void 0 : _selectionManagerRef_current.restoreSelection(aiResponseRef.current);\n            }, 50) // 短延迟确保DOM更新完成\n            ;\n        }\n    }, [\n        isStreaming,\n        isComplete\n    ]) // 🔧 关键修复：移除responseContent依赖，避免持续重新渲染\n    ;\n    // 🔧 简化的滚动状态管理 - 参考StreamingResponse的简单模式\n    const scrollStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        shouldAutoScroll: true,\n        lastContentLength: 0\n    });\n    // 🔧 简化的滚动控制 - 参考StreamingResponse的直接滚动方式\n    const scrollToBottom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (chatContainerRef.current) {\n            chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n        }\n    }, []);\n    // 🔧 添加流式响应状态监控 - 移除responseContent.length依赖，避免频繁重渲染\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83C\\uDF0A 流式响应状态变化:\", {\n            isStreaming,\n            isComplete,\n            responseContentLength: responseContentRef.current.length,\n            timestamp: Date.now()\n        });\n    }, [\n        isStreaming,\n        isComplete\n    ]);\n    // 🔧 检测用户是否在底部附近\n    const isUserNearBottom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!chatContainerRef.current) return false;\n        const container = chatContainerRef.current;\n        return container.scrollTop + container.clientHeight >= container.scrollHeight - 100;\n    }, []);\n    // 🔧 聊天历史变化时的滚动控制 - 简化版本，参考StreamingResponse\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (chatContainerRef.current && chatHistory.length > 0) {\n            // 简化逻辑：如果用户在底部或启用自动滚动，直接滚动到底部\n            if (isUserNearBottom() || scrollStateRef.current.shouldAutoScroll) {\n                chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n            }\n        }\n    }, [\n        chatHistory.length,\n        isUserNearBottom\n    ]);\n    // 🔧 流式响应时的实时滚动 - 简化版本，参考StreamingResponse的直接模式\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isStreaming && responseContentRef.current && chatContainerRef.current) {\n            const currentLength = responseContentRef.current.length;\n            // 简化逻辑：内容增加时，如果用户在底部就直接滚动\n            if (currentLength > scrollStateRef.current.lastContentLength) {\n                scrollStateRef.current.lastContentLength = currentLength;\n                if (isUserNearBottom() || scrollStateRef.current.shouldAutoScroll) {\n                    // 直接滚动，无需额外的函数调用\n                    chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n                }\n            }\n        }\n    }, [\n        isStreaming,\n        isUserNearBottom\n    ]) // 🔧 简化依赖项\n    ;\n    // 🔧 用户滚动事件处理 - 智能检测用户滚动意图\n    const handleUserScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (chatContainerRef.current) {\n            // 当用户手动滚动时，根据位置决定是否启用自动滚动\n            scrollStateRef.current.shouldAutoScroll = isUserNearBottom();\n        }\n    }, [\n        isUserNearBottom\n    ]);\n    // 处理会话切换\n    const handleSessionSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (sessionId)=>{\n        try {\n            console.log(\"\\uD83D\\uDD04 开始切换会话:\", sessionId);\n            // 🔧 关键修复：在切换会话前，先清理当前AI响应状态\n            // 这是解决会话切换时AI响应残留问题的核心修复\n            console.log(\"\\uD83E\\uDDF9 清理AI响应状态...\");\n            // 通知父组件清理AI响应状态\n            // 这里需要父组件提供清理回调函数\n            if (onClearAIResponse) {\n                onClearAIResponse();\n            }\n            setCurrentSessionId(sessionId);\n            // 加载选中会话的历史记录\n            const sessionHistory = await chatHistoryService.loadHistory(sessionId);\n            setChatHistory(sessionHistory);\n            // 通知父组件对话历史变化\n            if (onChatHistoryChange) {\n                onChatHistoryChange(sessionHistory);\n            }\n            console.log(\"✅ 会话已切换:\", sessionId, \"历史记录:\", sessionHistory.length, \"条\");\n            console.log(\"✅ AI响应状态已清理\");\n        } catch (error) {\n            console.error(\"❌ 切换会话失败:\", error);\n        }\n    }, [\n        chatHistoryService,\n        onChatHistoryChange,\n        onClearAIResponse\n    ]);\n    // 处理文件关联变化\n    const handleFilesChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (newFiles)=>{\n        try {\n            console.log(\"\\uD83D\\uDD25 ChatInterface - 接收到文件关联变化:\", newFiles);\n            console.log(\"\\uD83D\\uDD25 ChatInterface - 当前associatedFiles:\", associatedFiles);\n            // 保存到ChatHistoryService\n            await chatHistoryService.saveCurrentFileAssociations(newFiles);\n            // 通知父组件\n            console.log(\"\\uD83D\\uDD25 ChatInterface - 调用父组件onFilesChange:\", newFiles);\n            onFilesChange(newFiles);\n            console.log(\"✅ 文件关联已更新:\", newFiles.length, \"个文件\");\n        } catch (error) {\n            console.error(\"❌ 保存文件关联失败:\", error);\n        }\n    }, [\n        chatHistoryService,\n        onFilesChange,\n        associatedFiles\n    ]);\n    // 处理单条消息插入到编辑器\n    const handleInsertMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageContent)=>{\n        console.log(\"\\uD83D\\uDD04 尝试插入消息内容到编辑器:\", messageContent.substring(0, 100) + \"...\");\n        if (onMessageContentInsert) {\n            console.log(\"✅ 使用消息内容插入弹窗\");\n            // 使用专门的消息内容插入接口，会显示插入弹窗\n            onMessageContentInsert(messageContent);\n        } else if (onContentInsert) {\n            console.log(\"✅ 直接使用onContentInsert接口插入内容\");\n            // 直接使用onContentInsert插入特定消息内容\n            onContentInsert(messageContent, {\n                position: \"cursor\",\n                addNewlines: true // 添加新行\n            });\n        } else if (onInsertToEditor) {\n            console.log(\"⚠️ 使用降级插入方法\");\n            // 创建一个临时的插入函数，直接操作编辑器\n            try {\n                // 尝试直接访问编辑器并插入内容\n                const activeEditor = document.querySelector('textarea, [contenteditable=\"true\"]');\n                if (activeEditor) {\n                    if (activeEditor instanceof HTMLTextAreaElement) {\n                        // 处理textarea\n                        const start = activeEditor.selectionStart || 0;\n                        const end = activeEditor.selectionEnd || 0;\n                        const currentValue = activeEditor.value;\n                        const newValue = currentValue.slice(0, start) + \"\\n\" + messageContent + \"\\n\" + currentValue.slice(end);\n                        activeEditor.value = newValue;\n                        activeEditor.selectionStart = activeEditor.selectionEnd = start + messageContent.length + 2;\n                        // 触发change事件\n                        const event = new Event(\"input\", {\n                            bubbles: true\n                        });\n                        activeEditor.dispatchEvent(event);\n                        console.log(\"✅ 直接插入到textarea成功\");\n                    } else if (activeEditor.contentEditable === \"true\") {\n                        // 处理contenteditable元素\n                        const selection = window.getSelection();\n                        if (selection && selection.rangeCount > 0) {\n                            const range = selection.getRangeAt(0);\n                            range.deleteContents();\n                            const textNode = document.createTextNode(\"\\n\" + messageContent + \"\\n\");\n                            range.insertNode(textNode);\n                            // 移动光标到插入内容后\n                            range.setStartAfter(textNode);\n                            range.setEndAfter(textNode);\n                            selection.removeAllRanges();\n                            selection.addRange(range);\n                            console.log(\"✅ 直接插入到contenteditable成功\");\n                        }\n                    }\n                } else {\n                    // 如果找不到编辑器，使用原有方法\n                    console.log(\"⚠️ 未找到编辑器元素，使用原有插入方法\");\n                    onInsertToEditor();\n                }\n            } catch (error) {\n                console.error(\"❌ 直接插入失败，使用原有方法:\", error);\n                onInsertToEditor();\n            }\n        } else {\n            console.warn(\"⚠️ 没有可用的插入接口\");\n        }\n    }, [\n        onMessageContentInsert,\n        onContentInsert,\n        onInsertToEditor\n    ]);\n    // 处理重新生成AI消息\n    const handleRegenerateMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageIndex)=>{\n        // 找到该AI消息前面的用户消息\n        const messages = chatHistory.slice(0, messageIndex);\n        const lastUserMessage = messages.reverse().find((msg)=>msg.type === \"user\");\n        if (lastUserMessage) {\n            console.log(\"\\uD83D\\uDD04 重新生成AI消息，基于用户消息:\", lastUserMessage.content.substring(0, 50));\n            console.log(\"\\uD83D\\uDCCA 历史消息限制：只包含索引 0-\".concat(messageIndex - 1, \" 的消息\"));\n            // 删除从当前AI消息开始的所有后续消息\n            const newHistory = chatHistory.slice(0, messageIndex);\n            setChatHistory(newHistory);\n            // 保存更新后的历史记录\n            chatHistoryService.saveCurrentHistory(newHistory);\n            // 重新发送用户消息，传递目标消息索引以限制历史消息范围\n            onSendMessage(lastUserMessage.content, messageIndex);\n            // 关闭工具面板\n            setActiveMessageTools(null);\n        }\n    }, [\n        chatHistory,\n        onSendMessage,\n        chatHistoryService\n    ]);\n    // 处理编辑用户消息\n    const handleEditMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageId, currentContent)=>{\n        setEditingMessageId(messageId);\n        setEditingContent(currentContent);\n        setActiveMessageTools(null);\n    }, []);\n    // 处理保存编辑的消息\n    const handleSaveEditedMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageId, newContent)=>{\n        const messageIndex = chatHistory.findIndex((msg)=>msg.id === messageId);\n        if (messageIndex === -1) return;\n        // 更新消息内容\n        const updatedMessage = {\n            ...chatHistory[messageIndex],\n            content: newContent,\n            updatedAt: Date.now()\n        };\n        // 删除该消息之后的所有消息\n        const newHistory = [\n            ...chatHistory.slice(0, messageIndex),\n            updatedMessage\n        ];\n        setChatHistory(newHistory);\n        // 保存更新后的历史记录\n        chatHistoryService.saveCurrentHistory(newHistory);\n        // 重新发送编辑后的消息\n        onSendMessage(newContent);\n        // 清除编辑状态\n        setEditingMessageId(null);\n        setEditingContent(\"\");\n        console.log(\"✅ 消息已编辑并重发:\", newContent.substring(0, 50));\n    }, [\n        chatHistory,\n        onSendMessage,\n        chatHistoryService\n    ]);\n    // 处理多项对比\n    const handleMultipleComparison = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageIndex)=>{\n        // 找到该AI消息前面的用户消息\n        const messages = chatHistory.slice(0, messageIndex);\n        const lastUserMessage = messages.reverse().find((msg)=>msg.type === \"user\");\n        if (lastUserMessage) {\n            console.log(\"\\uD83D\\uDD04 开启多项对比，基于用户消息:\", lastUserMessage.content.substring(0, 50));\n            console.log(\"\\uD83C\\uDFAF 记录触发消息索引:\", messageIndex);\n            // 设置对比内容并打开弹窗\n            setComparisonMessageContent(lastUserMessage.content);\n            setComparisonTriggerMessageIndex(messageIndex) // 记录触发的消息索引\n            ;\n            setShowMultipleComparison(true);\n            // 关闭工具面板\n            setActiveMessageTools(null);\n        }\n    }, [\n        chatHistory\n    ]);\n    // 处理多项对比结果选择\n    const handleComparisonResultSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((selectedResult)=>{\n        console.log(\"\\uD83C\\uDFAF 多项对比结果选择，替换最后一条AI消息:\", selectedResult.substring(0, 100) + \"...\");\n        // 替换最后一条AI消息的内容\n        setChatHistory((prev)=>{\n            const newHistory = [\n                ...prev\n            ];\n            // 从后往前找最后一条AI消息\n            for(let i = newHistory.length - 1; i >= 0; i--){\n                if (newHistory[i].type === \"assistant\") {\n                    // 替换这条AI消息的内容\n                    newHistory[i] = {\n                        ...newHistory[i],\n                        content: selectedResult,\n                        timestamp: Date.now() // 更新时间戳\n                    };\n                    console.log(\"✅ 已替换AI消息:\", newHistory[i].id);\n                    break;\n                }\n            }\n            // 保存更新后的历史记录\n            chatHistoryService.saveCurrentHistory(newHistory).catch((error)=>{\n                console.error(\"❌ 保存替换后的消息失败:\", error);\n            });\n            return newHistory;\n        });\n        // 关闭弹窗\n        setShowMultipleComparison(false);\n        setComparisonMessageContent(\"\");\n    }, [\n        chatHistoryService\n    ]);\n    // 处理取消编辑\n    const handleCancelEdit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setEditingMessageId(null);\n        setEditingContent(\"\");\n    }, []);\n    // 获取可用模型列表\n    const loadAvailableModels = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!(currentConfig === null || currentConfig === void 0 ? void 0 : currentConfig.apiKey)) {\n            console.warn(\"⚠️ 没有可用的API配置\");\n            return;\n        }\n        setIsLoadingModels(true);\n        try {\n            const aiService = _services_aiService__WEBPACK_IMPORTED_MODULE_5__.AIService.getInstance();\n            const result = await aiService.getAvailableModels(currentConfig);\n            if (result.success && result.data && result.data.length > 0) {\n                // 转换为模型对象格式\n                const models = result.data.map((modelId)=>({\n                        id: modelId,\n                        name: modelId.toUpperCase().replace(/-/g, \" \"),\n                        description: getModelDescription(modelId)\n                    }));\n                setAvailableModels(models);\n                console.log(\"✅ 成功获取模型列表:\", models.length, \"个模型\");\n            } else {\n                console.warn(\"⚠️ 获取模型列表失败\");\n                setAvailableModels([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取模型列表失败:\", error);\n            setAvailableModels([]);\n        } finally{\n            setIsLoadingModels(false);\n        }\n    }, [\n        currentConfig\n    ]);\n    // 获取模型描述\n    const getModelDescription = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const lowerModelId = modelId.toLowerCase();\n        // GPT系列模型\n        if (lowerModelId.includes(\"gpt-4\")) {\n            if (lowerModelId.includes(\"turbo\")) {\n                return \"更快的GPT-4，性价比更高\";\n            }\n            return \"最强大的模型，适合复杂任务\";\n        } else if (lowerModelId.includes(\"gpt-3.5\")) {\n            return \"快速且经济的选择\";\n        } else if (lowerModelId.includes(\"claude\")) {\n            return \"Anthropic Claude，强大的推理能力\";\n        } else if (lowerModelId.includes(\"gemini\")) {\n            return \"Google Gemini，多模态能力强\";\n        }\n        // 其他模型\n        return \"可用的AI模型: \".concat(modelId);\n    }, []);\n    // 点击外部关闭消息工具面板\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (activeMessageTools) {\n                // 检查点击是否在工具面板外部\n                const target = event.target;\n                if (!target.closest(\".message-tools-panel\") && !target.closest(\".message-tools-button\")) {\n                    setActiveMessageTools(null);\n                }\n            }\n            // 点击外部取消编辑状态（除非点击在编辑区域内）\n            if (editingMessageId) {\n                const target = event.target;\n                if (!target.closest(\"textarea\") && !target.closest(\"button\")) {\n                    handleCancelEdit();\n                }\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        activeMessageTools,\n        editingMessageId,\n        handleCancelEdit\n    ]);\n    // 暴露方法给父组件\n    react__WEBPACK_IMPORTED_MODULE_1___default().useImperativeHandle(ref, ()=>({\n            handleSessionSelect\n        }), [\n        handleSessionSelect\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            (associatedFiles.length > 0 || focusedFile) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 bg-gray-800/20 border-b border-amber-500/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2 cursor-pointer hover:bg-gray-700/20 rounded-md p-1 -m-1 transition-all duration-200\",\n                        onClick: ()=>setIsFileAssociationCollapsed(!isFileAssociationCollapsed),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                width: \"14\",\n                                height: \"14\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                className: \"text-amber-400 transition-transform duration-200 \".concat(isFileAssociationCollapsed ? \"rotate-0\" : \"rotate-90\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1741,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1734,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                width: \"14\",\n                                height: \"14\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                className: \"text-amber-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1744,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1743,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-amber-200 font-handwritten\",\n                                children: \"关联文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1746,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-400\",\n                                children: [\n                                    \"(\",\n                                    focusedFile ? associatedFiles.includes(focusedFile) ? associatedFiles.length : associatedFiles.length + 1 : associatedFiles.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1747,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500 ml-auto\",\n                                children: isFileAssociationCollapsed ? \"点击展开\" : \"点击折叠\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1750,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 1730,\n                        columnNumber: 11\n                    }, this),\n                    !isFileAssociationCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2 mt-2\",\n                        children: (()=>{\n                            // 创建文件显示列表，聚焦文件优先\n                            const allFiles = new Set([\n                                ...focusedFile ? [\n                                    focusedFile\n                                ] : [],\n                                ...associatedFiles\n                            ]);\n                            return Array.from(allFiles).map((fileId, index)=>{\n                                const filePath = filePaths.get(fileId) || \"加载中... (\".concat(fileId.substring(0, 8), \"...)\");\n                                const isFocused = focusedFile === fileId;\n                                const isManuallyAssociated = associatedFiles.includes(fileId);\n                                // 确定文件类型和样式\n                                const fileType = isFocused ? \"auto\" : \"manual\";\n                                const styleConfig = {\n                                    auto: {\n                                        className: \"bg-blue-500/20 text-blue-200 border-blue-500/50\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"12\",\n                                            height: \"12\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1777,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1776,\n                                            columnNumber: 25\n                                        }, this),\n                                        label: \"自动\",\n                                        title: \"当前编辑文件（自动关联）\"\n                                    },\n                                    manual: {\n                                        className: \"bg-green-500/20 text-green-200 border-green-500/50\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"12\",\n                                            height: \"12\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1787,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1786,\n                                            columnNumber: 25\n                                        }, this),\n                                        label: \"手动\",\n                                        title: \"手动关联文件\"\n                                    }\n                                };\n                                const config = styleConfig[fileType];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 px-2 py-1 \".concat(config.className, \" border rounded-md text-xs\"),\n                                    title: config.title,\n                                    children: [\n                                        config.icon,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs opacity-75 font-handwritten\",\n                                            children: config.label\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1804,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono\",\n                                            title: filePath,\n                                            children: filePath\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1805,\n                                            columnNumber: 23\n                                        }, this),\n                                        isManuallyAssociated && !isFocused && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                const newFiles = associatedFiles.filter((id)=>id !== fileId);\n                                                handleFilesChange(newFiles);\n                                            },\n                                            className: \"ml-1 p-0.5 text-green-300 hover:text-red-400 hover:bg-red-500/10 rounded transition-all duration-200\",\n                                            title: \"移除手动关联文件\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"10\",\n                                                height: \"10\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 1818,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1817,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1809,\n                                            columnNumber: 25\n                                        }, this),\n                                        isFocused && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-1 px-1 py-0.5 bg-blue-600/30 text-blue-100 rounded text-xs font-handwritten\",\n                                            children: \"当前\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1825,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, fileId, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1798,\n                                    columnNumber: 21\n                                }, this);\n                            });\n                        })()\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 1756,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 1729,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                className: \"flex-1 overflow-y-auto custom-scrollbar p-4 space-y-4\",\n                onScroll: handleUserScroll,\n                children: [\n                    chatHistory.length === 0 && !isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center h-full text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto mb-4 rounded-lg bg-amber-500/20 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"32\",\n                                    height: \"32\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    className: \"text-amber-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 1848,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1847,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1846,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-handwritten text-amber-200 mb-2\",\n                                children: \"开始对话\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1851,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm font-handwritten\",\n                                children: currentConfig ? \"输入消息开始与AI对话\" : \"请先在配置页面设置API Key\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1854,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 1845,\n                        columnNumber: 11\n                    }, this),\n                    chatHistory.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex \".concat(message.type === \"user\" ? \"justify-end\" : \"justify-start\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-[80%] p-3 rounded-lg \".concat(message.type === \"user\" ? \"bg-blue-500/20 text-blue-200 border border-blue-500/50\" : \"bg-green-500/20 text-green-200 border border-green-500/50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4\",\n                                                        children: message.type === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1877,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1876,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M17.5,12A1.5,1.5 0 0,1 16,10.5A1.5,1.5 0 0,1 17.5,9A1.5,1.5 0 0,1 19,10.5A1.5,1.5 0 0,1 17.5,12M10,10.5C10,11.3 9.3,12 8.5,12S7,11.3 7,10.5C7,9.7 7.7,9 8.5,9S10,9.7 10,10.5M12,14C9.8,14 8,15.8 8,18V20H16V18C16,15.8 14.2,14 12,14M12,4C14.2,4 16,5.8 16,8S14.2,12 12,12S8,10.2 8,8S9.8,4 12,4M12,2A6,6 0 0,0 6,8C6,11.3 8.7,14 12,14S18,11.3 18,8A6,6 0 0,0 12,2Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1881,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1880,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1874,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-handwritten\",\n                                                        children: message.type === \"user\" ? \"你\" : \"AI助手\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1885,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs opacity-60\",\n                                                        children: new Date(message.timestamp).toLocaleTimeString()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1888,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1873,\n                                                columnNumber: 17\n                                            }, this),\n                                            message.type === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveMessageTools(activeMessageTools === message.id ? null : message.id),\n                                                        className: \"message-tools-button p-1 rounded-md bg-blue-400/10 hover:bg-blue-400/20 border border-blue-400/20 hover:border-blue-400/40 transition-all duration-200 text-blue-300 hover:text-blue-200 opacity-90 hover:opacity-100\",\n                                                        title: \"消息工具\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"12\",\n                                                            height: \"12\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1902,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1901,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1896,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    activeMessageTools === message.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"message-tools-panel absolute right-0 top-full mt-1 bg-gray-800 border border-blue-500/30 rounded-lg shadow-lg z-50 min-w-[120px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                handleEditMessage(message.id, message.content);\n                                                            },\n                                                            className: \"w-full px-3 py-2 text-left text-sm text-blue-200 hover:bg-blue-500/20 transition-all duration-200 font-handwritten flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    width: \"14\",\n                                                                    height: \"14\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1916,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 1915,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"编辑重发\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1909,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1908,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1895,\n                                                columnNumber: 19\n                                            }, this),\n                                            message.type === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveMessageTools(activeMessageTools === message.id ? null : message.id),\n                                                        className: \"message-tools-button p-1 rounded-md bg-amber-400/10 hover:bg-amber-400/20 border border-amber-400/20 hover:border-amber-400/40 transition-all duration-200 text-amber-300 hover:text-amber-200 opacity-90 hover:opacity-100\",\n                                                        title: \"消息工具\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"12\",\n                                                            height: \"12\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1934,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1933,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1928,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    activeMessageTools === message.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"message-tools-panel absolute right-0 top-full mt-1 bg-gray-800 border border-green-500/30 rounded-lg shadow-lg z-50 min-w-[140px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    handleInsertMessage(message.content);\n                                                                    setActiveMessageTools(null);\n                                                                },\n                                                                className: \"w-full px-3 py-2 text-left text-sm text-green-200 hover:bg-green-500/20 transition-all duration-200 font-handwritten border-b border-green-500/20 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        width: \"14\",\n                                                                        height: \"14\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 1949,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1948,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"插入到编辑器\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1941,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    const messageIndex = chatHistory.findIndex((msg)=>msg.id === message.id);\n                                                                    if (messageIndex !== -1) {\n                                                                        handleRegenerateMessage(messageIndex);\n                                                                    }\n                                                                },\n                                                                className: \"w-full px-3 py-2 text-left text-sm text-amber-200 hover:bg-amber-500/20 transition-all duration-200 font-handwritten flex items-center gap-2 border-b border-amber-500/20\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        width: \"14\",\n                                                                        height: \"14\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 1963,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1962,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"重新生成\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1953,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    const messageIndex = chatHistory.findIndex((msg)=>msg.id === message.id);\n                                                                    if (messageIndex !== -1) {\n                                                                        handleMultipleComparison(messageIndex);\n                                                                    }\n                                                                },\n                                                                className: \"w-full px-3 py-2 text-left text-sm text-purple-200 hover:bg-purple-500/20 transition-all duration-200 font-handwritten flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        width: \"14\",\n                                                                        height: \"14\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V6.5H5V5H19M19,19H5V8.5H19V19Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 1977,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1976,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"多项对比\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1967,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1940,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1927,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 1872,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: editingMessageId === message.id ? // 编辑模式\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: editingContent,\n                                                    onChange: (e)=>setEditingContent(e.target.value),\n                                                    className: \"w-full p-2 bg-gray-700 border border-blue-500/30 rounded-md text-blue-200 font-handwritten resize-none focus:outline-none focus:border-blue-500/60\",\n                                                    rows: Math.max(2, editingContent.split(\"\\n\").length),\n                                                    placeholder: \"编辑消息内容...\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 1991,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleCancelEdit,\n                                                            className: \"px-3 py-1 text-xs bg-gray-600 hover:bg-gray-500 text-gray-200 rounded-md transition-all duration-200 font-handwritten\",\n                                                            children: \"取消\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2000,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSaveEditedMessage(message.id, editingContent),\n                                                            className: \"px-3 py-1 text-xs bg-blue-600 hover:bg-blue-500 text-white rounded-md transition-all duration-200 font-handwritten\",\n                                                            disabled: !editingContent.trim(),\n                                                            children: \"保存并重发\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2006,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 1999,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1990,\n                                            columnNumber: 19\n                                        }, this) : // 正常显示模式\n                                        renderMessageContentInternal(message.content)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 1987,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1866,\n                                columnNumber: 13\n                            }, this)\n                        }, message.id, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                            lineNumber: 1862,\n                            columnNumber: 11\n                        }, this)),\n                    messageLayers.map((layer)=>{\n                        var _layer_metadata;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full\",\n                            children: layer.type === \"helper-response\" && ((_layer_metadata = layer.metadata) === null || _layer_metadata === void 0 ? void 0 : _layer_metadata.humanBlock) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HelperResponseLayer__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                messageId: layer.id,\n                                humanBlockContent: layer.metadata.humanBlock,\n                                onIntegrationComplete: handleIntegrationComplete,\n                                onMainAIResponse: handleMainAIResponse,\n                                onError: handleHelperError,\n                                className: \"my-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2028,\n                                columnNumber: 15\n                            }, this)\n                        }, layer.id, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                            lineNumber: 2026,\n                            columnNumber: 11\n                        }, this);\n                    }),\n                    (isLoading || isStreaming && !isComplete) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-[80%] p-3 bg-green-500/20 text-green-200 border border-green-500/50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M17.5,12A1.5,1.5 0 0,1 16,10.5A1.5,1.5 0 0,1 17.5,9A1.5,1.5 0 0,1 19,10.5A1.5,1.5 0 0,1 17.5,12M10,10.5C10,11.3 9.3,12 8.5,12S7,11.3 7,10.5C7,9.7 7.7,9 8.5,9S10,9.7 10,10.5M12,14C9.8,14 8,15.8 8,18V20H16V18C16,15.8 14.2,14 12,14M12,4C14.2,4 16,5.8 16,8S14.2,12 12,12S8,10.2 8,8S9.8,4 12,4M12,2A6,6 0 0,0 6,8C6,11.3 8.7,14 12,14S18,11.3 18,8A6,6 0 0,0 12,2Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2047,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2046,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2045,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-handwritten\",\n                                            children: \"AI助手\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2050,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs opacity-60\",\n                                            children: isLoading && !responseContent ? \"正在思考...\" : isStreaming ? \"正在输入...\" : \"刚刚\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2051,\n                                            columnNumber: 17\n                                        }, this),\n                                        isStreaming && onStop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onStop,\n                                            className: \"ml-auto p-1 text-gray-400 hover:text-red-400 hover:bg-red-500/10 rounded transition-all duration-200\",\n                                            title: \"停止生成\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"12\",\n                                                height: \"12\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M6,6H18V18H6V6Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2062,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2061,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2056,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2044,\n                                    columnNumber: 15\n                                }, this),\n                                responseContent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: aiResponseRef,\n                                    className: \"text-sm\",\n                                    children: [\n                                        renderMessageContentInternal(responseContent),\n                                        isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse text-amber-400 ml-1\",\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2073,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2070,\n                                    columnNumber: 17\n                                }, this) : /* 等待动画 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-amber-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-amber-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"0ms\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2080,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-amber-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"150ms\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2081,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-amber-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"300ms\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2082,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2079,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2078,\n                                    columnNumber: 17\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-2 bg-red-500/20 border border-red-500/50 rounded text-red-200 text-xs\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2091,\n                                                columnNumber: 21\n                                            }, this),\n                                            onRetry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onRetry,\n                                                className: \"ml-2 px-2 py-1 bg-red-500/30 hover:bg-red-500/50 rounded text-xs transition-colors\",\n                                                children: \"重试\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2093,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2090,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2089,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                            lineNumber: 2043,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 2042,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 1839,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-800/30 border-t border-amber-500/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: textareaRef,\n                                        value: inputMessage,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyDown,\n                                        placeholder: currentConfig ? \"输入消息...\" : \"请先配置API Key\",\n                                        disabled: !currentConfig || isLoading,\n                                        className: \"w-full min-h-[40px] max-h-[120px] p-3 pr-12 bg-gray-800 border border-gray-600 rounded-lg text-amber-200 placeholder-gray-500 resize-none focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 disabled:opacity-50 transition-all duration-200\",\n                                        rows: 1\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2112,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSend,\n                                        disabled: !inputMessage.trim() || !currentConfig || isLoading,\n                                        className: \"absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-md transition-all duration-200 \".concat(inputMessage.trim() && currentConfig && !isLoading ? \"text-amber-400 hover:text-amber-300 hover:bg-amber-500/20\" : \"text-gray-500 cursor-not-allowed\"),\n                                        title: isLoading ? \"发送中...\" : \"Enter 发送\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"16\",\n                                            height: \"16\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            className: \"animate-spin\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2135,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2134,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"16\",\n                                            height: \"16\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M2,21L23,12L2,3V10L17,12L2,14V21Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2139,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2138,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2124,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mt-3 px-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4 text-xs text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"12\",\n                                                        height: \"12\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"currentColor\",\n                                                        className: \"opacity-60\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2150,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M6.25,7.72H10V10.5H14V13.5H10V16.25H6.25V13.5H3.5V10.5H6.25V7.72Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2151,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2149,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Shift+Enter 换行\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2148,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"12\",\n                                                        height: \"12\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"currentColor\",\n                                                        className: \"opacity-60\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2157,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M6.25,7.72H17.75V10.5H6.25V7.72Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2158,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2156,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Enter 发送\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2155,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2147,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: [\n                                                    inputMessage.length,\n                                                    \" 字符\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2165,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative tools-panel-container\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowToolsPanel(!showToolsPanel),\n                                                        className: \"group flex items-center gap-1 px-2 py-1 rounded-md text-xs transition-all duration-200 \".concat(showToolsPanel ? \"text-amber-300 bg-amber-500/20 border border-amber-500/40\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 border border-transparent\"),\n                                                        title: \"工具\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                width: \"12\",\n                                                                height: \"12\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: \"currentColor\",\n                                                                className: \"transition-transform duration-200 group-hover:rotate-12\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 2178,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2177,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"工具\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2180,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2169,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    showToolsPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute bottom-full right-0 mb-3 bg-gray-800/95 backdrop-blur-sm border border-amber-500/30 rounded-xl shadow-2xl p-2 z-50 animate-in fade-in-0 zoom-in-95 duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-amber-200 font-medium mb-2 px-2 py-1\",\n                                                                children: \"快捷工具\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2187,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setShowFileAssociation(true);\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        className: \"p-2 bg-blue-500/20 text-blue-200 border border-blue-500/50 rounded-lg hover:bg-blue-500/30 transition-all duration-200\",\n                                                                        title: \"关联文件\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                lineNumber: 2200,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2199,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2191,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setShowMediaUploader(!showMediaUploader);\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        className: \"p-2 bg-purple-500/20 text-purple-200 border border-purple-500/50 rounded-lg hover:bg-purple-500/30 transition-all duration-200\",\n                                                                        title: \"上传媒体文件\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M9,16V10H5L12,3L19,10H15V16H9M5,20V18H19V20H5Z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                lineNumber: 2214,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2213,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2205,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setShowPromptManager(true);\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        disabled: isLoading,\n                                                                        className: \"p-2 rounded-lg transition-all duration-200 \".concat(activePromptConfig ? \"bg-amber-500/30 text-amber-200 border border-amber-500/70\" : \"bg-amber-500/20 text-amber-200 border border-amber-500/50 hover:bg-amber-500/30\"),\n                                                                        title: activePromptConfig ? \"当前配置: \".concat(activePromptConfig.name) : \"管理提示词模板\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                    lineNumber: 2232,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M12,15.39L8.24,17.66L9.23,13.38L5.91,10.5L10.29,10.13L12,6.09L13.71,10.13L18.09,10.5L14.77,13.38L15.76,17.66M22,9.24L14.81,8.63L12,2L9.19,8.63L2,9.24L7.45,13.97L5.82,21L12,17.27L18.18,21L16.54,13.97L22,9.24Z\",\n                                                                                    opacity: \"0.6\",\n                                                                                    transform: \"scale(0.4) translate(24, 24)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                    lineNumber: 2233,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2231,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2219,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            if (onShowAudienceSettings) {\n                                                                                onShowAudienceSettings();\n                                                                            }\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        className: \"p-2 bg-green-500/20 text-green-200 border border-green-500/50 rounded-lg hover:bg-green-500/30 transition-all duration-200\",\n                                                                        title: \"设置受众\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M16,4C18.11,4 19.81,5.69 19.81,7.8C19.81,9.91 18.11,11.6 16,11.6C13.89,11.6 12.2,9.91 12.2,7.8C12.2,5.69 13.89,4 16,4M16,13.4C18.67,13.4 24,14.73 24,17.4V20H8V17.4C8,14.73 13.33,13.4 16,13.4M8.8,11.6C6.69,11.6 5,9.91 5,7.8C5,5.69 6.69,4 8.8,4C9.13,4 9.45,4.05 9.75,4.14C9.28,5.16 9,6.3 9,7.5C9,8.7 9.28,9.84 9.75,10.86C9.45,10.95 9.13,11 8.8,11.6M8.8,13.4C7.12,13.4 3.5,14.26 3.5,17.4V20H6.5V17.4C6.5,16.55 7.45,15.1 8.8,13.4Z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                lineNumber: 2252,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2251,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2241,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2189,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-full right-4 -mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-gray-800 border-r border-b border-amber-500/30 transform rotate-45\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 2259,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2258,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2185,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2168,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2164,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 2110,\n                        columnNumber: 9\n                    }, this),\n                    showMediaUploader && artworkId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-4 bg-gray-800/50 border border-purple-500/30 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-purple-200\",\n                                        children: \"媒体文件上传\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2272,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowMediaUploader(false),\n                                        className: \"text-gray-400 hover:text-gray-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"16\",\n                                            height: \"16\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2278,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2277,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2273,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2271,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MediaUploader__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                onUploadSuccess: handleMediaUploadSuccess,\n                                onError: handleMediaUploadError,\n                                artworkId: artworkId,\n                                className: \"mb-4\",\n                                maxFiles: 5\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2282,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 2270,\n                        columnNumber: 11\n                    }, this),\n                    uploadedMedia.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowMediaList(!showMediaList),\n                                className: \"flex items-center gap-2 px-3 py-2 bg-green-500/20 text-green-200 border border-green-500/50 rounded-md hover:bg-green-500/30 transition-all duration-200 font-handwritten text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"16\",\n                                        height: \"16\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2300,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"已上传 \",\n                                            uploadedMedia.length,\n                                            \" 个媒体文件\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2302,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"14\",\n                                        height: \"14\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"currentColor\",\n                                        className: \"transition-transform duration-200 \".concat(showMediaList ? \"rotate-180\" : \"\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2310,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2303,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2295,\n                                columnNumber: 13\n                            }, this),\n                            showMediaList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 p-4 bg-gray-800/50 border border-green-500/30 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-3\",\n                                    children: uploadedMedia.map((mediaFile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 p-3 bg-gray-700/50 rounded-lg border border-gray-600/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: mediaFile.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"20\",\n                                                            height: \"20\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            className: \"text-blue-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2328,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2327,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2326,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"20\",\n                                                            height: \"20\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            className: \"text-purple-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2334,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2333,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2332,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2324,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-200 truncate\",\n                                                            children: mediaFile.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2342,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: [\n                                                                mediaFile.mimeType,\n                                                                \" • \",\n                                                                (mediaFile.size / 1024 / 1024).toFixed(2),\n                                                                \" MB\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2345,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: new Date(mediaFile.uploadedAt).toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2348,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2341,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>removeMediaFile(mediaFile.id),\n                                                        className: \"w-8 h-8 bg-red-500/20 text-red-400 rounded-lg hover:bg-red-500/30 transition-colors flex items-center justify-center\",\n                                                        title: \"删除文件\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"14\",\n                                                            height: \"14\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2361,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2360,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2355,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2354,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, mediaFile.id, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2319,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2317,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2316,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 2294,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SessionManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: showSessionManager,\n                onClose: ()=>setShowSessionManager(false),\n                onSessionSelect: handleSessionSelect,\n                currentSessionId: currentSessionId\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2377,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FileAssociationTreeDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showFileAssociation,\n                onClose: ()=>setShowFileAssociation(false),\n                artworkId: artworkId,\n                onFilesChange: handleFilesChange\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2385,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PromptManagerModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isOpen: showPromptManager,\n                onClose: ()=>setShowPromptManager(false),\n                onConfigSelect: handlePromptConfigSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2393,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultipleComparisonModal__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                isOpen: showMultipleComparison,\n                onClose: ()=>setShowMultipleComparison(false),\n                messageContent: comparisonMessageContent,\n                onResultSelect: handleComparisonResultSelect,\n                onSendMessage: onMultipleComparisonSend || onSendMessage,\n                targetMessageIndex: comparisonTriggerMessageIndex\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2400,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n        lineNumber: 1724,\n        columnNumber: 5\n    }, this);\n}, \"RfxeR6O005vLzWT/JUmeZeKO01c=\")), \"RfxeR6O005vLzWT/JUmeZeKO01c=\");\n_c1 = ChatInterface;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ChatInterface);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatInterface$React.forwardRef\");\n$RefreshReg$(_c1, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AIAssistant/ChatInterface.tsx\n"));

/***/ })

});