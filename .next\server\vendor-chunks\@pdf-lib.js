"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@pdf-lib";
exports.ids = ["vendor-chunks/@pdf-lib"];
exports.modules = {

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Encoding.js":
/*!*************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/Encoding.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Encodings: () => (/* binding */ Encodings)\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/utils.js\");\n/* harmony import */ var _all_encodings_compressed_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./all-encodings.compressed.json */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/all-encodings.compressed.json\");\n/* tslint:disable max-classes-per-file */\n\n\nvar decompressedEncodings = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.decompressJson)(_all_encodings_compressed_json__WEBPACK_IMPORTED_MODULE_1__);\nvar allUnicodeMappings = JSON.parse(decompressedEncodings);\nvar Encoding = /** @class */ (function () {\n    function Encoding(name, unicodeMappings) {\n        var _this = this;\n        this.canEncodeUnicodeCodePoint = function (codePoint) {\n            return codePoint in _this.unicodeMappings;\n        };\n        this.encodeUnicodeCodePoint = function (codePoint) {\n            var mapped = _this.unicodeMappings[codePoint];\n            if (!mapped) {\n                var str = String.fromCharCode(codePoint);\n                var hexCode = \"0x\" + (0,_utils__WEBPACK_IMPORTED_MODULE_0__.padStart)(codePoint.toString(16), 4, '0');\n                var msg = _this.name + \" cannot encode \\\"\" + str + \"\\\" (\" + hexCode + \")\";\n                throw new Error(msg);\n            }\n            return { code: mapped[0], name: mapped[1] };\n        };\n        this.name = name;\n        this.supportedCodePoints = Object.keys(unicodeMappings)\n            .map(Number)\n            .sort(function (a, b) { return a - b; });\n        this.unicodeMappings = unicodeMappings;\n    }\n    return Encoding;\n}());\nvar Encodings = {\n    Symbol: new Encoding('Symbol', allUnicodeMappings.symbol),\n    ZapfDingbats: new Encoding('ZapfDingbats', allUnicodeMappings.zapfdingbats),\n    WinAnsi: new Encoding('WinAnsi', allUnicodeMappings.win1252),\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Encoding.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Font.js":
/*!*********************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/Font.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Font: () => (/* binding */ Font),\n/* harmony export */   FontNames: () => (/* binding */ FontNames)\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/utils.js\");\n/* harmony import */ var _Courier_Bold_compressed_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Courier-Bold.compressed.json */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Courier-Bold.compressed.json\");\n/* harmony import */ var _Courier_BoldOblique_compressed_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Courier-BoldOblique.compressed.json */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Courier-BoldOblique.compressed.json\");\n/* harmony import */ var _Courier_Oblique_compressed_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Courier-Oblique.compressed.json */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Courier-Oblique.compressed.json\");\n/* harmony import */ var _Courier_compressed_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Courier.compressed.json */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Courier.compressed.json\");\n/* harmony import */ var _Helvetica_Bold_compressed_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Helvetica-Bold.compressed.json */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Helvetica-Bold.compressed.json\");\n/* harmony import */ var _Helvetica_BoldOblique_compressed_json__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Helvetica-BoldOblique.compressed.json */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Helvetica-BoldOblique.compressed.json\");\n/* harmony import */ var _Helvetica_Oblique_compressed_json__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Helvetica-Oblique.compressed.json */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Helvetica-Oblique.compressed.json\");\n/* harmony import */ var _Helvetica_compressed_json__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Helvetica.compressed.json */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Helvetica.compressed.json\");\n/* harmony import */ var _Times_Bold_compressed_json__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Times-Bold.compressed.json */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Times-Bold.compressed.json\");\n/* harmony import */ var _Times_BoldItalic_compressed_json__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Times-BoldItalic.compressed.json */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Times-BoldItalic.compressed.json\");\n/* harmony import */ var _Times_Italic_compressed_json__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./Times-Italic.compressed.json */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Times-Italic.compressed.json\");\n/* harmony import */ var _Times_Roman_compressed_json__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Times-Roman.compressed.json */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Times-Roman.compressed.json\");\n/* harmony import */ var _Symbol_compressed_json__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./Symbol.compressed.json */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Symbol.compressed.json\");\n/* harmony import */ var _ZapfDingbats_compressed_json__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./ZapfDingbats.compressed.json */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/ZapfDingbats.compressed.json\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// prettier-ignore\nvar compressedJsonForFontName = {\n    'Courier': _Courier_compressed_json__WEBPACK_IMPORTED_MODULE_4__,\n    'Courier-Bold': _Courier_Bold_compressed_json__WEBPACK_IMPORTED_MODULE_1__,\n    'Courier-Oblique': _Courier_Oblique_compressed_json__WEBPACK_IMPORTED_MODULE_3__,\n    'Courier-BoldOblique': _Courier_BoldOblique_compressed_json__WEBPACK_IMPORTED_MODULE_2__,\n    'Helvetica': _Helvetica_compressed_json__WEBPACK_IMPORTED_MODULE_8__,\n    'Helvetica-Bold': _Helvetica_Bold_compressed_json__WEBPACK_IMPORTED_MODULE_5__,\n    'Helvetica-Oblique': _Helvetica_Oblique_compressed_json__WEBPACK_IMPORTED_MODULE_7__,\n    'Helvetica-BoldOblique': _Helvetica_BoldOblique_compressed_json__WEBPACK_IMPORTED_MODULE_6__,\n    'Times-Roman': _Times_Roman_compressed_json__WEBPACK_IMPORTED_MODULE_12__,\n    'Times-Bold': _Times_Bold_compressed_json__WEBPACK_IMPORTED_MODULE_9__,\n    'Times-Italic': _Times_Italic_compressed_json__WEBPACK_IMPORTED_MODULE_11__,\n    'Times-BoldItalic': _Times_BoldItalic_compressed_json__WEBPACK_IMPORTED_MODULE_10__,\n    'Symbol': _Symbol_compressed_json__WEBPACK_IMPORTED_MODULE_13__,\n    'ZapfDingbats': _ZapfDingbats_compressed_json__WEBPACK_IMPORTED_MODULE_14__,\n};\nvar FontNames;\n(function (FontNames) {\n    FontNames[\"Courier\"] = \"Courier\";\n    FontNames[\"CourierBold\"] = \"Courier-Bold\";\n    FontNames[\"CourierOblique\"] = \"Courier-Oblique\";\n    FontNames[\"CourierBoldOblique\"] = \"Courier-BoldOblique\";\n    FontNames[\"Helvetica\"] = \"Helvetica\";\n    FontNames[\"HelveticaBold\"] = \"Helvetica-Bold\";\n    FontNames[\"HelveticaOblique\"] = \"Helvetica-Oblique\";\n    FontNames[\"HelveticaBoldOblique\"] = \"Helvetica-BoldOblique\";\n    FontNames[\"TimesRoman\"] = \"Times-Roman\";\n    FontNames[\"TimesRomanBold\"] = \"Times-Bold\";\n    FontNames[\"TimesRomanItalic\"] = \"Times-Italic\";\n    FontNames[\"TimesRomanBoldItalic\"] = \"Times-BoldItalic\";\n    FontNames[\"Symbol\"] = \"Symbol\";\n    FontNames[\"ZapfDingbats\"] = \"ZapfDingbats\";\n})(FontNames || (FontNames = {}));\nvar fontCache = {};\nvar Font = /** @class */ (function () {\n    function Font() {\n        var _this = this;\n        this.getWidthOfGlyph = function (glyphName) {\n            return _this.CharWidths[glyphName];\n        };\n        this.getXAxisKerningForPair = function (leftGlyphName, rightGlyphName) {\n            return (_this.KernPairXAmounts[leftGlyphName] || {})[rightGlyphName];\n        };\n    }\n    Font.load = function (fontName) {\n        var cachedFont = fontCache[fontName];\n        if (cachedFont)\n            return cachedFont;\n        var json = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.decompressJson)(compressedJsonForFontName[fontName]);\n        var font = Object.assign(new Font(), JSON.parse(json));\n        font.CharWidths = font.CharMetrics.reduce(function (acc, metric) {\n            acc[metric.N] = metric.WX;\n            return acc;\n        }, {});\n        font.KernPairXAmounts = font.KernPairs.reduce(function (acc, _a) {\n            var name1 = _a[0], name2 = _a[1], width = _a[2];\n            if (!acc[name1])\n                acc[name1] = {};\n            acc[name1][name2] = width;\n            return acc;\n        }, {});\n        fontCache[fontName] = font;\n        return font;\n    };\n    return Font;\n}());\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Font.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Encodings: () => (/* reexport safe */ _Encoding__WEBPACK_IMPORTED_MODULE_1__.Encodings),\n/* harmony export */   Font: () => (/* reexport safe */ _Font__WEBPACK_IMPORTED_MODULE_0__.Font),\n/* harmony export */   FontNames: () => (/* reexport safe */ _Font__WEBPACK_IMPORTED_MODULE_0__.FontNames)\n/* harmony export */ });\n/* harmony import */ var _Font__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Font */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Font.js\");\n/* harmony import */ var _Encoding__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Encoding */ \"(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Encoding.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBkZi1saWIvc3RhbmRhcmQtZm9udHMvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBdUI7QUFDSSIsInNvdXJjZXMiOlsid2VicGFjazovL2FydHdvcmstcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvQHBkZi1saWIvc3RhbmRhcmQtZm9udHMvZXMvaW5kZXguanM/YmRmOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL0ZvbnQnO1xuZXhwb3J0ICogZnJvbSAnLi9FbmNvZGluZyc7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@pdf-lib/standard-fonts/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/utils.js":
/*!**********************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/utils.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeFromBase64: () => (/* binding */ decodeFromBase64),\n/* harmony export */   decompressJson: () => (/* binding */ decompressJson),\n/* harmony export */   padStart: () => (/* binding */ padStart)\n/* harmony export */ });\n/* harmony import */ var pako__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pako */ \"(ssr)/./node_modules/pako/index.js\");\n/* harmony import */ var pako__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pako__WEBPACK_IMPORTED_MODULE_0__);\n/*\n * The `chars`, `lookup`, and `decodeFromBase64` members of this file are\n * licensed under the following:\n *\n *     base64-arraybuffer\n *     https://github.com/niklasvh/base64-arraybuffer\n *\n *     Copyright (c) 2012 Niklas von Hertzen\n *     Licensed under the MIT license.\n *\n */\n\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nvar lookup = new Uint8Array(256);\nfor (var i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nvar decodeFromBase64 = function (base64) {\n    var bufferLength = base64.length * 0.75;\n    var len = base64.length;\n    var i;\n    var p = 0;\n    var encoded1;\n    var encoded2;\n    var encoded3;\n    var encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    var bytes = new Uint8Array(bufferLength);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return bytes;\n};\nvar arrayToString = function (array) {\n    var str = '';\n    for (var i = 0; i < array.length; i++) {\n        str += String.fromCharCode(array[i]);\n    }\n    return str;\n};\nvar decompressJson = function (compressedJson) {\n    return arrayToString(pako__WEBPACK_IMPORTED_MODULE_0___default().inflate(decodeFromBase64(compressedJson)));\n};\nvar padStart = function (value, length, padChar) {\n    var padding = '';\n    for (var idx = 0, len = length - value.length; idx < len; idx++) {\n        padding += padChar;\n    }\n    return padding + value;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@pdf-lib/standard-fonts/es/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/upng/UPNG.js":
/*!********************************************!*\
  !*** ./node_modules/@pdf-lib/upng/UPNG.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var pako__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pako */ \"(ssr)/./node_modules/pako/index.js\");\n/* harmony import */ var pako__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pako__WEBPACK_IMPORTED_MODULE_0__);\n\n\nvar UPNG = {};\n\n\t\n\nUPNG.toRGBA8 = function(out)\n{\n\tvar w = out.width, h = out.height;\n\tif(out.tabs.acTL==null) return [UPNG.toRGBA8.decodeImage(out.data, w, h, out).buffer];\n\t\n\tvar frms = [];\n\tif(out.frames[0].data==null) out.frames[0].data = out.data;\n\t\n\tvar len = w*h*4, img = new Uint8Array(len), empty = new Uint8Array(len), prev=new Uint8Array(len);\n\tfor(var i=0; i<out.frames.length; i++)\n\t{\n\t\tvar frm = out.frames[i];\n\t\tvar fx=frm.rect.x, fy=frm.rect.y, fw = frm.rect.width, fh = frm.rect.height;\n\t\tvar fdata = UPNG.toRGBA8.decodeImage(frm.data, fw,fh, out);\n\t\t\n\t\tif(i!=0) for(var j=0; j<len; j++) prev[j]=img[j];\n\t\t\n\t\tif     (frm.blend==0) UPNG._copyTile(fdata, fw, fh, img, w, h, fx, fy, 0);\n\t\telse if(frm.blend==1) UPNG._copyTile(fdata, fw, fh, img, w, h, fx, fy, 1);\n\t\t\n\t\tfrms.push(img.buffer.slice(0));\n\t\t\n\t\tif     (frm.dispose==0) {}\n\t\telse if(frm.dispose==1) UPNG._copyTile(empty, fw, fh, img, w, h, fx, fy, 0);\n\t\telse if(frm.dispose==2) for(var j=0; j<len; j++) img[j]=prev[j];\n\t}\n\treturn frms;\n}\nUPNG.toRGBA8.decodeImage = function(data, w, h, out)\n{\n\tvar area = w*h, bpp = UPNG.decode._getBPP(out);\n\tvar bpl = Math.ceil(w*bpp/8);\t// bytes per line\n\n\tvar bf = new Uint8Array(area*4), bf32 = new Uint32Array(bf.buffer);\n\tvar ctype = out.ctype, depth = out.depth;\n\tvar rs = UPNG._bin.readUshort;\n\t\n\t//console.log(ctype, depth);\n\tvar time = Date.now();\n\n\tif     (ctype==6) { // RGB + alpha\n\t\tvar qarea = area<<2;\n\t\tif(depth== 8) for(var i=0; i<qarea;i+=4) {  bf[i] = data[i];  bf[i+1] = data[i+1];  bf[i+2] = data[i+2];  bf[i+3] = data[i+3]; }\n\t\tif(depth==16) for(var i=0; i<qarea;i++ ) {  bf[i] = data[i<<1];  }\n\t}\n\telse if(ctype==2) {\t// RGB\n\t\tvar ts=out.tabs[\"tRNS\"];\n\t\tif(ts==null) {\n\t\t\tif(depth== 8) for(var i=0; i<area; i++) {  var ti=i*3;  bf32[i] = (255<<24)|(data[ti+2]<<16)|(data[ti+1]<<8)|data[ti];  }\n\t\t\tif(depth==16) for(var i=0; i<area; i++) {  var ti=i*6;  bf32[i] = (255<<24)|(data[ti+4]<<16)|(data[ti+2]<<8)|data[ti];  }\n\t\t}\n\t\telse {  var tr=ts[0], tg=ts[1], tb=ts[2];\n\t\t\tif(depth== 8) for(var i=0; i<area; i++) {  var qi=i<<2, ti=i*3;  bf32[i] = (255<<24)|(data[ti+2]<<16)|(data[ti+1]<<8)|data[ti];\n\t\t\t\tif(data[ti]   ==tr && data[ti+1]   ==tg && data[ti+2]   ==tb) bf[qi+3] = 0;  }\n\t\t\tif(depth==16) for(var i=0; i<area; i++) {  var qi=i<<2, ti=i*6;  bf32[i] = (255<<24)|(data[ti+4]<<16)|(data[ti+2]<<8)|data[ti];\n\t\t\t\tif(rs(data,ti)==tr && rs(data,ti+2)==tg && rs(data,ti+4)==tb) bf[qi+3] = 0;  }\n\t\t}\n\t}\n\telse if(ctype==3) {\t// palette\n\t\tvar p=out.tabs[\"PLTE\"], ap=out.tabs[\"tRNS\"], tl=ap?ap.length:0;\n\t\t//console.log(p, ap);\n\t\tif(depth==1) for(var y=0; y<h; y++) {  var s0 = y*bpl, t0 = y*w;\n\t\t\tfor(var i=0; i<w; i++) { var qi=(t0+i)<<2, j=((data[s0+(i>>3)]>>(7-((i&7)<<0)))& 1), cj=3*j;  bf[qi]=p[cj];  bf[qi+1]=p[cj+1];  bf[qi+2]=p[cj+2];  bf[qi+3]=(j<tl)?ap[j]:255;  }\n\t\t}\n\t\tif(depth==2) for(var y=0; y<h; y++) {  var s0 = y*bpl, t0 = y*w;\n\t\t\tfor(var i=0; i<w; i++) { var qi=(t0+i)<<2, j=((data[s0+(i>>2)]>>(6-((i&3)<<1)))& 3), cj=3*j;  bf[qi]=p[cj];  bf[qi+1]=p[cj+1];  bf[qi+2]=p[cj+2];  bf[qi+3]=(j<tl)?ap[j]:255;  }\n\t\t}\n\t\tif(depth==4) for(var y=0; y<h; y++) {  var s0 = y*bpl, t0 = y*w;\n\t\t\tfor(var i=0; i<w; i++) { var qi=(t0+i)<<2, j=((data[s0+(i>>1)]>>(4-((i&1)<<2)))&15), cj=3*j;  bf[qi]=p[cj];  bf[qi+1]=p[cj+1];  bf[qi+2]=p[cj+2];  bf[qi+3]=(j<tl)?ap[j]:255;  }\n\t\t}\n\t\tif(depth==8) for(var i=0; i<area; i++ ) {  var qi=i<<2, j=data[i]                      , cj=3*j;  bf[qi]=p[cj];  bf[qi+1]=p[cj+1];  bf[qi+2]=p[cj+2];  bf[qi+3]=(j<tl)?ap[j]:255;  }\n\t}\n\telse if(ctype==4) {\t// gray + alpha\n\t\tif(depth== 8)  for(var i=0; i<area; i++) {  var qi=i<<2, di=i<<1, gr=data[di];  bf[qi]=gr;  bf[qi+1]=gr;  bf[qi+2]=gr;  bf[qi+3]=data[di+1];  }\n\t\tif(depth==16)  for(var i=0; i<area; i++) {  var qi=i<<2, di=i<<2, gr=data[di];  bf[qi]=gr;  bf[qi+1]=gr;  bf[qi+2]=gr;  bf[qi+3]=data[di+2];  }\n\t}\n\telse if(ctype==0) {\t// gray\n\t\tvar tr = out.tabs[\"tRNS\"] ? out.tabs[\"tRNS\"] : -1;\n\t\tfor(var y=0; y<h; y++) {\n\t\t\tvar off = y*bpl, to = y*w;\n\t\t\tif     (depth== 1) for(var x=0; x<w; x++) {  var gr=255*((data[off+(x>>>3)]>>>(7 -((x&7)   )))& 1), al=(gr==tr*255)?0:255;  bf32[to+x]=(al<<24)|(gr<<16)|(gr<<8)|gr;  }\n\t\t\telse if(depth== 2) for(var x=0; x<w; x++) {  var gr= 85*((data[off+(x>>>2)]>>>(6 -((x&3)<<1)))& 3), al=(gr==tr* 85)?0:255;  bf32[to+x]=(al<<24)|(gr<<16)|(gr<<8)|gr;  }\n\t\t\telse if(depth== 4) for(var x=0; x<w; x++) {  var gr= 17*((data[off+(x>>>1)]>>>(4 -((x&1)<<2)))&15), al=(gr==tr* 17)?0:255;  bf32[to+x]=(al<<24)|(gr<<16)|(gr<<8)|gr;  }\n\t\t\telse if(depth== 8) for(var x=0; x<w; x++) {  var gr=data[off+     x], al=(gr                 ==tr)?0:255;  bf32[to+x]=(al<<24)|(gr<<16)|(gr<<8)|gr;  }\n\t\t\telse if(depth==16) for(var x=0; x<w; x++) {  var gr=data[off+(x<<1)], al=(rs(data,off+(x<<i))==tr)?0:255;  bf32[to+x]=(al<<24)|(gr<<16)|(gr<<8)|gr;  }\n\t\t}\n\t}\n\t//console.log(Date.now()-time);\n\treturn bf;\n}\n\n\n\nUPNG.decode = function(buff)\n{\n\tvar data = new Uint8Array(buff), offset = 8, bin = UPNG._bin, rUs = bin.readUshort, rUi = bin.readUint;\n\tvar out = {tabs:{}, frames:[]};\n\tvar dd = new Uint8Array(data.length), doff = 0;\t // put all IDAT data into it\n\tvar fd, foff = 0;\t// frames\n\t\n\tvar mgck = [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a];\n\tfor(var i=0; i<8; i++) if(data[i]!=mgck[i]) throw \"The input is not a PNG file!\";\n\n\twhile(offset<data.length)\n\t{\n\t\tvar len  = bin.readUint(data, offset);  offset += 4;\n\t\tvar type = bin.readASCII(data, offset, 4);  offset += 4;\n\t\t//console.log(type,len);\n\t\t\n\t\tif     (type==\"IHDR\")  {  UPNG.decode._IHDR(data, offset, out);  }\n\t\telse if(type==\"IDAT\") {\n\t\t\tfor(var i=0; i<len; i++) dd[doff+i] = data[offset+i];\n\t\t\tdoff += len;\n\t\t}\n\t\telse if(type==\"acTL\")  {\n\t\t\tout.tabs[type] = {  num_frames:rUi(data, offset), num_plays:rUi(data, offset+4)  };\n\t\t\tfd = new Uint8Array(data.length);\n\t\t}\n\t\telse if(type==\"fcTL\")  {\n\t\t\tif(foff!=0) {  var fr = out.frames[out.frames.length-1];\n\t\t\t\tfr.data = UPNG.decode._decompress(out, fd.slice(0,foff), fr.rect.width, fr.rect.height);  foff=0;\n\t\t\t}\n\t\t\tvar rct = {x:rUi(data, offset+12),y:rUi(data, offset+16),width:rUi(data, offset+4),height:rUi(data, offset+8)};\n\t\t\tvar del = rUs(data, offset+22);  del = rUs(data, offset+20) / (del==0?100:del);\n\t\t\tvar frm = {rect:rct, delay:Math.round(del*1000), dispose:data[offset+24], blend:data[offset+25]};\n\t\t\t//console.log(frm);\n\t\t\tout.frames.push(frm);\n\t\t}\n\t\telse if(type==\"fdAT\") {\n\t\t\tfor(var i=0; i<len-4; i++) fd[foff+i] = data[offset+i+4];\n\t\t\tfoff += len-4;\n\t\t}\n\t\telse if(type==\"pHYs\") {\n\t\t\tout.tabs[type] = [bin.readUint(data, offset), bin.readUint(data, offset+4), data[offset+8]];\n\t\t}\n\t\telse if(type==\"cHRM\") {\n\t\t\tout.tabs[type] = [];\n\t\t\tfor(var i=0; i<8; i++) out.tabs[type].push(bin.readUint(data, offset+i*4));\n\t\t}\n\t\telse if(type==\"tEXt\") {\n\t\t\tif(out.tabs[type]==null) out.tabs[type] = {};\n\t\t\tvar nz = bin.nextZero(data, offset);\n\t\t\tvar keyw = bin.readASCII(data, offset, nz-offset);\n\t\t\tvar text = bin.readASCII(data, nz+1, offset+len-nz-1);\n\t\t\tout.tabs[type][keyw] = text;\n\t\t}\n\t\telse if(type==\"iTXt\") {\n\t\t\tif(out.tabs[type]==null) out.tabs[type] = {};\n\t\t\tvar nz = 0, off = offset;\n\t\t\tnz = bin.nextZero(data, off);\n\t\t\tvar keyw = bin.readASCII(data, off, nz-off);  off = nz + 1;\n\t\t\tvar cflag = data[off], cmeth = data[off+1];  off+=2;\n\t\t\tnz = bin.nextZero(data, off);\n\t\t\tvar ltag = bin.readASCII(data, off, nz-off);  off = nz + 1;\n\t\t\tnz = bin.nextZero(data, off);\n\t\t\tvar tkeyw = bin.readUTF8(data, off, nz-off);  off = nz + 1;\n\t\t\tvar text  = bin.readUTF8(data, off, len-(off-offset));\n\t\t\tout.tabs[type][keyw] = text;\n\t\t}\n\t\telse if(type==\"PLTE\") {\n\t\t\tout.tabs[type] = bin.readBytes(data, offset, len);\n\t\t}\n\t\telse if(type==\"hIST\") {\n\t\t\tvar pl = out.tabs[\"PLTE\"].length/3;\n\t\t\tout.tabs[type] = [];  for(var i=0; i<pl; i++) out.tabs[type].push(rUs(data, offset+i*2));\n\t\t}\n\t\telse if(type==\"tRNS\") {\n\t\t\tif     (out.ctype==3) out.tabs[type] = bin.readBytes(data, offset, len);\n\t\t\telse if(out.ctype==0) out.tabs[type] = rUs(data, offset);\n\t\t\telse if(out.ctype==2) out.tabs[type] = [ rUs(data,offset),rUs(data,offset+2),rUs(data,offset+4) ];\n\t\t\t//else console.log(\"tRNS for unsupported color type\",out.ctype, len);\n\t\t}\n\t\telse if(type==\"gAMA\") out.tabs[type] = bin.readUint(data, offset)/100000;\n\t\telse if(type==\"sRGB\") out.tabs[type] = data[offset];\n\t\telse if(type==\"bKGD\")\n\t\t{\n\t\t\tif     (out.ctype==0 || out.ctype==4) out.tabs[type] = [rUs(data, offset)];\n\t\t\telse if(out.ctype==2 || out.ctype==6) out.tabs[type] = [rUs(data, offset), rUs(data, offset+2), rUs(data, offset+4)];\n\t\t\telse if(out.ctype==3) out.tabs[type] = data[offset];\n\t\t}\n\t\telse if(type==\"IEND\") {\n\t\t\tbreak;\n\t\t}\n\t\t//else {  log(\"unknown chunk type\", type, len);  }\n\t\toffset += len;\n\t\tvar crc = bin.readUint(data, offset);  offset += 4;\n\t}\n\tif(foff!=0) {  var fr = out.frames[out.frames.length-1];\n\t\tfr.data = UPNG.decode._decompress(out, fd.slice(0,foff), fr.rect.width, fr.rect.height);  foff=0;\n\t}\t\n\tout.data = UPNG.decode._decompress(out, dd, out.width, out.height);\n\t\n\tdelete out.compress;  delete out.interlace;  delete out.filter;\n\treturn out;\n}\n\nUPNG.decode._decompress = function(out, dd, w, h) {\n\tvar time = Date.now();\n\tvar bpp = UPNG.decode._getBPP(out), bpl = Math.ceil(w*bpp/8), buff = new Uint8Array((bpl+1+out.interlace)*h);\n\tdd = UPNG.decode._inflate(dd,buff);\n\t//console.log(dd.length, buff.length);\n\t//console.log(Date.now()-time);\n\n\tvar time=Date.now();\n\tif     (out.interlace==0) dd = UPNG.decode._filterZero(dd, out, 0, w, h);\n\telse if(out.interlace==1) dd = UPNG.decode._readInterlace(dd, out);\n\t//console.log(Date.now()-time);\n\treturn dd;\n}\n\nUPNG.decode._inflate = function(data, buff) {  var out=UPNG[\"inflateRaw\"](new Uint8Array(data.buffer, 2,data.length-6),buff);  return out;  }\nUPNG.inflateRaw=function(){var H={};H.H={};H.H.N=function(N,W){var R=Uint8Array,i=0,m=0,J=0,h=0,Q=0,X=0,u=0,w=0,d=0,v,C;\nif(N[0]==3&&N[1]==0)return W?W:new R(0);var V=H.H,n=V.b,A=V.e,l=V.R,M=V.n,I=V.A,e=V.Z,b=V.m,Z=W==null;\nif(Z)W=new R(N.length>>>2<<3);while(i==0){i=n(N,d,1);m=n(N,d+1,2);d+=3;if(m==0){if((d&7)!=0)d+=8-(d&7);\nvar D=(d>>>3)+4,q=N[D-4]|N[D-3]<<8;if(Z)W=H.H.W(W,w+q);W.set(new R(N.buffer,N.byteOffset+D,q),w);d=D+q<<3;\nw+=q;continue}if(Z)W=H.H.W(W,w+(1<<17));if(m==1){v=b.J;C=b.h;X=(1<<9)-1;u=(1<<5)-1}if(m==2){J=A(N,d,5)+257;\nh=A(N,d+5,5)+1;Q=A(N,d+10,4)+4;d+=14;var E=d,j=1;for(var c=0;c<38;c+=2){b.Q[c]=0;b.Q[c+1]=0}for(var c=0;\nc<Q;c++){var K=A(N,d+c*3,3);b.Q[(b.X[c]<<1)+1]=K;if(K>j)j=K}d+=3*Q;M(b.Q,j);I(b.Q,j,b.u);v=b.w;C=b.d;\nd=l(b.u,(1<<j)-1,J+h,N,d,b.v);var r=V.V(b.v,0,J,b.C);X=(1<<r)-1;var S=V.V(b.v,J,h,b.D);u=(1<<S)-1;M(b.C,r);\nI(b.C,r,v);M(b.D,S);I(b.D,S,C)}while(!0){var T=v[e(N,d)&X];d+=T&15;var p=T>>>4;if(p>>>8==0){W[w++]=p}else if(p==256){break}else{var z=w+p-254;\nif(p>264){var _=b.q[p-257];z=w+(_>>>3)+A(N,d,_&7);d+=_&7}var $=C[e(N,d)&u];d+=$&15;var s=$>>>4,Y=b.c[s],a=(Y>>>4)+n(N,d,Y&15);\nd+=Y&15;while(w<z){W[w]=W[w++-a];W[w]=W[w++-a];W[w]=W[w++-a];W[w]=W[w++-a]}w=z}}}return W.length==w?W:W.slice(0,w)};\nH.H.W=function(N,W){var R=N.length;if(W<=R)return N;var V=new Uint8Array(R<<1);V.set(N,0);return V};\nH.H.R=function(N,W,R,V,n,A){var l=H.H.e,M=H.H.Z,I=0;while(I<R){var e=N[M(V,n)&W];n+=e&15;var b=e>>>4;\nif(b<=15){A[I]=b;I++}else{var Z=0,m=0;if(b==16){m=3+l(V,n,2);n+=2;Z=A[I-1]}else if(b==17){m=3+l(V,n,3);\nn+=3}else if(b==18){m=11+l(V,n,7);n+=7}var J=I+m;while(I<J){A[I]=Z;I++}}}return n};H.H.V=function(N,W,R,V){var n=0,A=0,l=V.length>>>1;\nwhile(A<R){var M=N[A+W];V[A<<1]=0;V[(A<<1)+1]=M;if(M>n)n=M;A++}while(A<l){V[A<<1]=0;V[(A<<1)+1]=0;A++}return n};\nH.H.n=function(N,W){var R=H.H.m,V=N.length,n,A,l,M,I,e=R.j;for(var M=0;M<=W;M++)e[M]=0;for(M=1;M<V;M+=2)e[N[M]]++;\nvar b=R.K;n=0;e[0]=0;for(A=1;A<=W;A++){n=n+e[A-1]<<1;b[A]=n}for(l=0;l<V;l+=2){I=N[l+1];if(I!=0){N[l]=b[I];\nb[I]++}}};H.H.A=function(N,W,R){var V=N.length,n=H.H.m,A=n.r;for(var l=0;l<V;l+=2)if(N[l+1]!=0){var M=l>>1,I=N[l+1],e=M<<4|I,b=W-I,Z=N[l]<<b,m=Z+(1<<b);\nwhile(Z!=m){var J=A[Z]>>>15-W;R[J]=e;Z++}}};H.H.l=function(N,W){var R=H.H.m.r,V=15-W;for(var n=0;n<N.length;\nn+=2){var A=N[n]<<W-N[n+1];N[n]=R[A]>>>V}};H.H.M=function(N,W,R){R=R<<(W&7);var V=W>>>3;N[V]|=R;N[V+1]|=R>>>8};\nH.H.I=function(N,W,R){R=R<<(W&7);var V=W>>>3;N[V]|=R;N[V+1]|=R>>>8;N[V+2]|=R>>>16};H.H.e=function(N,W,R){return(N[W>>>3]|N[(W>>>3)+1]<<8)>>>(W&7)&(1<<R)-1};\nH.H.b=function(N,W,R){return(N[W>>>3]|N[(W>>>3)+1]<<8|N[(W>>>3)+2]<<16)>>>(W&7)&(1<<R)-1};H.H.Z=function(N,W){return(N[W>>>3]|N[(W>>>3)+1]<<8|N[(W>>>3)+2]<<16)>>>(W&7)};\nH.H.i=function(N,W){return(N[W>>>3]|N[(W>>>3)+1]<<8|N[(W>>>3)+2]<<16|N[(W>>>3)+3]<<24)>>>(W&7)};H.H.m=function(){var N=Uint16Array,W=Uint32Array;\nreturn{K:new N(16),j:new N(16),X:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],T:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],q:new N(32),p:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],z:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],c:new W(32),J:new N(512),_:[],h:new N(32),$:[],w:new N(32768),C:[],v:[],d:new N(32768),D:[],u:new N(512),Q:[],r:new N(1<<15),s:new W(286),Y:new W(30),a:new W(19),t:new W(15e3),k:new N(1<<16),g:new N(1<<15)}}();\n(function(){var N=H.H.m,W=1<<15;for(var R=0;R<W;R++){var V=R;V=(V&2863311530)>>>1|(V&1431655765)<<1;\nV=(V&3435973836)>>>2|(V&858993459)<<2;V=(V&4042322160)>>>4|(V&252645135)<<4;V=(V&4278255360)>>>8|(V&16711935)<<8;\nN.r[R]=(V>>>16|V<<16)>>>17}function n(A,l,M){while(l--!=0)A.push(0,M)}for(var R=0;R<32;R++){N.q[R]=N.S[R]<<3|N.T[R];\nN.c[R]=N.p[R]<<4|N.z[R]}n(N._,144,8);n(N._,255-143,9);n(N._,279-255,7);n(N._,287-279,8);H.H.n(N._,9);\nH.H.A(N._,9,N.J);H.H.l(N._,9);n(N.$,32,5);H.H.n(N.$,5);H.H.A(N.$,5,N.h);H.H.l(N.$,5);n(N.Q,19,0);n(N.C,286,0);\nn(N.D,30,0);n(N.v,320,0)}());return H.H.N}()\n\n\nUPNG.decode._readInterlace = function(data, out)\n{\n\tvar w = out.width, h = out.height;\n\tvar bpp = UPNG.decode._getBPP(out), cbpp = bpp>>3, bpl = Math.ceil(w*bpp/8);\n\tvar img = new Uint8Array( h * bpl );\n\tvar di = 0;\n\n\tvar starting_row  = [ 0, 0, 4, 0, 2, 0, 1 ];\n\tvar starting_col  = [ 0, 4, 0, 2, 0, 1, 0 ];\n\tvar row_increment = [ 8, 8, 8, 4, 4, 2, 2 ];\n\tvar col_increment = [ 8, 8, 4, 4, 2, 2, 1 ];\n\n\tvar pass=0;\n\twhile(pass<7)\n\t{\n\t\tvar ri = row_increment[pass], ci = col_increment[pass];\n\t\tvar sw = 0, sh = 0;\n\t\tvar cr = starting_row[pass];  while(cr<h) {  cr+=ri;  sh++;  }\n\t\tvar cc = starting_col[pass];  while(cc<w) {  cc+=ci;  sw++;  }\n\t\tvar bpll = Math.ceil(sw*bpp/8);\n\t\tUPNG.decode._filterZero(data, out, di, sw, sh);\n\n\t\tvar y=0, row = starting_row[pass];\n\t\twhile(row<h)\n\t\t{\n\t\t\tvar col = starting_col[pass];\n\t\t\tvar cdi = (di+y*bpll)<<3;\n\n\t\t\twhile(col<w)\n\t\t\t{\n\t\t\t\tif(bpp==1) {\n\t\t\t\t\tvar val = data[cdi>>3];  val = (val>>(7-(cdi&7)))&1;\n\t\t\t\t\timg[row*bpl + (col>>3)] |= (val << (7-((col&7)<<0)));\n\t\t\t\t}\n\t\t\t\tif(bpp==2) {\n\t\t\t\t\tvar val = data[cdi>>3];  val = (val>>(6-(cdi&7)))&3;\n\t\t\t\t\timg[row*bpl + (col>>2)] |= (val << (6-((col&3)<<1)));\n\t\t\t\t}\n\t\t\t\tif(bpp==4) {\n\t\t\t\t\tvar val = data[cdi>>3];  val = (val>>(4-(cdi&7)))&15;\n\t\t\t\t\timg[row*bpl + (col>>1)] |= (val << (4-((col&1)<<2)));\n\t\t\t\t}\n\t\t\t\tif(bpp>=8) {\n\t\t\t\t\tvar ii = row*bpl+col*cbpp;\n\t\t\t\t\tfor(var j=0; j<cbpp; j++) img[ii+j] = data[(cdi>>3)+j];\n\t\t\t\t}\n\t\t\t\tcdi+=bpp;  col+=ci;\n\t\t\t}\n\t\t\ty++;  row += ri;\n\t\t}\n\t\tif(sw*sh!=0) di += sh * (1 + bpll);\n\t\tpass = pass + 1;\n\t}\n\treturn img;\n}\n\nUPNG.decode._getBPP = function(out) {\n\tvar noc = [1,null,3,1,2,null,4][out.ctype];\n\treturn noc * out.depth;\n}\n\nUPNG.decode._filterZero = function(data, out, off, w, h)\n{\n\tvar bpp = UPNG.decode._getBPP(out), bpl = Math.ceil(w*bpp/8), paeth = UPNG.decode._paeth;\n\tbpp = Math.ceil(bpp/8);\n\t\n\tvar i=0, di=1, type=data[off], x=0;\n\t\n\tif(type>1) data[off]=[0,0,1][type-2];  \n\tif(type==3) for(x=bpp; x<bpl; x++) data[x+1] = (data[x+1] + (data[x+1-bpp]>>>1) )&255;\n\n\tfor(var y=0; y<h; y++)  {\n\t\ti = off+y*bpl; di = i+y+1;\n\t\ttype = data[di-1]; x=0;\n\n\t\tif     (type==0)   for(; x<bpl; x++) data[i+x] = data[di+x];\n\t\telse if(type==1) { for(; x<bpp; x++) data[i+x] = data[di+x];\n\t\t\t\t\t\t   for(; x<bpl; x++) data[i+x] = (data[di+x] + data[i+x-bpp]);  }\n\t\telse if(type==2) { for(; x<bpl; x++) data[i+x] = (data[di+x] + data[i+x-bpl]);  }\n\t\telse if(type==3) { for(; x<bpp; x++) data[i+x] = (data[di+x] + ( data[i+x-bpl]>>>1));\n\t\t\t               for(; x<bpl; x++) data[i+x] = (data[di+x] + ((data[i+x-bpl]+data[i+x-bpp])>>>1) );  }\n\t\telse             { for(; x<bpp; x++) data[i+x] = (data[di+x] + paeth(0, data[i+x-bpl], 0));\n\t\t\t\t\t\t   for(; x<bpl; x++) data[i+x] = (data[di+x] + paeth(data[i+x-bpp], data[i+x-bpl], data[i+x-bpp-bpl]) );  }\n\t}\n\treturn data;\n}\n\nUPNG.decode._paeth = function(a,b,c)\n{\n\tvar p = a+b-c, pa = (p-a), pb = (p-b), pc = (p-c);\n\tif (pa*pa <= pb*pb && pa*pa <= pc*pc)  return a;\n\telse if (pb*pb <= pc*pc)  return b;\n\treturn c;\n}\n\nUPNG.decode._IHDR = function(data, offset, out)\n{\n\tvar bin = UPNG._bin;\n\tout.width  = bin.readUint(data, offset);  offset += 4;\n\tout.height = bin.readUint(data, offset);  offset += 4;\n\tout.depth     = data[offset];  offset++;\n\tout.ctype     = data[offset];  offset++;\n\tout.compress  = data[offset];  offset++;\n\tout.filter    = data[offset];  offset++;\n\tout.interlace = data[offset];  offset++;\n}\n\nUPNG._bin = {\n\tnextZero   : function(data,p)  {  while(data[p]!=0) p++;  return p;  },\n\treadUshort : function(buff,p)  {  return (buff[p]<< 8) | buff[p+1];  },\n\twriteUshort: function(buff,p,n){  buff[p] = (n>>8)&255;  buff[p+1] = n&255;  },\n\treadUint   : function(buff,p)  {  return (buff[p]*(256*256*256)) + ((buff[p+1]<<16) | (buff[p+2]<< 8) | buff[p+3]);  },\n\twriteUint  : function(buff,p,n){  buff[p]=(n>>24)&255;  buff[p+1]=(n>>16)&255;  buff[p+2]=(n>>8)&255;  buff[p+3]=n&255;  },\n\treadASCII  : function(buff,p,l){  var s = \"\";  for(var i=0; i<l; i++) s += String.fromCharCode(buff[p+i]);  return s;    },\n\twriteASCII : function(data,p,s){  for(var i=0; i<s.length; i++) data[p+i] = s.charCodeAt(i);  },\n\treadBytes  : function(buff,p,l){  var arr = [];   for(var i=0; i<l; i++) arr.push(buff[p+i]);   return arr;  },\n\tpad : function(n) { return n.length < 2 ? \"0\" + n : n; },\n\treadUTF8 : function(buff, p, l) {\n\t\tvar s = \"\", ns;\n\t\tfor(var i=0; i<l; i++) s += \"%\" + UPNG._bin.pad(buff[p+i].toString(16));\n\t\ttry {  ns = decodeURIComponent(s); }\n\t\tcatch(e) {  return UPNG._bin.readASCII(buff, p, l);  }\n\t\treturn  ns;\n\t}\n}\nUPNG._copyTile = function(sb, sw, sh, tb, tw, th, xoff, yoff, mode)\n{\n\tvar w = Math.min(sw,tw), h = Math.min(sh,th);\n\tvar si=0, ti=0;\n\tfor(var y=0; y<h; y++)\n\t\tfor(var x=0; x<w; x++)\n\t\t{\n\t\t\tif(xoff>=0 && yoff>=0) {  si = (y*sw+x)<<2;  ti = (( yoff+y)*tw+xoff+x)<<2;  }\n\t\t\telse                   {  si = ((-yoff+y)*sw-xoff+x)<<2;  ti = (y*tw+x)<<2;  }\n\t\t\t\n\t\t\tif     (mode==0) {  tb[ti] = sb[si];  tb[ti+1] = sb[si+1];  tb[ti+2] = sb[si+2];  tb[ti+3] = sb[si+3];  }\n\t\t\telse if(mode==1) {\n\t\t\t\tvar fa = sb[si+3]*(1/255), fr=sb[si]*fa, fg=sb[si+1]*fa, fb=sb[si+2]*fa; \n\t\t\t\tvar ba = tb[ti+3]*(1/255), br=tb[ti]*ba, bg=tb[ti+1]*ba, bb=tb[ti+2]*ba; \n\t\t\t\t\n\t\t\t\tvar ifa=1-fa, oa = fa+ba*ifa, ioa = (oa==0?0:1/oa);\n\t\t\t\ttb[ti+3] = 255*oa;  \n\t\t\t\ttb[ti+0] = (fr+br*ifa)*ioa;  \n\t\t\t\ttb[ti+1] = (fg+bg*ifa)*ioa;   \n\t\t\t\ttb[ti+2] = (fb+bb*ifa)*ioa;  \n\t\t\t}\n\t\t\telse if(mode==2){\t// copy only differences, otherwise zero\n\t\t\t\tvar fa = sb[si+3], fr=sb[si], fg=sb[si+1], fb=sb[si+2]; \n\t\t\t\tvar ba = tb[ti+3], br=tb[ti], bg=tb[ti+1], bb=tb[ti+2]; \n\t\t\t\tif(fa==ba && fr==br && fg==bg && fb==bb) {  tb[ti]=0;  tb[ti+1]=0;  tb[ti+2]=0;  tb[ti+3]=0;  }\n\t\t\t\telse {  tb[ti]=fr;  tb[ti+1]=fg;  tb[ti+2]=fb;  tb[ti+3]=fa;  }\n\t\t\t}\n\t\t\telse if(mode==3){\t// check if can be blended\n\t\t\t\tvar fa = sb[si+3], fr=sb[si], fg=sb[si+1], fb=sb[si+2]; \n\t\t\t\tvar ba = tb[ti+3], br=tb[ti], bg=tb[ti+1], bb=tb[ti+2]; \n\t\t\t\tif(fa==ba && fr==br && fg==bg && fb==bb) continue;\n\t\t\t\t//if(fa!=255 && ba!=0) return false;\n\t\t\t\tif(fa<220 && ba>20) return false;\n\t\t\t}\n\t\t}\n\treturn true;\n}\n\n\n\n\nUPNG.encode = function(bufs, w, h, ps, dels, tabs, forbidPlte)\n{\n\tif(ps==null) ps=0;\n\tif(forbidPlte==null) forbidPlte = false;\n\n\tvar nimg = UPNG.encode.compress(bufs, w, h, ps, [false, false, false, 0, forbidPlte]);\n\tUPNG.encode.compressPNG(nimg, -1);\n\t\n\treturn UPNG.encode._main(nimg, w, h, dels, tabs);\n}\n\nUPNG.encodeLL = function(bufs, w, h, cc, ac, depth, dels, tabs) {\n\tvar nimg = {  ctype: 0 + (cc==1 ? 0 : 2) + (ac==0 ? 0 : 4),      depth: depth,  frames: []  };\n\t\n\tvar time = Date.now();\n\tvar bipp = (cc+ac)*depth, bipl = bipp * w;\n\tfor(var i=0; i<bufs.length; i++)\n\t\tnimg.frames.push({  rect:{x:0,y:0,width:w,height:h},  img:new Uint8Array(bufs[i]), blend:0, dispose:1, bpp:Math.ceil(bipp/8), bpl:Math.ceil(bipl/8)  });\n\t\n\tUPNG.encode.compressPNG(nimg, 0, true);\n\t\n\tvar out = UPNG.encode._main(nimg, w, h, dels, tabs);\n\treturn out;\n}\n\nUPNG.encode._main = function(nimg, w, h, dels, tabs) {\n\tif(tabs==null) tabs={};\n\tvar crc = UPNG.crc.crc, wUi = UPNG._bin.writeUint, wUs = UPNG._bin.writeUshort, wAs = UPNG._bin.writeASCII;\n\tvar offset = 8, anim = nimg.frames.length>1, pltAlpha = false;\n\t\n\tvar leng = 8 + (16+5+4) /*+ (9+4)*/ + (anim ? 20 : 0);\n\tif(tabs[\"sRGB\"]!=null) leng += 8+1+4;\n\tif(tabs[\"pHYs\"]!=null) leng += 8+9+4;\n\tif(nimg.ctype==3) {\n\t\tvar dl = nimg.plte.length;\n\t\tfor(var i=0; i<dl; i++) if((nimg.plte[i]>>>24)!=255) pltAlpha = true;\n\t\tleng += (8 + dl*3 + 4) + (pltAlpha ? (8 + dl*1 + 4) : 0);\n\t}\n\tfor(var j=0; j<nimg.frames.length; j++)\n\t{\n\t\tvar fr = nimg.frames[j];\n\t\tif(anim) leng += 38;\n\t\tleng += fr.cimg.length + 12;\n\t\tif(j!=0) leng+=4;\n\t}\n\tleng += 12; \n\t\n\tvar data = new Uint8Array(leng);\n\tvar wr=[0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a];\n\tfor(var i=0; i<8; i++) data[i]=wr[i];\n\t\n\twUi(data,offset, 13);     offset+=4;\n\twAs(data,offset,\"IHDR\");  offset+=4;\n\twUi(data,offset,w);  offset+=4;\n\twUi(data,offset,h);  offset+=4;\n\tdata[offset] = nimg.depth;  offset++;  // depth\n\tdata[offset] = nimg.ctype;  offset++;  // ctype\n\tdata[offset] = 0;  offset++;  // compress\n\tdata[offset] = 0;  offset++;  // filter\n\tdata[offset] = 0;  offset++;  // interlace\n\twUi(data,offset,crc(data,offset-17,17));  offset+=4; // crc\n\n\t// 13 bytes to say, that it is sRGB\n\tif(tabs[\"sRGB\"]!=null) {\n\t\twUi(data,offset, 1);      offset+=4;\n\t\twAs(data,offset,\"sRGB\");  offset+=4;\n\t\tdata[offset] = tabs[\"sRGB\"];  offset++;\n\t\twUi(data,offset,crc(data,offset-5,5));  offset+=4; // crc\n\t}\n\tif(tabs[\"pHYs\"]!=null) {\n\t\twUi(data,offset, 9);      offset+=4;\n\t\twAs(data,offset,\"pHYs\");  offset+=4;\n\t\twUi(data,offset, tabs[\"pHYs\"][0]);      offset+=4;\n\t\twUi(data,offset, tabs[\"pHYs\"][1]);      offset+=4;\n\t\tdata[offset]=tabs[\"pHYs\"][2];\t\t\toffset++;\n\t\twUi(data,offset,crc(data,offset-13,13));  offset+=4; // crc\n\t}\n\n\tif(anim) {\n\t\twUi(data,offset, 8);      offset+=4;\n\t\twAs(data,offset,\"acTL\");  offset+=4;\n\t\twUi(data,offset, nimg.frames.length);     offset+=4;\n\t\twUi(data,offset, tabs[\"loop\"]!=null?tabs[\"loop\"]:0);      offset+=4;\n\t\twUi(data,offset,crc(data,offset-12,12));  offset+=4; // crc\n\t}\n\n\tif(nimg.ctype==3) {\n\t\tvar dl = nimg.plte.length;\n\t\twUi(data,offset, dl*3);  offset+=4;\n\t\twAs(data,offset,\"PLTE\");  offset+=4;\n\t\tfor(var i=0; i<dl; i++){\n\t\t\tvar ti=i*3, c=nimg.plte[i], r=(c)&255, g=(c>>>8)&255, b=(c>>>16)&255;\n\t\t\tdata[offset+ti+0]=r;  data[offset+ti+1]=g;  data[offset+ti+2]=b;\n\t\t}\n\t\toffset+=dl*3;\n\t\twUi(data,offset,crc(data,offset-dl*3-4,dl*3+4));  offset+=4; // crc\n\n\t\tif(pltAlpha) {\n\t\t\twUi(data,offset, dl);  offset+=4;\n\t\t\twAs(data,offset,\"tRNS\");  offset+=4;\n\t\t\tfor(var i=0; i<dl; i++)  data[offset+i]=(nimg.plte[i]>>>24)&255;\n\t\t\toffset+=dl;\n\t\t\twUi(data,offset,crc(data,offset-dl-4,dl+4));  offset+=4; // crc\n\t\t}\n\t}\n\t\n\tvar fi = 0;\n\tfor(var j=0; j<nimg.frames.length; j++)\n\t{\n\t\tvar fr = nimg.frames[j];\n\t\tif(anim) {\n\t\t\twUi(data, offset, 26);     offset+=4;\n\t\t\twAs(data, offset,\"fcTL\");  offset+=4;\n\t\t\twUi(data, offset, fi++);   offset+=4;\n\t\t\twUi(data, offset, fr.rect.width );   offset+=4;\n\t\t\twUi(data, offset, fr.rect.height);   offset+=4;\n\t\t\twUi(data, offset, fr.rect.x);   offset+=4;\n\t\t\twUi(data, offset, fr.rect.y);   offset+=4;\n\t\t\twUs(data, offset, dels[j]);   offset+=2;\n\t\t\twUs(data, offset,  1000);   offset+=2;\n\t\t\tdata[offset] = fr.dispose;  offset++;\t// dispose\n\t\t\tdata[offset] = fr.blend  ;  offset++;\t// blend\n\t\t\twUi(data,offset,crc(data,offset-30,30));  offset+=4; // crc\n\t\t}\n\t\t\t\t\n\t\tvar imgd = fr.cimg, dl = imgd.length;\n\t\twUi(data,offset, dl+(j==0?0:4));     offset+=4;\n\t\tvar ioff = offset;\n\t\twAs(data,offset,(j==0)?\"IDAT\":\"fdAT\");  offset+=4;\n\t\tif(j!=0) {  wUi(data, offset, fi++);  offset+=4;  }\n\t\tdata.set(imgd,offset);\n\t\toffset += dl;\n\t\twUi(data,offset,crc(data,ioff,offset-ioff));  offset+=4; // crc\n\t}\n\n\twUi(data,offset, 0);     offset+=4;\n\twAs(data,offset,\"IEND\");  offset+=4;\n\twUi(data,offset,crc(data,offset-4,4));  offset+=4; // crc\n\n\treturn data.buffer;\n}\n\nUPNG.encode.compressPNG = function(out, filter, levelZero) {\n\tfor(var i=0; i<out.frames.length; i++) {\n\t\tvar frm = out.frames[i], nw=frm.rect.width, nh=frm.rect.height;\n\t\tvar fdata = new Uint8Array(nh*frm.bpl+nh);\n\t\tfrm.cimg = UPNG.encode._filterZero(frm.img,nh,frm.bpp,frm.bpl,fdata, filter, levelZero);\n\t}\n}\n\n\n\nUPNG.encode.compress = function(bufs, w, h, ps, prms) // prms:  onlyBlend, minBits, forbidPlte\n{\n\t//var time = Date.now();\n\tvar onlyBlend = prms[0], evenCrd = prms[1], forbidPrev = prms[2], minBits = prms[3], forbidPlte = prms[4];\n\t\n\tvar ctype = 6, depth = 8, alphaAnd=255\n\t\n\tfor(var j=0; j<bufs.length; j++)  {  // when not quantized, other frames can contain colors, that are not in an initial frame\n\t\tvar img = new Uint8Array(bufs[j]), ilen = img.length;\n\t\tfor(var i=0; i<ilen; i+=4) alphaAnd &= img[i+3];\n\t}\n\tvar gotAlpha = (alphaAnd!=255);\n\t\n\t//console.log(\"alpha check\", Date.now()-time);  time = Date.now();\n\t\n\t//var brute = gotAlpha && forGIF;\t\t// brute : frames can only be copied, not \"blended\"\n\tvar frms = UPNG.encode.framize(bufs, w, h, onlyBlend, evenCrd, forbidPrev);\n\t//console.log(\"framize\", Date.now()-time);  time = Date.now();\n\t\n\tvar cmap={}, plte=[], inds=[];  \n\t\n\tif(ps!=0) {\n\t\tvar nbufs = [];  for(var i=0; i<frms.length; i++) nbufs.push(frms[i].img.buffer);\n\t\t\n\t\tvar abuf = UPNG.encode.concatRGBA(nbufs), qres = UPNG.quantize(abuf, ps);  \n\t\tvar cof = 0, bb = new Uint8Array(qres.abuf);\n\t\tfor(var i=0; i<frms.length; i++) {  var ti=frms[i].img, bln=ti.length;  inds.push(new Uint8Array(qres.inds.buffer, cof>>2, bln>>2));\n\t\t\tfor(var j=0; j<bln; j+=4) {  ti[j]=bb[cof+j];  ti[j+1]=bb[cof+j+1];  ti[j+2]=bb[cof+j+2];  ti[j+3]=bb[cof+j+3];  }    cof+=bln;  }\n\t\t\n\t\tfor(var i=0; i<qres.plte.length; i++) plte.push(qres.plte[i].est.rgba);\n\t\t//console.log(\"quantize\", Date.now()-time);  time = Date.now();\n\t}\n\telse {\n\t\t// what if ps==0, but there are <=256 colors?  we still need to detect, if the palette could be used\n\t\tfor(var j=0; j<frms.length; j++)  {  // when not quantized, other frames can contain colors, that are not in an initial frame\n\t\t\tvar frm = frms[j], img32 = new Uint32Array(frm.img.buffer), nw=frm.rect.width, ilen = img32.length;\n\t\t\tvar ind = new Uint8Array(ilen);  inds.push(ind);\n\t\t\tfor(var i=0; i<ilen; i++) {\n\t\t\t\tvar c = img32[i];\n\t\t\t\tif     (i!=0 && c==img32[i- 1]) ind[i]=ind[i-1];\n\t\t\t\telse if(i>nw && c==img32[i-nw]) ind[i]=ind[i-nw];\n\t\t\t\telse {\n\t\t\t\t\tvar cmc = cmap[c];\n\t\t\t\t\tif(cmc==null) {  cmap[c]=cmc=plte.length;  plte.push(c);  if(plte.length>=300) break;  }\n\t\t\t\t\tind[i]=cmc;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t//console.log(\"make palette\", Date.now()-time);  time = Date.now();\n\t}\n\t\n\tvar cc=plte.length; //console.log(\"colors:\",cc);\n\tif(cc<=256 && forbidPlte==false) {\n\t\tif(cc<= 2) depth=1;  else if(cc<= 4) depth=2;  else if(cc<=16) depth=4;  else depth=8;\n\t\tdepth =  Math.max(depth, minBits);\n\t}\n\t\n\tfor(var j=0; j<frms.length; j++)\n\t{\n\t\tvar frm = frms[j], nx=frm.rect.x, ny=frm.rect.y, nw=frm.rect.width, nh=frm.rect.height;\n\t\tvar cimg = frm.img, cimg32 = new Uint32Array(cimg.buffer);\n\t\tvar bpl = 4*nw, bpp=4;\n\t\tif(cc<=256 && forbidPlte==false) {\n\t\t\tbpl = Math.ceil(depth*nw/8);\n\t\t\tvar nimg = new Uint8Array(bpl*nh);\n\t\t\tvar inj = inds[j];\n\t\t\tfor(var y=0; y<nh; y++) {  var i=y*bpl, ii=y*nw;\n\t\t\t\tif     (depth==8) for(var x=0; x<nw; x++) nimg[i+(x)   ]   =  (inj[ii+x]             );\n\t\t\t\telse if(depth==4) for(var x=0; x<nw; x++) nimg[i+(x>>1)]  |=  (inj[ii+x]<<(4-(x&1)*4));\n\t\t\t\telse if(depth==2) for(var x=0; x<nw; x++) nimg[i+(x>>2)]  |=  (inj[ii+x]<<(6-(x&3)*2));\n\t\t\t\telse if(depth==1) for(var x=0; x<nw; x++) nimg[i+(x>>3)]  |=  (inj[ii+x]<<(7-(x&7)*1));\n\t\t\t}\n\t\t\tcimg=nimg;  ctype=3;  bpp=1;\n\t\t}\n\t\telse if(gotAlpha==false && frms.length==1) {\t// some next \"reduced\" frames may contain alpha for blending\n\t\t\tvar nimg = new Uint8Array(nw*nh*3), area=nw*nh;\n\t\t\tfor(var i=0; i<area; i++) { var ti=i*3, qi=i*4;  nimg[ti]=cimg[qi];  nimg[ti+1]=cimg[qi+1];  nimg[ti+2]=cimg[qi+2];  }\n\t\t\tcimg=nimg;  ctype=2;  bpp=3;  bpl=3*nw;\n\t\t}\n\t\tfrm.img=cimg;  frm.bpl=bpl;  frm.bpp=bpp;\n\t}\n\t//console.log(\"colors => palette indices\", Date.now()-time);  time = Date.now();\n\t\n\treturn {ctype:ctype, depth:depth, plte:plte, frames:frms  };\n}\nUPNG.encode.framize = function(bufs,w,h,alwaysBlend,evenCrd,forbidPrev) {\n\t/*  DISPOSE\n\t    - 0 : no change\n\t\t- 1 : clear to transparent\n\t\t- 2 : retstore to content before rendering (previous frame disposed)\n\t\tBLEND\n\t\t- 0 : replace\n\t\t- 1 : blend\n\t*/\n\tvar frms = [];\n\tfor(var j=0; j<bufs.length; j++) {\n\t\tvar cimg = new Uint8Array(bufs[j]), cimg32 = new Uint32Array(cimg.buffer);\n\t\tvar nimg;\n\t\t\n\t\tvar nx=0, ny=0, nw=w, nh=h, blend=alwaysBlend?1:0;\n\t\tif(j!=0) {\n\t\t\tvar tlim = (forbidPrev || alwaysBlend || j==1 || frms[j-2].dispose!=0)?1:2, tstp = 0, tarea = 1e9;\n\t\t\tfor(var it=0; it<tlim; it++)\n\t\t\t{\n\t\t\t\tvar pimg = new Uint8Array(bufs[j-1-it]), p32 = new Uint32Array(bufs[j-1-it]);\n\t\t\t\tvar mix=w,miy=h,max=-1,may=-1;\n\t\t\t\tfor(var y=0; y<h; y++) for(var x=0; x<w; x++) {\n\t\t\t\t\tvar i = y*w+x;\n\t\t\t\t\tif(cimg32[i]!=p32[i]) {\n\t\t\t\t\t\tif(x<mix) mix=x;  if(x>max) max=x;\n\t\t\t\t\t\tif(y<miy) miy=y;  if(y>may) may=y;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(max==-1) mix=miy=max=may=0;\n\t\t\t\tif(evenCrd) {  if((mix&1)==1)mix--;  if((miy&1)==1)miy--;  }\n\t\t\t\tvar sarea = (max-mix+1)*(may-miy+1);\n\t\t\t\tif(sarea<tarea) {\n\t\t\t\t\ttarea = sarea;  tstp = it;\n\t\t\t\t\tnx = mix; ny = miy; nw = max-mix+1; nh = may-miy+1;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// alwaysBlend: pokud zjistím, že blendit nelze, nastavím předchozímu snímku dispose=1. Zajistím, aby obsahoval můj obdélník.\n\t\t\tvar pimg = new Uint8Array(bufs[j-1-tstp]);\n\t\t\tif(tstp==1) frms[j-1].dispose = 2;\n\t\t\t\n\t\t\tnimg = new Uint8Array(nw*nh*4);\n\t\t\tUPNG._copyTile(pimg,w,h, nimg,nw,nh, -nx,-ny, 0);\n\t\t\t\n\t\t\tblend =  UPNG._copyTile(cimg,w,h, nimg,nw,nh, -nx,-ny, 3) ? 1 : 0;\n\t\t\tif(blend==1) UPNG.encode._prepareDiff(cimg,w,h,nimg,{x:nx,y:ny,width:nw,height:nh});\n\t\t\telse         UPNG._copyTile(cimg,w,h, nimg,nw,nh, -nx,-ny, 0);\n\t\t\t//UPNG._copyTile(cimg,w,h, nimg,nw,nh, -nx,-ny, blend==1?2:0);\n\t\t}\n\t\telse nimg = cimg.slice(0);\t// img may be rewritten further ... don't rewrite input\n\t\t\n\t\tfrms.push({rect:{x:nx,y:ny,width:nw,height:nh}, img:nimg, blend:blend, dispose:0});\n\t}\n\t\n\t\n\tif(alwaysBlend) for(var j=0; j<frms.length; j++) {\n\t\tvar frm = frms[j];  if(frm.blend==1) continue;\n\t\tvar r0 = frm.rect, r1 = frms[j-1].rect\n\t\tvar miX = Math.min(r0.x, r1.x), miY = Math.min(r0.y, r1.y);\n\t\tvar maX = Math.max(r0.x+r0.width, r1.x+r1.width), maY = Math.max(r0.y+r0.height, r1.y+r1.height);\n\t\tvar r = {x:miX, y:miY, width:maX-miX, height:maY-miY};\n\t\t\n\t\tfrms[j-1].dispose = 1;\n\t\tif(j-1!=0) \n\t\tUPNG.encode._updateFrame(bufs, w,h,frms, j-1,r, evenCrd);\n\t\tUPNG.encode._updateFrame(bufs, w,h,frms, j  ,r, evenCrd);\n\t}\n\tvar area = 0;\n\tif(bufs.length!=1) for(var i=0; i<frms.length; i++) {\n\t\tvar frm = frms[i];\n\t\tarea += frm.rect.width*frm.rect.height;\n\t\t//if(i==0 || frm.blend!=1) continue;\n\t\t//var ob = new Uint8Array(\n\t\t//console.log(frm.blend, frm.dispose, frm.rect);\n\t}\n\t//if(area!=0) console.log(area);\n\treturn frms;\n}\nUPNG.encode._updateFrame = function(bufs, w,h, frms, i, r, evenCrd) {\n\tvar U8 = Uint8Array, U32 = Uint32Array;\n\tvar pimg = new U8(bufs[i-1]), pimg32 = new U32(bufs[i-1]), nimg = i+1<bufs.length ? new U8(bufs[i+1]):null;\n\tvar cimg = new U8(bufs[i]), cimg32 = new U32(cimg.buffer);\n\t\n\tvar mix=w,miy=h,max=-1,may=-1;\n\tfor(var y=0; y<r.height; y++) for(var x=0; x<r.width; x++) {\n\t\tvar cx = r.x+x, cy = r.y+y;\n\t\tvar j = cy*w+cx, cc = cimg32[j];\n\t\t// no need to draw transparency, or to dispose it. Or, if writing the same color and the next one does not need transparency.\n\t\tif(cc==0 || (frms[i-1].dispose==0 && pimg32[j]==cc && (nimg==null || nimg[j*4+3]!=0))/**/) {}\n\t\telse {\n\t\t\tif(cx<mix) mix=cx;  if(cx>max) max=cx;\n\t\t\tif(cy<miy) miy=cy;  if(cy>may) may=cy;\n\t\t}\n\t}\n\tif(max==-1) mix=miy=max=may=0;\n\tif(evenCrd) {  if((mix&1)==1)mix--;  if((miy&1)==1)miy--;  }\n\tr = {x:mix, y:miy, width:max-mix+1, height:may-miy+1};\n\t\n\tvar fr = frms[i];  fr.rect = r;  fr.blend = 1;  fr.img = new Uint8Array(r.width*r.height*4);\n\tif(frms[i-1].dispose==0) {\n\t\tUPNG._copyTile(pimg,w,h, fr.img,r.width,r.height, -r.x,-r.y, 0);\n\t\tUPNG.encode._prepareDiff(cimg,w,h,fr.img,r);\n\t\t//UPNG._copyTile(cimg,w,h, fr.img,r.width,r.height, -r.x,-r.y, 2);\n\t}\n\telse\n\t\tUPNG._copyTile(cimg,w,h, fr.img,r.width,r.height, -r.x,-r.y, 0);\n}\nUPNG.encode._prepareDiff = function(cimg, w,h, nimg, rec) {\n\tUPNG._copyTile(cimg,w,h, nimg,rec.width,rec.height, -rec.x,-rec.y, 2);\n\t/*\n\tvar n32 = new Uint32Array(nimg.buffer);\n\tvar og = new Uint8Array(rec.width*rec.height*4), o32 = new Uint32Array(og.buffer);\n\tUPNG._copyTile(cimg,w,h, og,rec.width,rec.height, -rec.x,-rec.y, 0);\n\tfor(var i=4; i<nimg.length; i+=4) {\n\t\tif(nimg[i-1]!=0 && nimg[i+3]==0 && o32[i>>>2]==o32[(i>>>2)-1]) {\n\t\t\tn32[i>>>2]=o32[i>>>2];\n\t\t\t//var j = i, c=p32[(i>>>2)-1];\n\t\t\t//while(p32[j>>>2]==c) {  n32[j>>>2]=c;  j+=4;  }\n\t\t}\n\t}\n\tfor(var i=nimg.length-8; i>0; i-=4) {\n\t\tif(nimg[i+7]!=0 && nimg[i+3]==0 && o32[i>>>2]==o32[(i>>>2)+1]) {\n\t\t\tn32[i>>>2]=o32[i>>>2];\n\t\t\t//var j = i, c=p32[(i>>>2)-1];\n\t\t\t//while(p32[j>>>2]==c) {  n32[j>>>2]=c;  j+=4;  }\n\t\t}\n\t}*/\n}\n\nUPNG.encode._filterZero = function(img,h,bpp,bpl,data, filter, levelZero)\n{\n\tvar fls = [], ftry=[0,1,2,3,4];\n\tif     (filter!=-1)             ftry=[filter];\n\telse if(h*bpl>500000 || bpp==1) ftry=[0];\n\tvar opts;  if(levelZero) opts={level:0};\n\t\n\tvar CMPR = (levelZero && UZIP!=null) ? UZIP : (pako__WEBPACK_IMPORTED_MODULE_0___default());\n\t\n\tfor(var i=0; i<ftry.length; i++) {\n\t\tfor(var y=0; y<h; y++) UPNG.encode._filterLine(data, img, y, bpl, bpp, ftry[i]);\n\t\t//var nimg = new Uint8Array(data.length);\n\t\t//var sz = UZIP.F.deflate(data, nimg);  fls.push(nimg.slice(0,sz));\n\t\t//var dfl = pako[\"deflate\"](data), dl=dfl.length-4;\n\t\t//var crc = (dfl[dl+3]<<24)|(dfl[dl+2]<<16)|(dfl[dl+1]<<8)|(dfl[dl+0]<<0);\n\t\t//console.log(crc, UZIP.adler(data,2,data.length-6));\n\t\tfls.push(CMPR[\"deflate\"](data,opts));\n\t}\n\tvar ti, tsize=1e9;\n\tfor(var i=0; i<fls.length; i++) if(fls[i].length<tsize) {  ti=i;  tsize=fls[i].length;  }\n\treturn fls[ti];\n}\nUPNG.encode._filterLine = function(data, img, y, bpl, bpp, type)\n{\n\tvar i = y*bpl, di = i+y, paeth = UPNG.decode._paeth\n\tdata[di]=type;  di++;\n\n\tif(type==0) {\n\t\tif(bpl<500) for(var x=0; x<bpl; x++) data[di+x] = img[i+x];\n\t\telse data.set(new Uint8Array(img.buffer,i,bpl),di);\n\t}\n\telse if(type==1) {\n\t\tfor(var x=  0; x<bpp; x++) data[di+x] =  img[i+x];\n\t\tfor(var x=bpp; x<bpl; x++) data[di+x] = (img[i+x]-img[i+x-bpp]+256)&255;\n\t}\n\telse if(y==0) {\n\t\tfor(var x=  0; x<bpp; x++) data[di+x] = img[i+x];\n\n\t\tif(type==2) for(var x=bpp; x<bpl; x++) data[di+x] = img[i+x];\n\t\tif(type==3) for(var x=bpp; x<bpl; x++) data[di+x] = (img[i+x] - (img[i+x-bpp]>>1) +256)&255;\n\t\tif(type==4) for(var x=bpp; x<bpl; x++) data[di+x] = (img[i+x] - paeth(img[i+x-bpp], 0, 0) +256)&255;\n\t}\n\telse {\n\t\tif(type==2) { for(var x=  0; x<bpl; x++) data[di+x] = (img[i+x]+256 - img[i+x-bpl])&255;  }\n\t\tif(type==3) { for(var x=  0; x<bpp; x++) data[di+x] = (img[i+x]+256 - (img[i+x-bpl]>>1))&255;\n\t\t\t\t\t  for(var x=bpp; x<bpl; x++) data[di+x] = (img[i+x]+256 - ((img[i+x-bpl]+img[i+x-bpp])>>1))&255;  }\n\t\tif(type==4) { for(var x=  0; x<bpp; x++) data[di+x] = (img[i+x]+256 - paeth(0, img[i+x-bpl], 0))&255;\n\t\t\t\t\t  for(var x=bpp; x<bpl; x++) data[di+x] = (img[i+x]+256 - paeth(img[i+x-bpp], img[i+x-bpl], img[i+x-bpp-bpl]))&255;  }\n\t}\n}\n\nUPNG.crc = {\n\ttable : ( function() {\n\t   var tab = new Uint32Array(256);\n\t   for (var n=0; n<256; n++) {\n\t\t\tvar c = n;\n\t\t\tfor (var k=0; k<8; k++) {\n\t\t\t\tif (c & 1)  c = 0xedb88320 ^ (c >>> 1);\n\t\t\t\telse        c = c >>> 1;\n\t\t\t}\n\t\t\ttab[n] = c;  }\n\t\treturn tab;  })(),\n\tupdate : function(c, buf, off, len) {\n\t\tfor (var i=0; i<len; i++)  c = UPNG.crc.table[(c ^ buf[off+i]) & 0xff] ^ (c >>> 8);\n\t\treturn c;\n\t},\n\tcrc : function(b,o,l)  {  return UPNG.crc.update(0xffffffff,b,o,l) ^ 0xffffffff;  }\n}\n\n\nUPNG.quantize = function(abuf, ps)\n{\t\n\tvar oimg = new Uint8Array(abuf), nimg = oimg.slice(0), nimg32 = new Uint32Array(nimg.buffer);\n\t\n\tvar KD = UPNG.quantize.getKDtree(nimg, ps);\n\tvar root = KD[0], leafs = KD[1];\n\t\n\tvar planeDst = UPNG.quantize.planeDst;\n\tvar sb = oimg, tb = nimg32, len=sb.length;\n\t\t\n\tvar inds = new Uint8Array(oimg.length>>2);\n\tfor(var i=0; i<len; i+=4) {\n\t\tvar r=sb[i]*(1/255), g=sb[i+1]*(1/255), b=sb[i+2]*(1/255), a=sb[i+3]*(1/255);\n\t\t\n\t\t//  exact, but too slow :(\n\t\tvar nd = UPNG.quantize.getNearest(root, r, g, b, a);\n\t\t//var nd = root;\n\t\t//while(nd.left) nd = (planeDst(nd.est,r,g,b,a)<=0) ? nd.left : nd.right;\n\t\t\n\t\tinds[i>>2] = nd.ind;\n\t\ttb[i>>2] = nd.est.rgba;\n\t}\n\treturn {  abuf:nimg.buffer, inds:inds, plte:leafs  };\n}\n\nUPNG.quantize.getKDtree = function(nimg, ps, err) {\n\tif(err==null) err = 0.0001;\n\tvar nimg32 = new Uint32Array(nimg.buffer);\n\t\n\tvar root = {i0:0, i1:nimg.length, bst:null, est:null, tdst:0, left:null, right:null };  // basic statistic, extra statistic\n\troot.bst = UPNG.quantize.stats(  nimg,root.i0, root.i1  );  root.est = UPNG.quantize.estats( root.bst );\n\tvar leafs = [root];\n\t\n\twhile(leafs.length<ps)\n\t{\n\t\tvar maxL = 0, mi=0;\n\t\tfor(var i=0; i<leafs.length; i++) if(leafs[i].est.L > maxL) {  maxL=leafs[i].est.L;  mi=i;  }\n\t\tif(maxL<err) break;\n\t\tvar node = leafs[mi];\n\t\t\n\t\tvar s0 = UPNG.quantize.splitPixels(nimg,nimg32, node.i0, node.i1, node.est.e, node.est.eMq255);\n\t\tvar s0wrong = (node.i0>=s0 || node.i1<=s0);\n\t\t//console.log(maxL, leafs.length, mi);\n\t\tif(s0wrong) {  node.est.L=0;  continue;  }\n\t\t\n\t\t\n\t\tvar ln = {i0:node.i0, i1:s0, bst:null, est:null, tdst:0, left:null, right:null };  ln.bst = UPNG.quantize.stats( nimg, ln.i0, ln.i1 );  \n\t\tln.est = UPNG.quantize.estats( ln.bst );\n\t\tvar rn = {i0:s0, i1:node.i1, bst:null, est:null, tdst:0, left:null, right:null };  rn.bst = {R:[], m:[], N:node.bst.N-ln.bst.N};\n\t\tfor(var i=0; i<16; i++) rn.bst.R[i] = node.bst.R[i]-ln.bst.R[i];\n\t\tfor(var i=0; i< 4; i++) rn.bst.m[i] = node.bst.m[i]-ln.bst.m[i];\n\t\trn.est = UPNG.quantize.estats( rn.bst );\n\t\t\n\t\tnode.left = ln;  node.right = rn;\n\t\tleafs[mi]=ln;  leafs.push(rn);\n\t}\n\tleafs.sort(function(a,b) {  return b.bst.N-a.bst.N;  });\n\tfor(var i=0; i<leafs.length; i++) leafs[i].ind=i;\n\treturn [root, leafs];\n}\n\nUPNG.quantize.getNearest = function(nd, r,g,b,a)\n{\n\tif(nd.left==null) {  nd.tdst = UPNG.quantize.dist(nd.est.q,r,g,b,a);  return nd;  }\n\tvar planeDst = UPNG.quantize.planeDst(nd.est,r,g,b,a);\n\t\n\tvar node0 = nd.left, node1 = nd.right;\n\tif(planeDst>0) {  node0=nd.right;  node1=nd.left;  }\n\t\n\tvar ln = UPNG.quantize.getNearest(node0, r,g,b,a);\n\tif(ln.tdst<=planeDst*planeDst) return ln;\n\tvar rn = UPNG.quantize.getNearest(node1, r,g,b,a);\n\treturn rn.tdst<ln.tdst ? rn : ln;\n}\nUPNG.quantize.planeDst = function(est, r,g,b,a) {  var e = est.e;  return e[0]*r + e[1]*g + e[2]*b + e[3]*a - est.eMq;  }\nUPNG.quantize.dist     = function(q,   r,g,b,a) {  var d0=r-q[0], d1=g-q[1], d2=b-q[2], d3=a-q[3];  return d0*d0+d1*d1+d2*d2+d3*d3;  }\n\nUPNG.quantize.splitPixels = function(nimg, nimg32, i0, i1, e, eMq)\n{\n\tvar vecDot = UPNG.quantize.vecDot;\n\ti1-=4;\n\tvar shfs = 0;\n\twhile(i0<i1)\n\t{\n\t\twhile(vecDot(nimg, i0, e)<=eMq) i0+=4;\n\t\twhile(vecDot(nimg, i1, e)> eMq) i1-=4;\n\t\tif(i0>=i1) break;\n\t\t\n\t\tvar t = nimg32[i0>>2];  nimg32[i0>>2] = nimg32[i1>>2];  nimg32[i1>>2]=t;\n\t\t\n\t\ti0+=4;  i1-=4;\n\t}\n\twhile(vecDot(nimg, i0, e)>eMq) i0-=4;\n\treturn i0+4;\n}\nUPNG.quantize.vecDot = function(nimg, i, e)\n{\n\treturn nimg[i]*e[0] + nimg[i+1]*e[1] + nimg[i+2]*e[2] + nimg[i+3]*e[3];\n}\nUPNG.quantize.stats = function(nimg, i0, i1){\n\tvar R = [0,0,0,0,  0,0,0,0,  0,0,0,0,  0,0,0,0];\n\tvar m = [0,0,0,0];\n\tvar N = (i1-i0)>>2;\n\tfor(var i=i0; i<i1; i+=4)\n\t{\n\t\tvar r = nimg[i]*(1/255), g = nimg[i+1]*(1/255), b = nimg[i+2]*(1/255), a = nimg[i+3]*(1/255);\n\t\t//var r = nimg[i], g = nimg[i+1], b = nimg[i+2], a = nimg[i+3];\n\t\tm[0]+=r;  m[1]+=g;  m[2]+=b;  m[3]+=a;\n\t\t\n\t\tR[ 0] += r*r;  R[ 1] += r*g;  R[ 2] += r*b;  R[ 3] += r*a;  \n\t\t               R[ 5] += g*g;  R[ 6] += g*b;  R[ 7] += g*a; \n\t\t                              R[10] += b*b;  R[11] += b*a;  \n\t\t                                             R[15] += a*a;  \n\t}\n\tR[4]=R[1];  R[8]=R[2];  R[9]=R[6];  R[12]=R[3];  R[13]=R[7];  R[14]=R[11];\n\t\n\treturn {R:R, m:m, N:N};\n}\nUPNG.quantize.estats = function(stats){\n\tvar R = stats.R, m = stats.m, N = stats.N;\n\t\n\t// when all samples are equal, but N is large (millions), the Rj can be non-zero ( 0.0003.... - precission error)\n\tvar m0 = m[0], m1 = m[1], m2 = m[2], m3 = m[3], iN = (N==0 ? 0 : 1/N);\n\tvar Rj = [\n\t\tR[ 0] - m0*m0*iN,  R[ 1] - m0*m1*iN,  R[ 2] - m0*m2*iN,  R[ 3] - m0*m3*iN,  \n\t\tR[ 4] - m1*m0*iN,  R[ 5] - m1*m1*iN,  R[ 6] - m1*m2*iN,  R[ 7] - m1*m3*iN,\n\t\tR[ 8] - m2*m0*iN,  R[ 9] - m2*m1*iN,  R[10] - m2*m2*iN,  R[11] - m2*m3*iN,  \n\t\tR[12] - m3*m0*iN,  R[13] - m3*m1*iN,  R[14] - m3*m2*iN,  R[15] - m3*m3*iN \n\t];\n\t\n\tvar A = Rj, M = UPNG.M4;\n\tvar b = [0.5,0.5,0.5,0.5], mi = 0, tmi = 0;\n\t\n\tif(N!=0)\n\tfor(var i=0; i<10; i++) {\n\t\tb = M.multVec(A, b);  tmi = Math.sqrt(M.dot(b,b));  b = M.sml(1/tmi,  b);\n\t\tif(Math.abs(tmi-mi)<1e-9) break;  mi = tmi;\n\t}\t\n\t//b = [0,0,1,0];  mi=N;\n\tvar q = [m0*iN, m1*iN, m2*iN, m3*iN];\n\tvar eMq255 = M.dot(M.sml(255,q),b);\n\t\n\treturn {  Cov:Rj, q:q, e:b, L:mi,  eMq255:eMq255, eMq : M.dot(b,q),\n\t\t\t\trgba: (((Math.round(255*q[3])<<24) | (Math.round(255*q[2])<<16) |  (Math.round(255*q[1])<<8) | (Math.round(255*q[0])<<0))>>>0)  };\n}\nUPNG.M4 = {\n\tmultVec : function(m,v) {\n\t\t\treturn [\n\t\t\t\tm[ 0]*v[0] + m[ 1]*v[1] + m[ 2]*v[2] + m[ 3]*v[3],\n\t\t\t\tm[ 4]*v[0] + m[ 5]*v[1] + m[ 6]*v[2] + m[ 7]*v[3],\n\t\t\t\tm[ 8]*v[0] + m[ 9]*v[1] + m[10]*v[2] + m[11]*v[3],\n\t\t\t\tm[12]*v[0] + m[13]*v[1] + m[14]*v[2] + m[15]*v[3]\n\t\t\t];\n\t},\n\tdot : function(x,y) {  return  x[0]*y[0]+x[1]*y[1]+x[2]*y[2]+x[3]*y[3];  },\n\tsml : function(a,y) {  return [a*y[0],a*y[1],a*y[2],a*y[3]];  }\n}\n\nUPNG.encode.concatRGBA = function(bufs) {\n\tvar tlen = 0;\n\tfor(var i=0; i<bufs.length; i++) tlen += bufs[i].byteLength;\n\tvar nimg = new Uint8Array(tlen), noff=0;\n\tfor(var i=0; i<bufs.length; i++) {\n\t\tvar img = new Uint8Array(bufs[i]), il = img.length;\n\t\tfor(var j=0; j<il; j+=4) {  \n\t\t\tvar r=img[j], g=img[j+1], b=img[j+2], a = img[j+3];\n\t\t\tif(a==0) r=g=b=0;\n\t\t\tnimg[noff+j]=r;  nimg[noff+j+1]=g;  nimg[noff+j+2]=b;  nimg[noff+j+3]=a;  }\n\t\tnoff += il;\n\t}\n\treturn nimg.buffer;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UPNG);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@pdf-lib/upng/UPNG.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Courier-Bold.compressed.json":
/*!******************************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/Courier-Bold.compressed.json ***!
  \******************************************************************************/
/***/ ((module) => {

module.exports = "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";

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Courier-BoldOblique.compressed.json":
/*!*************************************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/Courier-BoldOblique.compressed.json ***!
  \*************************************************************************************/
/***/ ((module) => {

module.exports = "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";

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Courier-Oblique.compressed.json":
/*!*********************************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/Courier-Oblique.compressed.json ***!
  \*********************************************************************************/
/***/ ((module) => {

module.exports = "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";

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Courier.compressed.json":
/*!*************************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/Courier.compressed.json ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = "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";

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Helvetica-Bold.compressed.json":
/*!********************************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/Helvetica-Bold.compressed.json ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = "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";

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Helvetica-BoldOblique.compressed.json":
/*!***************************************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/Helvetica-BoldOblique.compressed.json ***!
  \***************************************************************************************/
/***/ ((module) => {

module.exports = "eJyNnVtzG0eyrf8KA0/7RMhzRIq6+U2+zMX2mJYsEuJMzANEtihsgYQMEITaO/Z/P41CV+bKlaug86JQf6uArsrKXNVX8H8m3y9vb7u7+8m3k4t/btazm+7o+PT0xcnRsxdPXzybPJr8dXl3/+vsthsa/L1bPHT386vZN98tF9dn7xfzPzbdrslmseAmR7smR9Bmdjtf9NxqEKbd/Objbve7Dwzb/7ifLeZXr+5uFkPLb45PBrL+6/xLd/3b/P7q4+Tb+9WmezT5/uNsNbu671a/d7vP/vjlvru77q7fLG9nd2Onv/tu+WXy7b+/OX5++uibk5MXj46Pj08fvXx28p9Hk/Oh8Woxv+t+W67n9/Pl3W5Xjx+D8Pbj/OrTXbdeT759OvCLbrUuzSaPH5/85fHjx8NOfl0OQ9gN5/vl5361G8XRf139n6Pjly+ePtr9+7z8+3L378vH5d/nR6+ul++7o9/79X13uz76x93VcvV5uZrdd9d/OTp6tVgcvdl9z/roTbfuVg8D9YDO10ezo/vV7Lq7na0+HS0/HP0yv1ve95+7b4ZGi6NXfzua3V3/3+XqaD58wXrzfj2/ns9W8279l6GzPw67up7f3fx+9bErc1B68vv98JHZ6rqqQ8PvZ5//Pk7J8+MXjybv6tbTJ8NcvFpf7QK9GsUfOtv+5uTx80eT3++v/z6dfHu8E4f/X+z+f/p4P1//7O5X86shoP/+n8n03eTbk+dDo1+Hrqw/z4Y4/u+jPX7y5Mked1+uFrNb46fDPBb+x2Y5xOv9wpSnT5/tlbvN7fvdRN3cZe16uVjMVsZfDBNT+OdudbXL/yo8PznZC7PbQVoP8THJOlx6UGY89/rzbNXdLboPLYk+VrsxW+++cf3JO/5iHO7nxWadu3A1lO0s7+Jj//ljd5ebD0OZL8VI1ovZ+mMO1p/dapnp8q7L8H4rWt5/XHWi7YflZiXo/EG0Xc+/CNg9dGJuuxBTT4f5nUirq+VieZfxurudR8lmYLGzgUS7PzazRcY3q24oZx/ms+PjmjTdulhNVV4+fzrOvci+Vxl9l9H3Gf3ge372fI9+zJ35q3+wpsLf8nf9PSfMP3KYf8of/Dnv8RcvvRryf+YP/pr7dZYH9Ftu9Tp/15v8wd9zv97mD57nD174rJ2OEz3Nrd5ldJn3+K+cfO+HxexTdx9sw0L+ftBinfLnoqdYKs7WV/P51Xx1tbnNs7bZ2fZ6WH+6vMfib6Ez9rFZHs/73Ooqt7rOrURxfsgfvMnoY+7yPKP/znv8lFt5CduScJv3eJfRMqPPouqz1QsLXOdI3Ofv2uQPPuRK2OZWwkl7R7vjnmL6uau7/IqJcPLicc3KVaP9oWy8ny+um0v99XIrzD2szh6x+3Kc5slxXCvuw+7AEH3Wx6zWjg+L5Wou+LprfMvVZjUs41cewJMnWDbreTl0TdGtRy26rG4280G5Xd7rI4edXL74K3IMvSXOh7lg4vhpOJSThwPXs5ubTqTtnuOhGB1w7OauW3Wi9odjodnNavYZTO1pzazhdKITPujhfT9bH4jwYXWljxVsAqI+nBSMnx8Oseef1/O1kIax3n9cbsKxYlr2Q3L7zK1mD6IeZlebe3XoUrz8w6L7krVGZd3OrlbqcOf9qlM7vl7ez65Cxbk0H2YSA2DKCuvQO9tdDyFVx6ibu5vZanO7mG3EbpY3w2HmJ/F1MxwHzMttyFkXXvlhz5PnI1uurj8Mx3nhwNCPUOIi6wcgkfsezmAPz57aHm4Hp9sscBe2sszEYnu9K/r1Wixgi7hjX3kityOSpRjUUJ/DKfGQ9+Ic4h9pSt0JYgb68h/zxpcmOan+dXH2/Ogo96AuF9fzhzkktH8k9swPmEVxeLcbHzo/9KG+EYN1OfeiMoGh5q/0/YVScdyeiBnVg38m9s5ngj7gZwFpJ37OMHgEnIScVCdWA33+5HkVx6seYlfkOr52xjzwUeq4/Ko64OXRytFoqn6kL4djp1Ktb4vGCuFMVgkZooe5Zk/0w9e499OX9dRz+Wd3dyMy903chZ/FqUF6chwskkOZ+4oXEjuabYz1isfq5z85chbVtx+XKzGqM9q7h4GqwE70qOBP6yJGYbNqoh14xPTiVi5wrDflKGcl+htT0KPY4tFWzQRvN4v7+edFL/rVKP+3cYCWSMPx1v18trief/iQ56pvW8OvcT+esCJZvDYOptmBVactXTXGe9eywVbG/BoD5Ish1T9efhuOGPAanJ0CrZafujs8ETJzXHU383U89PUSjZMNy3Gui3qosd4MVR3ORzzYdAxphdmIzLKV6v9qfOBfVOGnL+uxa7nSFa+DWZx/vP+Y4fdNA1wo37Kx3DdMpmuuji3hVevw4UBWxgD7+XKrNHjf5gqtGWktPa1ldN3ac65j2/fBwxJeMetxQbe4FwZ+H0zaPXG7POCIqWv2dbcbMZLGGr6Ux5leC3zwY1ef4hHOiyen4ONDAq+GRF7n7/ud8/W0Tv6isZD8fHD9/SVOnJ9K2H0dZYrJFtwyYpict2r8l9hti8MQtY+zBSwNtch3pyaxwn0u1BJgvhwPmzzVvjKBjVLoWgO6iWaKAxqnVc2qPhv5XR4gWgbLnltCXA820amMbSz531MnbOEitzk1O7+eXymj/SF+ERyYHTrc/ZUOa627jXl7czivD+7rVeM7XzVNOp4O2AzE73EjPnBA+WNruad9+yVieXZnB2TxSMC+7WAp0ASZXx7c02J5s5vvu6UI97Jtppu8jtUMGr6qUck3Bye3g5XcY95I3zu5jtvFnbt80Oye31ruftzs7kb+59Hk525199tsvtrdQ/735NXubvXk0Tenj//zaNzau0dA+35GNJo6wr8NW099a+8qAeHAUDgL33OWu4BLb+A2VYHu6z+g4DxBGUMW2P7qUED7wkH0Omy9HbZe+laNGaIwehfOQyzO6+gBhdEDh9EDraMHRKMHxUYPbKzrwIqdILkYtl7Y1nTYemZbl8PW8bFv1iEhg74D3gybT3yrfhBQiAVw+D6gNRaAKBagWCyAWSyAjbFw8hAyYRu0Pm7lEfW552MjLE1DVBzGqUidc6VWBcrVENWscVm4VT3L380lbFzVsYm5mE2iijauy9pkrm0TqMCNU5VX/jojqHdDVPTOVeVX9TxHlD3AuDICE7MbmESWYFz7gslsDiawQ5gQbaJi8IqKwDAqQtcwxtZhgvCPqoGJGKK6M67sxMR2ZbKxGNfuYjJbjAnsMyZEs6n4ISfkNrfqBWoEQrjQaAboQoaovo2TCzlnF6oKuJAhciHj0oWqepa/m13IuHIhE7MLmUQuZFy7kMnsQiaQCxknF6r8dUbgQobIhZwrF6rqeY4ou5Bx5UImZhcyiVzIuHYhk9mFTGAXMiG6UMXgQhWBC1WELmSMXcgE4UJVAxcyRMVnXLmQie3KZBcyrl3IZHYhE9iFTIguVPFDTshtbtUL1AiEcCEMDVpR5FTpUSRTIpGdKchgT5GTR0VRGlVoctbYH1tWFJVvxRbZvKJODhZFbWOxDXtZVMnQokiuFsTXDQ7+FjmZHInK6UKT88a8sOdFURlfbJHdL+pkgVHUPhjbsBlGlR0xqtEWgwbeGDgYZODoklFgq4yq8MvQAEwzcjKMKCr7jC2+4itspFHUbhrbsKVGlX01qtFcg/bQqItto33f4ofiJ1zXCXouUjIqlMhvg8RuCyJ4LVJyWpSkz0KDM7kf9liUlMOinv0VVXJXlLS3Ygt2VtTIV1EiVwXptaTgqEjJT4Ok3BQanMvYs5OipHwU9eyiqJKHoqQdFFuwf6LG7ola9E5QwDmBgm8CRddEzJ6JmnBMkMEvkVK1o6S8EvWDXsA+iZJ2SWzBHokaOyRq0R9BeZAZvpVte03bkRKuOI4eLdEQmYpxMkPn7IRVARs0RB5oXBpgVc/yd7P1GVe+Z2I2PZPI8YxruzOZvc4EMjrj5HKVv84I/M0QmZtz5WxVPc8RZU8zrgzNxOxmJpGVGdc+ZjKbmAnsYCZE+6oYvKsiMK6K0LWMsWWZIPyqamBWhqj+jCubMrFdmWxQxrU7mczWZAL7kgnRlCp+yAm5za16gRqBEC5U+4o25Iwq3AUyIhDYiUwCK3JGXuSCNCOTz8T3sx25oPzI1WxIrpEjuaAtyXX2JFfIlFwgVzLhtWDgS87ImEBQzmTyuYgve5MLypxcze7kGtmTC9qfXGeDcoUdypVoUcbBo4yBSRlDl3LINuWK8CkTwaicUYG6oKzK1QP1y2blgnYr19muXGG/ciUalvEHkatb0a5XrBUT4Vq1Y+hazsgIXCDXAoFdyyRwLWfkWi5I1zL5THw/u5YLyrVcza7lGrmWC9q1XGfXcoVcywVyLRNeCwau5YxcCwTlWiafi/iya7mgXMvV7FqukWu5oF3LdXYtV9i1XImuZRxcyxi4ljF0LYfsWq4I1zIRXMsZVagLyrVcPVC/7FouaNdynV3LFXYtV6JrGX8QuboV7XrFWjERrrUaf9HDd1cJmUDF5FeG2a1GAbyqEnKqiqVPjeJZ+l72qIqVQ1Ut+1NVyJ0q1t5UVXamysmXKiZXGvHrRMCRKiE/MqzcaBTPUwzZiSpWPlS17EJVIQ+qWDtQVdl/Kmf3qTx6z0jBeUYCvjMSdJ2K2HMqF44zSuA3lVBlVay8pmrNmmOfqVi7TFXZYypnh6k8+stIH1LWbVObPhM9euEqY66jrRiiwjVOxuKcnaUqYC2GyFuMS3Op6ln+brYX48pfTMwGYxI5jHFtMSazx5hAJmOcXKby1xmBzxgio3GunKaq5zmi7DXGldmYmN3GJLIb49pvTGbDMYEdx4RoORWD51QEplMRuo4xth0ThO9UDYzHENWecWU9JrYrk83HuHYfk9l+TGD/MSEaUMUPOSG3uVUvUCMQ2YW+G+iruBU/W1B1DEAipIXrPcRAFkRBKoziU1gITSG1fB3tquvYtyydHIXuAscEc1q7C4imHBQbCDAbCLBxIHvywxj3U9+KbvoDxh2Q8NYfKO5Ao6P+EOIOzLoLbOwukGibP4wl71vTsLUr9Oe+VUcHCLrsdP97bHVyd2T8yTVDo/9i+AxRDI1TII2raJqYQ2oSxdU4B9cEjrAJMcyGKdaVX2Q0zQhCb4jibzxPQpVoJipO01FeCIzTURFPR+U8HZXL6aiimI4q8XRUnqajCmk6qkDTUTFPx8gvMppmhNNREU9H5WI6RomnY8Q0HX8dZ+KFb9VdAarxBxRCDxw6BLQGHJDFGpiFGdgYYSA1uI524zzxrToCQHUEgMIIgMMIgNYRALIRALMRABtHAKSOwFGdrePHhmymRvbTOFUnvhUH+hNOFSAx0J9oqoDGgf4UpgoYDfQnmCogcaA/wUCd2DgdbeJWHuamMaaNHNMmj4kPyUARo92I0W7CaH+e7E95nvhWPC4qSBwEFZ4OggqNB0EFyQPJotDhUWH1fAZQPBbaoXLc8tS27FjIUT2BQRQOj5zj4RFQe000YDtqcuTHRs782MjYcjcC37JIO4qRdo6RdmqRdsSRdsUj7cwi7cgibWgT4r7J+aHOO36eqFOOnyfpbONnkdWgiPzg04ufJ3xmsSO9LVBlKy7RBaWFNryLH+qCBAoBqSoa1CQHhhpQjEjV4aJGHDmSqchIpXqLKiQ/CVSFpFJBsipqk5rkMuUGuWKpBRUvqVzHJHNJRxmqmwQqdFJVzVOTXP7UgJyAVG0K1Ij9gWSyClLJNaK6aSUSewmpXy8k4TDU4GAhNXyHGh0upORGJEdjiiJ4FAlkV6Qm5/plgtfwyla8fLdH4srdTtgd3o+XnXabUztG3W2VC1knvmklDgzr0nH8Bc1BOo2S4H6N55dJurzzy0Rd2fklv6PqiIJw8B1VUzEc+Abni4gwMPkNThZEiKrWilPQW2KfA8Fha7/1+EvMK4ggCRRHVlU0YxuMaVQgslHA+JLCUSZZxDq2aEVctDrcpG+FkuegXcBjg9FecQ4MUfSdq7hXFSNeGcS6IoyyMY6vCSKyVWvFNOgtsc+B4AgaT7EbjtPKCeZT34q3HAqKd4MKEjcgCk/3HgqNtx0KolsKhdHdhMLCjYRC6nrp6K2Z+RnOOaIw3S5chO+Zhq13Ycuv0JxN0sWZs4m6LrOj9dzXd2nnviOqFgPTYIjmwjhNiHE1KybmqTGJ5sc4T5IJPFMmxOkyTHOG6w6FgWevse6QepG/e5rRu4xgWtNCxDxPcJVolivmqQ4vU8F8R06THkWa+Siq6Y8tcg5EnRIhipwNUeWUiGrMi6hRcqT3OlX0OE0Ovdepmlw09jdt8HcNDvmjX2+UYs6koFM6BY1zCl5EgYxCSvmEEmUTSiqXUM+ZhCrlEUqcRahxDqEWMwgVyh96hy3HiXOn/Q5bbnAh9zOV9J2kkDHq1S4h5WwBlXIFFM6U+qYApIkhyhHjlCDGVXaYmFPDJMoL45wUJnBGmBDTwTDlAr7sQ2HgLGi87EPqRf7uaUbvMoJpT+/GMM8TXiWa7Yp5quO5Oc44KzTxLNP8s6zSgNvkbOAWlBQsc26wzinCeswUVilhSH7bjCmnT5JVFlGji+Z+p03lXVOBDGOFEo3lnG/UgtKOVM4+e7of8s4ZZZwLlGsuqCxzNeeXa5RZLnBOucLZ5ErMI+eUQeFFHo4IZ03rRR6WL8T3TwV7JxjkRX7fJQk5F0yjLDDO819PN2H6DdHsG6fJN67m3sQ89SbRzBvniTeB592EOO2GadbxGgSFgee8cQ2C1Iv83dOM3mUE050uSjDPk10lmuuKearrU2Mw1YZoqo3TVBtXU21inmqTaKqN81SbwFNtQpxqwzTV+OAnhYGnuvHgJ6kX+bunGb3LCKY6PSfJPE91lWiqK6ap/m2c5fHJhN9whpHV2UVGT9a5EB6tc+zP1jmDR+gcwjN0Du0hOkd1BoH5czJlK14xKyg+0ViQuKtSeLquVmi8f1IQ3Q8pjG6CFBbufBQS7yr+BvM2Xk3codigy4Oy+4iI9KA6OahwmxBwHmsnxtqJsS5Ditn9PkDika/C062cQuODXgXJh8OLQk9/FRYfCS8oPtv1G1bHGP3XE3zEtGzFR0wLEo+YFp4eMS00PmJakHzEtCj0iGlh9IhpYeER09eeRj6MOrQ9eTPZ382HrfhsTkHi2ZzC07M5hcZncwqSz+YUhZ7NKaxOEaD42NGb0Z9hq2Y+ouDKLpzHrTze88Z4z+V4z/N4eSJBEeM9p2eR3sBEOvFl5M0EHzJ8M64Url3GpfkNrQ8jrVcxYfYNUUiMq7iYmINjEkXIuA6TyRwrEyhBjFOW4HVoRpQvjevQpJ4L1IiVzCET27HibDJ+OFYpr0zg5DIhZli+1G4Icg2vq1Mrzjp1XX2U6oPEkHqGKJzGVThNzOE0icJpXIfTZA6nCZR6xin1Kn8rEKWec5V6VT0XqBErmXomtmPFqWf8cKxS6pnAqWdCTD18tJ0yBFIPH22nVpx66tH2KqXn2E6kwKE98BybbiLCrJ9j02oj5I3n2LTMqaqfY5Pq26bAyXvoeQfZ5rwpHIy5TurY5GsxTwke1f+fmOdkj3JK+ShT4qcHQWSWYhGk50DkJ1JBNJ8C2TcYpruc/b30rfoNgOoZE6AwKcBhD0Br+AFZOIFZDIGNgQNS89eRv6D6FksYkDjVeEvFCjSearwVZQkKnWq8xQIEFE81dmh3jvfCt+K7GgXFdzUKEu9qFJ7e1Sg0vqtREL2rURi9q1FYeFejkPiuxg5dLRc08nru6m12n3jmW3WUgKqxIMJRAodRIoVTV8B18IBs8MBs8M4+9p8/duWc68TYMoxqmWdr2ZiapZyaZZ4aPp0FRUyanc4CyjNkp7OOVnErD2QVvdyFdXc7z1O+CaW4yfaxaXjFRnrFJnsFP5IKinCRjXCRTXKRbZjwPm7lJO1z5uG7iC8JURDSu4jMVYTUu4gsUazyu4gscGz4XUTG5LV4/H5KiFxXH7+zmP03Hb8z106cj99ZIE9Ox+/EwcUMUa0YJ582rhzBxGwLJpE3GGcbMIEN3ITo4obJKPy1z4UKHZl6xV2uBbZ34+TxzoXRm9iuOWX5ppHvG2fzN4FXgCqkZaAKyxwMXhCMH8oBsTSY1MiBxiJhcitFeLkw3kgFXjgqXwnUGLpeR6oqFpMqwYpiqOGocm0xse2cvMoY10uNyS1jTYuOCdpYtznbeoEa5aRWo3Cgj2tSFDiOUeX1Kaoy1rGJiHhswHGPagpvlFOQo0yhjiKvZOlywKkUeFU7cDlANxErnL4coNXGate4HKBlXvn05QCpou1HgYs+qrwiRlV6YmwinDE2YH+MarLBKKf1Msq0akaRDTOo7GgkxnU0vkjXquW0pkaVV1ZS1foam3zNS+RaG1vwihvVtO5GOa2+Qc5rcHzJrhXOtB5H9esZqNbm2OBgBrbW6djocJqmNTuqBxMxrd9BXTWFg2FrrOihjVrXQwNc3aNwcG3SK31s8rXVJ636UW2s/bHR4SUqHwdE+dAStW3VQN8UDlpDPko4n+ATPed4PAAoPsdznlZ+4Ol64jmu8YDomZ3zsJoDC0/qnOO67aja6BMj9EMo9XoyjrXx6o1zGvWhV29czONvvHrjnCPRevXGhRiTxqs3xik66ZWVkTdeWSFOwTr0ygqJKmxfeWWFdArgwVdWSOVQHnhlhTQKqnx7Q0WQwyvf3giUQtt+eyNIKqwH394IKoX0wNsbQeNwNt/eCAqFUrzakGPFYcyvNjiiADZebXCuQtd+tcElClrr1QYXOFz61QbHFCh+JYBCwSFqvhKQFArY4VcCkqzC99VXAlILCuZXXglIOof24CsBSaVAN56F13HlsItn4YFRqFvPwoOgwnvgWXjQKKTNZ+FB4TA2noUHTqFLj45zVDhc9hPbEC5nFC4XKFwuqHC5msPlGoXLBQ6XKxwuV2K4nFO4TKBwGedwjb8cDMGqhEJVMQWqYhWmquUgVYVCVDEHqHIOT+UxOJVSaEZMgRkpheViDMkL34qnKxcYCkDibO+CQgA0ntddhKEDo2sIFzBkIPEkbYf8Z5nLVpy5guJZlgtncSumQkFivgtPc11onOeC5O8FF4Vmv7B6fgooTu8O7ab1mW/FU5aCaggAiesWhadTmkLj9YeC6KJDYXSlobBxxoDUETiKp7MXk/SI9g7FQXd5cuxKDSI9X52cr3AhBnCexk5MVkdlumN2ccWzc3dB5aVvxVPygsR5eOHp5LvQeMZdkDzNLgqdWxdWcwxQvJR7MclPbe9YvhCxo5sws5ucjZtG6m1k6m1y6vFlBFBEUm5EUm5CUk5H14Ot2Ospuh4gMZApuR7QOJBpcD1g1N0puB6QWEPTCT5wN0XvAiQe85qSdwGND3RNhXeBQo9uTdG7AMUH46ajd536VrwZMEXvAiRuCkzJu4DGy//T4F3A6Fdrp+BdQOK1/Cl41zEQvAo9Ha1r/yNlU7QuQPZ2CaD8C21Tsi6k+HaJ4/gTbdNgXcD87RJjZl0+1GVIuGUukmWjIpayIpa5Iti6QBG1YtYFKBcGXaidknP5vO2c69TGb84FKCaec0w8p5Z4jvhmkyueks48JZ3VlDTSh3rqc933qb4vR8Mbf6npEh0Pmb2RBiy+iAMCvokD2F7FAeZv3AD0V24A1nduANkbac521vfct+KLfJfJ+oCnd/su0foA0cSBYoMBVvMSUO22o5ktsJdofYDizeLLZH3A07HBJVofIDoCuAzWByxcR79E63NUS+gpkFv8ZebL0fte+FY8n7hE70OUzycuyfuQgvcBjqcZl8H7gNFpxuVodDAEczpk6tXMS/I6xPRq5qVwO5T4rc1L9Dtk9Ibm5ST/GPYlWZ7P1yY22oiBbloD3eiBbsRA2fdQUgN150MYX0+9tOv0YAbpJkQS2NP0bYikCndLNyKSkHwu34pICjleuhnBAnhfeseMuXJB9Y4ZS+SHrXfMWGZnTO+YMSePrByM0hC5pXGyTOPKN03M5mkSOahxtlET2EtNiIZqmFzVbzKFUuV7T1wDYHOGyGmNk906F55rYjZel7L7mkYWbJx92AQ24yosxaCTLZsgLctU4VumsXmZ0HAw05ONmcKmbQI7d7qTyILw8CptRPPk5iYcjI/yddNa8Wk5vOnN+GSvN4UMn275VSdU9/yUxs7fvOunGgj/V/f9lJZWAXnnT4m0Fqh7f0KDFQEpLQooqXUB9bw0oEqrA0p6gcAWvEagRssESrRSgASLBVJaL1CiJQMltWqgnhcOVGntQImXD9R4BUEtLiKo0DoSbgYHxxC3iUWBgWkjpTUFJVpWgiRWFtTz4hLUvL6gTEsMSrzKoMYLDWhLHZK03KAmHRUbCFNFmX0VtYa1YpPkrijyAoQar0HqUQGhiZUI1I3+UFqPUPtaANWqhPKBALbWJmxyKIB5hUIxLlJDOU38V0LKlv+uj6F4/8mF3d8k3P+Vh93WNmz5dZ6yFa/zFJSu81TXwx4Zom4Zl32rKnSwom1Gfe4B99d47vTYMey0Ieq0cdnpqkKnK9pm1OcecKeN506HZ5Wg55FT96MoxxCawEAC3zZ43+gfjyuKeXCQ7jA0pDQwlOSwoAEMCuhW0l72iYeDUh5MfcwHRmKIhmFcjqGqMICKthn1uQfcb+O50/bYB/TaGXXbBdlvk6HjxraC9aIf3HcXcuftIQzovDPqvAuy8+HP048dDX+enlkv+sGdl3+eftTGByWg65VQxyuW3Ya/ej12EP7qdSR92jd3V/zV61Gpv0AHvTVE3TUu+4t/JHfsHv6RXEJ97gH3Wf2R3L30fqAL23PZ8uMEQ6qXRfCDm4o24avp7+G9T8cawGXf6O/hvRcHFKDQjdD34fABWPi1ivdjpH2rj1t5DDmOVwP1QOy2PgXtk/oBkasx+LAV93WVgw9CvMV7NXbce9DHmbyijo+0Hgt8zAiGYEj2pqoLgWhExg9/EY0Nj22okzxKdWwzSvbia0YwVEOyh1VdCERDNX74i2io+L4kdZKHqt6XrNJwWrdYzGiwBnG4DnU/TV9IyIN25WtfxwM3pVddToN3JQ9/f3I0WX+eXe0+cjrScsKd/2zNSZYbWvzC4fRscm07LVtX+79dC8hN/Dr493UdqG/ZCB3h0PZ03APu2BDtvfKH/OltRr1A1CPjqVu7ihuDtN85Xko9MfIQPrANW1/CVh+3YkdSfe8pXacfO8IXdk8ifsif32b0JaNeIOqm9KK9RD8+MPaVX08/ifghf36b0ZeMeoGor9JMRkm8JlI7rN4SORHaQ+Prtg3+pcH7FufhtM6qRj1fiBtHJK7BnCTlQX7RVtIvkvaa0igaJ1NV9WtzPAhQeBQgPejv2mr8ReO+gXkoqOWx0Gsh4zj4rZCTiB/y57cZfcmoF4j6q84HR4lfDxg7m94OOCH+IL5iK9gXwXrFqMvyNHDU+Bn9sc/pEf0T4g/iK7aCfRGsV4z6LM/+9tqHCV4kr6SLW/GooKB6LRxR/gHjwtORw57in5R1HH/XuCD69eLC6NeLd2xpRzllKx4yFSROigpPJxqFxpOiguRJUVHopKiweOhVUHw69MMkXIKuiA6dnkQh0Jv9XB37xjhsIONMIYE4APYwIPRpAjrGBkgNAKA6R478pF1cXmheWqjtYKRG4nANxzE7zgM3LY3elRQCk2IcDFMwjFNE8mXgm8Zl4JuDl4EjhDDpH4HQYgzZgZ+A0C1S+No/AKEbxFA2fv5BqxTWqN60wsQhJlUFuv5JzRPfijbDf0hzTz+N7rR33E/oToCifTpPlv0J3QkQ3wNyxR3UmV1VcmR3yvYo/0qGFmgsh34lQzfJI2z8SoZW9bhbv5KhZYpG41cy9uoirM6LsAYv8uq7kOvuIp8HLfJJz6Jx0rNon/TEKwL49fkHIbRAu2r/IMTYoP79l21GvUD09ervz+6l2wle6SxbsdoKEot64akKC42LekFyUS8KLeqF0ZXOwsKVztsJ/tndWxwxIOjdnt5N6k1l24pHXXdpsMDTIdbdJNwwNiT/RsTdJN4eNkZHWXcTvBlcye7g9dS23B7FPRR99+QuPuEVEQWh9XxXFHM4xNNdkevAqGe7osAhyk92BQzByreS71K1M8+xw9+7OyZEsdO/d8dijl36vTvmOnb59+5Y4Njx790RhtjhJRcKBMdOXXKpUvpNoWMpcBwP/KaQbiJiqn9TSKuN+DZ+U0jLKdbyN4WkiHFPS4gMZZqD5hIyNqj3zmAODFH0jau4m5gjbhLF2riOsskcXxM4sibEmOL9xtOI+hwIjmD75uJygnfWlmEd3m35H25ahl0t816WegfoXYRgV3gR90ls1ecP8p7bDrdMVzgVh46kK5xPRPu+8T3cr688NwKHrtg1ebkSKXRLPvsRKHXp4LMfS7xqRwi6glftnsRWff4g96D9FAcdi2MvSIHOkAJ9IqVvfhv3kOXc0XC9kBl0LlwvfELtevFZ7sqBx0bqWQf2IR9MG4Ie4PE1fZD3r46vRwktiRDsviLYfTauZcO4lm3j+jzB84PP+FlA6aygXjBr3WGMekuM9xjpq0x94eqi+3Bfv3T//29On5laP3gdP2S43jMUmt/wjTjGMWrqpm9sEa89Bi3ERYxhFcNiuHZIafVqsNRCZ0WL3dw+E7juUGnxF0tJqzettRq6o1rkya+SF8oQpN2zHrgVJ6yg2ktE1jmA4/X3Z0aug27p4+jG6qFs2aUsR3T9ygR76d2/bBm38kDUlfPCU1EXGk+yC5In2UWhk+zCYvYWFK+c75BdOfew/REarMO419FcVphFiGx+EDZieV9v5ZSN0Mr5Q70wudobHmyEC7KraHcjtNvtPjRDNFHGKe2cc+7RvfwayYqvc0tORXum2uNiiJLSuIymPSFF6Vn5UqDG+GW2mphT1iTKW+M6eU3mDDaB0tg45TI/O8HT8Eduus6B4/w2TknunDPdlUMT5LltRCU+nDRQJveZxDoQ5wKjYs9zeEQMUTIYp2JwzsXAf6niWcTXuSUXQ+VQDIaoGIzLWKc/HkE7WArUGL8sBvW3I1iiYmj97QiWuRjS345gTsWQ/nYETcMfuek6B46LwTgVg3MuBlcOTZDnuxFVDHAFizK5zyQWg7h8VZVwUQVLIgqcGFHl8iA1FUnUqVSCeN36VCqbfL/uqRK4hA7er1NtUjmlG1xaOBhBXWDNG1y6ARfbwRtculEqPH2DS6tchEFNpRjUP1ofW7emIRVnVLlESU2FSvrX0wDqMnJZunwpVFZf3+JUzK3roHs9Xi+qYUKUH0j0gATYuAcILzXSKfC4Vf525/iinyF/1oc43SIP6oWdQlUyTWT3JyjHM3NDlhzE8UJAlOrZ+3ha/iKLo7LP32EOJ+5oZSsWeEHVbACJQi88VXehsaQLohItjO4nFhbuJxYS6/MBrPeFkZ1/PfGteNPxITkV8HQz8gE9CRAZESj+i2vOquUAquNwRD9dtoWhnRpxa95mP942THgrnXeb7Xbb9NitMNZtdtNtttAtDm0/kN0VZL/vULbirY2C4nN1e5RvdBSe7nHsaXquruB416MguqNRGN3M6MdaeeJbMa96rBVAItV6qhWgMdX6UCvA/Cf8nI21AiTmWJ9qpQ/z0Od56PM89I156OU89Hoe+jwPvZiHvjEPpQie2pYVgaNYBM6xCJxaETjiInDFi8CZFYEjKwJDVN91QcSsMkSpZZzyy7hKMhNzpplE6Wacc84ETjwTYvYZphTkyywUCkgHQ5SRxiktnYvcNDEnqEs5S02jVDXO+WoCJ224dvM0IkrfxrUbEnMi52s3xHVKi2s3JFBy52s3kXOaw006yHSklOwoUb6jpFIe9Zz1qFLio8S5jxqnP2qxAlChIsjvgohYQbohpWpAiQoiSKImUM9lEdRcGShTcaDE9YEalwhoUCVIqVBQUrWCei4XVKliUNJFgy24blCj0kGJqke8BjQmxZ8TeI75T8gBIOGJ5T95xgHzedafNL9Aw1PIf+JsAorPG/8JPs4kdppdPOHcfeHhSUkDYQdnTENK/j3yerLZZRLHZTiOy3Eel2lpXK6kcZkUx2WYxmWcxoUvSXWaxvEFKY4xSnmcQU9jjWoab5DjmINE4w4ajP0///v/AGoZ428=";

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Helvetica-Oblique.compressed.json":
/*!***********************************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/Helvetica-Oblique.compressed.json ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = "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";

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Helvetica.compressed.json":
/*!***************************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/Helvetica.compressed.json ***!
  \***************************************************************************/
/***/ ((module) => {

module.exports = "eJyNnVtzG8mxrf+KAk/nRGh8eBWleZPnItsaj0ZXWNvhB5BsUdgE0TLAFgjt2P/9AI2uzJUrV7X8olB/q4CuyspaVX0p8H8mP7V3d83yfvLj5MPfu/Xspnl0enH05Nmjs6dHz84mjye/tsv732d3za7AX5rF1+Z+fjXb426xUHh2N19shTBt5jef92f5e3M97+525K/3s8X86vnyZrEre7Q7Xv86f2iu/5jfX32e/Hi/6prHk58+z1azq/tm9bbZf/aXh/tmed1cv2nvZsuhbn/+c/sw+fGfPxw/efL4h5OT88fHR0dHj5+dHv/r8eT9rvBqMV82f7Tr+f28XU5+/GEng/Du8/zqdtms15Mfz3f8Q7Na98UmR0cnf9p90e4kv7e7Juyb81P7Zbvat+LR/7n6v4+Onz09f7z/96L/99n+32dH/b8Xj55ft5fNo7fb9X1zt3701+VVu/rSrmb3zfWfHj16vlg8erP/nvWjN826WX3dUQvVo/n60ezR/Wp23dzNVreP2k+Pfpsv2/vtl+aHXaHFo+cvHs2W1/+vXT2a775g3V2u59fz2WrerP+0q+wvu1Ndz5c3b68+N30f9DV5e7/7yGx1XdRdwZ9mX/4ydMnF8dPHk3+Uo/OT08eT5+urfaBXg/hzY8c/nBxdPJ68vb/+y3QnPun/+2H336dPD7319+Z+Nb/ahfOf/zOZ/mPy48nFTvh9V5H1l9kuiv/7mHDzcLWY3Rk/PT8/8H937S5alwtTzs+fHJRld3e576abZdau28VitjL+dNctPf/SrK72SV6EJ08uDsLsbietd9Hxmp2cQA36/vbanZ4O3zdbNctF86km0cdKNWbr/Teub73iT8+GTy26dQ7O1W5szvIpPm+/fG6WufiuKfP2OvP1Yrb+nIP1rVm1mbbLJsP7jSh5/3nViLKf2m4l6PyrKLuePwjYfG1E3zYhpp4O86VIq6t20YoIrZu7eZSsBxZ7E0i0+Xc3W2R8s2p2g1k0899ds+6NpijHR8dDRs9E+j3P6M+GLkom/pTRz/mDvzg6Pj6gX/2DJQIv8nf9Jcfpr96yvV3u0d/yGV/m9v/mY69k69/zGX/P9XqVv/6PXOp1/q43+YNvcyTe5Q++zx/8YOjZ2dDT01zqHxl9zGf8rxzVy91cdtvcB99wcafFgcqfi6Zy9sRM5Wo+v5qvrrq73B/d3rXXu+kHxkgwuFAZ+9gso8ucElfCgMW4zQ36lEvdZPQ5V3me0X/net3mUouclyJawnWE730Rwz6b9CrXSzi8iH2XP/g1Z+8ml3rIaJvRN6jqmedXTISTJ0clK1eV8jEbzRn7bLyfL66bHJLDXH/dbkScw/TsU8F9v0zz5DguI+7Tfl2IRmuf2arJ49OiXc0FXzeVb7nqVrt5/MoDePzsGIbNet6vW1MTy7JFD6ubbr5T7tp7vXTYy/0Xf0em0Jee/TQXTCygdis5uR64nt3cqDntwHEtRiuOfd81qwbG/umFLYZmN6vZFz/b6XnJrN0FRAMZF1ypb+blbD0S4XF1pRcL1gFR7y8ZDrFZLOZf1vO1kHZtvf/cdmGxaG5f5v2Q3N5zq9lXUdnZVXcv8MHLPy2ah6xVRtbd7GrVihNfrhp14uv2fnYVRpxL811PYgDc0HAcemV3l3O7NbdYpHbLm9mqu1vMOnGa9ma3zrwVXzdbhcWT9ctdyFkXnvuyZ3fdOnz56vrTbqEXVoa+QomTrC9AIvczvIIzPDm3M9ztnK5b4CnsamMmprzr/aBfr8UEtogntpRqI7cVSdvksrvxubsi3uW9mGL+mrrUnSBmoE//MW98apKd6l8Xe89XR7kGZbq4nn+dQ0L7R2LNfMEsBodXO37IV3rqQzZFUgxssu4vvmiYQFPzV/r5wlBxXO+IGY0H/0ylhzr6gF8FpJP4NcPOI+Ai5KQ4sWroRXHwq3LTQ5yKXMfXhTEPvJU6Lr+rCvjwqOVoNFVf6cvm2KVU7duisUI4k1VChsxk89fsiTYU5/HsZxdDnRftt2Z5IzL3TTyFX8WNJmc3OkiE6MOrNpGsKm294rb69U+OnJ3m3ed2JVr1is7uYai4wVviZ2USo7DZaOKMtYjpya2/w7Hu+lXOStSXUtCiWONkq8UE77rF/fzLYivqRQ30JA8NPLsolyaz1f18trief/qU+2pbt4bf43k8YceS5ZfRNBuZdbJk6VQZnsuaDdYy5vcYIJ8M6Yvw/ttuxYA34ewSaNXeNku8EDJzXDU383Vc+voQjZ0N03EeF+Yc3W5Uh+sRD3ZlDbmqRKalyPi4rKTUf9EIP3tW1q79ra54I8zi/Mv95wx/SgZoZq586/R4aON9Zd5oqrNjbRZ8Xls+jGRlDLBfL9PQsFsRXClzhVqP1Kae2jS6rg3KPI7t3KPLEp4xy7qgWdyLGz73waTdEzftiCPW43vXiZZQzC1Ucp3pY4FC71eqcYXztNyw6H18l8CrXSKv8/e9Tfn67FnJV72ifTk6//4WO84vJeyxjjLFZAtuGTFMzmvT2W+x2haHXdQ+zxYwNZRBvr80oVvd1hdjLr+MyyZPte90YGUoNLUG3UQzxQYN3ap6VffdW7lAtAyWNT8rPXi9swn10KONXQRWqC2ti+XPzs3Or+dXymh/jl8EC7Ox5e7vsX+8upV+ezOe10p1b60soZ9XTTpeDlgPUJ3NiEcWlL/Upnt2CrtFLBtqC7K4ErBvGx0KlSrcj55p0d7s+3vZinC3dTPtslSG8u6rKiP5ZvyKFmZyj3klfZdyHrebO8u8aHbPr43xX7r948h/PZ68bFbLP2bz1f4h8j8nz/cPqyePfzg9+tfj4ejgHgEd6hnRYOoIX8Sjg6sEhA1D4VU8ylXAqTdw66pAD+M/oOA8QRlCFtjh7lBAh4GD6HU4erc7Oj7xwxK0wEL7QXm/Ozz3oxIAQCEAwCEAQEsAAFEAQLEAABuGdmC9oyD5sDu6sKNp0D7uG3jkh6VJyKDugLsQ1i4nQ1dJhk4mQ5eToasmQyeSobNYABti4eTr7ujMjjbhaBuPSosAQc0HOhTC0WmIQmKcxqlzHqxFeSEQDVvjcuwW9ZVAlVrKoWxi7kKTqB+N6840mXvUBBrjxmmgF/46IxzyxnjcuyAHf5HBAQyRDRhXXmBiNgSTyBWMa2swmf3BBDYJE6JTFAx2UdA0l0LjMMbuYYKwkKJ1ucPYTIyP5aSwFZMqOVkxGJNrOZmsxoToNwWD6RS0yWgrEHmQ8WxEgx+gERmicBonI3LORlSUFwKRERmXRlTUVwJVaimNyMTc6SZRpxvXnW4yd7oJZETGyYgKf50RGpExNiIXpBEVGYzIEBmRcWVEJmYjMomMyLg2IpPZiExgIzIhGlHBYEQFTXMpNCJjbEQmCCMqWpc7jI3I+FhOCiMyqZKTFSMyuZaTyYhMiEZUMBhRQZuMtgKRERnPRoShQTeKnAIbRfIlEtmcgvyixsmmoii9KhR5VeNjDZHWFUvkXIk6JUwUddbEMpw6USVPiyIZWxBfVzhaXBTY50iVZhfKgONFTrYXReV9sUQ2wKiTC0ZRW2Esw34YVTbFqEZnDBrYY+DTSnk0yiiwW0ZVWGYo0FVSgc0zit8dGsJGoz42NCqGGsuMDo1krVGN/ho0MNnANxW+rXHy3Chm43WCtouUOgQlstwgseGC+EJTMluUpNVCgVea1qsuTRb1nEeoUhahpHMIS3AGoUbWihIZK0ivJUVTRcyWGjRpqFAC7BQpmSlKykpRz0aKKtkoStpEsQRbKGpsoKhF+wQFzBPoVJZF40TMtomaME2QO9nNbJgofSfJhVmiWk/yilFiiZEkTyaJWrRIUMAggW4k3WpK1ohSNsah9eiKhijkxskPnbMZFuWFQGSDxqUHFvWVQJVaSuszMaeESZQPxnUymMyZYAJ5nXEyusJfZ4QWZ4z9zQVpbkUGZzNEtmZceZqJ2dBMIjczrq3MZPYxE9jETIgOVjDYV0HTXAqNyxi7lgnCsorW5Q5jszI+lpPCpkyq5GTFoEyu5WSyJhOiLxUMplTQJqOtQORFxrMRlbqiEzmjgLpAXgQCm5FJLxQjO3JB+pHJrxSrVVZakqu5/12jBHBBZ4DrnAKukC+5QMZkwmvB0JocsjeBIs3JdHAnZ2RPLih/cjUblGvkUC5oi3KdPcoVNilXoksZB5syNhXl0KgcslO5IqzKxE50IZuVC6PpKuzKtVq6VgzL9Wq6JstyJXqWcTAtYxvBtoqRb7mQjatUDI3LGQXXBTIuENi4THqhGBmXC9K4TH6lWK2y0rhczZngGmWCCzoTXOdMcIWMywUyLhNeC4bG5ZCNCxRpXKaDcTkj43JBGZer2bhcI+NyQRuX62xcrrBxuRKNyzgYl7GpKIfG5ZCNyxVhXCZ2ogvZuFwYTVdhXK7V0rViXK5X0zUZlyvRuIyDcRnbCLZVjIzLhWxcq+GHPrwKhVBgCybLMsyGNQgvMiGzKlha1SC+ykRXT5pU0XKfF4V6vGDd30Xl3i6crKlgMqYBv04ETakgtiTj0pAGFeyoEDKjgpUVFS0bUVHIhgrWJlRUtqDC2YAKj/YzUDCfgUxTGTSegth2ChemM0hd6h42nIJHEk+YTVF04lWMpqiVxEsmU3i0mIGCwQxkk8g2E7KWgrOxDOmOzmKIQmicvMU5m0tRXghE9mJc+ktRXwlUqaW0GBNzV5tEfW1cd7bJ3NsmkM8YJ6Mp/HVGaDXG2GtckGZTZHAbQ2Q3xpXfmJgNxyRyHOPackxmzzGBTceE6DoFg+0UNM2l0HiMsfOYIKynaF3uMDYf42M5KezHpEpOVgzI5FpOJgsyIXpQwWBCBW0y2gpEPmQ8GdGfh9w89iPvDEMhfsBD9xgtUQNEAQPFYgXMwgRsiJCTw+96Pf7hxMjw010F/QSFTo1YoQGVV+KoZMFcfBj+XLzgVLxYc/qACfSRn3fouXVPfxQ7s0fFxQGFPgKeurynpY8AWU8As54ANvQEkGKpjtwLfh5swLW9Azzzo9I6QFBlpyWA/rUWuQGVToDwGaIYGqdAGlfRNDGH1CSKq3EOrgkcYRNimA1TrAv/kMMwzaUg9IYo/sZzJ3Du0/lSd/T7CGN3FMTdUTh3R+GyO4oouqNI3B2Fp+4oQuqOIlB3FMzdMfAPOQzTXAq7oyDujsJFdwwSd8eAqTt+HXriqR+VUwEq8QcUQg8cKgS0BByQxRqYhRnYEGEgJbiOZrag6I/iCqpHpQWAxHqq52kp1dO4iuoRrZB6RoujnoV1UU9KCxyV3jp25CuM/iguYnoUV/4HlJc0PU+rmQO1bX8Bx/VNj2jt0jNatuxZG49yndtKBVtZwTbXpJWLrF4RdWzjcr9HcaX/K44JiP5qPy7P7cicyVGJPqKw2D8IfxtG2GH18TccYYBKfgIKkQIOkQJaIgXI4gHM+gzYkJ9ASogczeJRbsEst2BWacFMtmCWWzATLZiJFsxSC2a5BXGl9be8xNqjLnyky83sKm3qZJu63Ca+SABFtLYTre1Ca19ODtfh534U7a9Hwut6nryup9HreiQvFnuFXLBnZdQBipb3cvC3Mz+Kc/9L9DdEecZ/Sf6GFPwNcJz+XwZ/A0aT/svB3+Ao17mtVLCVFWxzTdjfQBF1NH8DFNceLyd4Tfxyki6HX6asBp5c+eUkXf++FFkNCrnyy0m+4H054WvdPdmGbNnmrN7m7A0GjOOCBPoyUtVooSLfPTWNIVL1cKJCPLJIpkFGKo23qEJ6kEAZTSoNSFZF6lORPAq4QB6xVIKGDKk8RkjmIR3ltiqMhkWOeSryvYazE5CqTYEKjbedrYJUco2ogoGQQF5CqrIVKpIdhgqQ2ZCqfYcKsQWRzG5EcjSmKG5rI4ztitRkH79NhhvLx35o95SRxdvJrnyIh/vLygs76u+unvmh3csDhjftHMcffc2S8SfGtxas3zAQgFKf/xb2VB8T4zjU9lST/EEwCEvYcXxGjAMkdxyTVgtV0DleuEuXYsGRU7t0B6nca8HwGePwuSDDh7eOmEH48N5RaWm6eZQEEb6i1cIXdA5fEbc5Fhw+4yJ8YXCGIEYlhZJkHdBQ6ENVweAGIYQ4KinQUVbhDiWqQc+lUuiTEcpopm6oGuFQYJimQjcY4w5wQYa+yB8Eg3AXhIE2xiE2QQS3aLWwBp0DWsRtjgUH0XgK327JC3cb+qN4EdujeK3eI3Fd2/N0XdvTeF3bI7p67RldvfYsXL32JF6rvxr6/syP4rLzVe5vFz7YjPpq6GOPyz92R0/s6KP1/yvsZkDQdKflNoKf0m4jDKj4NHSDIeoL49QhxlWvmJi7xiTqH+PcSSZwT5kQu8sw9RnO3xQG7r3K7E0q9CPO3acRQY/idH5BiPpWTeYkUS+nx34DD3snob8jp06PIvV8FFX3xxI5B6JOiRBFzoaockpENeZF1Cg50k5uFT1Ok7F93KoIJEzatnwqOKRO2s58oTglUXUzs9IpnYLGOQWbziCjkFI+oUTZhJLKJdRzJqFKeYQSZxFqnEOoxQxChfKHtqzmOHHu1Des5gKQN7Rj8zRRyBnax3mRKeVLZRdnVilXQOFMKVuCIE0MUY4YpwQxrrLDxJwaJlFeGOekMIEzwoSYDoYpF3BjH4WBs6CyrY9U6H/c8nYaEfQ87oK7IER9rvbAkUS9XTB3dbzNgT3OCnU8y9T/LKs04DI5G7gEJQXLnBusc4qwHjOFVUoYkt9VY8rpk2SVRVQIkokUyClSILVIgQxjhRKN5ZxvVILSjlTOPtvDA3nnjDLOBco1F1SWuZrzyzXKLBc4p1zhbHIl5pFzyqCwY48jwllT26/HMmRK2MJ2SgyyI2xru2BGGSE3tbFGWWCc+79cbkL3G6LeN06db1z1vYm5602injfOHW8C97sJsdsNU6/jbQgKA/d55SYEqdDjeAviNCLob7wrcUGIelvdkyCJ+rpg7urySih0tSHqauPU1cZVV5uYu9ok6mrj3NUmcFebELvaMHU1vthNYeCurrzWTSp0Nb7yfBoRdDW+BX1BiLpavQNNEnV1wdTVfwy9PLyS/gf2MLLSu8jii78g4D0xwPbqLzB/wxegv+ILsLzjC6j0ILBZaJm9oQIovkXYI/HkrufpcV1P4zO6HtEzt57RQ8aehfcEexIfuP0B/Xb81FBjmdQfxYdKPSoJiig/vu55euh0oPDEFXB8ft0jeh7dM3rfbc/aeJTr3FYq2MoKtrkm/DgUFFFHe/AJKD4d/wNHxxD91xN8A74/im/A90i8Ad/z9AZ8T+Mb8D2Sb8D3Cr0B3zN6A75n4Q34N5PDWw8nfhRdu0fCmHuePLmn0Y57JN9h6hXy6J6V+AOKDvxmMN9TP4qvNr7JluvCextEb7CXAIlR/oZ6CWgc5W9EL4FC4/9N6CVgYfy/CXPEmzA9vBmmAdc+xhH0hsx/oOUWJfS+IUoB4yoPTMzJYBJlhHGdFiZzbphACWKcsgRvMp8Sonyp3GQmFTLHEKWPcZVDJuZEMomyybhOKZM5r0zg5DIhZli+j25omgPxMZfirFM3zQepPFmF1DNEqWdcpZ6JOfVMotQzrlPPZE49Eyj1jFPq4QP2U0KUepXH66RC6hmi1DOuUs/EnHomUeoZ16lnMqeeCZx6JsTUwxcIKEOmORAfcylOPfX2QJHS+34nUuA0HHnfTxcRKanf99NqJT0r7/tpmVNVv+8nVUxb/UZDRZUpHMpgIkeB0zmqMqljEZHasQAneFQraR4LpWSPckr5KFPipxc9ZJZOa6H8WPtEGhDVtzwOBd5OeB/k20naB/mWn3US5uK8zYtwKp62OCYhfSQ0iz8WRProLr/xWrY/pGvZntG1bM/UtWwv5GvZHtO1bM/4WraHfC3bw3gt2yO6ln03GNmZH8WR9C5ZFvA0Zt6hOQGSo+NdsCFg8d3Rd2g4jmaxETPRA3YN/sRRrQNmugNmogP8Mhy+V8V/luNvF+L+0at2AT78DpIRPkdVaERr7eI8sEpzG93ccH2OvITBq92IKDQqCp+3Xz43fb2889pYpBXNaWtVb3XVW9FTfOmOkqi+XbtjudxhfvHuxVZ0KBq0Ev1De7hAWTd385wZXSzUidN0tbh1Om6diBu/6IySiFunur3Lyb+Jh1s6FI3BV86G8+EG61Iy7bBOAnux3mOdVBGytMs6Ccmf8z7rpFCw0k5rFsCz09UPc+Xe6uqHJfLx2tUPy+zo6eqHOXl74TPR7OTyJpDVGx/tX2X6ptX6N9m/CdXu5YnAOJmLr3rCwOfFEH+NqmWaIUxgG3JhLFRqwnBNzBom0tRhvBbGPIkUJc0kRWhF4TSnmDDaTjW7mFZLido8Y3qtrWnGMaGSGWnuKcJKsVoI0lTkgpyPiiwmpSJ1oniankwY7QI1UZlW64LalGV6rQvy5GVKZdRuBNsqVmu+mtXCBUeY26KSvjPKaZ6Lso57LKOiH0ukPohynv+ingMedQ57VNOMmG7KnEmBZ8eRmzK6iJgp9U0ZrVZmzcpNGS3zDKpvykh1Vg1ZnlOjzDNrVP+DjJKzbCwxnlF5xo3ydxIqzb5RZael+wLR7eRNA/3F9fbkuTnKyZ1J/n7I5WxNJdScHYvwzB3V8U4Rs3jQ81we5Lb6wTyvR/k/iI6c42OJ8YSszvex1HiE8twf5dG8zOuAIK/qynj48sqAZL0+CIXUKiEU6KofzSuGKP8HnStXD7HEeOdWVxKx1HjnilVF1Ec9aVNVtnVlPHR5zfF+WGic+VGcI9/jggKQmCvf08IBaJwV34cFAjC69/0eFgJA4hT3fsKvH72fpDePypMBbGvaIcWcWq13SLGY2592SDHnSOQdUizEmKQdUsQpOpWdReHxCMZJ7yySIkVsZGeRLJFjp3cWSZGjWNlZJNUYT72zSIkU2bFNNvBoCYOrNtkIiQJb3WQj9BxUtclGSBxQuclGaDGYapNNliiQ9R0o5QEcxjDtQGFO0dM7UFjMcUs7UJhzxPIOFBZirNIOFOIUpcrOjfe8PaGuUMBqOzcqsgrfyM6NSgkKZnXnRkXn0FZ2blRUCvTolgVWOexhy0JiFOq8ZSEJKrxyy0LSKKRiy0JSOIxpy0LiFLrKG/7OOVz2xw6eK0bhcoHC5YIKl6s5XK5RuFzgcLnC4XIlhss5hcsECpdxDtfwA+7PM6FQFUyBKliFqWg5SEWhEBXMASqcw1N4DE6hFJoBU2AGSmH5MITkqR+VcACKvyX6IYUBePot0Q/YfED0W6IfQrOBhd8S/YDNdfQitOdF7LkexSsuF17Fo5gKPRL93fPU1z2N/dwjecuqV6j3exZvTvUodu8e+W/E9kdxu0GPSggAhYYAT5sSeloaAsiqC8x6DNjQY0BKCxyVK9szIOU20HlBsZObnJh2jweRTsxGJma4fwM452sj8rUR+Wp3ZBy18Sg3o63UuZV1bnPl+E4KKKLadssEUB5TcG/EOgTvSXjPdaFnu5yNXSX1Opl6XU49vpkAikjKTiRlF5JyOrjeuR/F17Wm6HqAxC6IKbke0LgLYhpcDxjtx5iC6wGJ2x+mE3x1coreBUi8JDkl7wIaX4ecCu8ChV58nKJ3AYqvOE4nuOdnit4FKNr3NHkX8GTCU/QuQGS10+BdwMIEO0XvclSc6qmRvX7qR6VNgOI7m9NkVMDT7wNN2agAx1/amwajAkY/lzdFo/KGtaFAm5uhfgl6SkYFNFdO/xL0NBgVsPhL0FM0KkdmVN4fXSjQ5YaoXzmckiMBzQ3RP2I4DY4ETMQ//kbhnmzDONnm8bxN4/bjYGTDD2V9RCdDZg80gcW7wCDgrV/Adr8XmD+kBOg3bQGWO7WA7Bmks72lPT23I5sqHcWp0jlOlU5tqnTEU6UrPlU6s6nSkU2Vhvply5k1wh8FAosLso/J1LBs/pWyj2hryGj6+xh8DQuW+AOy+Dsrk/sTIMOyzAvRZxrRXH/4hqzS3ka3Nz5bAy7i0Ig4NCoO/MBsz+ZhOM3juOsRbVb+OHglfEcrgtDWGtzqBreiYekZGUg++ADa4zBgoqN9decMV3eeAF0coZ2wl65mJZ22kk5YSXpeBJJymU65TEcugw8tEuOGpMcWSZDNVA8uksYNzo8ukpLaxw8vEmc/xe2Fg1ml7YXMlb2q7YUskdHWtheyzJabthcyJ/MtHB3KGA9JE8iLjcvxaqoYtKbxyDWBfcmEZE6mkFMbjxfRxmnE8jMr/hpVy+TgJrCNuzAWKmXorglXN7EWx+TvJlTjmJy+CGD3hsjzjbPxF6EV35umABNGg6UmA9Nq8ahNC6bX4pXmBxN4kkhPOVkQ00WRcM4wVvNbPXuYOmKraR4xoTKZmF613TytmEK2S48jFeYWqweSSpMBqTySVDKHRT6UVGJqvHgsqSSeeUCDyQcpzT8oqSkI9TwLoUoTEUp6LsISPB2hRjMSSjQpgYQOjJitAjWanVCSRoIFhJegzHaCGjsGaslkUaT5CiWassLz6mAc4km2+L5K1dP0hRrPYEH7TjjVPBZkMZWhPhLuNKGhNhbuNK2BBjMbUprcUOL5DbRWnyPNcqh9L6BqrkN5JGC1GQ+LpAsiFHniQ43nPvX6hNDEDAgqToKIR2YFPRVigXHnTxMiapU5EYuMTQ55ZkQxTg77ncJfLav2R5tw5D+X3h/F+3M9SvfhimXi9xa0yWibP8inMZ7PNVgwnqugTUbb/EE+l/F8rvDOE5ww8E2Fbyvfw+ePYq4EdCVUAehG0q38Bj45SvnU5SUcOG9Bm4y2+YN8OuP5XPYuBZzM2Eawrfgsn8+FfEJ7GwFOaGwj2FZ8lk/oQj4h/M37k0A2iWzTp/hE4m/eDwr+WeuTiDYZbfMH+VTqz1ofpMsdvdxbix3FFxv2ZGHn7I/ihNej+PwfhPjM6nKS3pC4nOCfGLxEBwcknrRdkl8DjU/aLoU7g0JP2i6DFwMLP5x4GTrncoJPFy4x/oBSXa9wOXZeyG3Qb1Vkr9JdiPOAb3NJ/T1pz+Z5wLe5ZOV70i9blG8y4VaV1t92mPIn6y+zq30Izwfaf3H+OyJHWSYtRypeeBv6aqMijrT90UPQtkGj3uaBNlDdaYa5Pjghn8Zv2OQvfciltrkUV1TN0YOkU8Mw17ZwqC3uRKQvfciltrkU11btTCySeuu5VE2+9axErLxeHkT+UCm/rZRPDaotGwZdXbads8ItorXGafqqjTzBgyy7lWW5HZUVSFH9Mi41A6TUDtCwIYA3+iQPuvRWl06NQS23ht91Hmqb3nUmDi3Iyy9DD7nUNpfiGqsV2SClt16HauW3XlmACoslnLMHUW4rynGl5bJu0NLLp0Pl8sunLECtxTrQ2YMotxXluNZybXjQPk0O96NO/SguwnpU7j4BEu9K9Dwt1Xoa35XoEb0R0TN6I6Jn4Y2InsS3PT5BxJ3sxsOiWa/7mx9PC4wfanJD6Y2cA9INzW/kHGh6I6fHuf38Rk7PRPvbeJTrrF6/6bmsYJtrol+/6RVRR3r9pke5Q+gvcn/KS6AnURjoYdW0v77ot1n6kd0fcWQ38QDFLZQHoVxmrwSirzVO3+1cnUDsD5UCnay2O1Sr4sTxb8yJPy+n/7Lc7TASTvwo+sUtjgRE+XLmlkYCUhgJgONVzm0YCcDoWuZ2GAlwlOvcVirYygq2uSY8EkARdWzjVeItjoQB5V3rWqC2VPasV1TR6PqO9UqB3FeV/epa5ejU9qtLua0Ko2GRvV3fjK4LjDarkg61nehapiSpbEQ/qHeT4QaxH0XP6JH4qfKe411gp/Gnynskf6q8V+inyntGP1Xes/BT5XeDDR1WNXdoQ4Cwdj1dhsYuc2OXlcYuZWOXubHLamOXorFL0dhlamy8T7gMTV/mpvNScaDhkfhRRBSEygNxEnM48uNw4jow4mE4CRyi9Cg8YggWXuJTIDhs6hJ/kMKvIh1FRLGr/CYSiTl2+ReRiOvY5d9DYoFjl34OKWKIXfgxpBgIjp264VCk/FsRR0rgOI79UoQsImJa+Z0IqVbiW/mVCC2nWOsfiVAixj3/RIQKZeqD6g9EDAXKrW3oA0MUfeMq7ibmiJtEsTauo2wyx9cEjqwJMab5ccAS7/1TIDiC6t7/QdqtF+hu/57QDfp2OLF/ZBOO/FqpDavoNq+iW7mKLosTrkq6MUocKoU3Rgk95PNsBaKK1h9etvJW45nQuN7pVuNQ03SrUfGHSgW2NU4N+s4TSljfcZvUrcYsQXvoVmOmD/K0W02pHaMPO1u+OXcWMdcfb84N1cSbc4Qe8nm2AlGF649L29Hd/Kxy3UmGJpCyqSoP1aps6wo1j+XcysrG+zbfgmQB2hRuQTJ7ECfbKkZ1H3myXC42+jt65+cRla9hbhsaicddjSTCqVmy/Y3EfZMjCb7TkYSy3ZGw7XmM/DKjK4EqYSiPCjMvj/2SMiTGs4ivc8nrXcNn95mLajSV6jWV6jVjvdTUeyn+sTDSKj3Y1HqwqfXgp4xEh95UMjDeAmP1c0ZzgSrxnI/FbV6P27wSm3ktNvNabP47I1HqdjQGC4EqDV6Mf5H9ZbXI7zISbVxWzrmsJO1ytC7LyvhuBaqcuB3r3bbeu22ld9OfqSO51vltJaxtpYlfBCrTDNnMv3PRlUCVAK0qPbMa7RkRsXXlBOvKCdZVO12Pnlqh0Q90AlXq2o1lS1fPlq6SLXxdxXItW7qaVXR6ItwvNC7ivL/JyN+XN7TNpWyRwRyaT9K3XKNvlQh/q2TDNzEL6b99apiXXXiReRy/YZMRrLnya2ht5TW0tv4a2heo6Ol5If7wy9A2fIRO8EV/9/AttXeHol4Ty+tIh8dt9FWmnrm6aD7dly89/P+H8wtTywf7Vc5RxrbSyVqopvjKlf6YW5jQLK+UFu0hl1hX8MgJ1yMnRGtT6nerM/TFYZithlX8sR/F20IrXLsDEjeJVrRiBxpvDa3COh0Y3fFZweociL0wb2h4Ke4UwGGwnBvZLdoOg3QFyzUg2B7A3hyAQ2uAlMYAKm1xFA9CRzlfhINUz0XtY4PXOdmtqU7sYBkOhm8FMuQbEjwP8CH4Tr5g3M2SPPCwT8HL7b7zzA/CuZx32JwuhaLTXdapLutSl9HsCULuyy735TBXOviKzduGg6HmQLyKAyx31GAoGqLxaJwGpXE1Mk3Mw9MkGqPGeaCawKPVhDhkDdO49RubmBx8u/M8Yh/GRmJiGBbZYVpKEVNinhimnDBOiQGvGDNR47aIi0x0o+ToNzFaQMHuA4UsM4mOYDjagmPhDSZGg7Db1KmD2SoKz35RFDcHI8o54KEnhaLTUVVGIp54sqJTRftKetzJvJJI0Wbg3j6FZptJdB1xy35Qwt9qIkTWk/5SE3NlPfIPNZFE1pP/TBMLbD3przQRJuuhP9J0yjRYT8FuPUZiPhkW+WRayidTYj4ZpowxThkDuxKYKNMo4iIT3ShpPSZG6ynYraeQZSbRegxH63EsrMfEaD32Mn7qYLYe8beR6BPuLkaU9cA7AxSKTkdVWY94YYAVnSraetLbAswriRStB14VoNBsM4nWI94TKEp4eo0GFAW2oaiyGUVVWlIsIowpFmB7imoyqSgnq4oyGVYU2baCSplKWrSwIIKRRU45GkWVqbFEzteoU9ZGkbMzqpyjQa1yaVmhyKLGx0KhrTAWIUMMIthi4MsaJ4uMIhklicouYxEyzSB+qaRXMtCgChsNOvhl5NJS+VUiGVK21/qLRLrEd1KXDXfsLSJdZjS9kwWrV4ik9rUS4m2NkzXXXh866HlrldhSVd5kGD7C129DobTqJw7foPfWrdPkTRy/obLhci3+lHhS8Hukp5ZP6BEhVfhOHxu0hXM42v/NXCbxbqBjcauviB8Smdr910L2fz7+WSRlpiUMM2RUyo3e4Q7uaRYH5XBf+OswzR/M5ivO7YDKhA4ovoPtHF+8dmpvWzvyt6ed+evizso+Zif26rShkghPrU1Rb3Kb/N16QLpNjWxTfHXecW5qI5raiKa28SjXua1UsJUVbHNN0nvtrog6+hvsjnLs6fd2N2XdeG5H8f2+TVghOorLQufpLcBNWAA68lWfM1/qOSvrOye2qDNUEuqJtckXfRtMKEBxG88mJRTwtL7dcEIBjmvdTUgoYLSq3QwJBUe5zm2lgq2sYJtrkn6GxxVRR//RHUdxQb3BhDoE/2GC2/ceMPiA4va9hxR84Ol52wMHH3DcNPcQgg+Mtu9ti6v6kY1mR+aqjsTOli27qtO4h2UbXdUZbcLZoqs6iRtStji9HhmKBZrcKNqytM226lw2SuxI2gZbdSTayhuOtsVW/SjXWe0n2rKtOs010duFttFWncWNQdtgq4Z8+TJEHx9InBCiNqUHEsxVg9UDCZao6fmBBAvcHfxAgjHFIS1dKRaiKOekcUpM5yOxECnqUs5T0ypxShlrQi1OrUCVBsosNrHeEM5n4zqpTa61hdPbeKVvU6LDK9OQ60gpAihRxqOk4oN6DhGqFCWUOBKocceiFscAKhQq9bsaOVj6AzweUKIhEaTxYImBEdQ8NlCuxzKNENRGYtlqWm+7HC2ojzaQxwxKethgiZE28uBBqZ4UPIS+lfXquR3FNfi3sF4FlNfg33i9ChTXq47j0vxbXK86o6X5t7Je9aNc57ZSwVZWsM01SetVV0Qdfb3qKF4rfMOZiBHVP81EiYvGqZkoSbkf0kzEnFubZyISWoEqDZS9pGYiliqVrXRanolYoO5LM9HAy/0qhaiJxqkPnYumm5ib7lLuQ9MoLMa53SZwHxahFajSQNmHJtYbwn1oXPehybW2cB8apz7EX0GqYWpq0Kg/oyZCEQrkcEQ5923QKWRB47gEkfsZxbaCR4Ig+zwUGG8o933QdP+HImNt5TwIGuTCv/73/wO+9kRf";

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Symbol.compressed.json":
/*!************************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/Symbol.compressed.json ***!
  \************************************************************************/
/***/ ((module) => {

module.exports = "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";

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Times-Bold.compressed.json":
/*!****************************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/Times-Bold.compressed.json ***!
  \****************************************************************************/
/***/ ((module) => {

module.exports = "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";

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Times-BoldItalic.compressed.json":
/*!**********************************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/Times-BoldItalic.compressed.json ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = "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";

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Times-Italic.compressed.json":
/*!******************************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/Times-Italic.compressed.json ***!
  \******************************************************************************/
/***/ ((module) => {

module.exports = "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";

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/Times-Roman.compressed.json":
/*!*****************************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/Times-Roman.compressed.json ***!
  \*****************************************************************************/
/***/ ((module) => {

module.exports = "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";

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/ZapfDingbats.compressed.json":
/*!******************************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/ZapfDingbats.compressed.json ***!
  \******************************************************************************/
/***/ ((module) => {

module.exports = "eJxtmNtu20YQhl+F4FULyMGeD7pz3AY1ChtG7NpFA18w1NomIlECSRcxgrx7SVk7+wOdG8H5OJydf2Z2d5gf9cV+t0v9VK/r+6vXsXlOlbHe28paq229qj/t++m62aXZ4J/m8PRb1z9/baZxefK63Z6eXN5dVMvTCh83u277xr/6kLrnl2XNq7TpXnczuZyabdee98/b2VzM/x4/dd/T5qab2pd6PQ2vaVVfvDRD005puE3Lu7eH1HbN9hTjx4/77/X6y5lcnUmjVzHIVVDicVX/1W/SsO36dLMfu6nb9/X6TAoBD+5euvZbn8axXtuZ36dhPJrVQqgPQoh5hev91LWLkIv94W1Ygq9+aX+tZAx2tfz64284/sblN/rqfLP/mqrbt3FKu7G67Nv9cNgPzZQ2H6rz7bb6vLgZq89pTMO/M/xfEqturJpqSM/d7GJIm2oamk3aNcO3av80O5xh3yyKmm1193ZIT02bqovTKjP+MAf++7zsZvZ3276kYyWWXB0z99S18/PbafPHQ71W4fjn/fxnFO+ZvkrT0LVzTr78qB/+nk38bHM9exgP8zr1z9U7jt6840YW5uSJKcZOCaBBnKgm5mU8MVNYyMwWFvO7Ukagkmgg6sDWQ5yFFqjzUrLEaQ3BEmiwNsMSaZS0vgWfOkPHWQowNeTUc0kumnxZvsgPxlGai6VTGUqAVCTQ6QkWnc77DKEiLktSUBJKqHIQZ86d8gCpHYoiEzMsb1ubYy8vW50DChB5ZhGqrijD0EqUIeiaEHIfCg5Kpuu0ApiToaGPSY0uaQsyr65L2oKi1yFt1PLaQ3lzfXTgXodGoJYzglndSLDMPg1sTPJpQJHJigw0QrGERqD9YhyTOgONQDUyuF1zaxuokc/BW2ztXCMrGZ9WMW1oQZHIXWNBkSCfRZEL5BMUiZw6CzVSFCfUSGZFNjIldoKDkonTKQiJIGzWmFd3BizJJ9SINoLDriOfUCOZS+zg+KGD1qGiLNMLxtJD1/ns00ON6EzyUCM6vbxhoBKaqbG3DFQCNiL1iHccBPV0DHhQH/JW8EW90dkyFKGywCJU0WkVSvSGeiSUODWFFD0HYdPQVoiRgfPMA+/nnRgiAyNYSjpWNQcNSMrtFCUH4ZIRpSCWocFCSuhCEY6hoUClc0WC52BJlCYYLQdhN+hygRRRlo5BKRRLS6oihSqh+ZzzRGG1Mo4Iz1LoP0qsxDGFzk0JE42ji0jCPejomJKCuwil4m5CiRMEUMVSzVLDUstSx1Juc0oVWMpqY295qVltmtWmWW2a1aZZbZrVplltmtWmWW2G1WZYbYbVZlhthtVmWG2G1WZYbYbVZlhtltVmWW2W1WZZbZbVZlltltVmWW2W1QYjQCh7E2aAQHeGhCFgPoNoy8KNb2wxBhmGKBxoUZXlLGsLI6AsftEDHV0wIURVbANLcTKlGGBIKPOAxCmhePCKUwFzAmpDFRQvjA9R06Hq8TONvshgKDCuRAZTXigUxjxNFfKRo3CLhnIJBMFRvMZpqpNBMlQJzGT5WFQMVQI/AikPMIhEU1aDjqJvQwmjSHB05cC9jbYwc5UtAHNLhDw41ha+lEqF4JaH3gmB61SYcqInxTDmQK8v08vjqv4zDf1N0w3Lf4A8/vwPpfK11w==";

/***/ }),

/***/ "(ssr)/./node_modules/@pdf-lib/standard-fonts/es/all-encodings.compressed.json":
/*!*******************************************************************************!*\
  !*** ./node_modules/@pdf-lib/standard-fonts/es/all-encodings.compressed.json ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = "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";

/***/ })

};
;