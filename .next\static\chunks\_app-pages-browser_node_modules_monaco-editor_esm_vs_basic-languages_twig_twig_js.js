"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_twig_twig_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/twig/twig.js":
/*!************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/twig/twig.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/twig/twig.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\$\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\s]+)/g,\n  comments: {\n    blockComment: [\"{#\", \"#}\"]\n  },\n  brackets: [\n    [\"{#\", \"#}\"],\n    [\"{%\", \"%}\"],\n    [\"{{\", \"}}\"],\n    [\"(\", \")\"],\n    [\"[\", \"]\"],\n    // HTML\n    [\"<!--\", \"-->\"],\n    [\"<\", \">\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{# \", close: \" #}\" },\n    { open: \"{% \", close: \" %}\" },\n    { open: \"{{ \", close: \" }}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    // HTML\n    { open: \"<\", close: \">\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \"\",\n  ignoreCase: true,\n  keywords: [\n    // (opening) tags\n    \"apply\",\n    \"autoescape\",\n    \"block\",\n    \"deprecated\",\n    \"do\",\n    \"embed\",\n    \"extends\",\n    \"flush\",\n    \"for\",\n    \"from\",\n    \"if\",\n    \"import\",\n    \"include\",\n    \"macro\",\n    \"sandbox\",\n    \"set\",\n    \"use\",\n    \"verbatim\",\n    \"with\",\n    // closing tags\n    \"endapply\",\n    \"endautoescape\",\n    \"endblock\",\n    \"endembed\",\n    \"endfor\",\n    \"endif\",\n    \"endmacro\",\n    \"endsandbox\",\n    \"endset\",\n    \"endwith\",\n    // literals\n    \"true\",\n    \"false\"\n  ],\n  tokenizer: {\n    root: [\n      // whitespace\n      [/\\s+/],\n      // Twig Tag Delimiters\n      [/{#/, \"comment.twig\", \"@commentState\"],\n      [/{%[-~]?/, \"delimiter.twig\", \"@blockState\"],\n      [/{{[-~]?/, \"delimiter.twig\", \"@variableState\"],\n      // HTML\n      [/<!DOCTYPE/, \"metatag.html\", \"@doctype\"],\n      [/<!--/, \"comment.html\", \"@comment\"],\n      [/(<)((?:[\\w\\-]+:)?[\\w\\-]+)(\\s*)(\\/>)/, [\"delimiter.html\", \"tag.html\", \"\", \"delimiter.html\"]],\n      [/(<)(script)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@script\" }]],\n      [/(<)(style)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@style\" }]],\n      [/(<)((?:[\\w\\-]+:)?[\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/(<\\/)((?:[\\w\\-]+:)?[\\w\\-]+)/, [\"delimiter.html\", { token: \"tag.html\", next: \"@otherTag\" }]],\n      [/</, \"delimiter.html\"],\n      [/[^<{]+/]\n      // text\n    ],\n    /**\n     * Comment Tag Handling\n     */\n    commentState: [\n      [/#}/, \"comment.twig\", \"@pop\"],\n      [/./, \"comment.twig\"]\n    ],\n    /**\n     * Block Tag Handling\n     */\n    blockState: [\n      [/[-~]?%}/, \"delimiter.twig\", \"@pop\"],\n      // whitespace\n      [/\\s+/],\n      // verbatim\n      // Unlike other blocks, verbatim ehas its own state\n      // transition to ensure we mark its contents as strings.\n      [\n        /(verbatim)(\\s*)([-~]?%})/,\n        [\"keyword.twig\", \"\", { token: \"delimiter.twig\", next: \"@rawDataState\" }]\n      ],\n      { include: \"expression\" }\n    ],\n    rawDataState: [\n      // endverbatim\n      [\n        /({%[-~]?)(\\s*)(endverbatim)(\\s*)([-~]?%})/,\n        [\"delimiter.twig\", \"\", \"keyword.twig\", \"\", { token: \"delimiter.twig\", next: \"@popall\" }]\n      ],\n      [/./, \"string.twig\"]\n    ],\n    /**\n     * Variable Tag Handling\n     */\n    variableState: [[/[-~]?}}/, \"delimiter.twig\", \"@pop\"], { include: \"expression\" }],\n    stringState: [\n      // closing double quoted string\n      [/\"/, \"string.twig\", \"@pop\"],\n      // interpolation start\n      [/#{\\s*/, \"string.twig\", \"@interpolationState\"],\n      // string part\n      [/[^#\"\\\\]*(?:(?:\\\\.|#(?!\\{))[^#\"\\\\]*)*/, \"string.twig\"]\n    ],\n    interpolationState: [\n      // interpolation end\n      [/}/, \"string.twig\", \"@pop\"],\n      { include: \"expression\" }\n    ],\n    /**\n     * Expression Handling\n     */\n    expression: [\n      // whitespace\n      [/\\s+/],\n      // operators - math\n      [/\\+|-|\\/{1,2}|%|\\*{1,2}/, \"operators.twig\"],\n      // operators - logic\n      [/(and|or|not|b-and|b-xor|b-or)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - comparison (symbols)\n      [/==|!=|<|>|>=|<=/, \"operators.twig\"],\n      // operators - comparison (words)\n      [/(starts with|ends with|matches)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - containment\n      [/(in)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - test\n      [/(is)(\\s+)/, [\"operators.twig\", \"\"]],\n      // operators - misc\n      [/\\||~|:|\\.{1,2}|\\?{1,2}/, \"operators.twig\"],\n      // names\n      [\n        /[^\\W\\d][\\w]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword.twig\",\n            \"@default\": \"variable.twig\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d+(\\.\\d+)?/, \"number.twig\"],\n      // punctuation\n      [/\\(|\\)|\\[|\\]|{|}|,/, \"delimiter.twig\"],\n      // strings\n      [/\"([^#\"\\\\]*(?:\\\\.[^#\"\\\\]*)*)\"|\\'([^\\'\\\\]*(?:\\\\.[^\\'\\\\]*)*)\\'/, \"string.twig\"],\n      // opening double quoted string\n      [/\"/, \"string.twig\", \"@stringState\"],\n      // misc syntactic constructs\n      // These are not operators per se, but for the purposes of lexical analysis we\n      // can treat them as such.\n      // arrow functions\n      [/=>/, \"operators.twig\"],\n      // assignment\n      [/=/, \"operators.twig\"]\n    ],\n    /**\n     * HTML\n     */\n    doctype: [\n      [/[^>]+/, \"metatag.content.html\"],\n      [/>/, \"metatag.html\", \"@pop\"]\n    ],\n    comment: [\n      [/-->/, \"comment.html\", \"@pop\"],\n      [/[^-]+/, \"comment.content.html\"],\n      [/./, \"comment.content.html\"]\n    ],\n    otherTag: [\n      [/\\/?>/, \"delimiter.html\", \"@pop\"],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/]\n      // whitespace\n    ],\n    // -- BEGIN <script> tags handling\n    // After <script\n    script: [\n      [/type/, \"attribute.name.html\", \"@scriptAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(script\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <script ... type\n    scriptAfterType: [\n      [/=/, \"delimiter.html\", \"@scriptAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type =\n    scriptAfterTypeEquals: [\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@scriptWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ],\n      // cover invalid e.g. <script type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <script ... type = $S2\n    scriptWithCustomType: [\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@scriptEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/script\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    scriptEmbedded: [\n      [/<\\/script/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^<]+/, \"\"]\n    ],\n    // -- END <script> tags handling\n    // -- BEGIN <style> tags handling\n    // After <style\n    style: [\n      [/type/, \"attribute.name.html\", \"@styleAfterType\"],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [\n        /(<\\/)(style\\s*)(>)/,\n        [\"delimiter.html\", \"tag.html\", { token: \"delimiter.html\", next: \"@pop\" }]\n      ]\n    ],\n    // After <style ... type\n    styleAfterType: [\n      [/=/, \"delimiter.html\", \"@styleAfterTypeEquals\"],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type =\n    styleAfterTypeEquals: [\n      [\n        /\"([^\"]*)\"/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        /'([^']*)'/,\n        {\n          token: \"attribute.value.html\",\n          switchTo: \"@styleWithCustomType.$1\"\n        }\n      ],\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded\",\n          nextEmbedded: \"text/css\"\n        }\n      ],\n      // cover invalid e.g. <style type=>\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    // After <style ... type = $S2\n    styleWithCustomType: [\n      [\n        />/,\n        {\n          token: \"delimiter.html\",\n          next: \"@styleEmbedded.$S2\",\n          nextEmbedded: \"$S2\"\n        }\n      ],\n      [/\"([^\"]*)\"/, \"attribute.value.html\"],\n      [/'([^']*)'/, \"attribute.value.html\"],\n      [/[\\w\\-]+/, \"attribute.name.html\"],\n      [/=/, \"delimiter.html\"],\n      [/[ \\t\\r\\n]+/],\n      // whitespace\n      [/<\\/style\\s*>/, { token: \"@rematch\", next: \"@pop\" }]\n    ],\n    styleEmbedded: [\n      [/<\\/style/, { token: \"@rematch\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/[^<]+/, \"\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/twig/twig.js\n"));

/***/ })

}]);