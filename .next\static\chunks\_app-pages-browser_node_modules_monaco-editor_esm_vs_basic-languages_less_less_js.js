"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_less_less_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/less/less.js":
/*!************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/less/less.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/less/less.ts\nvar conf = {\n  wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|([@#!.:]?[\\w-?]+%?)|[@#!.]/g,\n  comments: {\n    blockComment: [\"/*\", \"*/\"],\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/\"),\n      end: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".less\",\n  identifier: \"-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  identifierPlus: \"-?-?([a-zA-Z:.]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-:.]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@nestedJSBegin\" },\n      [\"[ \\\\t\\\\r\\\\n]+\", \"\"],\n      { include: \"@comments\" },\n      { include: \"@keyword\" },\n      { include: \"@strings\" },\n      { include: \"@numbers\" },\n      [\"[*_]?[a-zA-Z\\\\-\\\\s]+(?=:.*(;|(\\\\\\\\$)))\", \"attribute.name\", \"@attribute\"],\n      [\"url(\\\\-prefix)?\\\\(\", { token: \"tag\", next: \"@urldeclaration\" }],\n      [\"[{}()\\\\[\\\\]]\", \"@brackets\"],\n      [\"[,:;]\", \"delimiter\"],\n      [\"#@identifierPlus\", \"tag.id\"],\n      [\"&\", \"tag\"],\n      [\"\\\\.@identifierPlus(?=\\\\()\", \"tag.class\", \"@attribute\"],\n      [\"\\\\.@identifierPlus\", \"tag.class\"],\n      [\"@identifierPlus\", \"tag\"],\n      { include: \"@operators\" },\n      [\"@(@identifier(?=[:,\\\\)]))\", \"variable\", \"@attribute\"],\n      [\"@(@identifier)\", \"variable\"],\n      [\"@\", \"key\", \"@atRules\"]\n    ],\n    nestedJSBegin: [\n      [\"``\", \"delimiter.backtick\"],\n      [\n        \"`\",\n        {\n          token: \"delimiter.backtick\",\n          next: \"@nestedJSEnd\",\n          nextEmbedded: \"text/javascript\"\n        }\n      ]\n    ],\n    nestedJSEnd: [\n      [\n        \"`\",\n        {\n          token: \"delimiter.backtick\",\n          next: \"@pop\",\n          nextEmbedded: \"@pop\"\n        }\n      ]\n    ],\n    operators: [[\"[<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~]\", \"operator\"]],\n    keyword: [\n      [\n        \"(@[\\\\s]*import|![\\\\s]*important|true|false|when|iscolor|isnumber|isstring|iskeyword|isurl|ispixel|ispercentage|isem|hue|saturation|lightness|alpha|lighten|darken|saturate|desaturate|fadein|fadeout|fade|spin|mix|round|ceil|floor|percentage)\\\\b\",\n        \"keyword\"\n      ]\n    ],\n    urldeclaration: [\n      { include: \"@strings\" },\n      [\"[^)\\r\\n]+\", \"string\"],\n      [\"\\\\)\", { token: \"tag\", next: \"@pop\" }]\n    ],\n    attribute: [\n      { include: \"@nestedJSBegin\" },\n      { include: \"@comments\" },\n      { include: \"@strings\" },\n      { include: \"@numbers\" },\n      { include: \"@keyword\" },\n      [\"[a-zA-Z\\\\-]+(?=\\\\()\", \"attribute.value\", \"@attribute\"],\n      [\">\", \"operator\", \"@pop\"],\n      [\"@identifier\", \"attribute.value\"],\n      { include: \"@operators\" },\n      [\"@(@identifier)\", \"variable\"],\n      [\"[)\\\\}]\", \"@brackets\", \"@pop\"],\n      [\"[{}()\\\\[\\\\]>]\", \"@brackets\"],\n      [\"[;]\", \"delimiter\", \"@pop\"],\n      [\"[,=:]\", \"delimiter\"],\n      [\"\\\\s\", \"\"],\n      [\".\", \"attribute.value\"]\n    ],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [\".\", \"comment\"]\n    ],\n    numbers: [\n      [\"(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?\", { token: \"attribute.value.number\", next: \"@units\" }],\n      [\"#[0-9a-fA-F_]+(?!\\\\w)\", \"attribute.value.hex\"]\n    ],\n    units: [\n      [\n        \"(em|ex|ch|rem|fr|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?\",\n        \"attribute.value.unit\",\n        \"@pop\"\n      ]\n    ],\n    strings: [\n      ['~?\"', { token: \"string.delimiter\", next: \"@stringsEndDoubleQuote\" }],\n      [\"~?'\", { token: \"string.delimiter\", next: \"@stringsEndQuote\" }]\n    ],\n    stringsEndDoubleQuote: [\n      ['\\\\\\\\\"', \"string\"],\n      ['\"', { token: \"string.delimiter\", next: \"@popall\" }],\n      [\".\", \"string\"]\n    ],\n    stringsEndQuote: [\n      [\"\\\\\\\\'\", \"string\"],\n      [\"'\", { token: \"string.delimiter\", next: \"@popall\" }],\n      [\".\", \"string\"]\n    ],\n    atRules: [\n      { include: \"@comments\" },\n      { include: \"@strings\" },\n      [\"[()]\", \"delimiter\"],\n      [\"[\\\\{;]\", \"delimiter\", \"@pop\"],\n      [\".\", \"key\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/less/less.js\n"));

/***/ })

}]);