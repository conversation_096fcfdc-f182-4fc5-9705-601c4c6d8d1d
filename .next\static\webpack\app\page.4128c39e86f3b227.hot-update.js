"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LoadingScreen */ \"(app-pages-browser)/./src/components/LoadingScreen.tsx\");\n/* harmony import */ var _components_ArtworkGrid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ArtworkGrid */ \"(app-pages-browser)/./src/components/ArtworkGrid/index.tsx\");\n/* harmony import */ var _components_FontManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/FontManager */ \"(app-pages-browser)/./src/components/FontManager/index.tsx\");\n/* harmony import */ var _components_SVGDecorations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/SVGDecorations */ \"(app-pages-browser)/./src/components/SVGDecorations/index.tsx\");\n/* harmony import */ var _services_artworkService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/artworkService */ \"(app-pages-browser)/./src/services/artworkService.ts\");\n/* harmony import */ var _services_database_DatabaseService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/database/DatabaseService */ \"(app-pages-browser)/./src/services/database/DatabaseService.ts\");\n/* harmony import */ var _services_fontPersistenceService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/fontPersistenceService */ \"(app-pages-browser)/./src/services/fontPersistenceService.ts\");\n/* harmony import */ var _components_common_CreateArtworkDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/common/CreateArtworkDialog */ \"(app-pages-browser)/./src/components/common/CreateArtworkDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [artworks, setArtworks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingArtworks, setIsLoadingArtworks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFontManager, setShowFontManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [colorScheme, setColorScheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [appliedFont, setAppliedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const artworkService = _services_artworkService__WEBPACK_IMPORTED_MODULE_6__.ArtworkService.getInstance();\n    const databaseService = _services_database_DatabaseService__WEBPACK_IMPORTED_MODULE_7__.DatabaseService.getInstance();\n    const persistenceService = _services_fontPersistenceService__WEBPACK_IMPORTED_MODULE_8__.FontPersistenceService.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 应用初始化过程\n        const initializeApp = async ()=>{\n            try {\n                console.log(\"\\uD83C\\uDFA8 初始化宫崎骏风格作品展示平台...\");\n                // 初始化IndexedDB\n                const dbResult = await databaseService.initialize();\n                if (!dbResult.success) {\n                    throw new Error(dbResult.error || \"IndexedDB初始化失败\");\n                }\n                // 恢复字体应用状态（字体持久化功能）\n                try {\n                    console.log(\"\\uD83D\\uDD04 尝试恢复字体应用状态...\");\n                    // 延迟一点时间确保DOM完全加载\n                    await new Promise((resolve)=>setTimeout(resolve, 300));\n                    const restoreResult = await persistenceService.restoreFontApplication();\n                    if (restoreResult.success && restoreResult.data) {\n                        console.log(\"✅ 字体应用状态已恢复\");\n                        // 获取当前应用的字体配置\n                        const configResult = await persistenceService.getActiveConfig();\n                        if (configResult.success && configResult.data) {\n                            const fontFamily = configResult.data.fontFamily;\n                            setAppliedFont(fontFamily);\n                            console.log(\"\\uD83C\\uDFAF 当前应用的字体:\", fontFamily);\n                            // 立即更新CSS变量\n                            document.documentElement.style.setProperty(\"--font-family-applied\", \"'\".concat(fontFamily, \"', sans-serif\"));\n                            document.documentElement.style.setProperty(\"--font-family-handwritten\", \"'\".concat(fontFamily, \"', cursive, var(--font-family-primary)\"));\n                            // 强制应用到所有元素\n                            const applyFontToAllElements = ()=>{\n                                // 应用到body\n                                document.body.style.setProperty(\"font-family\", \"'\".concat(fontFamily, \"', sans-serif\"), \"important\");\n                                // 白名单方式：只对指定的元素应用用户字体\n                                const whitelistSelectors = [\n                                    // 页面标题和导航\n                                    \"header h1, header h2, header p\",\n                                    \"nav, nav *\",\n                                    // 主要内容区域\n                                    \".welcome-content, .welcome-content *\",\n                                    \".artwork-title, .card-title\",\n                                    // 按钮和交互元素\n                                    \".btn:not(.monaco-editor .btn)\",\n                                    \"button:not(.monaco-editor button)\",\n                                    // 卡片内容\n                                    \".card:not(.monaco-editor .card), .card:not(.monaco-editor .card) *\",\n                                    // 特定的字体应用类\n                                    \".font-applied, .font-handwritten, .font-primary\",\n                                    \".handdrawn-text\",\n                                    // 表单元素（非编辑器内）\n                                    \"input:not(.monaco-editor input)\",\n                                    \"textarea:not(.monaco-editor textarea)\",\n                                    \"label:not(.monaco-editor label)\",\n                                    \"select:not(.monaco-editor select)\",\n                                    // 文本内容（非编辑器内）\n                                    \"p:not(.monaco-editor p)\",\n                                    \"span:not(.monaco-editor span)\",\n                                    \"div:not(.monaco-editor div):not(.monaco-editor)\",\n                                    \"a:not(.monaco-editor a)\",\n                                    // 标题（非编辑器内）\n                                    \"h1:not(.monaco-editor h1), h2:not(.monaco-editor h2), h3:not(.monaco-editor h3), h4:not(.monaco-editor h4), h5:not(.monaco-editor h5), h6:not(.monaco-editor h6)\"\n                                ];\n                                whitelistSelectors.forEach((selector)=>{\n                                    try {\n                                        const elements = document.querySelectorAll(selector);\n                                        elements.forEach((element)=>{\n                                            element.style.setProperty(\"font-family\", \"'\".concat(fontFamily, \"', sans-serif\"), \"important\");\n                                        });\n                                    } catch (e) {\n                                        // 忽略选择器错误\n                                        console.warn(\"字体应用选择器错误:\", selector, e);\n                                    }\n                                });\n                                // 强制重排\n                                document.body.offsetHeight;\n                            };\n                            // 立即应用\n                            applyFontToAllElements();\n                            // 延迟再次应用，确保所有动态加载的元素也能应用字体\n                            setTimeout(applyFontToAllElements, 500);\n                            setTimeout(applyFontToAllElements, 1000);\n                        }\n                    } else {\n                        console.log(\"\\uD83D\\uDCDD 没有需要恢复的字体配置\");\n                    }\n                } catch (fontError) {\n                    console.warn(\"⚠️ 字体恢复失败，但不影响应用启动:\", fontError);\n                }\n                // 加载现有作品\n                await loadArtworks();\n                console.log(\"✅ 应用初始化完成\");\n            } catch (error) {\n                console.error(\"❌ 应用初始化失败:\", error);\n            } finally{\n                // 延迟隐藏加载屏幕，让用户看到完整的加载动画\n                setTimeout(()=>{\n                    setIsLoading(false);\n                }, 300);\n            }\n        };\n        initializeApp();\n    }, []);\n    // 加载作品列表\n    const loadArtworks = async ()=>{\n        try {\n            setIsLoadingArtworks(true);\n            const result = await artworkService.getAllArtworks();\n            if (result.success && result.data) {\n                setArtworks(result.data);\n            } else {\n                console.error(\"加载作品失败:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"加载作品失败:\", error);\n        } finally{\n            setIsLoadingArtworks(false);\n        }\n    };\n    // 打开创建作品对话框\n    const openCreateDialog = ()=>{\n        setShowCreateDialog(true);\n    };\n    // 处理作品创建成功\n    const handleCreateSuccess = async (artworkId)=>{\n        try {\n            // 获取新创建的作品详情\n            const result = await artworkService.getArtwork(artworkId);\n            if (result.success && result.data) {\n                console.log(\"✅ 作品创建成功:\", result.data.id);\n                // 将新作品添加到列表顶部\n                setArtworks((prev)=>[\n                        result.data,\n                        ...prev\n                    ]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取新作品失败:\", error);\n        }\n    };\n    // 处理作品点击\n    const handleArtworkClick = (artwork)=>{\n        console.log(\"点击作品:\", artwork.title);\n        // 这里将来会导航到作品详情页\n        alert(\"即将打开作品: \".concat(artwork.title));\n    };\n    // 处理作品编辑\n    const handleArtworkEdit = (artwork)=>{\n        console.log(\"编辑作品:\", artwork.title);\n        alert(\"编辑功能即将推出: \".concat(artwork.title));\n    };\n    // 处理作品删除\n    const handleArtworkDelete = async (artwork)=>{\n        if (confirm('确定要删除作品\"'.concat(artwork.title, '\"吗？此操作不可撤销。'))) {\n            try {\n                const result = await artworkService.deleteArtwork(artwork.id);\n                if (result.success) {\n                    setArtworks((prev)=>prev.filter((a)=>a.id !== artwork.id));\n                    console.log(\"✅ 作品删除成功\");\n                } else {\n                    throw new Error(result.error || \"删除失败\");\n                }\n            } catch (error) {\n                console.error(\"❌ 删除作品失败:\", error);\n                alert(\"删除作品失败，请重试\");\n            }\n        }\n    };\n    // 提取作品内容为文本\n    const extractTextContent = (content)=>{\n        switch(content.type){\n            case \"text\":\n                var _content_data;\n                return ((_content_data = content.data) === null || _content_data === void 0 ? void 0 : _content_data.text) || \"无文本内容\";\n            case \"canvas\":\n                var _content_data1, _content_data2;\n                return \"[画布内容] - 尺寸: \".concat(((_content_data1 = content.data) === null || _content_data1 === void 0 ? void 0 : _content_data1.width) || \"未知\", \"x\").concat(((_content_data2 = content.data) === null || _content_data2 === void 0 ? void 0 : _content_data2.height) || \"未知\");\n            case \"mixed\":\n                var _content_data3, _content_data4;\n                const textPart = ((_content_data3 = content.data) === null || _content_data3 === void 0 ? void 0 : _content_data3.text) || \"\";\n                const canvasPart = ((_content_data4 = content.data) === null || _content_data4 === void 0 ? void 0 : _content_data4.canvas) ? \"[包含画布内容]\" : \"\";\n                return [\n                    textPart,\n                    canvasPart\n                ].filter(Boolean).join(\"\\n\");\n            default:\n                return \"未知内容类型\";\n        }\n    };\n    // 处理字体应用\n    const handleFontApplied = (font)=>{\n        setAppliedFont(font.family);\n        console.log(\"✅ 字体已应用到页面:\", font.family);\n        // 更新CSS变量\n        document.documentElement.style.setProperty(\"--font-family-applied\", \"'\".concat(font.family, \"', sans-serif\"));\n        document.documentElement.style.setProperty(\"--font-family-handwritten\", \"'\".concat(font.family, \"', cursive, var(--font-family-primary)\"));\n        // 应用字体到页面的所有主要元素\n        const elementsToApply = [\n            \"body\",\n            \".font-handwritten\",\n            \".font-applied\",\n            \"h1, h2, h3, h4, h5, h6\",\n            \".artwork-title\",\n            \".welcome-content h2\",\n            \".welcome-content p\",\n            \".card-title\",\n            \".btn\",\n            \"p, span, div, label, input, textarea\"\n        ];\n        elementsToApply.forEach((selector)=>{\n            try {\n                const elements = document.querySelectorAll(selector);\n                elements.forEach((element)=>{\n                    element.style.fontFamily = \"'\".concat(font.family, \"', sans-serif\");\n                });\n            } catch (e) {\n            // 忽略选择器错误\n            }\n        });\n        // 强制应用到body元素\n        document.body.style.fontFamily = \"'\".concat(font.family, \"', sans-serif\");\n        // 显示应用成功的提示\n        console.log('✅ 字体\"'.concat(font.name, '\"已成功应用到整个页面！'));\n    };\n    // 处理作品导出\n    const handleArtworkExport = (artwork)=>{\n        try {\n            // 创建文本格式的导出内容\n            const textContent = \"\\n===========================================\\n           作品导出文档\\n===========================================\\n\\n作品标题: \".concat(artwork.title, \"\\n创建时间: \").concat(new Date(artwork.createdAt).toLocaleString(\"zh-CN\"), \"\\n修改时间: \").concat(new Date(artwork.updatedAt).toLocaleString(\"zh-CN\"), \"\\n导出时间: \").concat(new Date().toLocaleString(\"zh-CN\"), \"\\n\\n-------------------------------------------\\n作品描述:\\n\").concat(artwork.description || \"无描述\", \"\\n\\n-------------------------------------------\\n作品内容:\\n\").concat(extractTextContent(artwork.content), \"\\n\\n-------------------------------------------\\n内容设置:\\n\").concat(artwork.content.settings ? Object.entries(artwork.content.settings).map((param)=>{\n                let [key, value] = param;\n                return \"\".concat(key, \": \").concat(value);\n            }).join(\"\\n\") : \"无特殊设置\", \"\\n\\n-------------------------------------------\\n标签信息:\\n\").concat(artwork.tags && artwork.tags.length > 0 ? artwork.tags.join(\", \") : \"无标签\", \"\\n\\n-------------------------------------------\\n技术信息:\\n作品ID: \").concat(artwork.id, \"\\n内容类型: \").concat(artwork.content.type, \"\\n文件大小: \").concat((artwork.metadata.fileSize / 1024).toFixed(2), \" KB\\n平台: 宫崎骏风格作品展示平台\\n\\n===========================================\\n      导出完成 - 感谢使用\\n===========================================\\n\").trim();\n            const dataBlob = new Blob([\n                textContent\n            ], {\n                type: \"text/plain; charset=utf-8\"\n            });\n            const url = URL.createObjectURL(dataBlob);\n            const link = document.createElement(\"a\");\n            link.href = url;\n            link.download = \"\".concat(artwork.title.replace(/[^a-zA-Z0-9\\u4e00-\\u9fa5]/g, \"_\"), \".txt\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            URL.revokeObjectURL(url);\n            console.log(\"✅ 作品导出成功 (TXT格式)\");\n        } catch (error) {\n            console.error(\"❌ 导出作品失败:\", error);\n            alert(\"导出作品失败，请重试\");\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 343,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gradient-to-r from-gray-900 to-gray-800 border-b border-amber-500/30 sticky top-0 z-30 shadow-lg shadow-black/20 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SVGDecorations__WEBPACK_IMPORTED_MODULE_5__.SVGIcons.Palette, {\n                                        size: 32,\n                                        color: \"#F59E0B\",\n                                        className: \"handdrawn-float\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-amber-100 font-handwritten\",\n                                                children: \"作品展示平台\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-amber-200/70\",\n                                                children: \"宫崎骏风格创作空间\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowFontManager(true),\n                                        className: \"px-4 py-2 bg-amber-500/20 hover:bg-amber-500/30 text-amber-400 hover:text-amber-300 border border-amber-500/50 hover:border-amber-500/70 rounded-lg transition-all duration-200 flex items-center gap-2 font-handwritten\",\n                                        title: \"字体管理\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SVGDecorations__WEBPACK_IMPORTED_MODULE_5__.SVGIcons.Palette, {\n                                                size: 16,\n                                                color: \"currentColor\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"字体管理\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: openCreateDialog,\n                                        className: \"px-4 py-2 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-black font-medium rounded-lg transition-all duration-200 flex items-center gap-2 font-handwritten shadow-lg shadow-amber-500/25\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SVGDecorations__WEBPACK_IMPORTED_MODULE_5__.SVGIcons.Create, {\n                                                size: 16,\n                                                color: \"currentColor\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"创建作品\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ArtworkGrid__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    artworks: artworks,\n                    isLoading: isLoadingArtworks,\n                    onArtworkClick: handleArtworkClick,\n                    onArtworkEdit: handleArtworkEdit,\n                    onArtworkDelete: handleArtworkDelete,\n                    onArtworkExport: handleArtworkExport,\n                    onCreateNew: openCreateDialog\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 389,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FontManager__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showFontManager,\n                onClose: ()=>setShowFontManager(false),\n                onFontApplied: handleFontApplied\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 402,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_CreateArtworkDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showCreateDialog,\n                onClose: ()=>setShowCreateDialog(false),\n                onSuccess: handleCreateSuccess\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 409,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 347,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"q3AP0uRibS2yhgTduwVLYy7MitA=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});