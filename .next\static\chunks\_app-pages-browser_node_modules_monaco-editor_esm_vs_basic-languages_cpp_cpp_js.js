"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_cpp_cpp_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/cpp/cpp.js":
/*!**********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/cpp/cpp.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/cpp/cpp.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#pragma\\\\s+region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#pragma\\\\s+endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".cpp\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.angle\", open: \"<\", close: \">\" }\n  ],\n  keywords: [\n    \"abstract\",\n    \"amp\",\n    \"array\",\n    \"auto\",\n    \"bool\",\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"char\",\n    \"class\",\n    \"const\",\n    \"constexpr\",\n    \"const_cast\",\n    \"continue\",\n    \"cpu\",\n    \"decltype\",\n    \"default\",\n    \"delegate\",\n    \"delete\",\n    \"do\",\n    \"double\",\n    \"dynamic_cast\",\n    \"each\",\n    \"else\",\n    \"enum\",\n    \"event\",\n    \"explicit\",\n    \"export\",\n    \"extern\",\n    \"false\",\n    \"final\",\n    \"finally\",\n    \"float\",\n    \"for\",\n    \"friend\",\n    \"gcnew\",\n    \"generic\",\n    \"goto\",\n    \"if\",\n    \"in\",\n    \"initonly\",\n    \"inline\",\n    \"int\",\n    \"interface\",\n    \"interior_ptr\",\n    \"internal\",\n    \"literal\",\n    \"long\",\n    \"mutable\",\n    \"namespace\",\n    \"new\",\n    \"noexcept\",\n    \"nullptr\",\n    \"__nullptr\",\n    \"operator\",\n    \"override\",\n    \"partial\",\n    \"pascal\",\n    \"pin_ptr\",\n    \"private\",\n    \"property\",\n    \"protected\",\n    \"public\",\n    \"ref\",\n    \"register\",\n    \"reinterpret_cast\",\n    \"restrict\",\n    \"return\",\n    \"safe_cast\",\n    \"sealed\",\n    \"short\",\n    \"signed\",\n    \"sizeof\",\n    \"static\",\n    \"static_assert\",\n    \"static_cast\",\n    \"struct\",\n    \"switch\",\n    \"template\",\n    \"this\",\n    \"thread_local\",\n    \"throw\",\n    \"tile_static\",\n    \"true\",\n    \"try\",\n    \"typedef\",\n    \"typeid\",\n    \"typename\",\n    \"union\",\n    \"unsigned\",\n    \"using\",\n    \"virtual\",\n    \"void\",\n    \"volatile\",\n    \"wchar_t\",\n    \"where\",\n    \"while\",\n    \"_asm\",\n    // reserved word with one underscores\n    \"_based\",\n    \"_cdecl\",\n    \"_declspec\",\n    \"_fastcall\",\n    \"_if_exists\",\n    \"_if_not_exists\",\n    \"_inline\",\n    \"_multiple_inheritance\",\n    \"_pascal\",\n    \"_single_inheritance\",\n    \"_stdcall\",\n    \"_virtual_inheritance\",\n    \"_w64\",\n    \"__abstract\",\n    // reserved word with two underscores\n    \"__alignof\",\n    \"__asm\",\n    \"__assume\",\n    \"__based\",\n    \"__box\",\n    \"__builtin_alignof\",\n    \"__cdecl\",\n    \"__clrcall\",\n    \"__declspec\",\n    \"__delegate\",\n    \"__event\",\n    \"__except\",\n    \"__fastcall\",\n    \"__finally\",\n    \"__forceinline\",\n    \"__gc\",\n    \"__hook\",\n    \"__identifier\",\n    \"__if_exists\",\n    \"__if_not_exists\",\n    \"__inline\",\n    \"__int128\",\n    \"__int16\",\n    \"__int32\",\n    \"__int64\",\n    \"__int8\",\n    \"__interface\",\n    \"__leave\",\n    \"__m128\",\n    \"__m128d\",\n    \"__m128i\",\n    \"__m256\",\n    \"__m256d\",\n    \"__m256i\",\n    \"__m512\",\n    \"__m512d\",\n    \"__m512i\",\n    \"__m64\",\n    \"__multiple_inheritance\",\n    \"__newslot\",\n    \"__nogc\",\n    \"__noop\",\n    \"__nounwind\",\n    \"__novtordisp\",\n    \"__pascal\",\n    \"__pin\",\n    \"__pragma\",\n    \"__property\",\n    \"__ptr32\",\n    \"__ptr64\",\n    \"__raise\",\n    \"__restrict\",\n    \"__resume\",\n    \"__sealed\",\n    \"__single_inheritance\",\n    \"__stdcall\",\n    \"__super\",\n    \"__thiscall\",\n    \"__try\",\n    \"__try_cast\",\n    \"__typeof\",\n    \"__unaligned\",\n    \"__unhook\",\n    \"__uuidof\",\n    \"__value\",\n    \"__virtual_inheritance\",\n    \"__w64\",\n    \"__wchar_t\"\n  ],\n  operators: [\n    \"=\",\n    \">\",\n    \"<\",\n    \"!\",\n    \"~\",\n    \"?\",\n    \":\",\n    \"==\",\n    \"<=\",\n    \">=\",\n    \"!=\",\n    \"&&\",\n    \"||\",\n    \"++\",\n    \"--\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \"%\",\n    \"<<\",\n    \">>\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"%=\",\n    \"<<=\",\n    \">>=\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[0abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  integersuffix: /([uU](ll|LL|l|L)|(ll|LL|l|L)?[uU]?)/,\n  floatsuffix: /[fFlL]?/,\n  encoding: /u|u8|U|L/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // C++ 11 Raw String\n      [/@encoding?R\\\"(?:([^ ()\\\\\\t]*))\\(/, { token: \"string.raw.begin\", next: \"@raw.$1\" }],\n      // identifiers and keywords\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // The preprocessor checks must be before whitespace as they check /^\\s*#/ which\n      // otherwise fails to match later after other whitespace has been removed.\n      // Inclusion\n      [/^\\s*#\\s*include/, { token: \"keyword.directive.include\", next: \"@include\" }],\n      // Preprocessor directive\n      [/^\\s*#\\s*\\w+/, \"keyword.directive\"],\n      // whitespace\n      { include: \"@whitespace\" },\n      // [[ attributes ]].\n      [/\\[\\s*\\[/, { token: \"annotation\", next: \"@annotation\" }],\n      // delimiters and operators\n      [/[{}()<>\\[\\]]/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d*\\d+[eE]([\\-+]?\\d+)?(@floatsuffix)/, \"number.float\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?(@floatsuffix)/, \"number.float\"],\n      [/0[xX][0-9a-fA-F']*[0-9a-fA-F](@integersuffix)/, \"number.hex\"],\n      [/0[0-7']*[0-7](@integersuffix)/, \"number.octal\"],\n      [/0[bB][0-1']*[0-1](@integersuffix)/, \"number.binary\"],\n      [/\\d[\\d']*\\d(@integersuffix)/, \"number\"],\n      [/\\d(@integersuffix)/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string\"],\n      // characters\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@doccomment\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*\\\\$/, \"comment\", \"@linecomment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    //For use with continuous line comments\n    linecomment: [\n      [/.*[^\\\\]$/, \"comment\", \"@pop\"],\n      [/[^]+/, \"comment\"]\n    ],\n    //Identical copy of comment above, except for the addition of .doc\n    doccomment: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    raw: [\n      [/[^)]+/, \"string.raw\"],\n      [/\\)$S2\\\"/, { token: \"string.raw.end\", next: \"@pop\" }],\n      [/\\)/, \"string.raw\"]\n    ],\n    annotation: [\n      { include: \"@whitespace\" },\n      [/using|alignas/, \"keyword\"],\n      [/[a-zA-Z0-9_]+/, \"annotation\"],\n      [/[,:]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [/\\]\\s*\\]/, { token: \"annotation\", next: \"@pop\" }]\n    ],\n    include: [\n      [\n        /(\\s*)(<)([^<>]*)(>)/,\n        [\n          \"\",\n          \"keyword.directive.include.begin\",\n          \"string.include.identifier\",\n          { token: \"keyword.directive.include.end\", next: \"@pop\" }\n        ]\n      ],\n      [\n        /(\\s*)(\")([^\"]*)(\")/,\n        [\n          \"\",\n          \"keyword.directive.include.begin\",\n          \"string.include.identifier\",\n          { token: \"keyword.directive.include.end\", next: \"@pop\" }\n        ]\n      ]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/cpp/cpp.js\n"));

/***/ })

}]);