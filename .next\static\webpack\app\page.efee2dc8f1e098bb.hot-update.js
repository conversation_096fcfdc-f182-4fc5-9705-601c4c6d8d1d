"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/LoadingScreen */ \"(app-pages-browser)/./src/components/LoadingScreen.tsx\");\n/* harmony import */ var _components_ArtworkGrid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ArtworkGrid */ \"(app-pages-browser)/./src/components/ArtworkGrid/index.tsx\");\n/* harmony import */ var _components_FontManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/FontManager */ \"(app-pages-browser)/./src/components/FontManager/index.tsx\");\n/* harmony import */ var _components_SVGDecorations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/SVGDecorations */ \"(app-pages-browser)/./src/components/SVGDecorations/index.tsx\");\n/* harmony import */ var _services_artworkService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/artworkService */ \"(app-pages-browser)/./src/services/artworkService.ts\");\n/* harmony import */ var _services_database_DatabaseService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/database/DatabaseService */ \"(app-pages-browser)/./src/services/database/DatabaseService.ts\");\n/* harmony import */ var _services_fontPersistenceService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/fontPersistenceService */ \"(app-pages-browser)/./src/services/fontPersistenceService.ts\");\n/* harmony import */ var _components_common_CreateArtworkDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/common/CreateArtworkDialog */ \"(app-pages-browser)/./src/components/common/CreateArtworkDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [artworks, setArtworks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingArtworks, setIsLoadingArtworks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFontManager, setShowFontManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [colorScheme, setColorScheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [appliedFont, setAppliedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const artworkService = _services_artworkService__WEBPACK_IMPORTED_MODULE_6__.ArtworkService.getInstance();\n    const databaseService = _services_database_DatabaseService__WEBPACK_IMPORTED_MODULE_7__.DatabaseService.getInstance();\n    const persistenceService = _services_fontPersistenceService__WEBPACK_IMPORTED_MODULE_8__.FontPersistenceService.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 应用初始化过程\n        const initializeApp = async ()=>{\n            try {\n                console.log(\"\\uD83C\\uDFA8 初始化宫崎骏风格作品展示平台...\");\n                // 初始化IndexedDB\n                const dbResult = await databaseService.initialize();\n                if (!dbResult.success) {\n                    throw new Error(dbResult.error || \"IndexedDB初始化失败\");\n                }\n                // 恢复字体应用状态（字体持久化功能）\n                try {\n                    console.log(\"\\uD83D\\uDD04 尝试恢复字体应用状态...\");\n                    // 延迟一点时间确保DOM完全加载\n                    await new Promise((resolve)=>setTimeout(resolve, 300));\n                    const restoreResult = await persistenceService.restoreFontApplication();\n                    if (restoreResult.success && restoreResult.data) {\n                        console.log(\"✅ 字体应用状态已恢复\");\n                        // 获取当前应用的字体配置\n                        const configResult = await persistenceService.getActiveConfig();\n                        if (configResult.success && configResult.data) {\n                            const fontFamily = configResult.data.fontFamily;\n                            setAppliedFont(fontFamily);\n                            console.log(\"\\uD83C\\uDFAF 当前应用的字体:\", fontFamily);\n                            // 立即更新CSS变量\n                            document.documentElement.style.setProperty(\"--font-family-applied\", \"'\".concat(fontFamily, \"', sans-serif\"));\n                            document.documentElement.style.setProperty(\"--font-family-handwritten\", \"'\".concat(fontFamily, \"', cursive, var(--font-family-primary)\"));\n                            // 强制应用到所有元素\n                            const applyFontToAllElements = ()=>{\n                                // 应用到body\n                                document.body.style.setProperty(\"font-family\", \"'\".concat(fontFamily, \"', sans-serif\"), \"important\");\n                                // 白名单方式：只对指定的元素应用用户字体\n                                const whitelistSelectors = [\n                                    // 页面标题和导航\n                                    \"header h1, header h2, header p\",\n                                    \"nav, nav *\",\n                                    // 主要内容区域\n                                    \".welcome-content, .welcome-content *\",\n                                    \".artwork-title, .card-title\",\n                                    // 按钮和交互元素\n                                    \".btn:not(.monaco-editor .btn)\",\n                                    \"button:not(.monaco-editor button)\",\n                                    // 卡片内容\n                                    \".card:not(.monaco-editor .card), .card:not(.monaco-editor .card) *\",\n                                    // 特定的字体应用类\n                                    \".font-applied, .font-handwritten, .font-primary\",\n                                    \".handdrawn-text\",\n                                    // 表单元素（非编辑器内）\n                                    \"input:not(.monaco-editor input)\",\n                                    \"textarea:not(.monaco-editor textarea)\",\n                                    \"label:not(.monaco-editor label)\",\n                                    \"select:not(.monaco-editor select)\",\n                                    // 文本内容（非编辑器内）\n                                    \"p:not(.monaco-editor p)\",\n                                    \"span:not(.monaco-editor span)\",\n                                    \"div:not(.monaco-editor div):not(.monaco-editor)\",\n                                    \"a:not(.monaco-editor a)\",\n                                    // 标题（非编辑器内）\n                                    \"h1:not(.monaco-editor h1), h2:not(.monaco-editor h2), h3:not(.monaco-editor h3), h4:not(.monaco-editor h4), h5:not(.monaco-editor h5), h6:not(.monaco-editor h6)\"\n                                ];\n                                whitelistSelectors.forEach((selector)=>{\n                                    try {\n                                        const elements = document.querySelectorAll(selector);\n                                        elements.forEach((element)=>{\n                                            element.style.setProperty(\"font-family\", \"'\".concat(fontFamily, \"', sans-serif\"), \"important\");\n                                        });\n                                    } catch (e) {\n                                        // 忽略选择器错误\n                                        console.warn(\"字体应用选择器错误:\", selector, e);\n                                    }\n                                });\n                                // 强制重排\n                                document.body.offsetHeight;\n                            };\n                            // 立即应用\n                            applyFontToAllElements();\n                            // 延迟再次应用，确保所有动态加载的元素也能应用字体\n                            setTimeout(applyFontToAllElements, 500);\n                            setTimeout(applyFontToAllElements, 1000);\n                        }\n                    } else {\n                        console.log(\"\\uD83D\\uDCDD 没有需要恢复的字体配置\");\n                    }\n                } catch (fontError) {\n                    console.warn(\"⚠️ 字体恢复失败，但不影响应用启动:\", fontError);\n                }\n                // 加载现有作品\n                await loadArtworks();\n                console.log(\"✅ 应用初始化完成\");\n            } catch (error) {\n                console.error(\"❌ 应用初始化失败:\", error);\n            } finally{\n                // 延迟隐藏加载屏幕，让用户看到完整的加载动画\n                setTimeout(()=>{\n                    setIsLoading(false);\n                }, 300);\n            }\n        };\n        initializeApp();\n    }, []);\n    // 加载作品列表\n    const loadArtworks = async ()=>{\n        try {\n            setIsLoadingArtworks(true);\n            const result = await artworkService.getAllArtworks();\n            if (result.success && result.data) {\n                setArtworks(result.data);\n            } else {\n                console.error(\"加载作品失败:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"加载作品失败:\", error);\n        } finally{\n            setIsLoadingArtworks(false);\n        }\n    };\n    // 打开创建作品对话框\n    const openCreateDialog = ()=>{\n        setShowCreateDialog(true);\n    };\n    // 处理作品创建成功\n    const handleCreateSuccess = async (artworkId)=>{\n        try {\n            // 获取新创建的作品详情\n            const result = await artworkService.getArtwork(artworkId);\n            if (result.success && result.data) {\n                console.log(\"✅ 作品创建成功:\", result.data.id);\n                // 将新作品添加到列表顶部\n                setArtworks((prev)=>[\n                        result.data,\n                        ...prev\n                    ]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取新作品失败:\", error);\n        }\n    };\n    // 处理作品点击\n    const handleArtworkClick = (artwork)=>{\n        console.log(\"点击作品:\", artwork.title);\n        // 这里将来会导航到作品详情页\n        alert(\"即将打开作品: \".concat(artwork.title));\n    };\n    // 处理作品编辑\n    const handleArtworkEdit = (artwork)=>{\n        console.log(\"编辑作品:\", artwork.title);\n        alert(\"编辑功能即将推出: \".concat(artwork.title));\n    };\n    // 处理作品删除\n    const handleArtworkDelete = async (artwork)=>{\n        if (confirm('确定要删除作品\"'.concat(artwork.title, '\"吗？此操作不可撤销。'))) {\n            try {\n                const result = await artworkService.deleteArtwork(artwork.id);\n                if (result.success) {\n                    setArtworks((prev)=>prev.filter((a)=>a.id !== artwork.id));\n                    console.log(\"✅ 作品删除成功\");\n                } else {\n                    throw new Error(result.error || \"删除失败\");\n                }\n            } catch (error) {\n                console.error(\"❌ 删除作品失败:\", error);\n                alert(\"删除作品失败，请重试\");\n            }\n        }\n    };\n    // 提取作品内容为文本\n    const extractTextContent = (content)=>{\n        switch(content.type){\n            case \"text\":\n                var _content_data;\n                return ((_content_data = content.data) === null || _content_data === void 0 ? void 0 : _content_data.text) || \"无文本内容\";\n            case \"canvas\":\n                var _content_data1, _content_data2;\n                return \"[画布内容] - 尺寸: \".concat(((_content_data1 = content.data) === null || _content_data1 === void 0 ? void 0 : _content_data1.width) || \"未知\", \"x\").concat(((_content_data2 = content.data) === null || _content_data2 === void 0 ? void 0 : _content_data2.height) || \"未知\");\n            case \"mixed\":\n                var _content_data3, _content_data4;\n                const textPart = ((_content_data3 = content.data) === null || _content_data3 === void 0 ? void 0 : _content_data3.text) || \"\";\n                const canvasPart = ((_content_data4 = content.data) === null || _content_data4 === void 0 ? void 0 : _content_data4.canvas) ? \"[包含画布内容]\" : \"\";\n                return [\n                    textPart,\n                    canvasPart\n                ].filter(Boolean).join(\"\\n\");\n            default:\n                return \"未知内容类型\";\n        }\n    };\n    // 处理字体应用\n    const handleFontApplied = (font)=>{\n        setAppliedFont(font.family);\n        console.log(\"✅ 字体已应用到页面:\", font.family);\n        // 更新CSS变量\n        document.documentElement.style.setProperty(\"--font-family-applied\", \"'\".concat(font.family, \"', sans-serif\"));\n        document.documentElement.style.setProperty(\"--font-family-handwritten\", \"'\".concat(font.family, \"', cursive, var(--font-family-primary)\"));\n        // 应用字体到页面的所有主要元素\n        const elementsToApply = [\n            \"body\",\n            \".font-handwritten\",\n            \".font-applied\",\n            \"h1, h2, h3, h4, h5, h6\",\n            \".artwork-title\",\n            \".welcome-content h2\",\n            \".welcome-content p\",\n            \".card-title\",\n            \".btn\",\n            \"p, span, div, label, input, textarea\"\n        ];\n        elementsToApply.forEach((selector)=>{\n            try {\n                const elements = document.querySelectorAll(selector);\n                elements.forEach((element)=>{\n                    element.style.fontFamily = \"'\".concat(font.family, \"', sans-serif\");\n                });\n            } catch (e) {\n            // 忽略选择器错误\n            }\n        });\n        // 强制应用到body元素\n        document.body.style.fontFamily = \"'\".concat(font.family, \"', sans-serif\");\n        // 显示应用成功的提示\n        console.log('✅ 字体\"'.concat(font.name, '\"已成功应用到整个页面！'));\n    };\n    // 处理作品导出\n    const handleArtworkExport = (artwork)=>{\n        try {\n            // 创建文本格式的导出内容\n            const textContent = \"\\n===========================================\\n           作品导出文档\\n===========================================\\n\\n作品标题: \".concat(artwork.title, \"\\n创建时间: \").concat(new Date(artwork.createdAt).toLocaleString(\"zh-CN\"), \"\\n修改时间: \").concat(new Date(artwork.updatedAt).toLocaleString(\"zh-CN\"), \"\\n导出时间: \").concat(new Date().toLocaleString(\"zh-CN\"), \"\\n\\n-------------------------------------------\\n作品描述:\\n\").concat(artwork.description || \"无描述\", \"\\n\\n-------------------------------------------\\n作品内容:\\n\").concat(extractTextContent(artwork.content), \"\\n\\n-------------------------------------------\\n内容设置:\\n\").concat(artwork.content.settings ? Object.entries(artwork.content.settings).map((param)=>{\n                let [key, value] = param;\n                return \"\".concat(key, \": \").concat(value);\n            }).join(\"\\n\") : \"无特殊设置\", \"\\n\\n-------------------------------------------\\n标签信息:\\n\").concat(artwork.tags && artwork.tags.length > 0 ? artwork.tags.join(\", \") : \"无标签\", \"\\n\\n-------------------------------------------\\n技术信息:\\n作品ID: \").concat(artwork.id, \"\\n内容类型: \").concat(artwork.content.type, \"\\n文件大小: \").concat((artwork.metadata.fileSize / 1024).toFixed(2), \" KB\\n平台: 宫崎骏风格作品展示平台\\n\\n===========================================\\n      导出完成 - 感谢使用\\n===========================================\\n\").trim();\n            const dataBlob = new Blob([\n                textContent\n            ], {\n                type: \"text/plain; charset=utf-8\"\n            });\n            const url = URL.createObjectURL(dataBlob);\n            const link = document.createElement(\"a\");\n            link.href = url;\n            link.download = \"\".concat(artwork.title.replace(/[^a-zA-Z0-9\\u4e00-\\u9fa5]/g, \"_\"), \".txt\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            URL.revokeObjectURL(url);\n            console.log(\"✅ 作品导出成功 (TXT格式)\");\n        } catch (error) {\n            console.error(\"❌ 导出作品失败:\", error);\n            alert(\"导出作品失败，请重试\");\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 342,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gradient-to-r from-gray-900 to-gray-800 border-b border-amber-500/30 sticky top-0 z-30 shadow-lg shadow-black/20 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SVGDecorations__WEBPACK_IMPORTED_MODULE_5__.SVGIcons.Palette, {\n                                        size: 32,\n                                        color: \"#F59E0B\",\n                                        className: \"handdrawn-float\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-amber-100 font-handwritten\",\n                                                children: \"作品展示平台\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-amber-200/70\",\n                                                children: \"宫崎骏风格创作空间\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowFontManager(true),\n                                        className: \"px-4 py-2 bg-amber-500/20 hover:bg-amber-500/30 text-amber-400 hover:text-amber-300 border border-amber-500/50 hover:border-amber-500/70 rounded-lg transition-all duration-200 flex items-center gap-2 font-handwritten\",\n                                        title: \"字体管理\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SVGDecorations__WEBPACK_IMPORTED_MODULE_5__.SVGIcons.Palette, {\n                                                size: 16,\n                                                color: \"currentColor\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"字体管理\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: openCreateDialog,\n                                        className: \"px-4 py-2 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-black font-medium rounded-lg transition-all duration-200 flex items-center gap-2 font-handwritten shadow-lg shadow-amber-500/25\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SVGDecorations__WEBPACK_IMPORTED_MODULE_5__.SVGIcons.Create, {\n                                                size: 16,\n                                                color: \"currentColor\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"创建作品\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ArtworkGrid__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    artworks: artworks,\n                    isLoading: isLoadingArtworks,\n                    onArtworkClick: handleArtworkClick,\n                    onArtworkEdit: handleArtworkEdit,\n                    onArtworkDelete: handleArtworkDelete,\n                    onArtworkExport: handleArtworkExport,\n                    onCreateNew: openCreateDialog\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 388,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FontManager__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showFontManager,\n                onClose: ()=>setShowFontManager(false),\n                onFontApplied: handleFontApplied\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 401,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_CreateArtworkDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showCreateDialog,\n                onClose: ()=>setShowCreateDialog(false),\n                onSuccess: handleCreateSuccess\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 408,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 346,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"q3AP0uRibS2yhgTduwVLYy7MitA=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});