"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_scss_scss_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/scss/scss.js":
/*!************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/scss/scss.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/scss/scss.ts\nvar conf = {\n  wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|([@$#!.:]?[\\w-?]+%?)|[@#!.]/g,\n  comments: {\n    blockComment: [\"/*\", \"*/\"],\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/\"),\n      end: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".scss\",\n  ws: \"[ \t\\n\\r\\f]*\",\n  // whitespaces (referenced in several rules)\n  identifier: \"-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  tokenizer: {\n    root: [{ include: \"@selector\" }],\n    selector: [\n      { include: \"@comments\" },\n      { include: \"@import\" },\n      { include: \"@variabledeclaration\" },\n      { include: \"@warndebug\" },\n      // sass: log statements\n      [\"[@](include)\", { token: \"keyword\", next: \"@includedeclaration\" }],\n      // sass: include statement\n      [\n        \"[@](keyframes|-webkit-keyframes|-moz-keyframes|-o-keyframes)\",\n        { token: \"keyword\", next: \"@keyframedeclaration\" }\n      ],\n      [\"[@](page|content|font-face|-moz-document)\", { token: \"keyword\" }],\n      // sass: placeholder for includes\n      [\"[@](charset|namespace)\", { token: \"keyword\", next: \"@declarationbody\" }],\n      [\"[@](function)\", { token: \"keyword\", next: \"@functiondeclaration\" }],\n      [\"[@](mixin)\", { token: \"keyword\", next: \"@mixindeclaration\" }],\n      [\"url(\\\\-prefix)?\\\\(\", { token: \"meta\", next: \"@urldeclaration\" }],\n      { include: \"@controlstatement\" },\n      // sass control statements\n      { include: \"@selectorname\" },\n      [\"[&\\\\*]\", \"tag\"],\n      // selector symbols\n      [\"[>\\\\+,]\", \"delimiter\"],\n      // selector operators\n      [\"\\\\[\", { token: \"delimiter.bracket\", next: \"@selectorattribute\" }],\n      [\"{\", { token: \"delimiter.curly\", next: \"@selectorbody\" }]\n    ],\n    selectorbody: [\n      [\"[*_]?@identifier@ws:(?=(\\\\s|\\\\d|[^{;}]*[;}]))\", \"attribute.name\", \"@rulevalue\"],\n      // rule definition: to distinguish from a nested selector check for whitespace, number or a semicolon\n      { include: \"@selector\" },\n      // sass: nested selectors\n      [\"[@](extend)\", { token: \"keyword\", next: \"@extendbody\" }],\n      // sass: extend other selectors\n      [\"[@](return)\", { token: \"keyword\", next: \"@declarationbody\" }],\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    selectorname: [\n      [\"#{\", { token: \"meta\", next: \"@variableinterpolation\" }],\n      // sass: interpolation\n      [\"(\\\\.|#(?=[^{])|%|(@identifier)|:)+\", \"tag\"]\n      // selector (.foo, div, ...)\n    ],\n    selectorattribute: [{ include: \"@term\" }, [\"]\", { token: \"delimiter.bracket\", next: \"@pop\" }]],\n    term: [\n      { include: \"@comments\" },\n      [\"url(\\\\-prefix)?\\\\(\", { token: \"meta\", next: \"@urldeclaration\" }],\n      { include: \"@functioninvocation\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@variablereference\" },\n      [\"(and\\\\b|or\\\\b|not\\\\b)\", \"operator\"],\n      { include: \"@name\" },\n      [\"([<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~,])\", \"operator\"],\n      [\",\", \"delimiter\"],\n      [\"!default\", \"literal\"],\n      [\"\\\\(\", { token: \"delimiter.parenthesis\", next: \"@parenthizedterm\" }]\n    ],\n    rulevalue: [\n      { include: \"@term\" },\n      [\"!important\", \"literal\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@nestedproperty\" }],\n      // sass: nested properties\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    nestedproperty: [\n      [\"[*_]?@identifier@ws:\", \"attribute.name\", \"@rulevalue\"],\n      { include: \"@comments\" },\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    warndebug: [[\"[@](warn|debug)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    import: [[\"[@](import)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    variabledeclaration: [\n      // sass variables\n      [\"\\\\$@identifier@ws:\", \"variable.decl\", \"@declarationbody\"]\n    ],\n    urldeclaration: [\n      { include: \"@strings\" },\n      [\"[^)\\r\\n]+\", \"string\"],\n      [\"\\\\)\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    parenthizedterm: [\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    declarationbody: [\n      { include: \"@term\" },\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    extendbody: [\n      { include: \"@selectorname\" },\n      [\"!optional\", \"literal\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    variablereference: [\n      // sass variable reference\n      [\"\\\\$@identifier\", \"variable.ref\"],\n      [\"\\\\.\\\\.\\\\.\", \"operator\"],\n      // var args in reference\n      [\"#{\", { token: \"meta\", next: \"@variableinterpolation\" }]\n      // sass var resolve\n    ],\n    variableinterpolation: [\n      { include: \"@variablereference\" },\n      [\"}\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [\".\", \"comment\"]\n    ],\n    name: [[\"@identifier\", \"attribute.value\"]],\n    numbers: [\n      [\"(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?\", { token: \"number\", next: \"@units\" }],\n      [\"#[0-9a-fA-F_]+(?!\\\\w)\", \"number.hex\"]\n    ],\n    units: [\n      [\n        \"(em|ex|ch|rem|fr|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?\",\n        \"number\",\n        \"@pop\"\n      ]\n    ],\n    functiondeclaration: [\n      [\"@identifier@ws\\\\(\", { token: \"meta\", next: \"@parameterdeclaration\" }],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@functionbody\" }]\n    ],\n    mixindeclaration: [\n      // mixin with parameters\n      [\"@identifier@ws\\\\(\", { token: \"meta\", next: \"@parameterdeclaration\" }],\n      // mixin without parameters\n      [\"@identifier\", \"meta\"],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@selectorbody\" }]\n    ],\n    parameterdeclaration: [\n      [\"\\\\$@identifier@ws:\", \"variable.decl\"],\n      [\"\\\\.\\\\.\\\\.\", \"operator\"],\n      // var args in declaration\n      [\",\", \"delimiter\"],\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    includedeclaration: [\n      { include: \"@functioninvocation\" },\n      [\"@identifier\", \"meta\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }],\n      // missing semicolon\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@selectorbody\" }]\n    ],\n    keyframedeclaration: [\n      [\"@identifier\", \"meta\"],\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@keyframebody\" }]\n    ],\n    keyframebody: [\n      { include: \"@term\" },\n      [\"{\", { token: \"delimiter.curly\", next: \"@selectorbody\" }],\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    controlstatement: [\n      [\n        \"[@](if|else|for|while|each|media)\",\n        { token: \"keyword.flow\", next: \"@controlstatementdeclaration\" }\n      ]\n    ],\n    controlstatementdeclaration: [\n      [\"(in|from|through|if|to)\\\\b\", { token: \"keyword.flow\" }],\n      { include: \"@term\" },\n      [\"{\", { token: \"delimiter.curly\", switchTo: \"@selectorbody\" }]\n    ],\n    functionbody: [\n      [\"[@](return)\", { token: \"keyword\" }],\n      { include: \"@variabledeclaration\" },\n      { include: \"@term\" },\n      { include: \"@controlstatement\" },\n      [\";\", \"delimiter\"],\n      [\"}\", { token: \"delimiter.curly\", next: \"@pop\" }]\n    ],\n    functioninvocation: [[\"@identifier\\\\(\", { token: \"meta\", next: \"@functionarguments\" }]],\n    functionarguments: [\n      [\"\\\\$@identifier@ws:\", \"attribute.name\"],\n      [\"[,]\", \"delimiter\"],\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"meta\", next: \"@pop\" }]\n    ],\n    strings: [\n      ['~?\"', { token: \"string.delimiter\", next: \"@stringenddoublequote\" }],\n      [\"~?'\", { token: \"string.delimiter\", next: \"@stringendquote\" }]\n    ],\n    stringenddoublequote: [\n      [\"\\\\\\\\.\", \"string\"],\n      ['\"', { token: \"string.delimiter\", next: \"@pop\" }],\n      [\".\", \"string\"]\n    ],\n    stringendquote: [\n      [\"\\\\\\\\.\", \"string\"],\n      [\"'\", { token: \"string.delimiter\", next: \"@pop\" }],\n      [\".\", \"string\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/scss/scss.js\n"));

/***/ })

}]);