"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_wgsl_wgsl_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/wgsl/wgsl.js":
/*!************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/wgsl/wgsl.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/wgsl/wgsl.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" }\n  ]\n};\nfunction qw(str) {\n  let result = [];\n  const words = str.split(/\\t+|\\r+|\\n+| +/);\n  for (let i = 0; i < words.length; ++i) {\n    if (words[i].length > 0) {\n      result.push(words[i]);\n    }\n  }\n  return result;\n}\nvar atoms = qw(\"true false\");\nvar keywords = qw(`\n\t\t\t  alias\n\t\t\t  break\n\t\t\t  case\n\t\t\t  const\n\t\t\t  const_assert\n\t\t\t  continue\n\t\t\t  continuing\n\t\t\t  default\n\t\t\t  diagnostic\n\t\t\t  discard\n\t\t\t  else\n\t\t\t  enable\n\t\t\t  fn\n\t\t\t  for\n\t\t\t  if\n\t\t\t  let\n\t\t\t  loop\n\t\t\t  override\n\t\t\t  requires\n\t\t\t  return\n\t\t\t  struct\n\t\t\t  switch\n\t\t\t  var\n\t\t\t  while\n\t\t\t  `);\nvar reserved = qw(`\n\t\t\t  NULL\n\t\t\t  Self\n\t\t\t  abstract\n\t\t\t  active\n\t\t\t  alignas\n\t\t\t  alignof\n\t\t\t  as\n\t\t\t  asm\n\t\t\t  asm_fragment\n\t\t\t  async\n\t\t\t  attribute\n\t\t\t  auto\n\t\t\t  await\n\t\t\t  become\n\t\t\t  binding_array\n\t\t\t  cast\n\t\t\t  catch\n\t\t\t  class\n\t\t\t  co_await\n\t\t\t  co_return\n\t\t\t  co_yield\n\t\t\t  coherent\n\t\t\t  column_major\n\t\t\t  common\n\t\t\t  compile\n\t\t\t  compile_fragment\n\t\t\t  concept\n\t\t\t  const_cast\n\t\t\t  consteval\n\t\t\t  constexpr\n\t\t\t  constinit\n\t\t\t  crate\n\t\t\t  debugger\n\t\t\t  decltype\n\t\t\t  delete\n\t\t\t  demote\n\t\t\t  demote_to_helper\n\t\t\t  do\n\t\t\t  dynamic_cast\n\t\t\t  enum\n\t\t\t  explicit\n\t\t\t  export\n\t\t\t  extends\n\t\t\t  extern\n\t\t\t  external\n\t\t\t  fallthrough\n\t\t\t  filter\n\t\t\t  final\n\t\t\t  finally\n\t\t\t  friend\n\t\t\t  from\n\t\t\t  fxgroup\n\t\t\t  get\n\t\t\t  goto\n\t\t\t  groupshared\n\t\t\t  highp\n\t\t\t  impl\n\t\t\t  implements\n\t\t\t  import\n\t\t\t  inline\n\t\t\t  instanceof\n\t\t\t  interface\n\t\t\t  layout\n\t\t\t  lowp\n\t\t\t  macro\n\t\t\t  macro_rules\n\t\t\t  match\n\t\t\t  mediump\n\t\t\t  meta\n\t\t\t  mod\n\t\t\t  module\n\t\t\t  move\n\t\t\t  mut\n\t\t\t  mutable\n\t\t\t  namespace\n\t\t\t  new\n\t\t\t  nil\n\t\t\t  noexcept\n\t\t\t  noinline\n\t\t\t  nointerpolation\n\t\t\t  noperspective\n\t\t\t  null\n\t\t\t  nullptr\n\t\t\t  of\n\t\t\t  operator\n\t\t\t  package\n\t\t\t  packoffset\n\t\t\t  partition\n\t\t\t  pass\n\t\t\t  patch\n\t\t\t  pixelfragment\n\t\t\t  precise\n\t\t\t  precision\n\t\t\t  premerge\n\t\t\t  priv\n\t\t\t  protected\n\t\t\t  pub\n\t\t\t  public\n\t\t\t  readonly\n\t\t\t  ref\n\t\t\t  regardless\n\t\t\t  register\n\t\t\t  reinterpret_cast\n\t\t\t  require\n\t\t\t  resource\n\t\t\t  restrict\n\t\t\t  self\n\t\t\t  set\n\t\t\t  shared\n\t\t\t  sizeof\n\t\t\t  smooth\n\t\t\t  snorm\n\t\t\t  static\n\t\t\t  static_assert\n\t\t\t  static_cast\n\t\t\t  std\n\t\t\t  subroutine\n\t\t\t  super\n\t\t\t  target\n\t\t\t  template\n\t\t\t  this\n\t\t\t  thread_local\n\t\t\t  throw\n\t\t\t  trait\n\t\t\t  try\n\t\t\t  type\n\t\t\t  typedef\n\t\t\t  typeid\n\t\t\t  typename\n\t\t\t  typeof\n\t\t\t  union\n\t\t\t  unless\n\t\t\t  unorm\n\t\t\t  unsafe\n\t\t\t  unsized\n\t\t\t  use\n\t\t\t  using\n\t\t\t  varying\n\t\t\t  virtual\n\t\t\t  volatile\n\t\t\t  wgsl\n\t\t\t  where\n\t\t\t  with\n\t\t\t  writeonly\n\t\t\t  yield\n\t\t\t  `);\nvar predeclared_enums = qw(`\n\t\tread write read_write\n\t\tfunction private workgroup uniform storage\n\t\tperspective linear flat\n\t\tcenter centroid sample\n\t\tvertex_index instance_index position front_facing frag_depth\n\t\t\tlocal_invocation_id local_invocation_index\n\t\t\tglobal_invocation_id workgroup_id num_workgroups\n\t\t\tsample_index sample_mask\n\t\trgba8unorm\n\t\trgba8snorm\n\t\trgba8uint\n\t\trgba8sint\n\t\trgba16uint\n\t\trgba16sint\n\t\trgba16float\n\t\tr32uint\n\t\tr32sint\n\t\tr32float\n\t\trg32uint\n\t\trg32sint\n\t\trg32float\n\t\trgba32uint\n\t\trgba32sint\n\t\trgba32float\n\t\tbgra8unorm\n`);\nvar predeclared_types = qw(`\n\t\tbool\n\t\tf16\n\t\tf32\n\t\ti32\n\t\tsampler sampler_comparison\n\t\ttexture_depth_2d\n\t\ttexture_depth_2d_array\n\t\ttexture_depth_cube\n\t\ttexture_depth_cube_array\n\t\ttexture_depth_multisampled_2d\n\t\ttexture_external\n\t\ttexture_external\n\t\tu32\n\t\t`);\nvar predeclared_type_generators = qw(`\n\t\tarray\n\t\tatomic\n\t\tmat2x2\n\t\tmat2x3\n\t\tmat2x4\n\t\tmat3x2\n\t\tmat3x3\n\t\tmat3x4\n\t\tmat4x2\n\t\tmat4x3\n\t\tmat4x4\n\t\tptr\n\t\ttexture_1d\n\t\ttexture_2d\n\t\ttexture_2d_array\n\t\ttexture_3d\n\t\ttexture_cube\n\t\ttexture_cube_array\n\t\ttexture_multisampled_2d\n\t\ttexture_storage_1d\n\t\ttexture_storage_2d\n\t\ttexture_storage_2d_array\n\t\ttexture_storage_3d\n\t\tvec2\n\t\tvec3\n\t\tvec4\n\t\t`);\nvar predeclared_type_aliases = qw(`\n\t\tvec2i vec3i vec4i\n\t\tvec2u vec3u vec4u\n\t\tvec2f vec3f vec4f\n\t\tvec2h vec3h vec4h\n\t\tmat2x2f mat2x3f mat2x4f\n\t\tmat3x2f mat3x3f mat3x4f\n\t\tmat4x2f mat4x3f mat4x4f\n\t\tmat2x2h mat2x3h mat2x4h\n\t\tmat3x2h mat3x3h mat3x4h\n\t\tmat4x2h mat4x3h mat4x4h\n\t\t`);\nvar predeclared_intrinsics = qw(`\n  bitcast all any select arrayLength abs acos acosh asin asinh atan atanh atan2\n  ceil clamp cos cosh countLeadingZeros countOneBits countTrailingZeros cross\n  degrees determinant distance dot exp exp2 extractBits faceForward firstLeadingBit\n  firstTrailingBit floor fma fract frexp inverseBits inverseSqrt ldexp length\n  log log2 max min mix modf normalize pow quantizeToF16 radians reflect refract\n  reverseBits round saturate sign sin sinh smoothstep sqrt step tan tanh transpose\n  trunc dpdx dpdxCoarse dpdxFine dpdy dpdyCoarse dpdyFine fwidth fwidthCoarse fwidthFine\n  textureDimensions textureGather textureGatherCompare textureLoad textureNumLayers\n  textureNumLevels textureNumSamples textureSample textureSampleBias textureSampleCompare\n  textureSampleCompareLevel textureSampleGrad textureSampleLevel textureSampleBaseClampToEdge\n  textureStore atomicLoad atomicStore atomicAdd atomicSub atomicMax atomicMin\n  atomicAnd atomicOr atomicXor atomicExchange atomicCompareExchangeWeak pack4x8snorm\n  pack4x8unorm pack2x16snorm pack2x16unorm pack2x16float unpack4x8snorm unpack4x8unorm\n  unpack2x16snorm unpack2x16unorm unpack2x16float storageBarrier workgroupBarrier\n  workgroupUniformLoad\n`);\nvar operators = qw(`\n\t\t\t\t\t &\n\t\t\t\t\t &&\n\t\t\t\t\t ->\n\t\t\t\t\t /\n\t\t\t\t\t =\n\t\t\t\t\t ==\n\t\t\t\t\t !=\n\t\t\t\t\t >\n\t\t\t\t\t >=\n\t\t\t\t\t <\n\t\t\t\t\t <=\n\t\t\t\t\t %\n\t\t\t\t\t -\n\t\t\t\t\t --\n\t\t\t\t\t +\n\t\t\t\t\t ++\n\t\t\t\t\t |\n\t\t\t\t\t ||\n\t\t\t\t\t *\n\t\t\t\t\t <<\n\t\t\t\t\t >>\n\t\t\t\t\t +=\n\t\t\t\t\t -=\n\t\t\t\t\t *=\n\t\t\t\t\t /=\n\t\t\t\t\t %=\n\t\t\t\t\t &=\n\t\t\t\t\t |=\n\t\t\t\t\t ^=\n\t\t\t\t\t >>=\n\t\t\t\t\t <<=\n\t\t\t\t\t `);\nvar directive_re = /enable|requires|diagnostic/;\nvar ident_re = /[_\\p{XID_Start}]\\p{XID_Continue}*/u;\nvar predefined_token = \"variable.predefined\";\nvar language = {\n  tokenPostfix: \".wgsl\",\n  defaultToken: \"invalid\",\n  unicode: true,\n  atoms,\n  keywords,\n  reserved,\n  predeclared_enums,\n  predeclared_types,\n  predeclared_type_generators,\n  predeclared_type_aliases,\n  predeclared_intrinsics,\n  operators,\n  symbols: /[!%&*+\\-\\.\\/:;<=>^|_~,]+/,\n  tokenizer: {\n    root: [\n      [directive_re, \"keyword\", \"@directive\"],\n      [\n        // Identifier-like things, but also include '_'\n        ident_re,\n        {\n          cases: {\n            \"@atoms\": predefined_token,\n            \"@keywords\": \"keyword\",\n            \"@reserved\": \"invalid\",\n            \"@predeclared_enums\": predefined_token,\n            \"@predeclared_types\": predefined_token,\n            \"@predeclared_type_generators\": predefined_token,\n            \"@predeclared_type_aliases\": predefined_token,\n            \"@predeclared_intrinsics\": predefined_token,\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@commentOrSpace\" },\n      { include: \"@numbers\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [\"@\", \"annotation\", \"@attribute\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@default\": \"delimiter\"\n          }\n        }\n      ],\n      [/./, \"invalid\"]\n    ],\n    commentOrSpace: [\n      [/\\s+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@blockComment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    blockComment: [\n      // Soak up uninteresting text: anything except * or /\n      [/[^\\/*]+/, \"comment\"],\n      // Recognize the start of a nested block comment.\n      [/\\/\\*/, \"comment\", \"@push\"],\n      // Recognize the end of a nested block comment.\n      [/\\*\\//, \"comment\", \"@pop\"],\n      // Recognize insignificant * and /\n      [/[\\/*]/, \"comment\"]\n    ],\n    attribute: [\n      // For things like '@fragment' both '@' and 'fragment'\n      // are marked as annotations.  This should work even if\n      // there are spaces or comments between the two tokens.\n      { include: \"@commentOrSpace\" },\n      [/\\w+/, \"annotation\", \"@pop\"]\n    ],\n    directive: [\n      // For things like 'enable f16;', 'enable' maps to 'meta'\n      // and 'f16' maps to 'meta.tag'.\n      { include: \"@commentOrSpace\" },\n      [/[()]/, \"@brackets\"],\n      [/,/, \"delimiter\"],\n      [ident_re, \"meta.content\"],\n      [/;/, \"delimiter\", \"@pop\"]\n    ],\n    numbers: [\n      // Decimal float literals\n      // https://www.w3.org/TR/WGSL/#syntax-decimal_float_literal\n      // 0, with type-specifying suffix.\n      [/0[fh]/, \"number.float\"],\n      // Other decimal integer, with type-specifying suffix.\n      [/[1-9][0-9]*[fh]/, \"number.float\"],\n      // Has decimal point, at least one digit after decimal.\n      [/[0-9]*\\.[0-9]+([eE][+-]?[0-9]+)?[fh]?/, \"number.float\"],\n      // Has decimal point, at least one digit before decimal.\n      [/[0-9]+\\.[0-9]*([eE][+-]?[0-9]+)?[fh]?/, \"number.float\"],\n      // Has at least one digit, and has an exponent.\n      [/[0-9]+[eE][+-]?[0-9]+[fh]?/, \"number.float\"],\n      // Hex float literals\n      // https://www.w3.org/TR/WGSL/#syntax-hex_float_literal\n      [/0[xX][0-9a-fA-F]*\\.[0-9a-fA-F]+(?:[pP][+-]?[0-9]+[fh]?)?/, \"number.hex\"],\n      [/0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*(?:[pP][+-]?[0-9]+[fh]?)?/, \"number.hex\"],\n      [/0[xX][0-9a-fA-F]+[pP][+-]?[0-9]+[fh]?/, \"number.hex\"],\n      // Hexadecimal integer literals\n      // https://www.w3.org/TR/WGSL/#syntax-hex_int_literal\n      [/0[xX][0-9a-fA-F]+[iu]?/, \"number.hex\"],\n      // Decimal integer literals\n      // https://www.w3.org/TR/WGSL/#syntax-decimal_int_literal\n      // We need two rules here because 01 is not valid.\n      [/[1-9][0-9]*[iu]?/, \"number\"],\n      [/0[iu]?/, \"number\"]\n      // Must match last\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/wgsl/wgsl.js\n"));

/***/ })

}]);