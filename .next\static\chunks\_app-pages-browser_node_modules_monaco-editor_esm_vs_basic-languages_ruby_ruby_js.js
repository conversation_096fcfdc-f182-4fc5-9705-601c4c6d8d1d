"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_ruby_ruby_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/ruby/ruby.js":
/*!************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/ruby/ruby.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/ruby/ruby.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"=begin\", \"=end\"]\n  },\n  brackets: [\n    [\"(\", \")\"],\n    [\"{\", \"}\"],\n    [\"[\", \"]\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  indentationRules: {\n    increaseIndentPattern: new RegExp(\n      `^\\\\s*((begin|class|(private|protected)\\\\s+def|def|else|elsif|ensure|for|if|module|rescue|unless|until|when|while|case)|([^#]*\\\\sdo\\\\b)|([^#]*=\\\\s*(case|if|unless)))\\\\b([^#\\\\{;]|(\"|'|/).*\\\\4)*(#.*)?$`\n    ),\n    decreaseIndentPattern: new RegExp(\n      \"^\\\\s*([}\\\\]]([,)]?\\\\s*(#|$)|\\\\.[a-zA-Z_]\\\\w*\\\\b)|(end|rescue|ensure|else|elsif|when)\\\\b)\"\n    )\n  }\n};\nvar language = {\n  tokenPostfix: \".ruby\",\n  keywords: [\n    \"__LINE__\",\n    \"__ENCODING__\",\n    \"__FILE__\",\n    \"BEGIN\",\n    \"END\",\n    \"alias\",\n    \"and\",\n    \"begin\",\n    \"break\",\n    \"case\",\n    \"class\",\n    \"def\",\n    \"defined?\",\n    \"do\",\n    \"else\",\n    \"elsif\",\n    \"end\",\n    \"ensure\",\n    \"for\",\n    \"false\",\n    \"if\",\n    \"in\",\n    \"module\",\n    \"next\",\n    \"nil\",\n    \"not\",\n    \"or\",\n    \"redo\",\n    \"rescue\",\n    \"retry\",\n    \"return\",\n    \"self\",\n    \"super\",\n    \"then\",\n    \"true\",\n    \"undef\",\n    \"unless\",\n    \"until\",\n    \"when\",\n    \"while\",\n    \"yield\"\n  ],\n  keywordops: [\"::\", \"..\", \"...\", \"?\", \":\", \"=>\"],\n  builtins: [\n    \"require\",\n    \"public\",\n    \"private\",\n    \"include\",\n    \"extend\",\n    \"attr_reader\",\n    \"protected\",\n    \"private_class_method\",\n    \"protected_class_method\",\n    \"new\"\n  ],\n  // these are closed by 'end' (if, while and until are handled separately)\n  declarations: [\n    \"module\",\n    \"class\",\n    \"def\",\n    \"case\",\n    \"do\",\n    \"begin\",\n    \"for\",\n    \"if\",\n    \"while\",\n    \"until\",\n    \"unless\"\n  ],\n  linedecls: [\"def\", \"case\", \"do\", \"begin\", \"for\", \"if\", \"while\", \"until\", \"unless\"],\n  operators: [\n    \"^\",\n    \"&\",\n    \"|\",\n    \"<=>\",\n    \"==\",\n    \"===\",\n    \"!~\",\n    \"=~\",\n    \">\",\n    \">=\",\n    \"<\",\n    \"<=\",\n    \"<<\",\n    \">>\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"**\",\n    \"~\",\n    \"+@\",\n    \"-@\",\n    \"[]\",\n    \"[]=\",\n    \"`\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"**=\",\n    \"/=\",\n    \"^=\",\n    \"%=\",\n    \"<<=\",\n    \">>=\",\n    \"&=\",\n    \"&&=\",\n    \"||=\",\n    \"|=\"\n  ],\n  brackets: [\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" }\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%\\.]+/,\n  // escape sequences\n  escape: /(?:[abefnrstv\\\\\"'\\n\\r]|[0-7]{1,3}|x[0-9A-Fa-f]{1,2}|u[0-9A-Fa-f]{4})/,\n  escapes: /\\\\(?:C\\-(@escape|.)|c(@escape|.)|@escape)/,\n  decpart: /\\d(_?\\d)*/,\n  decimal: /0|@decpart/,\n  delim: /[^a-zA-Z0-9\\s\\n\\r]/,\n  heredelim: /(?:\\w+|'[^']*'|\"[^\"]*\"|`[^`]*`)/,\n  regexpctl: /[(){}\\[\\]\\$\\^|\\-*+?\\.]/,\n  regexpesc: /\\\\(?:[AzZbBdDfnrstvwWn0\\\\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})?/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    // Main entry.\n    // root.<decl> where decl is the current opening declaration (like 'class')\n    root: [\n      // identifiers and keywords\n      // most complexity here is due to matching 'end' correctly with declarations.\n      // We distinguish a declaration that comes first on a line, versus declarations further on a line (which are most likey modifiers)\n      [\n        /^(\\s*)([a-z_]\\w*[!?=]?)/,\n        [\n          \"white\",\n          {\n            cases: {\n              \"for|until|while\": {\n                token: \"keyword.$2\",\n                next: \"@dodecl.$2\"\n              },\n              \"@declarations\": {\n                token: \"keyword.$2\",\n                next: \"@root.$2\"\n              },\n              end: { token: \"keyword.$S2\", next: \"@pop\" },\n              \"@keywords\": \"keyword\",\n              \"@builtins\": \"predefined\",\n              \"@default\": \"identifier\"\n            }\n          }\n        ]\n      ],\n      [\n        /[a-z_]\\w*[!?=]?/,\n        {\n          cases: {\n            \"if|unless|while|until\": {\n              token: \"keyword.$0x\",\n              next: \"@modifier.$0x\"\n            },\n            for: { token: \"keyword.$2\", next: \"@dodecl.$2\" },\n            \"@linedecls\": { token: \"keyword.$0\", next: \"@root.$0\" },\n            end: { token: \"keyword.$S2\", next: \"@pop\" },\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[A-Z][\\w]*[!?=]?/, \"constructor.identifier\"],\n      // constant\n      [/\\$[\\w]*/, \"global.constant\"],\n      // global\n      [/@[\\w]*/, \"namespace.instance.identifier\"],\n      // instance\n      [/@@@[\\w]*/, \"namespace.class.identifier\"],\n      // class\n      // here document\n      [/<<[-~](@heredelim).*/, { token: \"string.heredoc.delimiter\", next: \"@heredoc.$1\" }],\n      [/[ \\t\\r\\n]+<<(@heredelim).*/, { token: \"string.heredoc.delimiter\", next: \"@heredoc.$1\" }],\n      [/^<<(@heredelim).*/, { token: \"string.heredoc.delimiter\", next: \"@heredoc.$1\" }],\n      // whitespace\n      { include: \"@whitespace\" },\n      // strings\n      [/\"/, { token: \"string.d.delim\", next: '@dstring.d.\"' }],\n      [/'/, { token: \"string.sq.delim\", next: \"@sstring.sq\" }],\n      // % literals. For efficiency, rematch in the 'pstring' state\n      [/%([rsqxwW]|Q?)/, { token: \"@rematch\", next: \"pstring\" }],\n      // commands and symbols\n      [/`/, { token: \"string.x.delim\", next: \"@dstring.x.`\" }],\n      [/:(\\w|[$@])\\w*[!?=]?/, \"string.s\"],\n      [/:\"/, { token: \"string.s.delim\", next: '@dstring.s.\"' }],\n      [/:'/, { token: \"string.s.delim\", next: \"@sstring.s\" }],\n      // regular expressions. Lookahead for a (not escaped) closing forwardslash on the same line\n      [/\\/(?=(\\\\\\/|[^\\/\\n])+\\/)/, { token: \"regexp.delim\", next: \"@regexp\" }],\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@keywordops\": \"keyword\",\n            \"@operators\": \"operator\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/[;,]/, \"delimiter\"],\n      // numbers\n      [/0[xX][0-9a-fA-F](_?[0-9a-fA-F])*/, \"number.hex\"],\n      [/0[_oO][0-7](_?[0-7])*/, \"number.octal\"],\n      [/0[bB][01](_?[01])*/, \"number.binary\"],\n      [/0[dD]@decpart/, \"number\"],\n      [\n        /@decimal((\\.@decpart)?([eE][\\-+]?@decpart)?)/,\n        {\n          cases: {\n            $1: \"number.float\",\n            \"@default\": \"number\"\n          }\n        }\n      ]\n    ],\n    // used to not treat a 'do' as a block opener if it occurs on the same\n    // line as a 'do' statement: 'while|until|for'\n    // dodecl.<decl> where decl is the declarations started, like 'while'\n    dodecl: [\n      [/^/, { token: \"\", switchTo: \"@root.$S2\" }],\n      // get out of do-skipping mode on a new line\n      [\n        /[a-z_]\\w*[!?=]?/,\n        {\n          cases: {\n            end: { token: \"keyword.$S2\", next: \"@pop\" },\n            // end on same line\n            do: { token: \"keyword\", switchTo: \"@root.$S2\" },\n            // do on same line: not an open bracket here\n            \"@linedecls\": {\n              token: \"@rematch\",\n              switchTo: \"@root.$S2\"\n            },\n            // other declaration on same line: rematch\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@root\" }\n    ],\n    // used to prevent potential modifiers ('if|until|while|unless') to match\n    // with 'end' keywords.\n    // modifier.<decl>x where decl is the declaration starter, like 'if'\n    modifier: [\n      [/^/, \"\", \"@pop\"],\n      // it was a modifier: get out of modifier mode on a new line\n      [\n        /[a-z_]\\w*[!?=]?/,\n        {\n          cases: {\n            end: { token: \"keyword.$S2\", next: \"@pop\" },\n            // end on same line\n            \"then|else|elsif|do\": {\n              token: \"keyword\",\n              switchTo: \"@root.$S2\"\n            },\n            // real declaration and not a modifier\n            \"@linedecls\": {\n              token: \"@rematch\",\n              switchTo: \"@root.$S2\"\n            },\n            // other declaration => not a modifier\n            \"@keywords\": \"keyword\",\n            \"@builtins\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@root\" }\n    ],\n    // single quote strings (also used for symbols)\n    // sstring.<kind>  where kind is 'sq' (single quote) or 's' (symbol)\n    sstring: [\n      [/[^\\\\']+/, \"string.$S2\"],\n      [/\\\\\\\\|\\\\'|\\\\$/, \"string.$S2.escape\"],\n      [/\\\\./, \"string.$S2.invalid\"],\n      [/'/, { token: \"string.$S2.delim\", next: \"@pop\" }]\n    ],\n    // double quoted \"string\".\n    // dstring.<kind>.<delim> where kind is 'd' (double quoted), 'x' (command), or 's' (symbol)\n    // and delim is the ending delimiter (\" or `)\n    dstring: [\n      [/[^\\\\`\"#]+/, \"string.$S2\"],\n      [/#/, \"string.$S2.escape\", \"@interpolated\"],\n      [/\\\\$/, \"string.$S2.escape\"],\n      [/@escapes/, \"string.$S2.escape\"],\n      [/\\\\./, \"string.$S2.escape.invalid\"],\n      [\n        /[`\"]/,\n        {\n          cases: {\n            \"$#==$S3\": { token: \"string.$S2.delim\", next: \"@pop\" },\n            \"@default\": \"string.$S2\"\n          }\n        }\n      ]\n    ],\n    // literal documents\n    // heredoc.<close> where close is the closing delimiter\n    heredoc: [\n      [\n        /^(\\s*)(@heredelim)$/,\n        {\n          cases: {\n            \"$2==$S2\": [\"string.heredoc\", { token: \"string.heredoc.delimiter\", next: \"@pop\" }],\n            \"@default\": [\"string.heredoc\", \"string.heredoc\"]\n          }\n        }\n      ],\n      [/.*/, \"string.heredoc\"]\n    ],\n    // interpolated sequence\n    interpolated: [\n      [/\\$\\w*/, \"global.constant\", \"@pop\"],\n      [/@\\w*/, \"namespace.class.identifier\", \"@pop\"],\n      [/@@@\\w*/, \"namespace.instance.identifier\", \"@pop\"],\n      [\n        /[{]/,\n        {\n          token: \"string.escape.curly\",\n          switchTo: \"@interpolated_compound\"\n        }\n      ],\n      [\"\", \"\", \"@pop\"]\n      // just a # is interpreted as a #\n    ],\n    // any code\n    interpolated_compound: [\n      [/[}]/, { token: \"string.escape.curly\", next: \"@pop\" }],\n      { include: \"@root\" }\n    ],\n    // %r quoted regexp\n    // pregexp.<open>.<close> where open/close are the open/close delimiter\n    pregexp: [\n      { include: \"@whitespace\" },\n      // turns out that you can quote using regex control characters, aargh!\n      // for example; %r|kgjgaj| is ok (even though | is used for alternation)\n      // so, we need to match those first\n      [\n        /[^\\(\\{\\[\\\\]/,\n        {\n          cases: {\n            \"$#==$S3\": { token: \"regexp.delim\", next: \"@pop\" },\n            \"$#==$S2\": { token: \"regexp.delim\", next: \"@push\" },\n            // nested delimiters are allowed..\n            \"~[)}\\\\]]\": \"@brackets.regexp.escape.control\",\n            \"~@regexpctl\": \"regexp.escape.control\",\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexcontrol\" }\n    ],\n    // We match regular expression quite precisely\n    regexp: [\n      { include: \"@regexcontrol\" },\n      [/[^\\\\\\/]/, \"regexp\"],\n      [\"/[ixmp]*\", { token: \"regexp.delim\" }, \"@pop\"]\n    ],\n    regexcontrol: [\n      [\n        /(\\{)(\\d+(?:,\\d*)?)(\\})/,\n        [\n          \"@brackets.regexp.escape.control\",\n          \"regexp.escape.control\",\n          \"@brackets.regexp.escape.control\"\n        ]\n      ],\n      [\n        /(\\[)(\\^?)/,\n        [\"@brackets.regexp.escape.control\", { token: \"regexp.escape.control\", next: \"@regexrange\" }]\n      ],\n      [/(\\()(\\?[:=!])/, [\"@brackets.regexp.escape.control\", \"regexp.escape.control\"]],\n      [/\\(\\?#/, { token: \"regexp.escape.control\", next: \"@regexpcomment\" }],\n      [/[()]/, \"@brackets.regexp.escape.control\"],\n      [/@regexpctl/, \"regexp.escape.control\"],\n      [/\\\\$/, \"regexp.escape\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/\\\\\\./, \"regexp.invalid\"],\n      [/#/, \"regexp.escape\", \"@interpolated\"]\n    ],\n    regexrange: [\n      [/-/, \"regexp.escape.control\"],\n      [/\\^/, \"regexp.invalid\"],\n      [/\\\\$/, \"regexp.escape\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/[^\\]]/, \"regexp\"],\n      [/\\]/, \"@brackets.regexp.escape.control\", \"@pop\"]\n    ],\n    regexpcomment: [\n      [/[^)]+/, \"comment\"],\n      [/\\)/, { token: \"regexp.escape.control\", next: \"@pop\" }]\n    ],\n    // % quoted strings\n    // A bit repetitive since we need to often special case the kind of ending delimiter\n    pstring: [\n      [/%([qws])\\(/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.(.)\" }],\n      [/%([qws])\\[/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.[.]\" }],\n      [/%([qws])\\{/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.{.}\" }],\n      [/%([qws])</, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.<.>\" }],\n      [/%([qws])(@delim)/, { token: \"string.$1.delim\", switchTo: \"@qstring.$1.$2.$2\" }],\n      [/%r\\(/, { token: \"regexp.delim\", switchTo: \"@pregexp.(.)\" }],\n      [/%r\\[/, { token: \"regexp.delim\", switchTo: \"@pregexp.[.]\" }],\n      [/%r\\{/, { token: \"regexp.delim\", switchTo: \"@pregexp.{.}\" }],\n      [/%r</, { token: \"regexp.delim\", switchTo: \"@pregexp.<.>\" }],\n      [/%r(@delim)/, { token: \"regexp.delim\", switchTo: \"@pregexp.$1.$1\" }],\n      [/%(x|W|Q?)\\(/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.(.)\" }],\n      [/%(x|W|Q?)\\[/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.[.]\" }],\n      [/%(x|W|Q?)\\{/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.{.}\" }],\n      [/%(x|W|Q?)</, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.<.>\" }],\n      [/%(x|W|Q?)(@delim)/, { token: \"string.$1.delim\", switchTo: \"@qqstring.$1.$2.$2\" }],\n      [/%([rqwsxW]|Q?)./, { token: \"invalid\", next: \"@pop\" }],\n      // recover\n      [/./, { token: \"invalid\", next: \"@pop\" }]\n      // recover\n    ],\n    // non-expanded quoted string.\n    // qstring.<kind>.<open>.<close>\n    //  kind = q|w|s  (single quote, array, symbol)\n    //  open = open delimiter\n    //  close = close delimiter\n    qstring: [\n      [/\\\\$/, \"string.$S2.escape\"],\n      [/\\\\./, \"string.$S2.escape\"],\n      [\n        /./,\n        {\n          cases: {\n            \"$#==$S4\": { token: \"string.$S2.delim\", next: \"@pop\" },\n            \"$#==$S3\": { token: \"string.$S2.delim\", next: \"@push\" },\n            // nested delimiters are allowed..\n            \"@default\": \"string.$S2\"\n          }\n        }\n      ]\n    ],\n    // expanded quoted string.\n    // qqstring.<kind>.<open>.<close>\n    //  kind = Q|W|x  (double quote, array, command)\n    //  open = open delimiter\n    //  close = close delimiter\n    qqstring: [[/#/, \"string.$S2.escape\", \"@interpolated\"], { include: \"@qstring\" }],\n    // whitespace & comments\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/^\\s*=begin\\b/, \"comment\", \"@comment\"],\n      [/#.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^=]+/, \"comment\"],\n      [/^\\s*=begin\\b/, \"comment.invalid\"],\n      // nested comment\n      [/^\\s*=end\\b.*/, \"comment\", \"@pop\"],\n      [/[=]/, \"comment\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/ruby/ruby.js\n"));

/***/ })

}]);