"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@monaco-editor";
exports.ids = ["vendor-chunks/@monaco-editor"];
exports.modules = {

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrayLikeToArray: () => (/* binding */ _arrayLikeToArray),\n/* harmony export */   arrayWithHoles: () => (/* binding */ _arrayWithHoles),\n/* harmony export */   defineProperty: () => (/* binding */ _defineProperty),\n/* harmony export */   iterableToArrayLimit: () => (/* binding */ _iterableToArrayLimit),\n/* harmony export */   nonIterableRest: () => (/* binding */ _nonIterableRest),\n/* harmony export */   objectSpread2: () => (/* binding */ _objectSpread2),\n/* harmony export */   objectWithoutProperties: () => (/* binding */ _objectWithoutProperties),\n/* harmony export */   objectWithoutPropertiesLoose: () => (/* binding */ _objectWithoutPropertiesLoose),\n/* harmony export */   slicedToArray: () => (/* binding */ _slicedToArray),\n/* harmony export */   unsupportedIterableToArray: () => (/* binding */ _unsupportedIterableToArray)\n/* harmony export */ });\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/config/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/config/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar config = {\n  paths: {\n    vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs'\n  }\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy9jb25maWcvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaUVBQWUsTUFBTSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXJ0d29yay1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL2NvbmZpZy9pbmRleC5qcz82YjgyIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBjb25maWcgPSB7XG4gIHBhdGhzOiB7XG4gICAgdnM6ICdodHRwczovL2Nkbi5qc2RlbGl2ci5uZXQvbnBtL21vbmFjby1lZGl0b3JAMC41Mi4yL21pbi92cydcbiAgfVxufTtcblxuZXhwb3J0IGRlZmF1bHQgY29uZmlnO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/config/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _loader_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _loader_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./loader/index.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1QztBQUNLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXJ0d29yay1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL2luZGV4LmpzPzgyNDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGxvYWRlciBmcm9tICcuL2xvYWRlci9pbmRleC5qcyc7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSAnLi9sb2FkZXIvaW5kZXguanMnO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/loader/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var state_local__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! state-local */ \"(ssr)/./node_modules/state-local/lib/es/state-local.js\");\n/* harmony import */ var _config_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../config/index.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/config/index.js\");\n/* harmony import */ var _validators_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../validators/index.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js\");\n/* harmony import */ var _utils_compose_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/compose.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js\");\n/* harmony import */ var _utils_deepMerge_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/deepMerge.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js\");\n/* harmony import */ var _utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/makeCancelable.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js\");\n\n\n\n\n\n\n\n\n/** the local state of the module */\n\nvar _state$create = state_local__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n  config: _config_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n  isInitialized: false,\n  resolve: null,\n  reject: null,\n  monaco: null\n}),\n    _state$create2 = (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.slicedToArray)(_state$create, 2),\n    getState = _state$create2[0],\n    setState = _state$create2[1];\n/**\n * set the loader configuration\n * @param {Object} config - the configuration object\n */\n\n\nfunction config(globalConfig) {\n  var _validators$config = _validators_index_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].config(globalConfig),\n      monaco = _validators$config.monaco,\n      config = (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectWithoutProperties)(_validators$config, [\"monaco\"]);\n\n  setState(function (state) {\n    return {\n      config: (0,_utils_deepMerge_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(state.config, config),\n      monaco: monaco\n    };\n  });\n}\n/**\n * handles the initialization of the monaco-editor\n * @return {Promise} - returns an instance of monaco (with a cancelable promise)\n */\n\n\nfunction init() {\n  var state = getState(function (_ref) {\n    var monaco = _ref.monaco,\n        isInitialized = _ref.isInitialized,\n        resolve = _ref.resolve;\n    return {\n      monaco: monaco,\n      isInitialized: isInitialized,\n      resolve: resolve\n    };\n  });\n\n  if (!state.isInitialized) {\n    setState({\n      isInitialized: true\n    });\n\n    if (state.monaco) {\n      state.resolve(state.monaco);\n      return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n    }\n\n    if (window.monaco && window.monaco.editor) {\n      storeMonacoInstance(window.monaco);\n      state.resolve(window.monaco);\n      return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n    }\n\n    (0,_utils_compose_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(injectScripts, getMonacoLoaderScript)(configureLoader);\n  }\n\n  return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n}\n/**\n * injects provided scripts into the document.body\n * @param {Object} script - an HTML script element\n * @return {Object} - the injected HTML script element\n */\n\n\nfunction injectScripts(script) {\n  return document.body.appendChild(script);\n}\n/**\n * creates an HTML script element with/without provided src\n * @param {string} [src] - the source path of the script\n * @return {Object} - the created HTML script element\n */\n\n\nfunction createScript(src) {\n  var script = document.createElement('script');\n  return src && (script.src = src), script;\n}\n/**\n * creates an HTML script element with the monaco loader src\n * @return {Object} - the created HTML script element\n */\n\n\nfunction getMonacoLoaderScript(configureLoader) {\n  var state = getState(function (_ref2) {\n    var config = _ref2.config,\n        reject = _ref2.reject;\n    return {\n      config: config,\n      reject: reject\n    };\n  });\n  var loaderScript = createScript(\"\".concat(state.config.paths.vs, \"/loader.js\"));\n\n  loaderScript.onload = function () {\n    return configureLoader();\n  };\n\n  loaderScript.onerror = state.reject;\n  return loaderScript;\n}\n/**\n * configures the monaco loader\n */\n\n\nfunction configureLoader() {\n  var state = getState(function (_ref3) {\n    var config = _ref3.config,\n        resolve = _ref3.resolve,\n        reject = _ref3.reject;\n    return {\n      config: config,\n      resolve: resolve,\n      reject: reject\n    };\n  });\n  var require = window.require;\n\n  require.config(state.config);\n\n  require(['vs/editor/editor.main'], function (monaco) {\n    storeMonacoInstance(monaco);\n    state.resolve(monaco);\n  }, function (error) {\n    state.reject(error);\n  });\n}\n/**\n * store monaco instance in local state\n */\n\n\nfunction storeMonacoInstance(monaco) {\n  if (!getState().monaco) {\n    setState({\n      monaco: monaco\n    });\n  }\n}\n/**\n * internal helper function\n * extracts stored monaco instance\n * @return {Object|null} - the monaco instance\n */\n\n\nfunction __getMonacoInstance() {\n  return getState(function (_ref4) {\n    var monaco = _ref4.monaco;\n    return monaco;\n  });\n}\n\nvar wrapperPromise = new Promise(function (resolve, reject) {\n  return setState({\n    resolve: resolve,\n    reject: reject\n  });\n});\nvar loader = {\n  config: config,\n  init: init,\n  __getMonacoInstance: __getMonacoInstance\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (loader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js":
/*!********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/compose.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar compose = function compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (compose);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9jb21wb3NlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLHFFQUFxRSxhQUFhO0FBQ2xGO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUEsaUVBQWUsT0FBTyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXJ0d29yay1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL3V0aWxzL2NvbXBvc2UuanM/ZmQ5ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgY29tcG9zZSA9IGZ1bmN0aW9uIGNvbXBvc2UoKSB7XG4gIGZvciAodmFyIF9sZW4gPSBhcmd1bWVudHMubGVuZ3RoLCBmbnMgPSBuZXcgQXJyYXkoX2xlbiksIF9rZXkgPSAwOyBfa2V5IDwgX2xlbjsgX2tleSsrKSB7XG4gICAgZm5zW19rZXldID0gYXJndW1lbnRzW19rZXldO1xuICB9XG5cbiAgcmV0dXJuIGZ1bmN0aW9uICh4KSB7XG4gICAgcmV0dXJuIGZucy5yZWR1Y2VSaWdodChmdW5jdGlvbiAoeSwgZikge1xuICAgICAgcmV0dXJuIGYoeSk7XG4gICAgfSwgeCk7XG4gIH07XG59O1xuXG5leHBvcnQgZGVmYXVsdCBjb21wb3NlO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js":
/*!******************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/curry.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        nextArgs[_key2] = arguments[_key2];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (curry);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9jdXJyeS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBOztBQUVBLHdFQUF3RSxhQUFhO0FBQ3JGO0FBQ0E7O0FBRUE7QUFDQSxpRkFBaUYsZUFBZTtBQUNoRztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGlFQUFlLEtBQUssRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FydHdvcmstcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9jdXJyeS5qcz80N2E4Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGN1cnJ5KGZuKSB7XG4gIHJldHVybiBmdW5jdGlvbiBjdXJyaWVkKCkge1xuICAgIHZhciBfdGhpcyA9IHRoaXM7XG5cbiAgICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgYXJncyA9IG5ldyBBcnJheShfbGVuKSwgX2tleSA9IDA7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICAgIGFyZ3NbX2tleV0gPSBhcmd1bWVudHNbX2tleV07XG4gICAgfVxuXG4gICAgcmV0dXJuIGFyZ3MubGVuZ3RoID49IGZuLmxlbmd0aCA/IGZuLmFwcGx5KHRoaXMsIGFyZ3MpIDogZnVuY3Rpb24gKCkge1xuICAgICAgZm9yICh2YXIgX2xlbjIgPSBhcmd1bWVudHMubGVuZ3RoLCBuZXh0QXJncyA9IG5ldyBBcnJheShfbGVuMiksIF9rZXkyID0gMDsgX2tleTIgPCBfbGVuMjsgX2tleTIrKykge1xuICAgICAgICBuZXh0QXJnc1tfa2V5Ml0gPSBhcmd1bWVudHNbX2tleTJdO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gY3VycmllZC5hcHBseShfdGhpcywgW10uY29uY2F0KGFyZ3MsIG5leHRBcmdzKSk7XG4gICAgfTtcbiAgfTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgY3Vycnk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\");\n\n\nfunction merge(target, source) {\n  Object.keys(source).forEach(function (key) {\n    if (source[key] instanceof Object) {\n      if (target[key]) {\n        Object.assign(source[key], merge(target[key], source[key]));\n      }\n    }\n  });\n  return (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectSpread2)((0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectSpread2)({}, target), source);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (merge);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9kZWVwTWVyZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkY7O0FBRTNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILFNBQVMsbUZBQWMsQ0FBQyxtRkFBYyxHQUFHO0FBQ3pDOztBQUVBLGlFQUFlLEtBQUssRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FydHdvcmstcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9kZWVwTWVyZ2UuanM/OWMyNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBvYmplY3RTcHJlYWQyIGFzIF9vYmplY3RTcHJlYWQyIH0gZnJvbSAnLi4vX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcyc7XG5cbmZ1bmN0aW9uIG1lcmdlKHRhcmdldCwgc291cmNlKSB7XG4gIE9iamVjdC5rZXlzKHNvdXJjZSkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7XG4gICAgaWYgKHNvdXJjZVtrZXldIGluc3RhbmNlb2YgT2JqZWN0KSB7XG4gICAgICBpZiAodGFyZ2V0W2tleV0pIHtcbiAgICAgICAgT2JqZWN0LmFzc2lnbihzb3VyY2Vba2V5XSwgbWVyZ2UodGFyZ2V0W2tleV0sIHNvdXJjZVtrZXldKSk7XG4gICAgICB9XG4gICAgfVxuICB9KTtcbiAgcmV0dXJuIF9vYmplY3RTcHJlYWQyKF9vYmplY3RTcHJlYWQyKHt9LCB0YXJnZXQpLCBzb3VyY2UpO1xufVxuXG5leHBvcnQgZGVmYXVsdCBtZXJnZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9pc09iamVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxXQUFXO0FBQ1g7O0FBRUEsaUVBQWUsUUFBUSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXJ0d29yay1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL3V0aWxzL2lzT2JqZWN0LmpzP2U0ZDciXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gaXNPYmplY3QodmFsdWUpIHtcbiAgcmV0dXJuIHt9LnRvU3RyaW5nLmNhbGwodmFsdWUpLmluY2x1ZGVzKCdPYmplY3QnKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgaXNPYmplY3Q7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CANCELATION_MESSAGE: () => (/* binding */ CANCELATION_MESSAGE),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325\nvar CANCELATION_MESSAGE = {\n  type: 'cancelation',\n  msg: 'operation is manually canceled'\n};\n\nfunction makeCancelable(promise) {\n  var hasCanceled_ = false;\n  var wrappedPromise = new Promise(function (resolve, reject) {\n    promise.then(function (val) {\n      return hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val);\n    });\n    promise[\"catch\"](reject);\n  });\n  return wrappedPromise.cancel = function () {\n    return hasCanceled_ = true;\n  }, wrappedPromise;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (makeCancelable);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9tYWtlQ2FuY2VsYWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBLGlFQUFlLGNBQWMsRUFBQztBQUNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXJ0d29yay1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9AbW9uYWNvLWVkaXRvci9sb2FkZXIvbGliL2VzL3V0aWxzL21ha2VDYW5jZWxhYmxlLmpzPzUwZWEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVGhlIHNvdXJjZSAoaGFzIGJlZW4gY2hhbmdlZCkgaXMgaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlYWN0L2lzc3Vlcy81NDY1I2lzc3VlY29tbWVudC0xNTc4ODgzMjVcbnZhciBDQU5DRUxBVElPTl9NRVNTQUdFID0ge1xuICB0eXBlOiAnY2FuY2VsYXRpb24nLFxuICBtc2c6ICdvcGVyYXRpb24gaXMgbWFudWFsbHkgY2FuY2VsZWQnXG59O1xuXG5mdW5jdGlvbiBtYWtlQ2FuY2VsYWJsZShwcm9taXNlKSB7XG4gIHZhciBoYXNDYW5jZWxlZF8gPSBmYWxzZTtcbiAgdmFyIHdyYXBwZWRQcm9taXNlID0gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkge1xuICAgIHByb21pc2UudGhlbihmdW5jdGlvbiAodmFsKSB7XG4gICAgICByZXR1cm4gaGFzQ2FuY2VsZWRfID8gcmVqZWN0KENBTkNFTEFUSU9OX01FU1NBR0UpIDogcmVzb2x2ZSh2YWwpO1xuICAgIH0pO1xuICAgIHByb21pc2VbXCJjYXRjaFwiXShyZWplY3QpO1xuICB9KTtcbiAgcmV0dXJuIHdyYXBwZWRQcm9taXNlLmNhbmNlbCA9IGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gaGFzQ2FuY2VsZWRfID0gdHJ1ZTtcbiAgfSwgd3JhcHBlZFByb21pc2U7XG59XG5cbmV4cG9ydCBkZWZhdWx0IG1ha2VDYW5jZWxhYmxlO1xuZXhwb3J0IHsgQ0FOQ0VMQVRJT05fTUVTU0FHRSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/validators/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   errorHandler: () => (/* binding */ errorHandler),\n/* harmony export */   errorMessages: () => (/* binding */ errorMessages)\n/* harmony export */ });\n/* harmony import */ var _utils_curry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/curry.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js\");\n/* harmony import */ var _utils_isObject_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/isObject.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js\");\n\n\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */\n\nfunction validateConfig(config) {\n  if (!config) errorHandler('configIsRequired');\n  if (!(0,_utils_isObject_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(config)) errorHandler('configType');\n\n  if (config.urls) {\n    informAboutDeprecation();\n    return {\n      paths: {\n        vs: config.urls.monacoBase\n      }\n    };\n  }\n\n  return config;\n}\n/**\n * logs deprecation message\n */\n\n\nfunction informAboutDeprecation() {\n  console.warn(errorMessages.deprecation);\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  configIsRequired: 'the configuration object is required',\n  configType: 'the configuration object should be an object',\n  \"default\": 'an unknown error accured in `@monaco-editor/loader` package',\n  deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = (0,_utils_curry_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(throwError)(errorMessages);\nvar validators = {\n  config: validateConfig\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validators);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/react/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@monaco-editor/react/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiffEditor: () => (/* binding */ we),\n/* harmony export */   Editor: () => (/* binding */ de),\n/* harmony export */   \"default\": () => (/* binding */ Ft),\n/* harmony export */   loader: () => (/* reexport safe */ _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   useMonaco: () => (/* binding */ Le)\n/* harmony export */ });\n/* harmony import */ var _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @monaco-editor/loader */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar le={wrapper:{display:\"flex\",position:\"relative\",textAlign:\"initial\"},fullWidth:{width:\"100%\"},hide:{display:\"none\"}},v=le;var ae={container:{display:\"flex\",height:\"100%\",width:\"100%\",justifyContent:\"center\",alignItems:\"center\"}},Y=ae;function Me({children:e}){return react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\",{style:Y.container},e)}var Z=Me;var $=Z;function Ee({width:e,height:r,isEditorReady:n,loading:t,_ref:a,className:m,wrapperProps:E}){return react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"section\",{style:{...v.wrapper,width:e,height:r},...E},!n&&react__WEBPACK_IMPORTED_MODULE_1__.createElement($,null,t),react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\",{ref:a,style:{...v.fullWidth,...!n&&v.hide},className:m}))}var ee=Ee;var H=(0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(ee);function Ce(e){(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(e,[])}var k=Ce;function he(e,r,n=!0){let t=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!0);(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(t.current||!n?()=>{t.current=!1}:e,r)}var l=he;function D(){}function h(e,r,n,t){return De(e,t)||be(e,r,n,t)}function De(e,r){return e.editor.getModel(te(e,r))}function be(e,r,n,t){return e.editor.createModel(r,n,t?te(e,t):void 0)}function te(e,r){return e.Uri.parse(r)}function Oe({original:e,modified:r,language:n,originalLanguage:t,modifiedLanguage:a,originalModelPath:m,modifiedModelPath:E,keepCurrentOriginalModel:g=!1,keepCurrentModifiedModel:N=!1,theme:x=\"light\",loading:P=\"Loading...\",options:y={},height:V=\"100%\",width:z=\"100%\",className:F,wrapperProps:j={},beforeMount:A=D,onMount:q=D}){let[M,O]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1),[T,s]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!0),u=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),c=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),w=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),d=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(q),o=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(A),b=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1);k(()=>{let i=_monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init();return i.then(f=>(c.current=f)&&s(!1)).catch(f=>f?.type!==\"cancelation\"&&console.error(\"Monaco initialization: error:\",f)),()=>u.current?I():i.cancel()}),l(()=>{if(u.current&&c.current){let i=u.current.getOriginalEditor(),f=h(c.current,e||\"\",t||n||\"text\",m||\"\");f!==i.getModel()&&i.setModel(f)}},[m],M),l(()=>{if(u.current&&c.current){let i=u.current.getModifiedEditor(),f=h(c.current,r||\"\",a||n||\"text\",E||\"\");f!==i.getModel()&&i.setModel(f)}},[E],M),l(()=>{let i=u.current.getModifiedEditor();i.getOption(c.current.editor.EditorOption.readOnly)?i.setValue(r||\"\"):r!==i.getValue()&&(i.executeEdits(\"\",[{range:i.getModel().getFullModelRange(),text:r||\"\",forceMoveMarkers:!0}]),i.pushUndoStop())},[r],M),l(()=>{u.current?.getModel()?.original.setValue(e||\"\")},[e],M),l(()=>{let{original:i,modified:f}=u.current.getModel();c.current.editor.setModelLanguage(i,t||n||\"text\"),c.current.editor.setModelLanguage(f,a||n||\"text\")},[n,t,a],M),l(()=>{c.current?.editor.setTheme(x)},[x],M),l(()=>{u.current?.updateOptions(y)},[y],M);let L=(0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{if(!c.current)return;o.current(c.current);let i=h(c.current,e||\"\",t||n||\"text\",m||\"\"),f=h(c.current,r||\"\",a||n||\"text\",E||\"\");u.current?.setModel({original:i,modified:f})},[n,r,a,e,t,m,E]),U=(0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{!b.current&&w.current&&(u.current=c.current.editor.createDiffEditor(w.current,{automaticLayout:!0,...y}),L(),c.current?.editor.setTheme(x),O(!0),b.current=!0)},[y,x,L]);(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{M&&d.current(u.current,c.current)},[M]),(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{!T&&!M&&U()},[T,M,U]);function I(){let i=u.current?.getModel();g||i?.original?.dispose(),N||i?.modified?.dispose(),u.current?.dispose()}return react__WEBPACK_IMPORTED_MODULE_1__.createElement(H,{width:z,height:V,isEditorReady:M,loading:P,_ref:w,className:F,wrapperProps:j})}var ie=Oe;var we=(0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(ie);function Pe(){let[e,r]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].__getMonacoInstance());return k(()=>{let n;return e||(n=_monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init(),n.then(t=>{r(t)})),()=>n?.cancel()}),e}var Le=Pe;function He(e){let r=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{r.current=e},[e]),r.current}var se=He;var _=new Map;function Ve({defaultValue:e,defaultLanguage:r,defaultPath:n,value:t,language:a,path:m,theme:E=\"light\",line:g,loading:N=\"Loading...\",options:x={},overrideServices:P={},saveViewState:y=!0,keepCurrentModel:V=!1,width:z=\"100%\",height:F=\"100%\",className:j,wrapperProps:A={},beforeMount:q=D,onMount:M=D,onChange:O,onValidate:T=D}){let[s,u]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1),[c,w]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!0),d=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),o=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),b=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),L=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(M),U=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(q),I=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(),i=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(t),f=se(m),Q=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1),B=(0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1);k(()=>{let p=_monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init();return p.then(R=>(d.current=R)&&w(!1)).catch(R=>R?.type!==\"cancelation\"&&console.error(\"Monaco initialization: error:\",R)),()=>o.current?pe():p.cancel()}),l(()=>{let p=h(d.current,e||t||\"\",r||a||\"\",m||n||\"\");p!==o.current?.getModel()&&(y&&_.set(f,o.current?.saveViewState()),o.current?.setModel(p),y&&o.current?.restoreViewState(_.get(m)))},[m],s),l(()=>{o.current?.updateOptions(x)},[x],s),l(()=>{!o.current||t===void 0||(o.current.getOption(d.current.editor.EditorOption.readOnly)?o.current.setValue(t):t!==o.current.getValue()&&(B.current=!0,o.current.executeEdits(\"\",[{range:o.current.getModel().getFullModelRange(),text:t,forceMoveMarkers:!0}]),o.current.pushUndoStop(),B.current=!1))},[t],s),l(()=>{let p=o.current?.getModel();p&&a&&d.current?.editor.setModelLanguage(p,a)},[a],s),l(()=>{g!==void 0&&o.current?.revealLine(g)},[g],s),l(()=>{d.current?.editor.setTheme(E)},[E],s);let X=(0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{if(!(!b.current||!d.current)&&!Q.current){U.current(d.current);let p=m||n,R=h(d.current,t||e||\"\",r||a||\"\",p||\"\");o.current=d.current?.editor.create(b.current,{model:R,automaticLayout:!0,...x},P),y&&o.current.restoreViewState(_.get(p)),d.current.editor.setTheme(E),g!==void 0&&o.current.revealLine(g),u(!0),Q.current=!0}},[e,r,n,t,a,m,x,P,y,E,g]);(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{s&&L.current(o.current,d.current)},[s]),(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{!c&&!s&&X()},[c,s,X]),i.current=t,(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{s&&O&&(I.current?.dispose(),I.current=o.current?.onDidChangeModelContent(p=>{B.current||O(o.current.getValue(),p)}))},[s,O]),(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{if(s){let p=d.current.editor.onDidChangeMarkers(R=>{let G=o.current.getModel()?.uri;if(G&&R.find(J=>J.path===G.path)){let J=d.current.editor.getModelMarkers({resource:G});T?.(J)}});return()=>{p?.dispose()}}return()=>{}},[s,T]);function pe(){I.current?.dispose(),V?y&&_.set(m,o.current.saveViewState()):o.current.getModel()?.dispose(),o.current.dispose()}return react__WEBPACK_IMPORTED_MODULE_1__.createElement(H,{width:z,height:F,isEditorReady:s,loading:N,_ref:b,className:j,wrapperProps:A})}var fe=Ve;var de=(0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(fe);var Ft=de;\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/react/dist/index.mjs\n");

/***/ })

};
;