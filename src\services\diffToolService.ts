/**
 * 差异工具服务
 * 提供文本追加、正则替换和差异计算功能
 */

import { 
  DatabaseResult, 
  DiffResult, 
  DiffChange, 
  DiffStats, 
  DiffOperationType,
  DiffChangeType,
  RegexReplaceParams,
  TextAppendParams 
} from '@/types';
import { FileTreeService } from './fileTreeService';


export class DiffToolService {
  private static instance: DiffToolService;
  private fileTreeService: FileTreeService;

  private constructor() {
    this.fileTreeService = FileTreeService.getInstance();
  }

  /**
   * 获取差异工具服务单例
   */
  public static getInstance(): DiffToolService {
    if (!DiffToolService.instance) {
      DiffToolService.instance = new DiffToolService();
    }
    return DiffToolService.instance;
  }

  /**
   * 文本追加功能
   * 在文件末尾或开头追加内容
   */
  public async appendText(
    fileId: string, 
    params: TextAppendParams
  ): Promise<DatabaseResult<DiffResult>> {
    try {
      // 获取文件内容
      const fileResult = await this.fileTreeService.getFile(fileId);
      if (!fileResult.success || !fileResult.data) {
        return { 
          success: false, 
          error: fileResult.error || '文件不存在' 
        };
      }

      const file = fileResult.data;
      const originalContent = file.content || '';
      
      // 根据位置追加内容
      let modifiedContent: string;
      if (params.position === 'start') {
        modifiedContent = params.content + (params.addNewline ? '\n' : '') + originalContent;
      } else {
        // 默认追加到末尾
        modifiedContent = originalContent + (params.addNewline ? '\n' : '') + params.content;
      }

      // 计算差异
      const changes = this.calculateDiff(originalContent, modifiedContent);
      const stats = this.calculateStats(changes);

      const diffResult: DiffResult = {
        fileId,
        filePath: file.path,
        operation: 'append',
        originalContent,
        modifiedContent,
        changes,
        stats,
        timestamp: Date.now()
      };

      console.log('✅ 文本追加操作完成:', file.path);
      return { success: true, data: diffResult };

    } catch (error) {
      const errorMessage = `文本追加失败: ${error instanceof Error ? error.message : String(error)}`;
      console.error('❌', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 正则替换功能
   * 使用正则表达式替换文件内容
   */
  public async regexReplace(
    fileId: string, 
    params: RegexReplaceParams
  ): Promise<DatabaseResult<DiffResult>> {
    try {
      // 获取文件内容
      const fileResult = await this.fileTreeService.getFile(fileId);
      if (!fileResult.success || !fileResult.data) {
        return { 
          success: false, 
          error: fileResult.error || '文件不存在' 
        };
      }

      const file = fileResult.data;
      const originalContent = file.content || '';

      // 构建正则表达式
      const flags = params.flags || (params.global ? 'gm' : 'm'); // 默认添加多行标志
      let regex: RegExp;
      
      try {
        regex = new RegExp(params.pattern, flags);
      } catch (regexError) {
        return { 
          success: false, 
          error: `正则表达式无效: ${regexError instanceof Error ? regexError.message : String(regexError)}` 
        };
      }

      // 先测试是否有匹配
      const testRegex = new RegExp(params.pattern, flags);
      if (!testRegex.test(originalContent)) {
        return { 
          success: false, 
          error: `没有找到匹配的内容进行替换。搜索模式: "${params.pattern}"` 
        };
      }

      // 执行替换
      const modifiedContent = originalContent.replace(regex, params.replacement);

      // 检查是否有实际变更
      if (originalContent === modifiedContent) {
        return { 
          success: false, 
          error: '替换操作未改变文件内容，可能是替换内容与原内容相同' 
        };
      }

      // 计算差异
      const changes = this.calculateDiff(originalContent, modifiedContent);
      const stats = this.calculateStats(changes);

      const diffResult: DiffResult = {
        fileId,
        filePath: file.path,
        operation: 'replace',
        originalContent,
        modifiedContent,
        changes,
        stats,
        timestamp: Date.now()
      };

      console.log('✅ 正则替换操作完成:', file.path);
      return { success: true, data: diffResult };

    } catch (error) {
      const errorMessage = `正则替换失败: ${error instanceof Error ? error.message : String(error)}`;
      console.error('❌', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 应用差异修改到文件
   * 将差异结果应用到实际文件中
   */
  public async applyDiff(diffResult: DiffResult): Promise<DatabaseResult<boolean>> {
    try {
      // 🔧 使用 'external' 来源，表示这是外部工具（df）的更新
      const updateResult = await this.fileTreeService.updateFileContent(
        diffResult.fileId, 
        diffResult.modifiedContent,
        'external' // 标记为外部工具更新
      );

      if (updateResult.success) {
        console.log('✅ 差异修改已应用到文件:', diffResult.filePath);
        return { success: true, data: true };
      }

      return { success: false, error: updateResult.error };

    } catch (error) {
      const errorMessage = `应用差异修改失败: ${error instanceof Error ? error.message : String(error)}`;
      console.error('❌', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 计算文本差异
   * 基于行级别的差异算法实现
   */
  private calculateDiff(original: string, modified: string): DiffChange[] {
    const originalLines = original.split('\n');
    const modifiedLines = modified.split('\n');
    const changes: DiffChange[] = [];

    // 简单的行级别差异算法
    const maxLines = Math.max(originalLines.length, modifiedLines.length);
    
    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[i];
      const modifiedLine = modifiedLines[i];

      if (originalLine === undefined) {
        // 新增行
        changes.push({
          type: 'add',
          lineNumber: i + 1,
          content: modifiedLine
        });
      } else if (modifiedLine === undefined) {
        // 删除行
        changes.push({
          type: 'delete',
          lineNumber: i + 1,
          content: originalLine
        });
      } else if (originalLine !== modifiedLine) {
        // 修改行
        changes.push({
          type: 'modify',
          lineNumber: i + 1,
          content: modifiedLine,
          originalContent: originalLine
        });
      } else {
        // 未变更行（可选择性包含）
        changes.push({
          type: 'unchanged',
          lineNumber: i + 1,
          content: originalLine
        });
      }
    }

    return changes;
  }

  /**
   * 计算差异统计信息
   */
  private calculateStats(changes: DiffChange[]): DiffStats {
    const stats: DiffStats = {
      additions: 0,
      deletions: 0,
      modifications: 0,
      totalChanges: 0
    };

    changes.forEach(change => {
      switch (change.type) {
        case 'add':
          stats.additions++;
          stats.totalChanges++;
          break;
        case 'delete':
          stats.deletions++;
          stats.totalChanges++;
          break;
        case 'modify':
          stats.modifications++;
          stats.totalChanges++;
          break;
        // 'unchanged' 不计入统计
      }
    });

    return stats;
  }

  /**
   * 预览差异（不实际修改文件）
   * 用于在UI中显示差异预览
   */
  public async previewAppend(
    fileId: string, 
    params: TextAppendParams
  ): Promise<DatabaseResult<DiffResult>> {
    // 复用appendText逻辑，但不实际修改文件
    return this.appendText(fileId, params);
  }

  /**
   * 预览正则替换（不实际修改文件）
   */
  public async previewRegexReplace(
    fileId: string,
    params: RegexReplaceParams
  ): Promise<DatabaseResult<DiffResult>> {
    // 复用regexReplace逻辑，但不实际修改文件
    return this.regexReplace(fileId, params);
  }

  /**
   * 智能字符串替换功能
   * 使用多种匹配策略进行字符串替换，提供详细的错误信息
   */
  public async intelligentStringReplace(
    fileId: string,
    findText: string,
    replaceText: string
  ): Promise<DatabaseResult<DiffResult>> {
    try {
      // 获取文件内容
      const fileResult = await this.fileTreeService.getFile(fileId);
      if (!fileResult.success || !fileResult.data) {
        return {
          success: false,
          error: fileResult.error || '文件不存在'
        };
      }

      const file = fileResult.data;
      const originalContent = file.content || '';

      // 使用智能匹配查找内容
      const matchResult = this.intelligentStringMatch(originalContent, findText);

      if (!matchResult.found) {
        let errorMessage = matchResult.error || '未找到匹配内容';

        if (matchResult.suggestions && matchResult.suggestions.length > 0) {
          errorMessage += '\n\n🔍 找到以下相似内容：\n' +
            matchResult.suggestions.map(s => `  • ${s}`).join('\n');
          errorMessage += '\n\n💡 建议：检查搜索内容的大小写、空格或换行符是否正确';
        }

        return {
          success: false,
          error: errorMessage
        };
      }

      // 执行替换
      const beforeMatch = originalContent.substring(0, matchResult.startIndex!);
      const afterMatch = originalContent.substring(matchResult.endIndex!);
      const modifiedContent = beforeMatch + replaceText + afterMatch;

      // 检查是否有实际变更
      if (originalContent === modifiedContent) {
        return {
          success: false,
          error: '替换操作未改变文件内容，替换内容与原内容相同'
        };
      }

      // 计算差异
      const changes = this.calculateDiff(originalContent, modifiedContent);
      const stats = this.calculateStats(changes);

      const diffResult: DiffResult = {
        fileId,
        filePath: file.path,
        operation: 'replace',
        originalContent,
        modifiedContent,
        changes,
        stats,
        timestamp: Date.now()
      };

      console.log(`✅ 智能字符串替换完成 (${matchResult.strategy}):`, file.path);
      console.log(`🔍 匹配策略: ${matchResult.strategy}`);
      console.log(`📝 匹配内容: "${matchResult.matchedText?.substring(0, 50)}${(matchResult.matchedText?.length || 0) > 50 ? '...' : ''}"`);

      return { success: true, data: diffResult };

    } catch (error) {
      const errorMessage = `智能字符串替换失败: ${error instanceof Error ? error.message : String(error)}`;
      console.error('❌', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 获取文件路径（通过文件ID）
   * 辅助方法，用于其他组件获取文件路径
   */
  public async getFilePath(fileId: string): Promise<string | null> {
    try {
      const fileResult = await this.fileTreeService.getFile(fileId);
      if (fileResult.success && fileResult.data) {
        return fileResult.data.path;
      }
      return null;
    } catch (error) {
      console.error('❌ 获取文件路径失败:', error);
      return null;
    }
  }

  /**
   * 验证正则表达式
   * 辅助方法，用于验证正则表达式的有效性
   */
  public validateRegex(pattern: string, flags?: string): { valid: boolean; error?: string } {
    try {
      new RegExp(pattern, flags);
      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : '正则表达式无效'
      };
    }
  }

  /**
   * 智能字符串匹配
   * 尝试多种匹配策略，提供详细的错误信息和建议
   */
  public intelligentStringMatch(
    content: string,
    pattern: string
  ): {
    found: boolean;
    matchedText?: string;
    startIndex?: number;
    endIndex?: number;
    strategy?: string;
    suggestions?: string[];
    error?: string;
  } {
    if (!pattern || !content) {
      return {
        found: false,
        error: '搜索内容或文件内容为空'
      };
    }

    // 策略1: 精确匹配
    let index = content.indexOf(pattern);
    if (index !== -1) {
      return {
        found: true,
        matchedText: pattern,
        startIndex: index,
        endIndex: index + pattern.length,
        strategy: 'exact'
      };
    }

    // 策略2: 忽略大小写匹配
    const lowerContent = content.toLowerCase();
    const lowerPattern = pattern.toLowerCase();
    index = lowerContent.indexOf(lowerPattern);
    if (index !== -1) {
      const matchedText = content.substring(index, index + pattern.length);
      return {
        found: true,
        matchedText,
        startIndex: index,
        endIndex: index + pattern.length,
        strategy: 'case-insensitive'
      };
    }

    // 策略3: 标准化空白字符匹配
    const normalizeWhitespace = (text: string) =>
      text.replace(/\s+/g, ' ').trim();

    const normalizedContent = normalizeWhitespace(content);
    const normalizedPattern = normalizeWhitespace(pattern);

    index = normalizedContent.indexOf(normalizedPattern);
    if (index !== -1) {
      // 需要在原始内容中找到对应位置
      const beforeNormalized = normalizedContent.substring(0, index);
      const beforeOriginal = this.findOriginalPosition(content, beforeNormalized);
      const matchLength = this.findOriginalMatchLength(
        content.substring(beforeOriginal),
        normalizedPattern
      );

      return {
        found: true,
        matchedText: content.substring(beforeOriginal, beforeOriginal + matchLength),
        startIndex: beforeOriginal,
        endIndex: beforeOriginal + matchLength,
        strategy: 'whitespace-normalized'
      };
    }

    // 策略4: 模糊匹配 - 查找包含关键词的行
    const suggestions = this.findSimilarContent(content, pattern);

    return {
      found: false,
      suggestions,
      error: `未找到匹配内容。搜索模式: "${pattern.substring(0, 100)}${pattern.length > 100 ? '...' : ''}"`
    };
  }

  /**
   * 查找相似内容，提供替换建议
   */
  private findSimilarContent(content: string, pattern: string): string[] {
    const lines = content.split('\n');
    const patternWords = pattern.toLowerCase().split(/\s+/).filter(word => word.length > 2);
    const suggestions: Array<{line: string, score: number, lineNumber: number}> = [];

    lines.forEach((line, index) => {
      const lowerLine = line.toLowerCase();
      let score = 0;

      // 计算匹配分数
      patternWords.forEach(word => {
        if (lowerLine.includes(word)) {
          score += word.length;
        }
      });

      if (score > 0) {
        suggestions.push({
          line: line.trim(),
          score,
          lineNumber: index + 1
        });
      }
    });

    // 按分数排序，返回前5个建议
    return suggestions
      .sort((a, b) => b.score - a.score)
      .slice(0, 5)
      .map(s => `第${s.lineNumber}行: ${s.line.substring(0, 100)}${s.line.length > 100 ? '...' : ''}`);
  }

  /**
   * 在原始文本中找到标准化文本的对应位置
   */
  private findOriginalPosition(original: string, normalizedPrefix: string): number {
    let originalPos = 0;
    let normalizedPos = 0;

    while (normalizedPos < normalizedPrefix.length && originalPos < original.length) {
      const originalChar = original[originalPos];
      const normalizedChar = normalizedPrefix[normalizedPos];

      if (/\s/.test(originalChar)) {
        // 跳过原始文本中的空白字符
        originalPos++;
      } else if (originalChar === normalizedChar) {
        originalPos++;
        normalizedPos++;
      } else {
        // 处理多个空白字符被标准化为单个空格的情况
        if (normalizedChar === ' ') {
          normalizedPos++;
        } else {
          originalPos++;
          normalizedPos++;
        }
      }
    }

    return originalPos;
  }

  /**
   * 计算原始匹配文本的长度
   */
  private findOriginalMatchLength(originalText: string, normalizedPattern: string): number {
    let originalPos = 0;
    let normalizedPos = 0;

    while (normalizedPos < normalizedPattern.length && originalPos < originalText.length) {
      const originalChar = originalText[originalPos];
      const normalizedChar = normalizedPattern[normalizedPos];

      if (/\s/.test(originalChar)) {
        originalPos++;
      } else if (originalChar === normalizedChar) {
        originalPos++;
        normalizedPos++;
      } else if (normalizedChar === ' ') {
        normalizedPos++;
      } else {
        originalPos++;
        normalizedPos++;
      }
    }

    return originalPos;
  }

  /**
   * 创建文件及其路径（如果不存在）
   * 支持递归创建文件夹结构
   */
  public async createFileWithPath(
    artworkId: string,
    filePath: string,
    content: string = '',
    operation: 'append' | 'replace' = 'append'
  ): Promise<DatabaseResult<DiffResult>> {
    try {
      // 获取文件树根节点
      const fileTreeResult = await this.fileTreeService.getFileTree(artworkId);
      if (!fileTreeResult.success || !fileTreeResult.data) {
        return { 
          success: false, 
          error: '无法获取文件树' 
        };
      }

      const rootNode = fileTreeResult.data;
      
      // 解析文件路径
      const pathParts = filePath.split('/').filter(part => part.trim() !== '');
      if (pathParts.length === 0) {
        return { 
          success: false, 
          error: '文件路径无效' 
        };
      }

      const fileName = pathParts[pathParts.length - 1];
      const folderParts = pathParts.slice(0, -1);

      // 递归创建文件夹结构
      let currentParentId = rootNode.id;
      
      for (const folderName of folderParts) {
        // 检查文件夹是否已存在
        const existingFolder = await this.findChildByName(artworkId, currentParentId, folderName);
        
        if (existingFolder) {
          currentParentId = existingFolder.id;
        } else {
          // 创建新文件夹
          const folderResult = await this.fileTreeService.createFolder(
            artworkId,
            currentParentId,
            folderName
          );
          
          if (!folderResult.success) {
            return { 
              success: false, 
              error: `创建文件夹失败: ${folderResult.error}` 
            };
          }
          
          currentParentId = folderResult.data!.id;
        }
      }

      // 检查文件是否已存在
      const existingFile = await this.findChildByName(artworkId, currentParentId, fileName);
      
      if (existingFile) {
        // 文件已存在，执行原有的追加或替换操作
        if (operation === 'append') {
          return this.appendText(existingFile.id, { 
            content, 
            position: 'end', 
            addNewline: true 
          });
        } else {
          // 对于replace操作，这里简化处理，直接替换整个文件内容
          const originalContent = existingFile.content || '';
          const modifiedContent = content;
          
          const changes = this.calculateDiff(originalContent, modifiedContent);
          const stats = this.calculateStats(changes);

          const diffResult: DiffResult = {
            fileId: existingFile.id,
            filePath: existingFile.path,
            operation: 'replace',
            originalContent,
            modifiedContent,
            changes,
            stats,
            timestamp: Date.now()
          };

          return { success: true, data: diffResult };
        }
      } else {
        // 文件不存在，创建新文件
        const fileResult = await this.fileTreeService.createFile(
          artworkId,
          currentParentId,
          fileName,
          'text',
          content
        );
        
        if (!fileResult.success) {
          return { 
            success: false, 
            error: `创建文件失败: ${fileResult.error}` 
          };
        }

        const newFile = fileResult.data!;
        
        // 创建差异结果（新文件创建）
        const changes = this.calculateDiff('', content);
        const stats = this.calculateStats(changes);

        const diffResult: DiffResult = {
          fileId: newFile.id,
          filePath: newFile.path,
          operation: 'create',
          originalContent: '',
          modifiedContent: content,
          changes,
          stats,
          timestamp: Date.now()
        };

        console.log('✅ 文件及路径创建成功:', filePath);
        

        
        return { success: true, data: diffResult };
      }

    } catch (error) {
      const errorMessage = `创建文件及路径失败: ${error instanceof Error ? error.message : String(error)}`;
      console.error('❌', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 根据名称查找子节点（文件或文件夹）
   * 辅助方法，用于检查文件/文件夹是否存在
   */
  private async findChildByName(
    artworkId: string, 
    parentId: string, 
    name: string
  ): Promise<any | null> {
    try {
      const allFilesResult = await this.fileTreeService.getFilesByArtwork(artworkId);
      if (!allFilesResult.success || !allFilesResult.data) {
        return null;
      }

      const files = allFilesResult.data;
      return files.find(file => 
        file.parentId === parentId && file.name === name
      ) || null;

    } catch (error) {
      console.error('❌ 查找子节点失败:', error);
      return null;
    }
  }

  /**
   * 检查文件是否存在
   * 用于UI组件判断显示状态
   */
  public async checkFileExists(artworkId: string, filePath: string): Promise<boolean> {
    try {
      const fileTreeResult = await this.fileTreeService.getFileTree(artworkId);
      if (!fileTreeResult.success || !fileTreeResult.data) {
        return false;
      }

      const rootNode = fileTreeResult.data;
      const pathParts = filePath.split('/').filter(part => part.trim() !== '');
      
      let currentParentId = rootNode.id;
      
      // 逐级检查路径
      for (const part of pathParts) {
        const child = await this.findChildByName(artworkId, currentParentId, part);
        if (!child) {
          return false;
        }
        currentParentId = child.id;
      }
      
      return true;
    } catch (error) {
      console.error('❌ 检查文件存在性失败:', error);
      return false;
    }
  }
}