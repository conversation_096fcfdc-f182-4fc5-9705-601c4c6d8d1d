"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_qsharp_qsharp_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/qsharp/qsharp.js":
/*!****************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/qsharp/qsharp.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/qsharp/qsharp.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  keywords: [\n    \"namespace\",\n    \"open\",\n    \"import\",\n    \"export\",\n    \"as\",\n    \"operation\",\n    \"function\",\n    \"body\",\n    \"adjoint\",\n    \"newtype\",\n    \"struct\",\n    \"controlled\",\n    \"if\",\n    \"elif\",\n    \"else\",\n    \"repeat\",\n    \"until\",\n    \"fixup\",\n    \"for\",\n    \"in\",\n    \"while\",\n    \"return\",\n    \"fail\",\n    \"within\",\n    \"apply\",\n    \"Adjoint\",\n    \"Controlled\",\n    \"Adj\",\n    \"Ctl\",\n    \"is\",\n    \"self\",\n    \"auto\",\n    \"distribute\",\n    \"invert\",\n    \"intrinsic\",\n    \"let\",\n    \"set\",\n    \"w/\",\n    \"new\",\n    \"not\",\n    \"and\",\n    \"or\",\n    \"use\",\n    \"borrow\",\n    \"using\",\n    \"borrowing\",\n    \"mutable\",\n    \"internal\"\n  ],\n  typeKeywords: [\n    \"Unit\",\n    \"Int\",\n    \"BigInt\",\n    \"Double\",\n    \"Bool\",\n    \"String\",\n    \"Qubit\",\n    \"Result\",\n    \"Pauli\",\n    \"Range\"\n  ],\n  invalidKeywords: [\n    \"abstract\",\n    \"base\",\n    \"bool\",\n    \"break\",\n    \"byte\",\n    \"case\",\n    \"catch\",\n    \"char\",\n    \"checked\",\n    \"class\",\n    \"const\",\n    \"continue\",\n    \"decimal\",\n    \"default\",\n    \"delegate\",\n    \"do\",\n    \"double\",\n    \"enum\",\n    \"event\",\n    \"explicit\",\n    \"extern\",\n    \"finally\",\n    \"fixed\",\n    \"float\",\n    \"foreach\",\n    \"goto\",\n    \"implicit\",\n    \"int\",\n    \"interface\",\n    \"lock\",\n    \"long\",\n    \"null\",\n    \"object\",\n    \"operator\",\n    \"out\",\n    \"override\",\n    \"params\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"readonly\",\n    \"ref\",\n    \"sbyte\",\n    \"sealed\",\n    \"short\",\n    \"sizeof\",\n    \"stackalloc\",\n    \"static\",\n    \"string\",\n    \"switch\",\n    \"this\",\n    \"throw\",\n    \"try\",\n    \"typeof\",\n    \"unit\",\n    \"ulong\",\n    \"unchecked\",\n    \"unsafe\",\n    \"ushort\",\n    \"virtual\",\n    \"void\",\n    \"volatile\"\n  ],\n  constants: [\"true\", \"false\", \"PauliI\", \"PauliX\", \"PauliY\", \"PauliZ\", \"One\", \"Zero\"],\n  builtin: [\n    \"X\",\n    \"Y\",\n    \"Z\",\n    \"H\",\n    \"HY\",\n    \"S\",\n    \"T\",\n    \"SWAP\",\n    \"CNOT\",\n    \"CCNOT\",\n    \"MultiX\",\n    \"R\",\n    \"RFrac\",\n    \"Rx\",\n    \"Ry\",\n    \"Rz\",\n    \"R1\",\n    \"R1Frac\",\n    \"Exp\",\n    \"ExpFrac\",\n    \"Measure\",\n    \"M\",\n    \"MultiM\",\n    \"Message\",\n    \"Length\",\n    \"Assert\",\n    \"AssertProb\",\n    \"AssertEqual\"\n  ],\n  operators: [\n    \"and=\",\n    \"<-\",\n    \"->\",\n    \"*\",\n    \"*=\",\n    \"@\",\n    \"!\",\n    \"^\",\n    \"^=\",\n    \":\",\n    \"::\",\n    \".\",\n    \"..\",\n    \"==\",\n    \"...\",\n    \"=\",\n    \"=>\",\n    \">\",\n    \">=\",\n    \"<\",\n    \"<=\",\n    \"-\",\n    \"-=\",\n    \"!=\",\n    \"or=\",\n    \"%\",\n    \"%=\",\n    \"|\",\n    \"+\",\n    \"+=\",\n    \"?\",\n    \"/\",\n    \"/=\",\n    \"&&&\",\n    \"&&&=\",\n    \"^^^\",\n    \"^^^=\",\n    \">>>\",\n    \">>>=\",\n    \"<<<\",\n    \"<<<=\",\n    \"|||\",\n    \"|||=\",\n    \"~~~\",\n    \"_\",\n    \"w/\",\n    \"w/=\"\n  ],\n  namespaceFollows: [\"namespace\", \"open\"],\n  importsFollows: [\"import\"],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%@._]+/,\n  escapes: /\\\\[\\s\\S]/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [\n        /[a-zA-Z_$][\\w$]*/,\n        {\n          cases: {\n            \"@namespaceFollows\": {\n              token: \"keyword.$0\",\n              next: \"@namespace\"\n            },\n            \"@importsFollows\": {\n              token: \"keyword.$0\",\n              next: \"@imports\"\n            },\n            \"@typeKeywords\": \"type\",\n            \"@keywords\": \"keyword\",\n            \"@constants\": \"constant\",\n            \"@builtin\": \"keyword\",\n            \"@invalidKeywords\": \"invalid\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, { cases: { \"@operators\": \"operator\", \"@default\": \"\" } }],\n      // numbers\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      //[/\"([^\"\\\\]|\\\\.)*$/, 'string.invalid' ],  // non-terminated string\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    namespace: [\n      { include: \"@whitespace\" },\n      [/[A-Za-z]\\w*/, \"namespace\"],\n      [/[\\.]/, \"delimiter\"],\n      [\"\", \"\", \"@pop\"]\n    ],\n    imports: [\n      { include: \"@whitespace\" },\n      [/[A-Za-z]\\w*(?=\\.)/, \"namespace\"],\n      [/[A-Za-z]\\w*/, \"identifier\"],\n      [/\\*/, \"wildcard\"],\n      [/[\\.,]/, \"delimiter\"],\n      [\"\", \"\", \"@pop\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/(\\/\\/).*/, \"comment\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/qsharp/qsharp.js\n"));

/***/ })

}]);