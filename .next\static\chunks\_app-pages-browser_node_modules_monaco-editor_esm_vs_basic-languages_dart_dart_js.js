"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_dart_dart_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/dart/dart.js":
/*!************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/dart/dart.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/dart/dart.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"`\", close: \"`\", notIn: [\"string\", \"comment\"] },\n    { open: \"/**\", close: \" */\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"`\", close: \"`\" }\n  ],\n  folding: {\n    markers: {\n      start: /^\\s*\\s*#?region\\b/,\n      end: /^\\s*\\s*#?endregion\\b/\n    }\n  }\n};\nvar language = {\n  defaultToken: \"invalid\",\n  tokenPostfix: \".dart\",\n  keywords: [\n    \"abstract\",\n    \"dynamic\",\n    \"implements\",\n    \"show\",\n    \"as\",\n    \"else\",\n    \"import\",\n    \"static\",\n    \"assert\",\n    \"enum\",\n    \"in\",\n    \"super\",\n    \"async\",\n    \"export\",\n    \"interface\",\n    \"switch\",\n    \"await\",\n    \"extends\",\n    \"is\",\n    \"sync\",\n    \"break\",\n    \"external\",\n    \"library\",\n    \"this\",\n    \"case\",\n    \"factory\",\n    \"mixin\",\n    \"throw\",\n    \"catch\",\n    \"false\",\n    \"new\",\n    \"true\",\n    \"class\",\n    \"final\",\n    \"null\",\n    \"try\",\n    \"const\",\n    \"finally\",\n    \"on\",\n    \"typedef\",\n    \"continue\",\n    \"for\",\n    \"operator\",\n    \"var\",\n    \"covariant\",\n    \"Function\",\n    \"part\",\n    \"void\",\n    \"default\",\n    \"get\",\n    \"rethrow\",\n    \"while\",\n    \"deferred\",\n    \"hide\",\n    \"return\",\n    \"with\",\n    \"do\",\n    \"if\",\n    \"set\",\n    \"yield\"\n  ],\n  typeKeywords: [\"int\", \"double\", \"String\", \"bool\"],\n  operators: [\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"~/\",\n    \"%\",\n    \"++\",\n    \"--\",\n    \"==\",\n    \"!=\",\n    \">\",\n    \"<\",\n    \">=\",\n    \"<=\",\n    \"=\",\n    \"-=\",\n    \"/=\",\n    \"%=\",\n    \">>=\",\n    \"^=\",\n    \"+=\",\n    \"*=\",\n    \"~/=\",\n    \"<<=\",\n    \"&=\",\n    \"!=\",\n    \"||\",\n    \"&&\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \"~\",\n    \"<<\",\n    \">>\",\n    \"!\",\n    \">>>\",\n    \"??\",\n    \"?\",\n    \":\",\n    \"|=\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+(_+\\d+)*/,\n  octaldigits: /[0-7]+(_+[0-7]+)*/,\n  binarydigits: /[0-1]+(_+[0-1]+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  regexpctl: /[(){}\\[\\]\\$\\^|\\-*+?\\.]/,\n  regexpesc: /\\\\(?:[bBdDfnrstvwWn0\\\\\\/]|@regexpctl|c[A-Z]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [[/[{}]/, \"delimiter.bracket\"], { include: \"common\" }],\n    common: [\n      // identifiers and keywords\n      [\n        /[a-z_$][\\w$]*/,\n        {\n          cases: {\n            \"@typeKeywords\": \"type.identifier\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[A-Z_$][\\w\\$]*/, \"type.identifier\"],\n      // show class names\n      // [/[A-Z][\\w\\$]*/, 'identifier'],\n      // whitespace\n      { include: \"@whitespace\" },\n      // regular expression: ensure it is terminated before beginning (otherwise it is an opeator)\n      [\n        /\\/(?=([^\\\\\\/]|\\\\.)+\\/([gimsuy]*)(\\s*)(\\.|;|,|\\)|\\]|\\}|$))/,\n        { token: \"regexp\", bracket: \"@open\", next: \"@regexp\" }\n      ],\n      // @ annotations.\n      [/@[a-zA-Z]+/, \"annotation\"],\n      // variable\n      // delimiters and operators\n      [/[()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/!(?=([^=]|$))/, \"delimiter\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/(@digits)[eE]([\\-+]?(@digits))?/, \"number.float\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?/, \"number.float\"],\n      [/0[xX](@hexdigits)n?/, \"number.hex\"],\n      [/0[oO]?(@octaldigits)n?/, \"number.octal\"],\n      [/0[bB](@binarydigits)n?/, \"number.binary\"],\n      [/(@digits)n?/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string_double\"],\n      [/'/, \"string\", \"@string_single\"]\n      //   [/[a-zA-Z]+/, \"variable\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@jsdoc\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/\\/.*$/, \"comment.doc\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    jsdoc: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    // We match regular expression quite precisely\n    regexp: [\n      [\n        /(\\{)(\\d+(?:,\\d*)?)(\\})/,\n        [\"regexp.escape.control\", \"regexp.escape.control\", \"regexp.escape.control\"]\n      ],\n      [\n        /(\\[)(\\^?)(?=(?:[^\\]\\\\\\/]|\\\\.)+)/,\n        [\"regexp.escape.control\", { token: \"regexp.escape.control\", next: \"@regexrange\" }]\n      ],\n      [/(\\()(\\?:|\\?=|\\?!)/, [\"regexp.escape.control\", \"regexp.escape.control\"]],\n      [/[()]/, \"regexp.escape.control\"],\n      [/@regexpctl/, \"regexp.escape.control\"],\n      [/[^\\\\\\/]/, \"regexp\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/\\\\\\./, \"regexp.invalid\"],\n      [/(\\/)([gimsuy]*)/, [{ token: \"regexp\", bracket: \"@close\", next: \"@pop\" }, \"keyword.other\"]]\n    ],\n    regexrange: [\n      [/-/, \"regexp.escape.control\"],\n      [/\\^/, \"regexp.invalid\"],\n      [/@regexpesc/, \"regexp.escape\"],\n      [/[^\\]]/, \"regexp\"],\n      [\n        /\\]/,\n        {\n          token: \"regexp.escape.control\",\n          next: \"@pop\",\n          bracket: \"@close\"\n        }\n      ]\n    ],\n    string_double: [\n      [/[^\\\\\"\\$]+/, \"string\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"],\n      [/\\$\\w+/, \"identifier\"]\n    ],\n    string_single: [\n      [/[^\\\\'\\$]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, \"string\", \"@pop\"],\n      [/\\$\\w+/, \"identifier\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/dart/dart.js\n"));

/***/ })

}]);