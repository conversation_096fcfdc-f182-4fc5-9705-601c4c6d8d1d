# 测试修复后的 df 替换工具

现在可以测试以下场景：

## 1. 精确匹配测试
```df
{
  "file": "test-content.txt",
  "type": "replace",
  "find": "Hello World",
  "content": "Hello Universe"
}
```

## 2. 大小写不敏感测试
```df
{
  "file": "test-content.txt",
  "type": "replace", 
  "find": "hello world",
  "content": "Hello Mars"
}
```

## 3. 空白字符标准化测试
```df
{
  "file": "test-content.txt",
  "type": "replace",
  "find": "Some content with    multiple   spaces\nand different\nline breaks.",
  "content": "Some content with normalized spaces and line breaks."
}
```

## 4. 函数替换测试
```df
{
  "file": "test-content.txt", 
  "type": "replace",
  "find": "function testFunction() {\n  console.log(\"Hello World\");\n  return true;\n}",
  "content": "function improvedFunction() {\n  console.log(\"Hello Universe\");\n  return 'success';\n}"
}
```

## 修复的问题

✅ **UI 预览修复**: CompactDiffDisplay 现在正确显示 find 和 content 分离的格式
✅ **智能匹配**: 使用新的 intelligentStringReplace 方法
✅ **参数传递**: 修复了 content 和 find 参数的混乱问题
✅ **错误提示**: 提供详细的匹配失败建议

## 预期行为

1. **UI 预览**: 正确显示"搜索内容"和"替换为"两个部分
2. **智能匹配**: 自动尝试多种匹配策略
3. **详细日志**: 控制台显示使用的匹配策略
4. **错误建议**: 匹配失败时显示相似内容建议
