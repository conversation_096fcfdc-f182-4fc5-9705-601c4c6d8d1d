"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_lexon_lexon_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/lexon/lexon.js":
/*!**************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/lexon/lexon.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/lexon/lexon.ts\nvar conf = {\n  comments: {\n    lineComment: \"COMMENT\"\n    // blockComment: ['COMMENT', '.'],\n  },\n  brackets: [[\"(\", \")\"]],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \":\", close: \".\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"`\", close: \"`\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \":\", close: \".\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*(::\\\\s*|COMMENT\\\\s+)#region\"),\n      end: new RegExp(\"^\\\\s*(::\\\\s*|COMMENT\\\\s+)#endregion\")\n    }\n  }\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  // defaultToken: 'invalid',\n  tokenPostfix: \".lexon\",\n  ignoreCase: true,\n  keywords: [\n    \"lexon\",\n    \"lex\",\n    \"clause\",\n    \"terms\",\n    \"contracts\",\n    \"may\",\n    \"pay\",\n    \"pays\",\n    \"appoints\",\n    \"into\",\n    \"to\"\n  ],\n  typeKeywords: [\"amount\", \"person\", \"key\", \"time\", \"date\", \"asset\", \"text\"],\n  operators: [\n    \"less\",\n    \"greater\",\n    \"equal\",\n    \"le\",\n    \"gt\",\n    \"or\",\n    \"and\",\n    \"add\",\n    \"added\",\n    \"subtract\",\n    \"subtracted\",\n    \"multiply\",\n    \"multiplied\",\n    \"times\",\n    \"divide\",\n    \"divided\",\n    \"is\",\n    \"be\",\n    \"certified\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // comment\n      [/^(\\s*)(comment:?(?:\\s.*|))$/, [\"\", \"comment\"]],\n      // special identifier cases\n      [\n        /\"/,\n        {\n          token: \"identifier.quote\",\n          bracket: \"@open\",\n          next: \"@quoted_identifier\"\n        }\n      ],\n      [\n        \"LEX$\",\n        {\n          token: \"keyword\",\n          bracket: \"@open\",\n          next: \"@identifier_until_period\"\n        }\n      ],\n      [\"LEXON\", { token: \"keyword\", bracket: \"@open\", next: \"@semver\" }],\n      [\n        \":\",\n        {\n          token: \"delimiter\",\n          bracket: \"@open\",\n          next: \"@identifier_until_period\"\n        }\n      ],\n      // identifiers and keywords\n      [\n        /[a-z_$][\\w$]*/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@typeKeywords\": \"keyword.type\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      // numbers\n      [/\\d*\\.\\d*\\.\\d*/, \"number.semver\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"]\n    ],\n    quoted_identifier: [\n      [/[^\\\\\"]+/, \"identifier\"],\n      [/\"/, { token: \"identifier.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    space_identifier_until_period: [\n      [\":\", \"delimiter\"],\n      [\" \", { token: \"white\", next: \"@identifier_rest\" }]\n    ],\n    identifier_until_period: [\n      { include: \"@whitespace\" },\n      [\":\", { token: \"delimiter\", next: \"@identifier_rest\" }],\n      [/[^\\\\.]+/, \"identifier\"],\n      [/\\./, { token: \"delimiter\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    identifier_rest: [\n      [/[^\\\\.]+/, \"identifier\"],\n      [/\\./, { token: \"delimiter\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    semver: [\n      { include: \"@whitespace\" },\n      [\":\", \"delimiter\"],\n      [/\\d*\\.\\d*\\.\\d*/, { token: \"number.semver\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [[/[ \\t\\r\\n]+/, \"white\"]]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/lexon/lexon.js\n"));

/***/ })

}]);