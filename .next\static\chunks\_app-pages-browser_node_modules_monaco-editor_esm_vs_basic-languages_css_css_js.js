"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_css_css_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/css/css.js":
/*!**********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/css/css.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/css/css.ts\nvar conf = {\n  wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|((::|[@#.!:])?[\\w-?]+%?)|::|[@#.!:]/g,\n  comments: {\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/\"),\n      end: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".css\",\n  ws: \"[ \t\\n\\r\\f]*\",\n  // whitespaces (referenced in several rules)\n  identifier: \"-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.bracket\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  tokenizer: {\n    root: [{ include: \"@selector\" }],\n    selector: [\n      { include: \"@comments\" },\n      { include: \"@import\" },\n      { include: \"@strings\" },\n      [\n        \"[@](keyframes|-webkit-keyframes|-moz-keyframes|-o-keyframes)\",\n        { token: \"keyword\", next: \"@keyframedeclaration\" }\n      ],\n      [\"[@](page|content|font-face|-moz-document)\", { token: \"keyword\" }],\n      [\"[@](charset|namespace)\", { token: \"keyword\", next: \"@declarationbody\" }],\n      [\n        \"(url-prefix)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      [\n        \"(url)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      { include: \"@selectorname\" },\n      [\"[\\\\*]\", \"tag\"],\n      // selector symbols\n      [\"[>\\\\+,]\", \"delimiter\"],\n      // selector operators\n      [\"\\\\[\", { token: \"delimiter.bracket\", next: \"@selectorattribute\" }],\n      [\"{\", { token: \"delimiter.bracket\", next: \"@selectorbody\" }]\n    ],\n    selectorbody: [\n      { include: \"@comments\" },\n      [\"[*_]?@identifier@ws:(?=(\\\\s|\\\\d|[^{;}]*[;}]))\", \"attribute.name\", \"@rulevalue\"],\n      // rule definition: to distinguish from a nested selector check for whitespace, number or a semicolon\n      [\"}\", { token: \"delimiter.bracket\", next: \"@pop\" }]\n    ],\n    selectorname: [\n      [\"(\\\\.|#(?=[^{])|%|(@identifier)|:)+\", \"tag\"]\n      // selector (.foo, div, ...)\n    ],\n    selectorattribute: [{ include: \"@term\" }, [\"]\", { token: \"delimiter.bracket\", next: \"@pop\" }]],\n    term: [\n      { include: \"@comments\" },\n      [\n        \"(url-prefix)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      [\n        \"(url)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      { include: \"@functioninvocation\" },\n      { include: \"@numbers\" },\n      { include: \"@name\" },\n      { include: \"@strings\" },\n      [\"([<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~,])\", \"delimiter\"],\n      [\",\", \"delimiter\"]\n    ],\n    rulevalue: [\n      { include: \"@comments\" },\n      { include: \"@strings\" },\n      { include: \"@term\" },\n      [\"!important\", \"keyword\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    warndebug: [[\"[@](warn|debug)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    import: [[\"[@](import)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    urldeclaration: [\n      { include: \"@strings\" },\n      [\"[^)\\r\\n]+\", \"string\"],\n      [\"\\\\)\", { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    parenthizedterm: [\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    declarationbody: [\n      { include: \"@term\" },\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n      // missing semicolon\n    ],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [/[^*/]+/, \"comment\"],\n      [/./, \"comment\"]\n    ],\n    name: [[\"@identifier\", \"attribute.value\"]],\n    numbers: [\n      [\"-?(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?\", { token: \"attribute.value.number\", next: \"@units\" }],\n      [\"#[0-9a-fA-F_]+(?!\\\\w)\", \"attribute.value.hex\"]\n    ],\n    units: [\n      [\n        \"(em|ex|ch|rem|fr|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?\",\n        \"attribute.value.unit\",\n        \"@pop\"\n      ]\n    ],\n    keyframedeclaration: [\n      [\"@identifier\", \"attribute.value\"],\n      [\"{\", { token: \"delimiter.bracket\", switchTo: \"@keyframebody\" }]\n    ],\n    keyframebody: [\n      { include: \"@term\" },\n      [\"{\", { token: \"delimiter.bracket\", next: \"@selectorbody\" }],\n      [\"}\", { token: \"delimiter.bracket\", next: \"@pop\" }]\n    ],\n    functioninvocation: [\n      [\"@identifier\\\\(\", { token: \"attribute.value\", next: \"@functionarguments\" }]\n    ],\n    functionarguments: [\n      [\"\\\\$@identifier@ws:\", \"attribute.name\"],\n      [\"[,]\", \"delimiter\"],\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"attribute.value\", next: \"@pop\" }]\n    ],\n    strings: [\n      ['~?\"', { token: \"string\", next: \"@stringenddoublequote\" }],\n      [\"~?'\", { token: \"string\", next: \"@stringendquote\" }]\n    ],\n    stringenddoublequote: [\n      [\"\\\\\\\\.\", \"string\"],\n      ['\"', { token: \"string\", next: \"@pop\" }],\n      [/[^\\\\\"]+/, \"string\"],\n      [\".\", \"string\"]\n    ],\n    stringendquote: [\n      [\"\\\\\\\\.\", \"string\"],\n      [\"'\", { token: \"string\", next: \"@pop\" }],\n      [/[^\\\\']+/, \"string\"],\n      [\".\", \"string\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/css/css.js\n"));

/***/ })

}]);