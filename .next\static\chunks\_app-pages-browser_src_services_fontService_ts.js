"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_services_fontService_ts"],{

/***/ "(app-pages-browser)/./src/services/fontService.ts":
/*!*************************************!*\
  !*** ./src/services/fontService.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FontService: function() { return /* binding */ FontService; }\n/* harmony export */ });\n/* harmony import */ var _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database/DatabaseService */ \"(app-pages-browser)/./src/services/database/DatabaseService.ts\");\n/**\r\n * 字体数据操作服务\r\n * 提供字体的上传、存储、管理和应用功能\r\n */ \nclass FontService {\n    /**\r\n   * 获取字体服务单例\r\n   */ static getInstance() {\n        if (!FontService.instance) {\n            FontService.instance = new FontService();\n        }\n        return FontService.instance;\n    }\n    /**\r\n   * 生成UUID\r\n   */ generateUUID() {\n        return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n            const r = Math.random() * 16 | 0;\n            const v = c === \"x\" ? r : r & 0x3 | 0x8;\n            return v.toString(16);\n        });\n    }\n    /**\r\n   * 验证字体文件\r\n   */ validateFontFile(file) {\n        var _file_name_split_pop;\n        // 检查文件大小\n        if (file.size > this.maxFileSize) {\n            return {\n                valid: false,\n                error: \"文件大小超过限制 (\".concat(Math.round(this.maxFileSize / 1024 / 1024), \"MB)\")\n            };\n        }\n        // 检查文件格式\n        const extension = (_file_name_split_pop = file.name.split(\".\").pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase();\n        if (!extension || !this.supportedFormats.includes(extension)) {\n            return {\n                valid: false,\n                error: \"不支持的文件格式，支持格式: \".concat(this.supportedFormats.join(\", \"))\n            };\n        }\n        return {\n            valid: true\n        };\n    }\n    /**\r\n   * 文件转ArrayBuffer\r\n   */ fileToArrayBuffer(file) {\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.onload = ()=>resolve(reader.result);\n            reader.onerror = ()=>reject(new Error(\"文件读取失败\"));\n            reader.readAsArrayBuffer(file);\n        });\n    }\n    /**\r\n   * 提取字体族名\r\n   */ extractFontFamily(fileName) {\n        // 从文件名提取字体族名，移除扩展名和特殊字符\n        return fileName.replace(/\\.[^/.]+$/, \"\") // 移除扩展名\n        .replace(/[-_]/g, \" \") // 替换连字符和下划线为空格\n        .replace(/\\b\\w/g, (l)=>l.toUpperCase()); // 首字母大写\n    }\n    /**\r\n   * 上传字体文件\r\n   */ async uploadFont(file, customName) {\n        try {\n            var _file_name_split_pop;\n            // 验证文件\n            const validation = this.validateFontFile(file);\n            if (!validation.valid) {\n                return {\n                    success: false,\n                    error: validation.error\n                };\n            }\n            // 检查是否已存在同名字体\n            const existingFonts = await this.getAllFonts();\n            if (existingFonts.success && existingFonts.data) {\n                const fontName = customName || file.name;\n                const duplicate = existingFonts.data.find((f)=>f.name === fontName);\n                if (duplicate) {\n                    return {\n                        success: false,\n                        error: \"字体名称已存在\"\n                    };\n                }\n            }\n            // 转换文件数据\n            const arrayBuffer = await this.fileToArrayBuffer(file);\n            const extension = ((_file_name_split_pop = file.name.split(\".\").pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase()) || \"ttf\";\n            const font = {\n                id: this.generateUUID(),\n                name: customName || file.name,\n                family: this.extractFontFamily(file.name),\n                data: arrayBuffer,\n                format: extension,\n                uploadedAt: Date.now(),\n                size: file.size\n            };\n            const result = await this.dbService.add(this.storeName, font);\n            if (result.success) {\n                console.log(\"✅ 字体上传成功:\", font.name);\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = \"字体上传失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取字体\r\n   */ async getFont(id) {\n        try {\n            const result = await this.dbService.get(this.storeName, id);\n            if (result.success && !result.data) {\n                return {\n                    success: true,\n                    data: null\n                };\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = \"获取字体失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取所有字体\r\n   */ async getAllFonts() {\n        try {\n            const result = await this.dbService.getAll(this.storeName);\n            if (result.success && result.data) {\n                // 按上传时间倒序排列\n                result.data.sort((a, b)=>b.uploadedAt - a.uploadedAt);\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = \"获取字体列表失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 删除字体\r\n   */ async deleteFont(id) {\n        try {\n            // 先检查字体是否存在\n            const existingResult = await this.getFont(id);\n            if (!existingResult.success || !existingResult.data) {\n                return {\n                    success: false,\n                    error: \"字体不存在\"\n                };\n            }\n            const result = await this.dbService.delete(this.storeName, id);\n            if (result.success) {\n                // 从已加载字体集合中移除\n                this.loadedFonts.delete(id);\n                // 移除CSS中的字体定义\n                this.removeFontFromCSS(existingResult.data.family);\n                console.log(\"✅ 字体删除成功:\", existingResult.data.name);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = \"删除字体失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 加载字体到CSS\r\n   */ async loadFontToCSS(fontId) {\n        try {\n            // 如果已经加载，直接返回成功\n            if (this.loadedFonts.has(fontId)) {\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            const fontResult = await this.getFont(fontId);\n            if (!fontResult.success || !fontResult.data) {\n                return {\n                    success: false,\n                    error: \"字体不存在\"\n                };\n            }\n            const font = fontResult.data;\n            // 创建Blob URL\n            const blob = new Blob([\n                font.data\n            ], {\n                type: this.getMimeType(font.format)\n            });\n            const fontUrl = URL.createObjectURL(blob);\n            // 创建@font-face规则\n            const fontFaceRule = \"\\n        @font-face {\\n          font-family: '\".concat(font.family, \"';\\n          src: url('\").concat(fontUrl, \"') format('\").concat(this.getFormatName(font.format), \"');\\n          font-display: swap;\\n        }\\n      \");\n            // 添加到页面\n            const styleElement = document.createElement(\"style\");\n            styleElement.id = \"font-\".concat(fontId);\n            styleElement.textContent = fontFaceRule;\n            document.head.appendChild(styleElement);\n            // 标记为已加载\n            this.loadedFonts.add(fontId);\n            console.log(\"✅ 字体加载成功:\", font.name);\n            return {\n                success: true,\n                data: true\n            };\n        } catch (error) {\n            const errorMessage = \"加载字体失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 从CSS中移除字体\r\n   */ removeFontFromCSS(fontFamily) {\n        const styleElements = document.querySelectorAll('style[id^=\"font-\"]');\n        styleElements.forEach((element)=>{\n            var _element_textContent;\n            if ((_element_textContent = element.textContent) === null || _element_textContent === void 0 ? void 0 : _element_textContent.includes(fontFamily)) {\n                element.remove();\n            }\n        });\n    }\n    /**\r\n   * 获取MIME类型\r\n   */ getMimeType(format) {\n        const mimeTypes = {\n            \"woff2\": \"font/woff2\",\n            \"woff\": \"font/woff\",\n            \"ttf\": \"font/ttf\",\n            \"otf\": \"font/otf\"\n        };\n        return mimeTypes[format] || \"font/ttf\";\n    }\n    /**\r\n   * 获取格式名称\r\n   */ getFormatName(format) {\n        const formatNames = {\n            \"woff2\": \"woff2\",\n            \"woff\": \"woff\",\n            \"ttf\": \"truetype\",\n            \"otf\": \"opentype\"\n        };\n        return formatNames[format] || \"truetype\";\n    }\n    /**\r\n   * 预加载所有字体\r\n   */ async preloadAllFonts() {\n        try {\n            const fontsResult = await this.getAllFonts();\n            if (!fontsResult.success || !fontsResult.data) {\n                return {\n                    success: false,\n                    error: fontsResult.error\n                };\n            }\n            let loadedCount = 0;\n            const fonts = fontsResult.data;\n            for (const font of fonts){\n                const loadResult = await this.loadFontToCSS(font.id);\n                if (loadResult.success) {\n                    loadedCount++;\n                }\n            }\n            console.log(\"✅ 预加载字体完成: \".concat(loadedCount, \"/\").concat(fonts.length));\n            return {\n                success: true,\n                data: loadedCount\n            };\n        } catch (error) {\n            const errorMessage = \"预加载字体失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取字体统计信息\r\n   */ async getFontStats() {\n        try {\n            const fontsResult = await this.getAllFonts();\n            if (!fontsResult.success || !fontsResult.data) {\n                return {\n                    success: false,\n                    error: fontsResult.error\n                };\n            }\n            const fonts = fontsResult.data;\n            const stats = {\n                total: fonts.length,\n                totalSize: fonts.reduce((sum, f)=>sum + f.size, 0),\n                formats: {}\n            };\n            // 统计格式\n            fonts.forEach((font)=>{\n                stats.formats[font.format] = (stats.formats[font.format] || 0) + 1;\n            });\n            return {\n                success: true,\n                data: stats\n            };\n        } catch (error) {\n            const errorMessage = \"获取字体统计失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 清空所有字体\r\n   */ async clearAllFonts() {\n        try {\n            const result = await this.dbService.clear(this.storeName);\n            if (result.success) {\n                // 清除已加载的字体\n                this.loadedFonts.clear();\n                // 移除所有字体样式\n                const styleElements = document.querySelectorAll('style[id^=\"font-\"]');\n                styleElements.forEach((element)=>element.remove());\n                console.log(\"✅ 所有字体已清空\");\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = \"清空字体失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    constructor(){\n        this.storeName = \"fonts\";\n        this.loadedFonts = new Set();\n        // 支持的字体格式\n        this.supportedFormats = [\n            \"woff2\",\n            \"woff\",\n            \"ttf\",\n            \"otf\"\n        ];\n        // 最大文件大小 (5MB)\n        this.maxFileSize = 5 * 1024 * 1024;\n        this.dbService = _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__.DatabaseService.getInstance();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/fontService.ts\n"));

/***/ })

}]);