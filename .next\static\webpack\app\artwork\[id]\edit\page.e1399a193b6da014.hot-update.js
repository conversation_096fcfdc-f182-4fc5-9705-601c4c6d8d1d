"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/services/editorDiffService.ts":
/*!*******************************************!*\
  !*** ./src/services/editorDiffService.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditorDiffCalculator: function() { return /* binding */ EditorDiffCalculator; }\n/* harmony export */ });\n/* harmony import */ var diff__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! diff */ \"(app-pages-browser)/./node_modules/diff/lib/index.mjs\");\n/**\r\n * 编辑器差异计算服务\r\n * 扩展现有的DiffToolService，为编辑器diff对比功能提供专用方法\r\n */ \nclass EditorDiffCalculator {\n    /**\r\n   * 获取编辑器差异计算服务单例\r\n   */ static getInstance() {\n        if (!EditorDiffCalculator.instance) {\n            EditorDiffCalculator.instance = new EditorDiffCalculator();\n        }\n        return EditorDiffCalculator.instance;\n    }\n    /**\r\n   * 计算编辑器专用的diff数据\r\n   * 复用现有的文件获取和diff计算逻辑\r\n   */ async calculateEditorDiff(artworkId, filePath, modifiedContent) {\n        let operation = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : \"replace\";\n        try {\n            // 1. 检查文件是否存在\n            const { FileTreeService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\"));\n            const fileTreeService = FileTreeService.getInstance();\n            const fileExists = await fileTreeService.checkFileExists(artworkId, filePath);\n            let originalContent = \"\";\n            let fileId = \"\";\n            if (fileExists) {\n                // 文件存在，获取原始内容\n                const fileTreeResult = await this.getFileByPath(artworkId, filePath);\n                if (!fileTreeResult.success || !fileTreeResult.data) {\n                    return {\n                        success: false,\n                        error: \"无法获取文件内容: \".concat(fileTreeResult.error)\n                    };\n                }\n                originalContent = fileTreeResult.data.content || \"\";\n                fileId = fileTreeResult.data.id;\n            } else {\n                // 文件不存在，原始内容为空\n                originalContent = \"\";\n                fileId = \"new-file-\".concat(Date.now());\n            }\n            // 2. 根据操作类型生成最终的修改内容\n            let finalModifiedContent = modifiedContent;\n            if (operation === \"append\" && originalContent) {\n                finalModifiedContent = originalContent + \"\\n\" + modifiedContent;\n            } else if (operation === \"replace\" && modifiedContent.includes(\"|||\")) {\n                // 处理正则替换格式：pattern|||replacement\n                const [pattern, replacement] = modifiedContent.split(\"|||\");\n                try {\n                    const regex = new RegExp(pattern, \"g\");\n                    finalModifiedContent = originalContent.replace(regex, replacement);\n                } catch (error) {\n                    return {\n                        success: false,\n                        error: \"正则表达式错误: \".concat(error instanceof Error ? error.message : String(error))\n                    };\n                }\n            }\n            // 3. 使用现有的diff计算逻辑\n            const diffStats = this.calculateDiffStats(originalContent, finalModifiedContent);\n            // 4. 生成react-diff-view兼容的数据\n            const reactDiffData = this.generateReactDiffViewData(originalContent, finalModifiedContent, filePath);\n            // 5. 构建EditorDiffData\n            const editorDiffData = {\n                // 复用现有的DiffStats字段\n                additions: diffStats.additions,\n                deletions: diffStats.deletions,\n                modifications: diffStats.modifications,\n                totalChanges: diffStats.totalChanges,\n                // 新增编辑器专用字段\n                hunks: reactDiffData.hunks,\n                oldSource: originalContent,\n                newSource: finalModifiedContent,\n                filePath,\n                operation\n            };\n            return {\n                success: true,\n                data: editorDiffData\n            };\n        } catch (error) {\n            const errorMessage = \"计算编辑器diff失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 生成react-diff-view兼容的数据格式\r\n   * 使用diff库生成hunks数据\r\n   */ generateReactDiffViewData(original, modified, filePath) {\n        try {\n            // 使用diff库生成patch\n            const patch = (0,diff__WEBPACK_IMPORTED_MODULE_0__.createPatch)(filePath, original, modified, \"\", \"\");\n            // 解析patch为结构化数据\n            const parsedDiff = (0,diff__WEBPACK_IMPORTED_MODULE_0__.parsePatch)(patch);\n            if (parsedDiff.length === 0) {\n                // 没有差异的情况\n                return {\n                    hunks: [],\n                    oldRevision: \"original\",\n                    newRevision: \"modified\",\n                    type: \"modify\"\n                };\n            }\n            const file = parsedDiff[0];\n            // 转换hunks格式\n            const hunks = file.hunks.map((hunk)=>{\n                var _hunk_changes;\n                console.log(\"\\uD83D\\uDD0D 处理hunk:\", {\n                    oldStart: hunk.oldStart,\n                    oldLines: hunk.oldLines,\n                    newStart: hunk.newStart,\n                    newLines: hunk.newLines,\n                    changesCount: ((_hunk_changes = hunk.changes) === null || _hunk_changes === void 0 ? void 0 : _hunk_changes.length) || 0,\n                    hasChanges: !!hunk.changes,\n                    hunkKeys: Object.keys(hunk)\n                });\n                return {\n                    oldStart: hunk.oldStart,\n                    oldLines: hunk.oldLines,\n                    newStart: hunk.newStart,\n                    newLines: hunk.newLines,\n                    changes: (hunk.changes || []).map((change)=>{\n                        var _change_value;\n                        const changeType = change.type === \"add\" ? \"insert\" : change.type === \"del\" ? \"delete\" : \"normal\";\n                        const oldLineNumber = change.type !== \"add\" ? change.ln1 : undefined;\n                        const newLineNumber = change.type !== \"del\" ? change.ln2 : undefined;\n                        console.log(\"\\uD83D\\uDD0D 处理change:\", {\n                            originalType: change.type,\n                            mappedType: changeType,\n                            oldLine: oldLineNumber,\n                            newLine: newLineNumber,\n                            content: ((_change_value = change.value) === null || _change_value === void 0 ? void 0 : _change_value.substring(0, 20)) || \"\"\n                        });\n                        return {\n                            type: changeType,\n                            oldLineNumber,\n                            newLineNumber,\n                            content: change.value || \"\",\n                            isNormal: change.type === \"normal\",\n                            // react-diff-view兼容字段\n                            ln1: oldLineNumber,\n                            ln2: newLineNumber,\n                            value: change.value || \"\"\n                        };\n                    }),\n                    content: hunk.changes.map((c)=>c.value || \"\").join(\"\")\n                };\n            });\n            return {\n                hunks,\n                oldRevision: \"original\",\n                newRevision: \"modified\",\n                type: this.detectChangeType(original, modified)\n            };\n        } catch (error) {\n            console.error(\"❌ 生成react-diff-view数据失败:\", error);\n            // 返回空的diff数据\n            return {\n                hunks: [],\n                oldRevision: \"original\",\n                newRevision: \"modified\",\n                type: \"modify\"\n            };\n        }\n    }\n    /**\r\n   * 查找下一个差异位置\r\n   * 用于差异导航功能\r\n   */ findNextDiff(hunks, currentLine) {\n        for (const hunk of hunks){\n            // 查找第一个大于当前行的差异\n            const diffLine = Math.min(hunk.oldStart, hunk.newStart);\n            if (diffLine > currentLine) {\n                return diffLine;\n            }\n        }\n        return null; // 没有找到下一个差异\n    }\n    /**\r\n   * 查找上一个差异位置\r\n   * 用于差异导航功能\r\n   */ findPrevDiff(hunks, currentLine) {\n        // 从后往前查找\n        for(let i = hunks.length - 1; i >= 0; i--){\n            const hunk = hunks[i];\n            const diffLine = Math.min(hunk.oldStart, hunk.newStart);\n            if (diffLine < currentLine) {\n                return diffLine;\n            }\n        }\n        return null; // 没有找到上一个差异\n    }\n    /**\r\n   * 计算差异统计信息\r\n   * 复用现有逻辑但适配新的数据格式\r\n   */ calculateDiffStats(original, modified) {\n        const originalLines = original.split(\"\\n\");\n        const modifiedLines = modified.split(\"\\n\");\n        let additions = 0;\n        let deletions = 0;\n        let modifications = 0;\n        const maxLines = Math.max(originalLines.length, modifiedLines.length);\n        for(let i = 0; i < maxLines; i++){\n            const originalLine = originalLines[i];\n            const modifiedLine = modifiedLines[i];\n            if (originalLine === undefined) {\n                additions++;\n            } else if (modifiedLine === undefined) {\n                deletions++;\n            } else if (originalLine !== modifiedLine) {\n                modifications++;\n            }\n        }\n        return {\n            additions,\n            deletions,\n            modifications,\n            totalChanges: additions + deletions + modifications\n        };\n    }\n    /**\r\n   * 根据文件路径获取文件信息\r\n   * 辅助方法，用于获取文件内容\r\n   */ async getFileByPath(artworkId, filePath) {\n        try {\n            // 直接使用FileTreeService实例\n            const { FileTreeService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\"));\n            const fileTreeService = FileTreeService.getInstance();\n            // 获取文件树\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (!fileTreeResult.success || !fileTreeResult.data) {\n                return {\n                    success: false,\n                    error: \"无法获取文件树\"\n                };\n            }\n            // 在文件树中查找目标文件\n            const findFileInTree = (node, targetPath)=>{\n                // 增强的路径标准化函数\n                const normalizePath = (path)=>{\n                    if (!path) return \"\";\n                    // 移除开头的斜杠，统一分隔符，但保持大小写（中文路径敏感）\n                    return path.replace(/^\\/+/, \"\").replace(/\\\\/g, \"/\").replace(/\\/+/g, \"/\");\n                };\n                const normalizedTarget = normalizePath(targetPath);\n                const normalizedNodePath = normalizePath(node.path || \"\");\n                const normalizedNodeName = normalizePath(node.name || \"\");\n                console.log(\"\\uD83D\\uDD0D 查找文件详情:\", {\n                    targetPath,\n                    normalizedTarget,\n                    nodeName: node.name,\n                    nodePath: node.path,\n                    normalizedNodePath,\n                    normalizedNodeName,\n                    nodeType: node.type\n                });\n                // 🔧 排除根目录：根目录不应该被匹配为文件\n                if (node.name === \"root\" || node.path === \"/\" || normalizedNodePath === \"\") {\n                    console.log(\"⏭️ 跳过根目录，继续搜索子节点\");\n                    // 直接搜索子节点，不匹配根目录本身\n                    if (node.children && Array.isArray(node.children)) {\n                        for (const child of node.children){\n                            const found = findFileInTree(child, targetPath);\n                            if (found) return found;\n                        }\n                    }\n                    return null;\n                }\n                // 🔧 只匹配文件类型的节点，排除文件夹\n                const isFile = node.type === \"file\" || !node.children || node.children.length === 0;\n                // 1. 精确路径匹配（仅限文件）\n                if (isFile && normalizedNodePath === normalizedTarget) {\n                    console.log(\"✅ 精确路径匹配（文件）:\", normalizedNodePath);\n                    return node;\n                }\n                // 2. 文件名匹配（仅限文件）\n                if (isFile && normalizedNodeName === normalizedTarget) {\n                    console.log(\"✅ 文件名匹配（文件）:\", normalizedNodeName);\n                    return node;\n                }\n                // 3. 路径末尾匹配（仅限文件）\n                const targetParts = normalizedTarget.split(\"/\").filter((p)=>p);\n                const nodeParts = normalizedNodePath.split(\"/\").filter((p)=>p);\n                if (isFile && targetParts.length <= nodeParts.length) {\n                    const nodePathSuffix = nodeParts.slice(-targetParts.length).join(\"/\");\n                    if (nodePathSuffix === normalizedTarget) {\n                        console.log(\"✅ 路径后缀匹配（文件）:\", {\n                            nodePathSuffix,\n                            normalizedTarget\n                        });\n                        return node;\n                    }\n                }\n                // 4. 文件名部分匹配（仅限文件）\n                const targetFileName = targetParts[targetParts.length - 1];\n                if (isFile && targetFileName && normalizedNodeName === targetFileName) {\n                    console.log(\"✅ 文件名部分匹配（文件）:\", {\n                        targetFileName,\n                        normalizedNodeName\n                    });\n                    return node;\n                }\n                // 5. 模糊匹配（仅限文件）\n                if (isFile && (normalizedNodePath.includes(normalizedTarget) || normalizedTarget.includes(normalizedNodePath))) {\n                    console.log(\"✅ 模糊路径匹配（文件）:\", {\n                        normalizedNodePath,\n                        normalizedTarget\n                    });\n                    return node;\n                }\n                // 递归检查子节点\n                if (node.children && Array.isArray(node.children)) {\n                    for (const child of node.children){\n                        const found = findFileInTree(child, targetPath);\n                        if (found) return found;\n                    }\n                }\n                return null;\n            };\n            const targetFile = findFileInTree(fileTreeResult.data, filePath);\n            if (!targetFile) {\n                return {\n                    success: false,\n                    error: \"文件不存在\"\n                };\n            }\n            // 获取完整的文件数据\n            const fileResult = await fileTreeService.getFile(targetFile.id);\n            if (!fileResult.success || !fileResult.data) {\n                return {\n                    success: false,\n                    error: \"无法获取文件内容\"\n                };\n            }\n            return {\n                success: true,\n                data: fileResult.data\n            };\n        } catch (error) {\n            return {\n                success: false,\n                error: \"获取文件失败: \".concat(error instanceof Error ? error.message : String(error))\n            };\n        }\n    }\n    /**\r\n   * 检测变更类型\r\n   * 用于react-diff-view的type字段\r\n   */ detectChangeType(original, modified) {\n        if (!original && modified) {\n            return \"add\";\n        } else if (original && !modified) {\n            return \"delete\";\n        } else {\n            return \"modify\";\n        }\n    }\n    /**\r\n   * 处理diff请求\r\n   * 统一的diff请求处理入口\r\n   */ async processDiffRequest(artworkId, diffRequest) {\n        return this.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n    }\n    /**\r\n   * 获取文件内容\r\n   * 公开方法，供其他组件使用\r\n   */ async getFileContent(artworkId, filePath) {\n        try {\n            const fileResult = await this.getFileByPath(artworkId, filePath);\n            if (fileResult.success && fileResult.data) {\n                return fileResult.data.content || \"\";\n            }\n            return \"\";\n        } catch (error) {\n            console.error(\"❌ 获取文件内容失败:\", error);\n            return \"\";\n        }\n    }\n    constructor(){\n    // 不再继承DiffToolService，独立实现\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/editorDiffService.ts\n"));

/***/ })

});