"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_xml_xml_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/xml/xml.js":
/*!**********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/xml/xml.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/editor.api.js?ce9f\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/basic-languages/xml/xml.ts\nvar conf = {\n  comments: {\n    blockComment: [\"<!--\", \"-->\"]\n  },\n  brackets: [[\"<\", \">\"]],\n  autoClosingPairs: [\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  surroundingPairs: [\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  onEnterRules: [\n    {\n      beforeText: new RegExp(`<([_:\\\\w][_:\\\\w-.\\\\d]*)([^/>]*(?!/)>)[^<]*$`, \"i\"),\n      afterText: /^<\\/([_:\\w][_:\\w-.\\d]*)\\s*>$/i,\n      action: {\n        indentAction: monaco_editor_core_exports.languages.IndentAction.IndentOutdent\n      }\n    },\n    {\n      beforeText: new RegExp(`<(\\\\w[\\\\w\\\\d]*)([^/>]*(?!/)>)[^<]*$`, \"i\"),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".xml\",\n  ignoreCase: true,\n  // Useful regular expressions\n  qualifiedName: /(?:[\\w\\.\\-]+:)?[\\w\\.\\-]+/,\n  tokenizer: {\n    root: [\n      [/[^<&]+/, \"\"],\n      { include: \"@whitespace\" },\n      // Standard opening tag\n      [/(<)(@qualifiedName)/, [{ token: \"delimiter\" }, { token: \"tag\", next: \"@tag\" }]],\n      // Standard closing tag\n      [\n        /(<\\/)(@qualifiedName)(\\s*)(>)/,\n        [{ token: \"delimiter\" }, { token: \"tag\" }, \"\", { token: \"delimiter\" }]\n      ],\n      // Meta tags - instruction\n      [/(<\\?)(@qualifiedName)/, [{ token: \"delimiter\" }, { token: \"metatag\", next: \"@tag\" }]],\n      // Meta tags - declaration\n      [/(<\\!)(@qualifiedName)/, [{ token: \"delimiter\" }, { token: \"metatag\", next: \"@tag\" }]],\n      // CDATA\n      [/<\\!\\[CDATA\\[/, { token: \"delimiter.cdata\", next: \"@cdata\" }],\n      [/&\\w+;/, \"string.escape\"]\n    ],\n    cdata: [\n      [/[^\\]]+/, \"\"],\n      [/\\]\\]>/, { token: \"delimiter.cdata\", next: \"@pop\" }],\n      [/\\]/, \"\"]\n    ],\n    tag: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/(@qualifiedName)(\\s*=\\s*)(\"[^\"]*\"|'[^']*')/, [\"attribute.name\", \"\", \"attribute.value\"]],\n      [\n        /(@qualifiedName)(\\s*=\\s*)(\"[^\">?\\/]*|'[^'>?\\/]*)(?=[\\?\\/]\\>)/,\n        [\"attribute.name\", \"\", \"attribute.value\"]\n      ],\n      [/(@qualifiedName)(\\s*=\\s*)(\"[^\">]*|'[^'>]*)/, [\"attribute.name\", \"\", \"attribute.value\"]],\n      [/@qualifiedName/, \"attribute.name\"],\n      [/\\?>/, { token: \"delimiter\", next: \"@pop\" }],\n      [/(\\/)(>)/, [{ token: \"tag\" }, { token: \"delimiter\", next: \"@pop\" }]],\n      [/>/, { token: \"delimiter\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/<!--/, { token: \"comment\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^<\\-]+/, \"comment.content\"],\n      [/-->/, { token: \"comment\", next: \"@pop\" }],\n      [/<!--/, \"comment.content.invalid\"],\n      [/[<\\-]/, \"comment.content\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/xml/xml.js\n"));

/***/ })

}]);