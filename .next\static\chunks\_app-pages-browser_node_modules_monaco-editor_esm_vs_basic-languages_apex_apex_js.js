"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_apex_apex_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/apex/apex.js":
/*!************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/apex/apex.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/apex/apex.ts\nvar conf = {\n  // the default separators except `@$`\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"<\", close: \">\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?region\\\\b)|(?:<editor-fold\\\\b))\"),\n      end: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?endregion\\\\b)|(?:</editor-fold>))\")\n    }\n  }\n};\nvar keywords = [\n  \"abstract\",\n  \"activate\",\n  \"and\",\n  \"any\",\n  \"array\",\n  \"as\",\n  \"asc\",\n  \"assert\",\n  \"autonomous\",\n  \"begin\",\n  \"bigdecimal\",\n  \"blob\",\n  \"boolean\",\n  \"break\",\n  \"bulk\",\n  \"by\",\n  \"case\",\n  \"cast\",\n  \"catch\",\n  \"char\",\n  \"class\",\n  \"collect\",\n  \"commit\",\n  \"const\",\n  \"continue\",\n  \"convertcurrency\",\n  \"decimal\",\n  \"default\",\n  \"delete\",\n  \"desc\",\n  \"do\",\n  \"double\",\n  \"else\",\n  \"end\",\n  \"enum\",\n  \"exception\",\n  \"exit\",\n  \"export\",\n  \"extends\",\n  \"false\",\n  \"final\",\n  \"finally\",\n  \"float\",\n  \"for\",\n  \"from\",\n  \"future\",\n  \"get\",\n  \"global\",\n  \"goto\",\n  \"group\",\n  \"having\",\n  \"hint\",\n  \"if\",\n  \"implements\",\n  \"import\",\n  \"in\",\n  \"inner\",\n  \"insert\",\n  \"instanceof\",\n  \"int\",\n  \"interface\",\n  \"into\",\n  \"join\",\n  \"last_90_days\",\n  \"last_month\",\n  \"last_n_days\",\n  \"last_week\",\n  \"like\",\n  \"limit\",\n  \"list\",\n  \"long\",\n  \"loop\",\n  \"map\",\n  \"merge\",\n  \"native\",\n  \"new\",\n  \"next_90_days\",\n  \"next_month\",\n  \"next_n_days\",\n  \"next_week\",\n  \"not\",\n  \"null\",\n  \"nulls\",\n  \"number\",\n  \"object\",\n  \"of\",\n  \"on\",\n  \"or\",\n  \"outer\",\n  \"override\",\n  \"package\",\n  \"parallel\",\n  \"pragma\",\n  \"private\",\n  \"protected\",\n  \"public\",\n  \"retrieve\",\n  \"return\",\n  \"returning\",\n  \"rollback\",\n  \"savepoint\",\n  \"search\",\n  \"select\",\n  \"set\",\n  \"short\",\n  \"sort\",\n  \"stat\",\n  \"static\",\n  \"strictfp\",\n  \"super\",\n  \"switch\",\n  \"synchronized\",\n  \"system\",\n  \"testmethod\",\n  \"then\",\n  \"this\",\n  \"this_month\",\n  \"this_week\",\n  \"throw\",\n  \"throws\",\n  \"today\",\n  \"tolabel\",\n  \"tomorrow\",\n  \"transaction\",\n  \"transient\",\n  \"trigger\",\n  \"true\",\n  \"try\",\n  \"type\",\n  \"undelete\",\n  \"update\",\n  \"upsert\",\n  \"using\",\n  \"virtual\",\n  \"void\",\n  \"volatile\",\n  \"webservice\",\n  \"when\",\n  \"where\",\n  \"while\",\n  \"yesterday\"\n];\nvar uppercaseFirstLetter = (lowercase) => lowercase.charAt(0).toUpperCase() + lowercase.substr(1);\nvar keywordsWithCaseVariations = [];\nkeywords.forEach((lowercase) => {\n  keywordsWithCaseVariations.push(lowercase);\n  keywordsWithCaseVariations.push(lowercase.toUpperCase());\n  keywordsWithCaseVariations.push(uppercaseFirstLetter(lowercase));\n});\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".apex\",\n  keywords: keywordsWithCaseVariations,\n  operators: [\n    \"=\",\n    \">\",\n    \"<\",\n    \"!\",\n    \"~\",\n    \"?\",\n    \":\",\n    \"==\",\n    \"<=\",\n    \">=\",\n    \"!=\",\n    \"&&\",\n    \"||\",\n    \"++\",\n    \"--\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \"%\",\n    \"<<\",\n    \">>\",\n    \">>>\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"%=\",\n    \"<<=\",\n    \">>=\",\n    \">>>=\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+(_+\\d+)*/,\n  octaldigits: /[0-7]+(_+[0-7]+)*/,\n  binarydigits: /[0-1]+(_+[0-1]+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [\n        /[a-z_$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // assume that identifiers starting with an uppercase letter are types\n      [\n        /[A-Z][\\w\\$]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // @ annotations.\n      [/@\\s*[a-zA-Z_\\$][\\w\\$]*/, \"annotation\"],\n      // numbers\n      [/(@digits)[eE]([\\-+]?(@digits))?[fFdD]?/, \"number.float\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?[fFdD]?/, \"number.float\"],\n      [/(@digits)[fFdD]/, \"number.float\"],\n      [/(@digits)[lL]?/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", '@string.\"'],\n      [/'/, \"string\", \"@string.'\"],\n      // characters\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@apexdoc\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      // [/\\/\\*/, 'comment', '@push' ],    // nested comment not allowed :-(\n      // [/\\/\\*/,    'comment.invalid' ],    // this breaks block comments in the shape of /* //*/\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    //Identical copy of comment above, except for the addition of .doc\n    apexdoc: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    string: [\n      [/[^\\\\\"']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/apex/apex.js\n"));

/***/ })

}]);