"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/quill-delta";
exports.ids = ["vendor-chunks/quill-delta"];
exports.modules = {

/***/ "(ssr)/./node_modules/quill-delta/dist/AttributeMap.js":
/*!*******************************************************!*\
  !*** ./node_modules/quill-delta/dist/AttributeMap.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst cloneDeep = __webpack_require__(/*! lodash.clonedeep */ \"(ssr)/./node_modules/lodash.clonedeep/index.js\");\nconst isEqual = __webpack_require__(/*! lodash.isequal */ \"(ssr)/./node_modules/lodash.isequal/index.js\");\nvar AttributeMap;\n(function (AttributeMap) {\n    function compose(a = {}, b = {}, keepNull = false) {\n        if (typeof a !== 'object') {\n            a = {};\n        }\n        if (typeof b !== 'object') {\n            b = {};\n        }\n        let attributes = cloneDeep(b);\n        if (!keepNull) {\n            attributes = Object.keys(attributes).reduce((copy, key) => {\n                if (attributes[key] != null) {\n                    copy[key] = attributes[key];\n                }\n                return copy;\n            }, {});\n        }\n        for (const key in a) {\n            if (a[key] !== undefined && b[key] === undefined) {\n                attributes[key] = a[key];\n            }\n        }\n        return Object.keys(attributes).length > 0 ? attributes : undefined;\n    }\n    AttributeMap.compose = compose;\n    function diff(a = {}, b = {}) {\n        if (typeof a !== 'object') {\n            a = {};\n        }\n        if (typeof b !== 'object') {\n            b = {};\n        }\n        const attributes = Object.keys(a)\n            .concat(Object.keys(b))\n            .reduce((attrs, key) => {\n            if (!isEqual(a[key], b[key])) {\n                attrs[key] = b[key] === undefined ? null : b[key];\n            }\n            return attrs;\n        }, {});\n        return Object.keys(attributes).length > 0 ? attributes : undefined;\n    }\n    AttributeMap.diff = diff;\n    function invert(attr = {}, base = {}) {\n        attr = attr || {};\n        const baseInverted = Object.keys(base).reduce((memo, key) => {\n            if (base[key] !== attr[key] && attr[key] !== undefined) {\n                memo[key] = base[key];\n            }\n            return memo;\n        }, {});\n        return Object.keys(attr).reduce((memo, key) => {\n            if (attr[key] !== base[key] && base[key] === undefined) {\n                memo[key] = null;\n            }\n            return memo;\n        }, baseInverted);\n    }\n    AttributeMap.invert = invert;\n    function transform(a, b, priority = false) {\n        if (typeof a !== 'object') {\n            return b;\n        }\n        if (typeof b !== 'object') {\n            return undefined;\n        }\n        if (!priority) {\n            return b; // b simply overwrites us without priority\n        }\n        const attributes = Object.keys(b).reduce((attrs, key) => {\n            if (a[key] === undefined) {\n                attrs[key] = b[key]; // null is a valid value\n            }\n            return attrs;\n        }, {});\n        return Object.keys(attributes).length > 0 ? attributes : undefined;\n    }\n    AttributeMap.transform = transform;\n})(AttributeMap || (AttributeMap = {}));\nexports[\"default\"] = AttributeMap;\n//# sourceMappingURL=AttributeMap.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/quill-delta/dist/AttributeMap.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/quill-delta/dist/Delta.js":
/*!************************************************!*\
  !*** ./node_modules/quill-delta/dist/Delta.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeMap = exports.OpIterator = exports.Op = void 0;\nconst diff = __webpack_require__(/*! fast-diff */ \"(ssr)/./node_modules/fast-diff/diff.js\");\nconst cloneDeep = __webpack_require__(/*! lodash.clonedeep */ \"(ssr)/./node_modules/lodash.clonedeep/index.js\");\nconst isEqual = __webpack_require__(/*! lodash.isequal */ \"(ssr)/./node_modules/lodash.isequal/index.js\");\nconst AttributeMap_1 = __webpack_require__(/*! ./AttributeMap */ \"(ssr)/./node_modules/quill-delta/dist/AttributeMap.js\");\nexports.AttributeMap = AttributeMap_1.default;\nconst Op_1 = __webpack_require__(/*! ./Op */ \"(ssr)/./node_modules/quill-delta/dist/Op.js\");\nexports.Op = Op_1.default;\nconst OpIterator_1 = __webpack_require__(/*! ./OpIterator */ \"(ssr)/./node_modules/quill-delta/dist/OpIterator.js\");\nexports.OpIterator = OpIterator_1.default;\nconst NULL_CHARACTER = String.fromCharCode(0); // Placeholder char for embed in diff()\nconst getEmbedTypeAndData = (a, b) => {\n    if (typeof a !== 'object' || a === null) {\n        throw new Error(`cannot retain a ${typeof a}`);\n    }\n    if (typeof b !== 'object' || b === null) {\n        throw new Error(`cannot retain a ${typeof b}`);\n    }\n    const embedType = Object.keys(a)[0];\n    if (!embedType || embedType !== Object.keys(b)[0]) {\n        throw new Error(`embed types not matched: ${embedType} != ${Object.keys(b)[0]}`);\n    }\n    return [embedType, a[embedType], b[embedType]];\n};\nclass Delta {\n    constructor(ops) {\n        // Assume we are given a well formed ops\n        if (Array.isArray(ops)) {\n            this.ops = ops;\n        }\n        else if (ops != null && Array.isArray(ops.ops)) {\n            this.ops = ops.ops;\n        }\n        else {\n            this.ops = [];\n        }\n    }\n    static registerEmbed(embedType, handler) {\n        this.handlers[embedType] = handler;\n    }\n    static unregisterEmbed(embedType) {\n        delete this.handlers[embedType];\n    }\n    static getHandler(embedType) {\n        const handler = this.handlers[embedType];\n        if (!handler) {\n            throw new Error(`no handlers for embed type \"${embedType}\"`);\n        }\n        return handler;\n    }\n    insert(arg, attributes) {\n        const newOp = {};\n        if (typeof arg === 'string' && arg.length === 0) {\n            return this;\n        }\n        newOp.insert = arg;\n        if (attributes != null &&\n            typeof attributes === 'object' &&\n            Object.keys(attributes).length > 0) {\n            newOp.attributes = attributes;\n        }\n        return this.push(newOp);\n    }\n    delete(length) {\n        if (length <= 0) {\n            return this;\n        }\n        return this.push({ delete: length });\n    }\n    retain(length, attributes) {\n        if (typeof length === 'number' && length <= 0) {\n            return this;\n        }\n        const newOp = { retain: length };\n        if (attributes != null &&\n            typeof attributes === 'object' &&\n            Object.keys(attributes).length > 0) {\n            newOp.attributes = attributes;\n        }\n        return this.push(newOp);\n    }\n    push(newOp) {\n        let index = this.ops.length;\n        let lastOp = this.ops[index - 1];\n        newOp = cloneDeep(newOp);\n        if (typeof lastOp === 'object') {\n            if (typeof newOp.delete === 'number' &&\n                typeof lastOp.delete === 'number') {\n                this.ops[index - 1] = { delete: lastOp.delete + newOp.delete };\n                return this;\n            }\n            // Since it does not matter if we insert before or after deleting at the same index,\n            // always prefer to insert first\n            if (typeof lastOp.delete === 'number' && newOp.insert != null) {\n                index -= 1;\n                lastOp = this.ops[index - 1];\n                if (typeof lastOp !== 'object') {\n                    this.ops.unshift(newOp);\n                    return this;\n                }\n            }\n            if (isEqual(newOp.attributes, lastOp.attributes)) {\n                if (typeof newOp.insert === 'string' &&\n                    typeof lastOp.insert === 'string') {\n                    this.ops[index - 1] = { insert: lastOp.insert + newOp.insert };\n                    if (typeof newOp.attributes === 'object') {\n                        this.ops[index - 1].attributes = newOp.attributes;\n                    }\n                    return this;\n                }\n                else if (typeof newOp.retain === 'number' &&\n                    typeof lastOp.retain === 'number') {\n                    this.ops[index - 1] = { retain: lastOp.retain + newOp.retain };\n                    if (typeof newOp.attributes === 'object') {\n                        this.ops[index - 1].attributes = newOp.attributes;\n                    }\n                    return this;\n                }\n            }\n        }\n        if (index === this.ops.length) {\n            this.ops.push(newOp);\n        }\n        else {\n            this.ops.splice(index, 0, newOp);\n        }\n        return this;\n    }\n    chop() {\n        const lastOp = this.ops[this.ops.length - 1];\n        if (lastOp && typeof lastOp.retain === 'number' && !lastOp.attributes) {\n            this.ops.pop();\n        }\n        return this;\n    }\n    filter(predicate) {\n        return this.ops.filter(predicate);\n    }\n    forEach(predicate) {\n        this.ops.forEach(predicate);\n    }\n    map(predicate) {\n        return this.ops.map(predicate);\n    }\n    partition(predicate) {\n        const passed = [];\n        const failed = [];\n        this.forEach((op) => {\n            const target = predicate(op) ? passed : failed;\n            target.push(op);\n        });\n        return [passed, failed];\n    }\n    reduce(predicate, initialValue) {\n        return this.ops.reduce(predicate, initialValue);\n    }\n    changeLength() {\n        return this.reduce((length, elem) => {\n            if (elem.insert) {\n                return length + Op_1.default.length(elem);\n            }\n            else if (elem.delete) {\n                return length - elem.delete;\n            }\n            return length;\n        }, 0);\n    }\n    length() {\n        return this.reduce((length, elem) => {\n            return length + Op_1.default.length(elem);\n        }, 0);\n    }\n    slice(start = 0, end = Infinity) {\n        const ops = [];\n        const iter = new OpIterator_1.default(this.ops);\n        let index = 0;\n        while (index < end && iter.hasNext()) {\n            let nextOp;\n            if (index < start) {\n                nextOp = iter.next(start - index);\n            }\n            else {\n                nextOp = iter.next(end - index);\n                ops.push(nextOp);\n            }\n            index += Op_1.default.length(nextOp);\n        }\n        return new Delta(ops);\n    }\n    compose(other) {\n        const thisIter = new OpIterator_1.default(this.ops);\n        const otherIter = new OpIterator_1.default(other.ops);\n        const ops = [];\n        const firstOther = otherIter.peek();\n        if (firstOther != null &&\n            typeof firstOther.retain === 'number' &&\n            firstOther.attributes == null) {\n            let firstLeft = firstOther.retain;\n            while (thisIter.peekType() === 'insert' &&\n                thisIter.peekLength() <= firstLeft) {\n                firstLeft -= thisIter.peekLength();\n                ops.push(thisIter.next());\n            }\n            if (firstOther.retain - firstLeft > 0) {\n                otherIter.next(firstOther.retain - firstLeft);\n            }\n        }\n        const delta = new Delta(ops);\n        while (thisIter.hasNext() || otherIter.hasNext()) {\n            if (otherIter.peekType() === 'insert') {\n                delta.push(otherIter.next());\n            }\n            else if (thisIter.peekType() === 'delete') {\n                delta.push(thisIter.next());\n            }\n            else {\n                const length = Math.min(thisIter.peekLength(), otherIter.peekLength());\n                const thisOp = thisIter.next(length);\n                const otherOp = otherIter.next(length);\n                if (otherOp.retain) {\n                    const newOp = {};\n                    if (typeof thisOp.retain === 'number') {\n                        newOp.retain =\n                            typeof otherOp.retain === 'number' ? length : otherOp.retain;\n                    }\n                    else {\n                        if (typeof otherOp.retain === 'number') {\n                            if (thisOp.retain == null) {\n                                newOp.insert = thisOp.insert;\n                            }\n                            else {\n                                newOp.retain = thisOp.retain;\n                            }\n                        }\n                        else {\n                            const action = thisOp.retain == null ? 'insert' : 'retain';\n                            const [embedType, thisData, otherData] = getEmbedTypeAndData(thisOp[action], otherOp.retain);\n                            const handler = Delta.getHandler(embedType);\n                            newOp[action] = {\n                                [embedType]: handler.compose(thisData, otherData, action === 'retain'),\n                            };\n                        }\n                    }\n                    // Preserve null when composing with a retain, otherwise remove it for inserts\n                    const attributes = AttributeMap_1.default.compose(thisOp.attributes, otherOp.attributes, typeof thisOp.retain === 'number');\n                    if (attributes) {\n                        newOp.attributes = attributes;\n                    }\n                    delta.push(newOp);\n                    // Optimization if rest of other is just retain\n                    if (!otherIter.hasNext() &&\n                        isEqual(delta.ops[delta.ops.length - 1], newOp)) {\n                        const rest = new Delta(thisIter.rest());\n                        return delta.concat(rest).chop();\n                    }\n                    // Other op should be delete, we could be an insert or retain\n                    // Insert + delete cancels out\n                }\n                else if (typeof otherOp.delete === 'number' &&\n                    (typeof thisOp.retain === 'number' ||\n                        (typeof thisOp.retain === 'object' && thisOp.retain !== null))) {\n                    delta.push(otherOp);\n                }\n            }\n        }\n        return delta.chop();\n    }\n    concat(other) {\n        const delta = new Delta(this.ops.slice());\n        if (other.ops.length > 0) {\n            delta.push(other.ops[0]);\n            delta.ops = delta.ops.concat(other.ops.slice(1));\n        }\n        return delta;\n    }\n    diff(other, cursor) {\n        if (this.ops === other.ops) {\n            return new Delta();\n        }\n        const strings = [this, other].map((delta) => {\n            return delta\n                .map((op) => {\n                if (op.insert != null) {\n                    return typeof op.insert === 'string' ? op.insert : NULL_CHARACTER;\n                }\n                const prep = delta === other ? 'on' : 'with';\n                throw new Error('diff() called ' + prep + ' non-document');\n            })\n                .join('');\n        });\n        const retDelta = new Delta();\n        const diffResult = diff(strings[0], strings[1], cursor, true);\n        const thisIter = new OpIterator_1.default(this.ops);\n        const otherIter = new OpIterator_1.default(other.ops);\n        diffResult.forEach((component) => {\n            let length = component[1].length;\n            while (length > 0) {\n                let opLength = 0;\n                switch (component[0]) {\n                    case diff.INSERT:\n                        opLength = Math.min(otherIter.peekLength(), length);\n                        retDelta.push(otherIter.next(opLength));\n                        break;\n                    case diff.DELETE:\n                        opLength = Math.min(length, thisIter.peekLength());\n                        thisIter.next(opLength);\n                        retDelta.delete(opLength);\n                        break;\n                    case diff.EQUAL:\n                        opLength = Math.min(thisIter.peekLength(), otherIter.peekLength(), length);\n                        const thisOp = thisIter.next(opLength);\n                        const otherOp = otherIter.next(opLength);\n                        if (isEqual(thisOp.insert, otherOp.insert)) {\n                            retDelta.retain(opLength, AttributeMap_1.default.diff(thisOp.attributes, otherOp.attributes));\n                        }\n                        else {\n                            retDelta.push(otherOp).delete(opLength);\n                        }\n                        break;\n                }\n                length -= opLength;\n            }\n        });\n        return retDelta.chop();\n    }\n    eachLine(predicate, newline = '\\n') {\n        const iter = new OpIterator_1.default(this.ops);\n        let line = new Delta();\n        let i = 0;\n        while (iter.hasNext()) {\n            if (iter.peekType() !== 'insert') {\n                return;\n            }\n            const thisOp = iter.peek();\n            const start = Op_1.default.length(thisOp) - iter.peekLength();\n            const index = typeof thisOp.insert === 'string'\n                ? thisOp.insert.indexOf(newline, start) - start\n                : -1;\n            if (index < 0) {\n                line.push(iter.next());\n            }\n            else if (index > 0) {\n                line.push(iter.next(index));\n            }\n            else {\n                if (predicate(line, iter.next(1).attributes || {}, i) === false) {\n                    return;\n                }\n                i += 1;\n                line = new Delta();\n            }\n        }\n        if (line.length() > 0) {\n            predicate(line, {}, i);\n        }\n    }\n    invert(base) {\n        const inverted = new Delta();\n        this.reduce((baseIndex, op) => {\n            if (op.insert) {\n                inverted.delete(Op_1.default.length(op));\n            }\n            else if (typeof op.retain === 'number' && op.attributes == null) {\n                inverted.retain(op.retain);\n                return baseIndex + op.retain;\n            }\n            else if (op.delete || typeof op.retain === 'number') {\n                const length = (op.delete || op.retain);\n                const slice = base.slice(baseIndex, baseIndex + length);\n                slice.forEach((baseOp) => {\n                    if (op.delete) {\n                        inverted.push(baseOp);\n                    }\n                    else if (op.retain && op.attributes) {\n                        inverted.retain(Op_1.default.length(baseOp), AttributeMap_1.default.invert(op.attributes, baseOp.attributes));\n                    }\n                });\n                return baseIndex + length;\n            }\n            else if (typeof op.retain === 'object' && op.retain !== null) {\n                const slice = base.slice(baseIndex, baseIndex + 1);\n                const baseOp = new OpIterator_1.default(slice.ops).next();\n                const [embedType, opData, baseOpData] = getEmbedTypeAndData(op.retain, baseOp.insert);\n                const handler = Delta.getHandler(embedType);\n                inverted.retain({ [embedType]: handler.invert(opData, baseOpData) }, AttributeMap_1.default.invert(op.attributes, baseOp.attributes));\n                return baseIndex + 1;\n            }\n            return baseIndex;\n        }, 0);\n        return inverted.chop();\n    }\n    transform(arg, priority = false) {\n        priority = !!priority;\n        if (typeof arg === 'number') {\n            return this.transformPosition(arg, priority);\n        }\n        const other = arg;\n        const thisIter = new OpIterator_1.default(this.ops);\n        const otherIter = new OpIterator_1.default(other.ops);\n        const delta = new Delta();\n        while (thisIter.hasNext() || otherIter.hasNext()) {\n            if (thisIter.peekType() === 'insert' &&\n                (priority || otherIter.peekType() !== 'insert')) {\n                delta.retain(Op_1.default.length(thisIter.next()));\n            }\n            else if (otherIter.peekType() === 'insert') {\n                delta.push(otherIter.next());\n            }\n            else {\n                const length = Math.min(thisIter.peekLength(), otherIter.peekLength());\n                const thisOp = thisIter.next(length);\n                const otherOp = otherIter.next(length);\n                if (thisOp.delete) {\n                    // Our delete either makes their delete redundant or removes their retain\n                    continue;\n                }\n                else if (otherOp.delete) {\n                    delta.push(otherOp);\n                }\n                else {\n                    const thisData = thisOp.retain;\n                    const otherData = otherOp.retain;\n                    let transformedData = typeof otherData === 'object' && otherData !== null\n                        ? otherData\n                        : length;\n                    if (typeof thisData === 'object' &&\n                        thisData !== null &&\n                        typeof otherData === 'object' &&\n                        otherData !== null) {\n                        const embedType = Object.keys(thisData)[0];\n                        if (embedType === Object.keys(otherData)[0]) {\n                            const handler = Delta.getHandler(embedType);\n                            if (handler) {\n                                transformedData = {\n                                    [embedType]: handler.transform(thisData[embedType], otherData[embedType], priority),\n                                };\n                            }\n                        }\n                    }\n                    // We retain either their retain or insert\n                    delta.retain(transformedData, AttributeMap_1.default.transform(thisOp.attributes, otherOp.attributes, priority));\n                }\n            }\n        }\n        return delta.chop();\n    }\n    transformPosition(index, priority = false) {\n        priority = !!priority;\n        const thisIter = new OpIterator_1.default(this.ops);\n        let offset = 0;\n        while (thisIter.hasNext() && offset <= index) {\n            const length = thisIter.peekLength();\n            const nextType = thisIter.peekType();\n            thisIter.next();\n            if (nextType === 'delete') {\n                index -= Math.min(length, index - offset);\n                continue;\n            }\n            else if (nextType === 'insert' && (offset < index || !priority)) {\n                index += length;\n            }\n            offset += length;\n        }\n        return index;\n    }\n}\nDelta.Op = Op_1.default;\nDelta.OpIterator = OpIterator_1.default;\nDelta.AttributeMap = AttributeMap_1.default;\nDelta.handlers = {};\nexports[\"default\"] = Delta;\nif (true) {\n    module.exports = Delta;\n    module.exports[\"default\"] = Delta;\n}\n//# sourceMappingURL=Delta.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/quill-delta/dist/Delta.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/quill-delta/dist/Op.js":
/*!*********************************************!*\
  !*** ./node_modules/quill-delta/dist/Op.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar Op;\n(function (Op) {\n    function length(op) {\n        if (typeof op.delete === 'number') {\n            return op.delete;\n        }\n        else if (typeof op.retain === 'number') {\n            return op.retain;\n        }\n        else if (typeof op.retain === 'object' && op.retain !== null) {\n            return 1;\n        }\n        else {\n            return typeof op.insert === 'string' ? op.insert.length : 1;\n        }\n    }\n    Op.length = length;\n})(Op || (Op = {}));\nexports[\"default\"] = Op;\n//# sourceMappingURL=Op.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcXVpbGwtZGVsdGEvZGlzdC9PcC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxnQkFBZ0I7QUFDakIsa0JBQWU7QUFDZiIsInNvdXJjZXMiOlsid2VicGFjazovL2FydHdvcmstcGxhdGZvcm0vLi9ub2RlX21vZHVsZXMvcXVpbGwtZGVsdGEvZGlzdC9PcC5qcz9hODY1Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xudmFyIE9wO1xuKGZ1bmN0aW9uIChPcCkge1xuICAgIGZ1bmN0aW9uIGxlbmd0aChvcCkge1xuICAgICAgICBpZiAodHlwZW9mIG9wLmRlbGV0ZSA9PT0gJ251bWJlcicpIHtcbiAgICAgICAgICAgIHJldHVybiBvcC5kZWxldGU7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodHlwZW9mIG9wLnJldGFpbiA9PT0gJ251bWJlcicpIHtcbiAgICAgICAgICAgIHJldHVybiBvcC5yZXRhaW47XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodHlwZW9mIG9wLnJldGFpbiA9PT0gJ29iamVjdCcgJiYgb3AucmV0YWluICE9PSBudWxsKSB7XG4gICAgICAgICAgICByZXR1cm4gMTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiB0eXBlb2Ygb3AuaW5zZXJ0ID09PSAnc3RyaW5nJyA/IG9wLmluc2VydC5sZW5ndGggOiAxO1xuICAgICAgICB9XG4gICAgfVxuICAgIE9wLmxlbmd0aCA9IGxlbmd0aDtcbn0pKE9wIHx8IChPcCA9IHt9KSk7XG5leHBvcnRzLmRlZmF1bHQgPSBPcDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPU9wLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/quill-delta/dist/Op.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/quill-delta/dist/OpIterator.js":
/*!*****************************************************!*\
  !*** ./node_modules/quill-delta/dist/OpIterator.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst Op_1 = __webpack_require__(/*! ./Op */ \"(ssr)/./node_modules/quill-delta/dist/Op.js\");\nclass Iterator {\n    constructor(ops) {\n        this.ops = ops;\n        this.index = 0;\n        this.offset = 0;\n    }\n    hasNext() {\n        return this.peekLength() < Infinity;\n    }\n    next(length) {\n        if (!length) {\n            length = Infinity;\n        }\n        const nextOp = this.ops[this.index];\n        if (nextOp) {\n            const offset = this.offset;\n            const opLength = Op_1.default.length(nextOp);\n            if (length >= opLength - offset) {\n                length = opLength - offset;\n                this.index += 1;\n                this.offset = 0;\n            }\n            else {\n                this.offset += length;\n            }\n            if (typeof nextOp.delete === 'number') {\n                return { delete: length };\n            }\n            else {\n                const retOp = {};\n                if (nextOp.attributes) {\n                    retOp.attributes = nextOp.attributes;\n                }\n                if (typeof nextOp.retain === 'number') {\n                    retOp.retain = length;\n                }\n                else if (typeof nextOp.retain === 'object' &&\n                    nextOp.retain !== null) {\n                    // offset should === 0, length should === 1\n                    retOp.retain = nextOp.retain;\n                }\n                else if (typeof nextOp.insert === 'string') {\n                    retOp.insert = nextOp.insert.substr(offset, length);\n                }\n                else {\n                    // offset should === 0, length should === 1\n                    retOp.insert = nextOp.insert;\n                }\n                return retOp;\n            }\n        }\n        else {\n            return { retain: Infinity };\n        }\n    }\n    peek() {\n        return this.ops[this.index];\n    }\n    peekLength() {\n        if (this.ops[this.index]) {\n            // Should never return 0 if our index is being managed correctly\n            return Op_1.default.length(this.ops[this.index]) - this.offset;\n        }\n        else {\n            return Infinity;\n        }\n    }\n    peekType() {\n        const op = this.ops[this.index];\n        if (op) {\n            if (typeof op.delete === 'number') {\n                return 'delete';\n            }\n            else if (typeof op.retain === 'number' ||\n                (typeof op.retain === 'object' && op.retain !== null)) {\n                return 'retain';\n            }\n            else {\n                return 'insert';\n            }\n        }\n        return 'retain';\n    }\n    rest() {\n        if (!this.hasNext()) {\n            return [];\n        }\n        else if (this.offset === 0) {\n            return this.ops.slice(this.index);\n        }\n        else {\n            const offset = this.offset;\n            const index = this.index;\n            const next = this.next();\n            const rest = this.ops.slice(this.index);\n            this.offset = offset;\n            this.index = index;\n            return [next].concat(rest);\n        }\n    }\n}\nexports[\"default\"] = Iterator;\n//# sourceMappingURL=OpIterator.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/quill-delta/dist/OpIterator.js\n");

/***/ })

};
;