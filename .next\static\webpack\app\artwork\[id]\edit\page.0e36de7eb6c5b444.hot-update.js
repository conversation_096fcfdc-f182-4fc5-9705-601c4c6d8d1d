"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/AIAssistant/CompactDiffDisplay.tsx":
/*!***********************************************************!*\
  !*** ./src/components/AIAssistant/CompactDiffDisplay.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CompactDiffDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_diffToolService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/diffToolService */ \"(app-pages-browser)/./src/services/diffToolService.ts\");\n/**\r\n * 紧凑差异显示组件\r\n * 用于在AI助手响应中显示文件修改建议\r\n * 支持折叠/展开状态，显示文件名、修改统计和跳转按钮\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CompactDiffDisplay(param) {\n    let { filePath, operation, content, find, stats, onJumpToEditor, onApplyChanges, onCreateFile, onOpenDetailedDiff, fileExists = true, artworkId, className = \"\" } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCheckingFile, setIsCheckingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actualFileExists, setActualFileExists] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(fileExists);\n    // 检查文件是否存在\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkFileExists = async ()=>{\n            if (!artworkId) {\n                setActualFileExists(fileExists);\n                return;\n            }\n            setIsCheckingFile(true);\n            try {\n                const diffToolService = _services_diffToolService__WEBPACK_IMPORTED_MODULE_2__.DiffToolService.getInstance();\n                const exists = await diffToolService.checkFileExists(artworkId, filePath);\n                setActualFileExists(exists);\n            } catch (error) {\n                console.error(\"❌ 检查文件存在性失败:\", error);\n                setActualFileExists(fileExists);\n            } finally{\n                setIsCheckingFile(false);\n            }\n        };\n        checkFileExists();\n    }, [\n        artworkId,\n        filePath,\n        fileExists\n    ]);\n    // 处理创建文件\n    const handleCreateFile = ()=>{\n        if (onCreateFile) {\n            onCreateFile(filePath, content, operation);\n        }\n    };\n    // 操作类型的显示文本和样式\n    const operationConfig = {\n        append: {\n            label: \"追加\",\n            color: \"text-green-300\",\n            bgColor: \"bg-green-500/20\",\n            borderColor: \"border-green-500/30\"\n        },\n        replace: {\n            label: \"替换\",\n            color: \"text-orange-300\",\n            bgColor: \"bg-orange-500/20\",\n            borderColor: \"border-orange-500/30\"\n        }\n    };\n    // 默认配置，用于处理未知操作类型\n    const defaultConfig = {\n        label: \"修改\",\n        color: \"text-blue-300\",\n        bgColor: \"bg-blue-500/20\",\n        borderColor: \"border-blue-500/30\"\n    };\n    const config = operationConfig[operation] || defaultConfig;\n    // 处理跳转到编辑器\n    const handleJumpToEditor = ()=>{\n        if (onJumpToEditor) {\n            onJumpToEditor(filePath);\n        }\n    };\n    // 处理应用修改\n    const handleApplyChanges = ()=>{\n        if (onApplyChanges) {\n            onApplyChanges(filePath, content, operation, find);\n        }\n    };\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = ()=>{\n        if (onOpenDetailedDiff) {\n            onOpenDetailedDiff({\n                filePath,\n                operation,\n                content,\n                previewMode: true\n            });\n        }\n    };\n    // 计算内容预览（限制行数）\n    const getContentPreview = function(content) {\n        let maxLines = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3;\n        const lines = content.split(\"\\n\");\n        if (lines.length <= maxLines) {\n            return content;\n        }\n        return lines.slice(0, maxLines).join(\"\\n\") + \"\\n... (还有 \".concat(lines.length - maxLines, \" 行)\");\n    };\n    // 渲染差异预览（用于替换操作）\n    const renderDiffPreview = (content, findText)=>{\n        // 🔧 修复：正确处理 JSON 格式的 df 代码块\n        // find 和 content 是分离的参数，不是 pattern|||replacement 格式\n        if (!findText) {\n            // 兼容旧格式：pattern|||replacement\n            const parts = content.split(\"|||\");\n            if (parts.length >= 2) {\n                const [pattern, replacement] = parts;\n                return renderReplaceDiff(pattern, replacement);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-red-300\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-1\",\n                        children: \"⚠️ 替换操作缺少查找内容\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-red-400\",\n                        children: \"请确保 JSON 中包含 find 字段\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this);\n        }\n        // 🚀 新格式：使用分离的 find 和 content 参数\n        return renderReplaceDiff(findText, content);\n    };\n    // 渲染替换差异对比\n    const renderReplaceDiff = (pattern, replacement)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-300 font-handwritten\",\n                            children: \"搜索内容：\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-300 text-xs mt-1\",\n                                    children: \"-\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-200 bg-red-900/20 px-2 py-1 rounded text-xs font-mono flex-1 whitespace-pre-wrap\",\n                                    children: pattern\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-300 font-handwritten\",\n                            children: \"替换为：\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-300 text-xs mt-1\",\n                                    children: \"+\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-green-200 bg-green-900/20 px-2 py-1 rounded text-xs font-mono flex-1 whitespace-pre-wrap\",\n                                    children: replacement\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-blue-300/70 bg-blue-900/10 px-2 py-1 rounded border border-blue-500/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-3 h-3\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this),\n                            \"将使用智能匹配算法搜索并替换内容\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n            lineNumber: 166,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"df-compact-display bg-blue-500/10 border border-blue-500/30 rounded-lg p-3 my-2 not-prose \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between cursor-pointer hover:bg-blue-500/5 rounded-md p-1 -m-1 transition-all duration-200\",\n                onClick: ()=>setIsExpanded(!isExpanded),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-blue-400 flex-shrink-0\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten text-blue-200 truncate\",\n                                title: filePath,\n                                children: filePath\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            isCheckingFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs px-2 py-1 rounded bg-gray-500/20 text-gray-300 border border-gray-500/30 font-handwritten\",\n                                children: \"检查中...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this) : !actualFileExists ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs px-2 py-1 rounded bg-yellow-500/20 text-yellow-300 border border-yellow-500/30 font-handwritten\",\n                                children: \"文件不存在\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs px-2 py-1 rounded \".concat(config.bgColor, \" \").concat(config.color, \" \").concat(config.borderColor, \" border font-handwritten\"),\n                                children: config.label\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            stats && (stats.additions > 0 || stats.deletions > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 text-xs\",\n                                children: [\n                                    stats.additions > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-400\",\n                                        children: [\n                                            \"+\",\n                                            stats.additions\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, this),\n                                    stats.deletions > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-400\",\n                                        children: [\n                                            \"-\",\n                                            stats.deletions\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 17\n                                    }, this),\n                                    stats.modifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-orange-400\",\n                                        children: [\n                                            \"~\",\n                                            stats.modifications\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-blue-400 transition-transform duration-200 \".concat(isExpanded ? \"rotate-90\" : \"\"),\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 pt-3 border-t border-blue-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-blue-300 mb-2 font-handwritten\",\n                                children: operation === \"replace\" ? \"替换差异预览：\" : \"修改内容预览：\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this),\n                            operation === \"replace\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs bg-blue-900/20 p-3 rounded-md overflow-x-auto border border-blue-500/20\",\n                                children: renderDiffPreview(content, find)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-xs text-blue-100 bg-blue-900/20 p-3 rounded-md overflow-x-auto whitespace-pre-wrap border border-blue-500/20\",\n                                children: isExpanded ? content : getContentPreview(content)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: actualFileExists ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleJumpToEditor,\n                                    className: \"flex items-center gap-1 text-xs px-3 py-1.5 bg-blue-500/20 text-blue-200 border border-blue-500/30 rounded-md hover:bg-blue-500/30 transition-all duration-200 font-handwritten\",\n                                    title: \"在编辑器中查看文件\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3 h-3\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"跳转编辑器\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleApplyChanges,\n                                    className: \"flex items-center gap-1 text-xs px-3 py-1.5 bg-green-500/20 text-green-200 border border-green-500/30 rounded-md hover:bg-green-500/30 transition-all duration-200 font-handwritten\",\n                                    title: \"直接应用此修改到文件\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3 h-3\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"应用修改\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 17\n                                }, this),\n                                onOpenDetailedDiff && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleOpenDetailedDiff,\n                                    className: \"flex items-center gap-1 text-xs px-3 py-1.5 bg-purple-500/20 text-purple-200 border border-purple-500/30 rounded-md hover:bg-purple-500/30 transition-all duration-200 font-handwritten\",\n                                    title: \"打开详细差异对比视图\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3 h-3\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19M6.5,17.5H9.5V16H6.5V17.5M6.5,13H11.5V11.5H6.5V13M6.5,8.5H17.5V7H6.5V8.5Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"详细对比\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCreateFile,\n                                    className: \"flex items-center gap-1 text-xs px-3 py-1.5 bg-yellow-500/20 text-yellow-200 border border-yellow-500/30 rounded-md hover:bg-yellow-500/30 transition-all duration-200 font-handwritten\",\n                                    title: \"创建文件并应用内容\",\n                                    disabled: isCheckingFile,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3 h-3\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 19\n                                        }, this),\n                                        isCheckingFile ? \"检查中...\" : \"创建文件\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-yellow-300/70 px-2 py-1.5 font-handwritten\",\n                                    children: \"文件不存在，将自动创建文件夹结构\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this),\n                    stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 pt-2 border-t border-blue-500/10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-blue-300/70 font-handwritten\",\n                            children: [\n                                \"统计：共 \",\n                                stats.totalChanges,\n                                \" 处修改\",\n                                stats.additions > 0 && \" • \".concat(stats.additions, \" 行新增\"),\n                                stats.deletions > 0 && \" • \".concat(stats.deletions, \" 行删除\"),\n                                stats.modifications > 0 && \" • \".concat(stats.modifications, \" 行修改\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n                lineNumber: 265,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\CompactDiffDisplay.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s(CompactDiffDisplay, \"pnOkb+mgKc7dXaA9IT8D3e13S6k=\");\n_c = CompactDiffDisplay;\nvar _c;\n$RefreshReg$(_c, \"CompactDiffDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AIAssistant/CompactDiffDisplay.tsx\n"));

/***/ })

});