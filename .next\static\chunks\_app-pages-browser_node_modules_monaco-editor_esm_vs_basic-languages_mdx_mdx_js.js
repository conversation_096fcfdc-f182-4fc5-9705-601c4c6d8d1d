"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_mdx_mdx_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/mdx/mdx.js":
/*!**********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/mdx/mdx.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/editor.api.js?ce9f\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/basic-languages/mdx/mdx.ts\nvar conf = {\n  comments: {\n    blockComment: [\"{/*\", \"*/}\"]\n  },\n  brackets: [[\"{\", \"}\"]],\n  autoClosingPairs: [\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"\\u201C\", close: \"\\u201D\" },\n    { open: \"\\u2018\", close: \"\\u2019\" },\n    { open: \"`\", close: \"`\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"_\", close: \"_\" },\n    { open: \"**\", close: \"**\" },\n    { open: \"<\", close: \">\" }\n  ],\n  onEnterRules: [\n    {\n      beforeText: /^\\s*- .+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"- \" }\n    },\n    {\n      beforeText: /^\\s*\\+ .+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"+ \" }\n    },\n    {\n      beforeText: /^\\s*\\* .+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"* \" }\n    },\n    {\n      beforeText: /^> /,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: \"> \" }\n    },\n    {\n      beforeText: /<\\w+/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    },\n    {\n      beforeText: /\\s+>\\s*$/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Indent }\n    },\n    {\n      beforeText: /<\\/\\w+>/,\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.Outdent }\n    },\n    ...Array.from({ length: 100 }, (_, index) => ({\n      beforeText: new RegExp(`^${index}\\\\. .+`),\n      action: { indentAction: monaco_editor_core_exports.languages.IndentAction.None, appendText: `${index + 1}. ` }\n    }))\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".mdx\",\n  control: /[!#()*+.[\\\\\\]_`{}\\-]/,\n  escapes: /\\\\@control/,\n  tokenizer: {\n    root: [\n      [/^---$/, { token: \"meta.content\", next: \"@frontmatter\", nextEmbedded: \"yaml\" }],\n      [/^\\s*import/, { token: \"keyword\", next: \"@import\", nextEmbedded: \"js\" }],\n      [/^\\s*export/, { token: \"keyword\", next: \"@export\", nextEmbedded: \"js\" }],\n      [/<\\w+/, { token: \"type.identifier\", next: \"@jsx\" }],\n      [/<\\/?\\w+>/, \"type.identifier\"],\n      [\n        /^(\\s*)(>*\\s*)(#{1,6}\\s)/,\n        [{ token: \"white\" }, { token: \"comment\" }, { token: \"keyword\", next: \"@header\" }]\n      ],\n      [/^(\\s*)(>*\\s*)([*+-])(\\s+)/, [\"white\", \"comment\", \"keyword\", \"white\"]],\n      [/^(\\s*)(>*\\s*)(\\d{1,9}\\.)(\\s+)/, [\"white\", \"comment\", \"number\", \"white\"]],\n      [/^(\\s*)(>*\\s*)(\\d{1,9}\\.)(\\s+)/, [\"white\", \"comment\", \"number\", \"white\"]],\n      [/^(\\s*)(>*\\s*)(-{3,}|\\*{3,}|_{3,})$/, [\"white\", \"comment\", \"keyword\"]],\n      [/`{3,}(\\s.*)?$/, { token: \"string\", next: \"@codeblock_backtick\" }],\n      [/~{3,}(\\s.*)?$/, { token: \"string\", next: \"@codeblock_tilde\" }],\n      [\n        /`{3,}(\\S+).*$/,\n        { token: \"string\", next: \"@codeblock_highlight_backtick\", nextEmbedded: \"$1\" }\n      ],\n      [\n        /~{3,}(\\S+).*$/,\n        { token: \"string\", next: \"@codeblock_highlight_tilde\", nextEmbedded: \"$1\" }\n      ],\n      [/^(\\s*)(-{4,})$/, [\"white\", \"comment\"]],\n      [/^(\\s*)(>+)/, [\"white\", \"comment\"]],\n      { include: \"content\" }\n    ],\n    content: [\n      [\n        /(\\[)(.+)(]\\()(.+)(\\s+\".*\")(\\))/,\n        [\"\", \"string.link\", \"\", \"type.identifier\", \"string.link\", \"\"]\n      ],\n      [/(\\[)(.+)(]\\()(.+)(\\))/, [\"\", \"type.identifier\", \"\", \"string.link\", \"\"]],\n      [/(\\[)(.+)(]\\[)(.+)(])/, [\"\", \"type.identifier\", \"\", \"type.identifier\", \"\"]],\n      [/(\\[)(.+)(]:\\s+)(\\S*)/, [\"\", \"type.identifier\", \"\", \"string.link\"]],\n      [/(\\[)(.+)(])/, [\"\", \"type.identifier\", \"\"]],\n      [/`.*`/, \"variable.source\"],\n      [/_/, { token: \"emphasis\", next: \"@emphasis_underscore\" }],\n      [/\\*(?!\\*)/, { token: \"emphasis\", next: \"@emphasis_asterisk\" }],\n      [/\\*\\*/, { token: \"strong\", next: \"@strong\" }],\n      [/{/, { token: \"delimiter.bracket\", next: \"@expression\", nextEmbedded: \"js\" }]\n    ],\n    import: [[/'\\s*(;|$)/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }]],\n    expression: [\n      [/{/, { token: \"delimiter.bracket\", next: \"@expression\" }],\n      [/}/, { token: \"delimiter.bracket\", next: \"@pop\", nextEmbedded: \"@pop\" }]\n    ],\n    export: [[/^\\s*$/, { token: \"delimiter.bracket\", next: \"@pop\", nextEmbedded: \"@pop\" }]],\n    jsx: [\n      [/\\s+/, \"\"],\n      [/(\\w+)(=)(\"(?:[^\"\\\\]|\\\\.)*\")/, [\"attribute.name\", \"operator\", \"string\"]],\n      [/(\\w+)(=)('(?:[^'\\\\]|\\\\.)*')/, [\"attribute.name\", \"operator\", \"string\"]],\n      [/(\\w+(?=\\s|>|={|$))/, [\"attribute.name\"]],\n      [/={/, { token: \"delimiter.bracket\", next: \"@expression\", nextEmbedded: \"js\" }],\n      [/>/, { token: \"type.identifier\", next: \"@pop\" }]\n    ],\n    header: [\n      [/.$/, { token: \"keyword\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"keyword\" }]\n    ],\n    strong: [\n      [/\\*\\*/, { token: \"strong\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"strong\" }]\n    ],\n    emphasis_underscore: [\n      [/_/, { token: \"emphasis\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"emphasis\" }]\n    ],\n    emphasis_asterisk: [\n      [/\\*(?!\\*)/, { token: \"emphasis\", next: \"@pop\" }],\n      { include: \"content\" },\n      [/./, { token: \"emphasis\" }]\n    ],\n    frontmatter: [[/^---$/, { token: \"meta.content\", nextEmbedded: \"@pop\", next: \"@pop\" }]],\n    codeblock_highlight_backtick: [\n      [/\\s*`{3,}\\s*$/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    codeblock_highlight_tilde: [\n      [/\\s*~{3,}\\s*$/, { token: \"string\", next: \"@pop\", nextEmbedded: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    codeblock_backtick: [\n      [/\\s*`{3,}\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ],\n    codeblock_tilde: [\n      [/\\s*~{3,}\\s*$/, { token: \"string\", next: \"@pop\" }],\n      [/.*$/, \"variable.source\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/mdx/mdx.js\n"));

/***/ })

}]);