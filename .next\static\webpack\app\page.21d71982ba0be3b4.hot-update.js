"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/monacoConfig.ts":
/*!***********************************!*\
  !*** ./src/utils/monacoConfig.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   configureMonacoEnvironment: function() { return /* binding */ configureMonacoEnvironment; },\n/* harmony export */   configureMonacoLocal: function() { return /* binding */ configureMonacoLocal; },\n/* harmony export */   initializeMonaco: function() { return /* binding */ initializeMonaco; }\n/* harmony export */ });\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var monaco_editor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! monaco-editor */ \"(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/editor.main.js?741c\");\n\n\n/**\n * 配置Monaco Editor使用本地包而不是CDN\n * 解决网络连接问题导致的加载失败\n */ function configureMonacoLocal() {\n    // 配置Monaco Editor使用本地npm包\n    _monaco_editor_react__WEBPACK_IMPORTED_MODULE_0__.loader.config({\n        monaco: monaco_editor__WEBPACK_IMPORTED_MODULE_1__\n    });\n    console.log(\"✅ Monaco Editor已配置为使用本地包\");\n}\n/**\n * 配置Monaco Environment for Web Workers\n * 使用webpack插件生成的worker文件\n */ function configureMonacoEnvironment() {\n    if (true) {\n        // 设置Monaco Environment，使用webpack插件生成的worker文件\n        window.MonacoEnvironment = {\n            getWorker (_, label) {\n                // 使用webpack插件生成的worker文件路径\n                switch(label){\n                    case \"json\":\n                        return new Worker(\"/_next/static/chunks/json.worker.js\");\n                    case \"css\":\n                    case \"scss\":\n                    case \"less\":\n                        return new Worker(\"/_next/static/chunks/css.worker.js\");\n                    case \"html\":\n                    case \"handlebars\":\n                    case \"razor\":\n                        return new Worker(\"/_next/static/chunks/html.worker.js\");\n                    case \"typescript\":\n                    case \"javascript\":\n                        return new Worker(\"/_next/static/chunks/ts.worker.js\");\n                    default:\n                        return new Worker(\"/_next/static/chunks/editor.worker.js\");\n                }\n            }\n        };\n        console.log(\"✅ Monaco Environment已配置\");\n    }\n}\n/**\n * 初始化Monaco Editor配置\n * 在应用启动时调用\n */ function initializeMonaco() {\n    configureMonacoEnvironment();\n    configureMonacoLocal();\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/monacoConfig.ts\n"));

/***/ })

});