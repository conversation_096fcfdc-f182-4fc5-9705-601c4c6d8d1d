"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/AIAssistant/ChatInterface.tsx":
/*!******************************************************!*\
  !*** ./src/components/AIAssistant/ChatInterface.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chatHistoryService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chatHistoryService */ \"(app-pages-browser)/./src/services/chatHistoryService.ts\");\n/* harmony import */ var _services_fileTreeService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\");\n/* harmony import */ var _services_diffToolService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/diffToolService */ \"(app-pages-browser)/./src/services/diffToolService.ts\");\n/* harmony import */ var _services_aiService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/aiService */ \"(app-pages-browser)/./src/services/aiService.ts\");\n/* harmony import */ var _utils_pathUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/pathUtils */ \"(app-pages-browser)/./src/utils/pathUtils.ts\");\n/* harmony import */ var _utils_SelectionManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/SelectionManager */ \"(app-pages-browser)/./src/utils/SelectionManager.ts\");\n/* harmony import */ var _SessionManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./SessionManager */ \"(app-pages-browser)/./src/components/AIAssistant/SessionManager.tsx\");\n/* harmony import */ var _FileAssociationTreeDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./FileAssociationTreeDialog */ \"(app-pages-browser)/./src/components/AIAssistant/FileAssociationTreeDialog.tsx\");\n/* harmony import */ var _CompactDiffDisplay__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./CompactDiffDisplay */ \"(app-pages-browser)/./src/components/AIAssistant/CompactDiffDisplay.tsx\");\n/* harmony import */ var _MediaUploader__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./MediaUploader */ \"(app-pages-browser)/./src/components/AIAssistant/MediaUploader.tsx\");\n/* harmony import */ var _HelperResponseLayer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./HelperResponseLayer */ \"(app-pages-browser)/./src/components/AIAssistant/HelperResponseLayer.tsx\");\n/* harmony import */ var _PromptManagerModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./PromptManagerModal */ \"(app-pages-browser)/./src/components/AIAssistant/PromptManagerModal.tsx\");\n/* harmony import */ var _MultipleComparisonModal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./MultipleComparisonModal */ \"(app-pages-browser)/./src/components/AIAssistant/MultipleComparisonModal.tsx\");\n/* harmony import */ var _utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/humanBlockParser */ \"(app-pages-browser)/./src/utils/humanBlockParser.ts\");\n/* harmony import */ var _services_promptConfigService__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/services/promptConfigService */ \"(app-pages-browser)/./src/services/promptConfigService.ts\");\n/**\r\n * AI对话界面组件\r\n * 传统的对话形式界面，用于与AI进行实时对话\r\n * 优化版本：解决文本选择被频繁重渲染取消的问题\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n // Renamed to avoid conflict\n\n\n\n\n\n\n\n\n\n\nconst ChatInterface = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c = _s(function ChatInterface(param, ref) {\n    let { currentConfig, onSendMessage, responseContent, isStreaming, isComplete, error, onRetry, onCopy, onStop, onInsertToEditor, onContentInsert, onMessageContentInsert, isLoading, className = \"\", currentPersona, onChatHistoryChange, // 文件关联相关props\n    associatedFiles = [], onFilesChange = ()=>{}, onShowFileAssociation = ()=>{}, // 🔧 AI响应状态清理回调\n    onClearAIResponse, // 🔧 作品ID，用于文件关联\n    artworkId, // 🔧 文件选择回调，用于跳转到编辑器\n    onFileSelect, // 🔧 详细对比回调，用于打开详细差异对比视图\n    onOpenDetailedDiff, // 🔧 聚焦文件状态，由父组件管理\n    focusedFile, // 🔧 媒体文件变化回调\n    onMediaFilesChange, // 🔧 受众设置回调\n    onShowAudienceSettings, // 🔧 会话管理回调\n    onShowSessionManager, // 🔧 多项对比消息构建回调\n    onMultipleComparisonSend } = param;\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [chatHistory, setChatHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSessionManager, setShowSessionManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFileAssociation, setShowFileAssociation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPromptManager, setShowPromptManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activePromptConfig, setActivePromptConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isFileAssociationCollapsed, setIsFileAssociationCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filePaths, setFilePaths] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [showToolsPanel, setShowToolsPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 消息工具面板状态 - 记录哪条消息的工具面板正在显示\n    const [activeMessageTools, setActiveMessageTools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 编辑消息状态\n    const [editingMessageId, setEditingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingContent, setEditingContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 多项对比弹窗状态\n    const [showMultipleComparison, setShowMultipleComparison] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [comparisonMessageContent, setComparisonMessageContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [comparisonTriggerMessageIndex, setComparisonTriggerMessageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    // 共享状态\n    const [availableModels, setAvailableModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingModels, setIsLoadingModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 单模型生成相关状态\n    const [selectedSingleModel, setSelectedSingleModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [singleModelResults, setSingleModelResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [streamingSingleResults, setStreamingSingleResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [generationCount, setGenerationCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3);\n    // 多模型对比相关状态\n    const [selectedMultiModels, setSelectedMultiModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [multiModelResults, setMultiModelResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [streamingMultiModels, setStreamingMultiModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // 🔧 聚焦文件状态现在由父组件管理，通过props传递\n    // 媒体上传相关状态\n    const [uploadedMedia, setUploadedMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showMediaUploader, setShowMediaUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMediaList, setShowMediaList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 消息层状态管理\n    const [messageLayers, setMessageLayers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pendingIntegratedMessage, setPendingIntegratedMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [processedMessages, setProcessedMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // 🔧 调试监听已移除，避免控制台日志过多\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const aiResponseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 🔧 响应内容引用，避免useEffect依赖responseContent导致重复渲染\n    const responseContentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    // 🔧 文本选择管理器\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 初始化选择管理器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!selectionManagerRef.current) {\n            selectionManagerRef.current = new _utils_SelectionManager__WEBPACK_IMPORTED_MODULE_7__.SelectionManager();\n        }\n        return ()=>{\n            if (selectionManagerRef.current && aiResponseRef.current) {\n                selectionManagerRef.current.stopListening(aiResponseRef.current);\n            }\n        };\n    }, []);\n    // 点击外部关闭工具面板\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (showToolsPanel) {\n                const target = event.target;\n                if (!target.closest(\".tools-panel-container\")) {\n                    setShowToolsPanel(false);\n                }\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        showToolsPanel\n    ]);\n    // 暂时禁用选择监听\n    // useEffect(() => {\n    //   if (selectionManagerRef.current && aiResponseRef.current) {\n    //     selectionManagerRef.current.startListening(aiResponseRef.current)\n    //   }\n    //   return () => {\n    //     if (selectionManagerRef.current && aiResponseRef.current) {\n    //       selectionManagerRef.current.stopListening(aiResponseRef.current)\n    //     }\n    //   }\n    // }, [])\n    // 🔧 同步responseContent到ref，避免useEffect直接依赖responseContent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        responseContentRef.current = responseContent;\n    }, [\n        responseContent\n    ]);\n    // 🔧 修复重复渲染问题：使用useMemo确保chatHistoryService引用稳定\n    const chatHistoryService = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>_services_chatHistoryService__WEBPACK_IMPORTED_MODULE_2__.ChatHistoryService.getInstance(), []);\n    // 辅助函数：标准化路径\n    const normalizePath = (path)=>{\n        if (!path) return \"\";\n        // 使用导入的工具函数，避免命名冲突\n        return (0,_utils_pathUtils__WEBPACK_IMPORTED_MODULE_6__.normalizePath)(path).toLowerCase();\n    };\n    // 辅助函数：根据路径查找文件ID\n    const findFileIdByPath = (rootNode, targetPath)=>{\n        // 标准化目标路径，确保相对路径和绝对路径都能正确匹配\n        const normalizedTargetPath = normalizePath(targetPath);\n        const searchNode = (node)=>{\n            // 标准化当前节点路径进行比较\n            if (normalizePath(node.path) === normalizedTargetPath) {\n                return node.id;\n            }\n            if (node.children) {\n                for (const child of node.children){\n                    const foundId = searchNode(child);\n                    if (foundId) {\n                        return foundId;\n                    }\n                }\n            }\n            return null;\n        };\n        return searchNode(rootNode);\n    };\n    // 辅助函数：解析替换内容（仅支持JSON格式）\n    const parseReplaceContent = (content, find)=>{\n        // JSON格式：直接使用find和content参数\n        if (find) {\n            console.log(\"✅ 使用JSON格式解析:\", {\n                find,\n                content: content.substring(0, 100) + \"...\"\n            });\n            return [\n                find,\n                content\n            ];\n        }\n        // 如果没有find参数，说明格式不正确\n        console.error(\"❌ 替换操作缺少查找内容\");\n        console.error('\\uD83D\\uDCDD 请使用JSON格式：{\"file\": \"...\", \"type\": \"replace\", \"find\": \"查找内容\", \"content\": \"替换内容\"}');\n        console.error(\"\\uD83D\\uDCDD 实际收到的内容:\", content.substring(0, 200));\n        // 返回错误状态，让调用方处理\n        return [\n            \"\",\n            content\n        ];\n    };\n    // 辅助函数：获取所有文件路径（用于调试）\n    const getAllFilePaths = (rootNode)=>{\n        const paths = [];\n        const collectPaths = (node)=>{\n            if (node.type === \"file\") {\n                paths.push(node.path);\n            }\n            if (node.children) {\n                node.children.forEach(collectPaths);\n            }\n        };\n        collectPaths(rootNode);\n        return paths;\n    };\n    // 辅助函数：根据文件ID查找文件完整路径\n    const findFilePathById = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (fileId)=>{\n        try {\n            if (!artworkId) return \"未知文件 (\".concat(fileId.substring(0, 8), \"...)\");\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_3__.FileTreeService.getInstance();\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (fileTreeResult.success && fileTreeResult.data) {\n                const filePath = getFilePath(fileTreeResult.data, fileId);\n                return filePath || \"未知文件 (\".concat(fileId.substring(0, 8), \"...)\");\n            }\n            return \"未知文件 (\".concat(fileId.substring(0, 8), \"...)\");\n        } catch (error) {\n            console.warn(\"获取文件路径失败:\", error);\n            return \"未知文件 (\".concat(fileId.substring(0, 8), \"...)\");\n        }\n    }, [\n        artworkId\n    ]);\n    // 辅助函数：获取文件的完整路径（不包括作品根节点）\n    const getFilePath = (rootNode, fileId)=>{\n        // 递归搜索文件树，构建路径\n        const buildPath = function(node, targetId) {\n            let currentPath = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n            // 如果找到目标节点，返回当前路径\n            if (node.id === targetId) {\n                return [\n                    ...currentPath,\n                    node.name\n                ];\n            }\n            // 如果有子节点，递归搜索\n            if (node.children) {\n                for (const child of node.children){\n                    const path = buildPath(child, targetId, [\n                        ...currentPath,\n                        node.name\n                    ]);\n                    if (path) {\n                        return path;\n                    }\n                }\n            }\n            return null;\n        };\n        // 从根节点开始搜索，但不包括根节点名称\n        const path = buildPath(rootNode, fileId, []);\n        if (path) {\n            // 移除第一个元素（根节点名称）\n            path.shift();\n            return path.join(\"/\");\n        }\n        return fileId.substring(0, 8) + \"...\"; // 如果找不到路径，返回ID的简短版本\n    };\n    // 递归搜索文件树中的文件名（保留用于兼容性）\n    const searchNodeForName = (node, fileId)=>{\n        if (node.id === fileId) {\n            return node.name;\n        }\n        if (node.children) {\n            for (const child of node.children){\n                const result = searchNodeForName(child, fileId);\n                if (result) return result;\n            }\n        }\n        return null;\n    };\n    // 🔧 聚焦文件状态现在由父组件统一管理，移除子组件中的事件监听\n    // 组件初始化时加载历史记录和当前会话ID\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeSession = async ()=>{\n            try {\n                // 获取当前会话ID\n                const sessionId = chatHistoryService.getCurrentSessionId();\n                setCurrentSessionId(sessionId);\n                // 加载当前会话的历史记录\n                const storedHistory = await chatHistoryService.getCurrentHistory();\n                if (storedHistory.length > 0) {\n                    setChatHistory(storedHistory);\n                    console.log(\"✅ 已从IndexedDB加载对话历史:\", storedHistory.length, \"条消息\");\n                    // 通知父组件对话历史变化\n                    if (onChatHistoryChange) {\n                        onChatHistoryChange(storedHistory);\n                    }\n                }\n            // 文件关联数据由父组件管理，这里不需要重复加载\n            } catch (error) {\n                console.error(\"❌ 加载对话历史失败:\", error);\n            }\n        };\n        initializeSession();\n    }, [\n        chatHistoryService,\n        onChatHistoryChange\n    ]);\n    // 加载激活的提示词配置\n    const loadActivePromptConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const promptConfigService = _services_promptConfigService__WEBPACK_IMPORTED_MODULE_16__.PromptConfigService.getInstance();\n            const result = await promptConfigService.getActiveConfig();\n            if (result.success && result.data) {\n                setActivePromptConfig(result.data);\n            } else {\n                setActivePromptConfig(null);\n            }\n        } catch (error) {\n            console.error(\"加载激活提示词配置失败:\", error);\n            setActivePromptConfig(null);\n        }\n    }, []);\n    // 组件挂载时加载激活配置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadActivePromptConfig();\n    }, [\n        loadActivePromptConfig\n    ]);\n    // 处理提示词配置选择\n    const handlePromptConfigSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setActivePromptConfig(config);\n        console.log(\"\\uD83D\\uDD2E 选择提示词配置:\", config.name);\n        // 重新加载激活配置以确保状态同步\n        setTimeout(()=>{\n            loadActivePromptConfig();\n        }, 100);\n    }, [\n        loadActivePromptConfig\n    ]);\n    // 生成唯一消息ID\n    const generateMessageId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((type)=>{\n        const timestamp = Date.now();\n        const random = Math.random().toString(36).substring(2, 11);\n        return \"\".concat(type, \"-\").concat(timestamp, \"-\").concat(random);\n    }, []);\n    // 添加用户消息到历史记录\n    const addUserMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message)=>{\n        setChatHistory((prev)=>{\n            // 检查消息是否已存在，避免重复添加\n            const exists = prev.some((m)=>m.id === message.id);\n            if (exists) {\n                console.log(\"⚠️ 消息已存在，跳过添加:\", message.content.substring(0, 20));\n                return prev;\n            }\n            const newHistory = [\n                ...prev,\n                message\n            ];\n            console.log(\"✅ 用户消息已添加:\", message.content.substring(0, 20), \"历史长度:\", newHistory.length);\n            // 异步保存到IndexedDB\n            chatHistoryService.saveCurrentHistory(newHistory).catch((error)=>{\n                console.error(\"❌ 保存用户消息失败:\", error);\n            });\n            // 异步通知父组件对话历史变化，避免在渲染过程中同步更新\n            setTimeout(()=>{\n                if (onChatHistoryChange) {\n                    onChatHistoryChange(newHistory);\n                }\n            }, 0);\n            return newHistory;\n        });\n    }, [\n        onChatHistoryChange,\n        chatHistoryService\n    ]);\n    // 处理跳转到编辑器\n    const handleJumpToEditor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filePath)=>{\n        try {\n            if (!artworkId) return;\n            // 通过文件路径查找文件ID\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_3__.FileTreeService.getInstance();\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (fileTreeResult.success && fileTreeResult.data) {\n                const fileId = findFileIdByPath(fileTreeResult.data, filePath);\n                if (fileId) {\n                    // 触发文件选择事件，通知父组件切换到编辑器\n                    onFileSelect === null || onFileSelect === void 0 ? void 0 : onFileSelect(fileId);\n                    // 可选：设置聚焦状态\n                    const chatHistoryService = _services_chatHistoryService__WEBPACK_IMPORTED_MODULE_2__.ChatHistoryService.getInstance();\n                    await chatHistoryService.setCurrentFocusedFile(fileId);\n                    console.log(\"\\uD83D\\uDD17 跳转到编辑器:\", filePath);\n                } else {\n                    console.warn(\"⚠️ 未找到文件:\", filePath);\n                }\n            }\n        } catch (error) {\n            console.error(\"❌ 跳转到编辑器失败:\", error);\n        }\n    }, [\n        artworkId,\n        onFileSelect\n    ]);\n    // 处理应用diff修改\n    const handleApplyDiff = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filePath, content, operation, find)=>{\n        try {\n            if (!artworkId) return;\n            // 标准化文件路径，确保相对路径和绝对路径都能正确处理\n            const normalizedPath = normalizePath(filePath);\n            console.log(\"\\uD83D\\uDD0D 查找文件:\", {\n                original: filePath,\n                normalized: normalizedPath\n            });\n            const diffToolService = _services_diffToolService__WEBPACK_IMPORTED_MODULE_4__.DiffToolService.getInstance();\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_3__.FileTreeService.getInstance();\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (!fileTreeResult.success || !fileTreeResult.data) {\n                console.error(\"❌ 获取文件树失败\");\n                return;\n            }\n            const fileId = findFileIdByPath(fileTreeResult.data, normalizedPath);\n            if (!fileId) {\n                // 增强错误日志，提供详细的路径匹配调试信息\n                const availablePaths = getAllFilePaths(fileTreeResult.data);\n                console.warn(\"⚠️ 未找到文件:\", {\n                    path: normalizedPath,\n                    originalPath: filePath,\n                    availableFiles: availablePaths.slice(0, 10),\n                    totalFiles: availablePaths.length\n                });\n                return;\n            }\n            let result;\n            if (operation === \"append\") {\n                result = await diffToolService.appendText(fileId, {\n                    content\n                });\n            } else if (operation === \"replace\") {\n                // 解析替换参数\n                const [pattern, replacement] = parseReplaceContent(content, find);\n                if (!pattern) {\n                    console.error(\"❌ 替换操作缺少查找内容\");\n                    return;\n                }\n                // 🚀 使用新的智能字符串替换功能\n                result = await diffToolService.intelligentStringReplace(fileId, pattern, replacement);\n                if (result.success) {\n                    console.log(\"✅ 智能字符串替换成功\");\n                    // 应用差异修改\n                    if (result.data) {\n                        await diffToolService.applyDiff(result.data);\n                    }\n                } else {\n                    console.error(\"❌ 智能字符串替换失败:\", result.error);\n                    // 显示详细错误信息（包含建议）\n                    if (result.error) {\n                        // 这里可以添加用户友好的错误提示UI\n                        console.log(\"\\uD83D\\uDCA1 替换失败详情:\\n\", result.error);\n                    }\n                    return;\n                }\n            }\n            if (result === null || result === void 0 ? void 0 : result.success) {\n                // 对于append操作，需要应用diff；对于replace操作，智能替换已经处理了应用\n                if (operation === \"append\" && result.data) {\n                    await diffToolService.applyDiff(result.data);\n                }\n                console.log(\"✅ 文件修改已应用:\", filePath);\n                // 🔧 延迟通知编辑器刷新文件内容，确保文件写入完成\n                if (onFileSelect && fileId) {\n                    setTimeout(()=>{\n                        onFileSelect(fileId);\n                        console.log(\"✅ 已通知编辑器刷新文件:\", filePath);\n                    }, 200) // 200ms延迟确保文件系统完成写入\n                    ;\n                }\n            // 可选：显示成功提示\n            } else {\n                console.error(\"❌ 应用修改失败:\", result === null || result === void 0 ? void 0 : result.error);\n            }\n        } catch (error) {\n            console.error(\"❌ 应用差异修改失败:\", error);\n        }\n    }, [\n        artworkId,\n        onFileSelect\n    ]);\n    // 处理媒体文件上传成功\n    const handleMediaUploadSuccess = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((mediaFile)=>{\n        setUploadedMedia((prev)=>{\n            const newMediaFiles = [\n                ...prev,\n                mediaFile\n            ];\n            // 通知父组件媒体文件变化\n            if (onMediaFilesChange) {\n                onMediaFilesChange(newMediaFiles);\n            }\n            return newMediaFiles;\n        });\n        console.log(\"✅ 媒体文件上传成功:\", mediaFile.filename);\n    }, [\n        onMediaFilesChange\n    ]);\n    // 处理媒体文件上传错误\n    const handleMediaUploadError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((error)=>{\n        console.error(\"❌ 媒体文件上传失败:\", error);\n    // 可以在这里添加错误提示UI\n    }, []);\n    // 移除已上传的媒体文件\n    const removeMediaFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((fileId)=>{\n        setUploadedMedia((prev)=>{\n            const newMediaFiles = prev.filter((file)=>file.id !== fileId);\n            // 通知父组件媒体文件变化\n            if (onMediaFilesChange) {\n                onMediaFilesChange(newMediaFiles);\n            }\n            return newMediaFiles;\n        });\n    }, [\n        onMediaFilesChange\n    ]);\n    // 处理创建文件\n    const handleCreateFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filePath, content, operation)=>{\n        try {\n            if (!artworkId) {\n                console.error(\"❌ 缺少作品ID\");\n                return;\n            }\n            // 标准化文件路径，确保相对路径和绝对路径都能正确处理\n            const normalizedPath = normalizePath(filePath);\n            console.log(\"\\uD83D\\uDD0D 创建文件:\", {\n                original: filePath,\n                normalized: normalizedPath\n            });\n            const diffToolService = _services_diffToolService__WEBPACK_IMPORTED_MODULE_4__.DiffToolService.getInstance();\n            // 使用新的createFileWithPath方法，传入标准化后的路径\n            const result = await diffToolService.createFileWithPath(artworkId, normalizedPath, content, operation);\n            if (result.success) {\n                // 应用创建结果\n                await diffToolService.applyDiff(result.data);\n                console.log(\"✅ 文件创建并应用成功:\", filePath);\n                // 文件树会通过事件系统自动刷新，不再需要手动通知\n                console.log(\"✅ 文件创建完成，文件树将自动刷新\");\n                // 可选：跳转到新创建的文件\n                if (result.data.fileId) {\n                    handleJumpToEditor(filePath);\n                }\n            } else {\n                // 增强错误日志，提供详细的创建失败调试信息\n                console.error(\"❌ 文件创建失败:\", {\n                    path: normalizedPath,\n                    originalPath: filePath,\n                    error: result.error,\n                    operation: operation\n                });\n            }\n        } catch (error) {\n            console.error(\"❌ 创建文件失败:\", error);\n        }\n    }, [\n        artworkId,\n        handleJumpToEditor\n    ]);\n    // 🔧 渲染内容缓存，避免重复渲染相同内容\n    const renderedContentCache = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    // 🔧 简化消息渲染函数 - 添加缓存机制\n    const renderMessageContentInternal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((content)=>{\n        // 检查缓存\n        if (renderedContentCache.current.has(content)) {\n            return renderedContentCache.current.get(content);\n        }\n        // 第一步：先识别df代码块，避免被普通代码块处理干扰\n        const dfRegex = /```df\\s*\\n([\\s\\S]*?)```/g;\n        const diffRegex = /<diff>([\\s\\S]*?)<\\/diff>/g;\n        // 添加human代码块正则表达式，用于BrainstormingHelper功能\n        const humanRegex = /```human\\s*\\n\\s*neme:\\s*([^\\n]+)\\s*\\n\\s*currentPersona\\.description:\\s*([^\\n]+)\\s*\\n\\s*问题：\\s*([\\s\\S]*?)```/g;\n        // 第二步：提取普通代码块，但排除df代码块\n        const codeBlockRegex = /```(?!df\\s)(\\w+)?\\n([\\s\\S]*?)```/g;\n        const codeBlocks = [];\n        let processedContent = content;\n        let codeBlockMatch;\n        // 提取普通代码块并替换为占位符（排除df代码块）\n        while((codeBlockMatch = codeBlockRegex.exec(content)) !== null){\n            const placeholder = \"__CODE_BLOCK_\".concat(codeBlocks.length, \"__\");\n            codeBlocks.push({\n                placeholder,\n                content: codeBlockMatch[2],\n                lang: codeBlockMatch[1]\n            });\n            processedContent = processedContent.replace(codeBlockMatch[0], placeholder);\n        }\n        // 第三步：在处理后的内容中识别特殊标记（包括df代码块）\n        const parts = [];\n        let lastIndex = 0;\n        let match;\n        // 创建一个包含所有匹配项的数组，按位置排序\n        const allMatches = [];\n        // 收集diff匹配项\n        diffRegex.lastIndex = 0;\n        while((match = diffRegex.exec(processedContent)) !== null){\n            allMatches.push({\n                type: \"diff\",\n                index: match.index,\n                length: match[0].length,\n                content: match[1]\n            });\n        }\n        // 收集df匹配项（仅支持JSON格式）\n        dfRegex.lastIndex = 0;\n        while((match = dfRegex.exec(content)) !== null){\n            const dfContent = match[1].trim();\n            try {\n                // 只解析JSON格式\n                if (dfContent.startsWith(\"{\") && dfContent.endsWith(\"}\")) {\n                    const parsedDf = JSON.parse(dfContent);\n                    // 验证必需字段\n                    if (!parsedDf.file || !parsedDf.type) {\n                        throw new Error(\"缺少必需字段：file 和 type\");\n                    }\n                    // 验证操作类型\n                    if (![\n                        \"replace\",\n                        \"append\",\n                        \"create\"\n                    ].includes(parsedDf.type)) {\n                        throw new Error(\"不支持的操作类型: \".concat(parsedDf.type));\n                    }\n                    // 对于replace操作，验证find字段\n                    if (parsedDf.type === \"replace\" && !parsedDf.find) {\n                        throw new Error(\"replace操作需要find字段\");\n                    }\n                    allMatches.push({\n                        type: \"df\",\n                        index: match.index,\n                        length: match[0].length,\n                        filePath: parsedDf.file,\n                        operation: parsedDf.type,\n                        content: parsedDf.content || \"\",\n                        find: parsedDf.find || \"\"\n                    });\n                } else {\n                    throw new Error(\"df代码块必须使用JSON格式\");\n                }\n            } catch (error) {\n                console.error(\"❌ 解析df代码块失败:\", error);\n                console.error(\"\\uD83D\\uDCDD 正确格式示例:\");\n                console.error(\"```df\");\n                console.error(\"{\");\n                console.error('  \"file\": \"path/to/file\",');\n                console.error('  \"type\": \"replace\",');\n                console.error('  \"find\": \"要查找的内容\",');\n                console.error('  \"content\": \"替换内容\"');\n                console.error(\"}\");\n                console.error(\"```\");\n                console.error(\"\\uD83D\\uDCDD 实际内容:\", dfContent);\n                // 添加错误项，用于在UI中显示错误信息\n                allMatches.push({\n                    type: \"df\",\n                    index: match.index,\n                    length: match[0].length,\n                    filePath: \"格式错误\",\n                    operation: \"error\",\n                    content: dfContent,\n                    error: error instanceof Error ? error.message : String(error)\n                });\n            }\n        }\n        // 收集human匹配项（用于BrainstormingHelper功能）\n        humanRegex.lastIndex = 0;\n        while((match = humanRegex.exec(content)) !== null){\n            allMatches.push({\n                type: \"human\",\n                index: match.index,\n                length: match[0].length,\n                roleName: match[1].trim(),\n                roleDescription: match[2].trim(),\n                question: match[3].trim()\n            });\n        }\n        // 按位置排序所有匹配项\n        allMatches.sort((a, b)=>a.index - b.index);\n        // 第三步：处理所有匹配项和普通内容\n        for (const matchItem of allMatches){\n            // 添加匹配项前的普通内容\n            if (matchItem.index > lastIndex) {\n                const beforeContent = processedContent.substring(lastIndex, matchItem.index);\n                if (beforeContent.trim()) {\n                    parts.push({\n                        type: \"markdown\",\n                        content: beforeContent\n                    });\n                }\n            }\n            // 添加匹配项内容\n            parts.push(matchItem);\n            lastIndex = matchItem.index + matchItem.length;\n        }\n        // 添加最后剩余的普通内容\n        if (lastIndex < processedContent.length) {\n            const afterContent = processedContent.substring(lastIndex);\n            if (afterContent.trim()) {\n                parts.push({\n                    type: \"markdown\",\n                    content: afterContent\n                });\n            }\n        }\n        // 如果没有特殊内容，直接渲染markdown（恢复代码块）\n        if (parts.length === 0) {\n            const finalContent = restoreCodeBlocks(processedContent, codeBlocks);\n            const result = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"prose prose-invert max-w-none\",\n                dangerouslySetInnerHTML: {\n                    __html: renderMarkdown(finalContent)\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 830,\n                columnNumber: 9\n            }, this);\n            // 缓存结果\n            renderedContentCache.current.set(content, result);\n            // 限制缓存大小，避免内存泄漏\n            if (renderedContentCache.current.size > 50) {\n                const firstKey = renderedContentCache.current.keys().next().value;\n                renderedContentCache.current.delete(firstKey);\n            }\n            return result;\n        }\n        // 第四步：渲染混合内容（恢复代码块）\n        const result = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: parts.map((part, index)=>{\n                if (part.type === \"df\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CompactDiffDisplay__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        filePath: part.filePath,\n                        operation: part.operation,\n                        content: part.content,\n                        find: part.find,\n                        onJumpToEditor: handleJumpToEditor,\n                        onApplyChanges: handleApplyDiff,\n                        onCreateFile: handleCreateFile,\n                        onOpenDetailedDiff: onOpenDetailedDiff,\n                        artworkId: artworkId\n                    }, index, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 854,\n                        columnNumber: 15\n                    }, this);\n                } else if (part.type === \"human\") {\n                    // human代码块现在通过handleSendMessage统一处理，这里只显示原始内容\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-purple-900/20 border border-purple-500/30 rounded-lg p-4 my-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-purple-300 text-sm font-handwritten mb-2\",\n                                children: \"\\uD83E\\uDD16 辅助AI协同思考请求\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-300\",\n                                                children: \"角色：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 875,\n                                                columnNumber: 24\n                                            }, this),\n                                            part.roleName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 875,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-300\",\n                                                children: \"描述：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 24\n                                            }, this),\n                                            part.roleDescription\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-300\",\n                                                children: \"问题：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 24\n                                            }, this),\n                                            part.question\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 874,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400 mt-2\",\n                                children: \"ℹ️ 此请求将通过消息发送时自动处理\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 879,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 870,\n                        columnNumber: 15\n                    }, this);\n                } else {\n                    // 恢复markdown内容中的代码块\n                    const restoredContent = restoreCodeBlocks(part.content, codeBlocks);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose prose-invert max-w-none\",\n                        dangerouslySetInnerHTML: {\n                            __html: renderMarkdown(restoredContent)\n                        }\n                    }, index, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 888,\n                        columnNumber: 15\n                    }, this);\n                }\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n            lineNumber: 850,\n            columnNumber: 7\n        }, this);\n        // 缓存结果\n        renderedContentCache.current.set(content, result);\n        // 限制缓存大小，避免内存泄漏\n        if (renderedContentCache.current.size > 50) {\n            const firstKey = renderedContentCache.current.keys().next().value;\n            renderedContentCache.current.delete(firstKey);\n        }\n        return result;\n    }, [\n        handleJumpToEditor,\n        handleApplyDiff,\n        handleCreateFile,\n        onOpenDetailedDiff,\n        artworkId\n    ]);\n    // 辅助函数：恢复代码块\n    const restoreCodeBlocks = (content, codeBlocks)=>{\n        let restoredContent = content;\n        codeBlocks.forEach((block)=>{\n            const codeBlockHtml = \"```\".concat(block.lang || \"\", \"\\n\").concat(block.content, \"```\");\n            restoredContent = restoredContent.replace(block.placeholder, codeBlockHtml);\n        });\n        return restoredContent;\n    };\n    // Markdown渲染函数（简化版，完全移除diff处理）\n    const renderMarkdown = (text)=>{\n        // 代码块处理\n        const codeBlockRegex = /```(\\w+)?\\n([\\s\\S]*?)```/g;\n        const inlineCodeRegex = /`([^`]+)`/g;\n        // 标题处理\n        const headingRegex = /^(#{1,6})\\s+(.+)$/gm;\n        // 粗体和斜体\n        const boldRegex = /\\*\\*(.*?)\\*\\*/g;\n        const italicRegex = /\\*(.*?)\\*/g;\n        // 链接处理\n        const linkRegex = /\\[([^\\]]+)\\]\\(([^)]+)\\)/g;\n        return text// 代码块\n        .replace(codeBlockRegex, (match, lang, code)=>{\n            return '<div class=\"code-block bg-gray-800 rounded-lg p-3 my-2 overflow-x-auto\">\\n          '.concat(lang ? '<div class=\"text-xs text-gray-400 mb-2\">'.concat(lang, \"</div>\") : \"\", '\\n          <pre class=\"text-sm text-green-300 font-mono whitespace-pre-wrap\">').concat(code.trim(), \"</pre>\\n        </div>\");\n        })// 行内代码\n        .replace(inlineCodeRegex, '<code class=\"bg-gray-800 text-green-300 px-1 py-0.5 rounded text-sm font-mono\">$1</code>')// 标题\n        .replace(headingRegex, (match, hashes, title)=>{\n            const level = hashes.length;\n            const className = level === 1 ? \"text-lg font-bold text-amber-200 mt-4 mb-2\" : level === 2 ? \"text-md font-bold text-amber-200 mt-3 mb-2\" : \"text-sm font-bold text-amber-200 mt-2 mb-1\";\n            return \"<h\".concat(level, ' class=\"').concat(className, '\">').concat(title, \"</h\").concat(level, \">\");\n        })// 粗体\n        .replace(boldRegex, '<strong class=\"font-bold text-amber-100\">$1</strong>')// 斜体\n        .replace(italicRegex, '<em class=\"italic text-amber-100\">$1</em>')// 链接\n        .replace(linkRegex, '<a href=\"$2\" class=\"text-blue-400 hover:text-blue-300 underline\" target=\"_blank\" rel=\"noopener noreferrer\">$1</a>')// 换行\n        .replace(/\\n/g, \"<br>\");\n    };\n    // 自动调整输入框高度\n    const adjustTextareaHeight = ()=>{\n        if (textareaRef.current) {\n            textareaRef.current.style.height = \"auto\";\n            textareaRef.current.style.height = \"\".concat(Math.min(textareaRef.current.scrollHeight, 120), \"px\");\n        }\n    };\n    // 处理输入变化\n    const handleInputChange = (e)=>{\n        setInputMessage(e.target.value);\n        adjustTextareaHeight();\n    };\n    // 发送消息\n    const handleSend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        let messageContent = inputMessage.trim();\n        // 检查是否有激活的提示词配置\n        try {\n            const promptConfigService = _services_promptConfigService__WEBPACK_IMPORTED_MODULE_16__.PromptConfigService.getInstance();\n            const activeConfigResult = await promptConfigService.getActiveConfig();\n            if (activeConfigResult.success && activeConfigResult.data) {\n                const configContentResult = await promptConfigService.getConfigContent(activeConfigResult.data.id);\n                if (configContentResult.success && configContentResult.data) {\n                    // 将提示词内容拼接到用户输入前面\n                    messageContent = \"\".concat(configContentResult.data, \" \").concat(messageContent);\n                    console.log(\"\\uD83D\\uDD2E 应用提示词配置:\", activeConfigResult.data.name, \"内容:\", configContentResult.data);\n                }\n            }\n        } catch (error) {\n            console.error(\"获取提示词配置失败:\", error);\n        // 继续使用原始消息内容\n        }\n        // 检测是否包含```human代码块\n        const hasHumanBlock = (0,_utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_15__.hasHumanBlocks)(messageContent);\n        console.log(\"\\uD83D\\uDD35 开始发送用户消息:\", messageContent.substring(0, 30), hasHumanBlock ? \"(包含Human代码块)\" : \"\");\n        // 创建用户消息对象\n        const userMessage = {\n            id: generateMessageId(\"user\"),\n            type: \"user\",\n            content: messageContent,\n            timestamp: Date.now(),\n            createdAt: Date.now()\n        };\n        // 先清空输入框\n        setInputMessage(\"\");\n        // 重置输入框高度\n        if (textareaRef.current) {\n            textareaRef.current.style.height = \"auto\";\n        }\n        // 添加用户消息到历史记录\n        addUserMessage(userMessage);\n        if (hasHumanBlock) {\n            // 如果包含```human代码块，处理辅助AI调用\n            handleHumanBlockMessage(messageContent);\n        } else {\n            // 正常消息处理\n            setTimeout(()=>{\n                onSendMessage(messageContent);\n            }, 0);\n        }\n    }, [\n        inputMessage,\n        isLoading,\n        generateMessageId,\n        addUserMessage,\n        onSendMessage\n    ]);\n    // 处理包含```human代码块的消息\n    const handleHumanBlockMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message)=>{\n        // 生成消息唯一标识符，防止重复处理\n        const messageHash = btoa(encodeURIComponent(message)).replace(/[^a-zA-Z0-9]/g, \"\").substring(0, 16);\n        if (processedMessages.has(messageHash)) {\n            console.log(\"⚠️ 消息已处理过，跳过重复处理:\", messageHash);\n            return;\n        }\n        // 标记消息为已处理\n        setProcessedMessages((prev)=>new Set([\n                ...prev,\n                messageHash\n            ]));\n        const humanBlocks = (0,_utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_15__.extractHumanBlocks)(message);\n        console.log(\"\\uD83E\\uDD16 检测到Human代码块:\", humanBlocks.length, \"个\", \"消息ID:\", messageHash);\n        // 为每个```human代码块创建HelperResponseLayer\n        humanBlocks.forEach((block, index)=>{\n            const layerId = \"helper-\".concat(messageHash, \"-\").concat(index);\n            const messageLayer = {\n                id: layerId,\n                type: \"helper-response\",\n                content: \"\",\n                timestamp: Date.now(),\n                metadata: {\n                    humanBlock: {\n                        roleName: block.roleName,\n                        roleDescription: block.roleDescription,\n                        question: block.question\n                    },\n                    originalMessage: message,\n                    isExpanded: true,\n                    layerId\n                }\n            };\n            // 添加到消息层列表\n            setMessageLayers((prev)=>[\n                    ...prev,\n                    messageLayer\n                ]);\n        });\n        // 移除```human代码块后的消息内容\n        const cleanMessage = (0,_utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_15__.removeHumanBlocks)(message);\n        // 如果还有其他内容，也发送给主AI\n        if (cleanMessage.trim()) {\n            setTimeout(()=>{\n                onSendMessage(cleanMessage);\n            }, 100);\n        }\n    }, [\n        onSendMessage,\n        processedMessages\n    ]);\n    // 处理辅助AI集成完成\n    const handleIntegrationComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((integratedMessage)=>{\n        console.log(\"\\uD83D\\uDD04 辅助AI集成完成，准备发送给主AI\");\n        setPendingIntegratedMessage(integratedMessage);\n    }, []);\n    // 处理主AI回复\n    const handleMainAIResponse = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((response)=>{\n        console.log(\"✅ 主AI回复完成\");\n    // 这里可以添加额外的处理逻辑\n    }, []);\n    // 处理辅助AI错误\n    const handleHelperError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((error)=>{\n        console.error(\"❌ 辅助AI处理错误:\", error);\n    // 可以显示错误提示\n    }, []);\n    // 发送集成消息给主AI\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingIntegratedMessage) {\n            setTimeout(()=>{\n                onSendMessage(pendingIntegratedMessage);\n                setPendingIntegratedMessage(\"\");\n            }, 500);\n        }\n    }, [\n        pendingIntegratedMessage,\n        onSendMessage\n    ]);\n    // 处理键盘事件\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    // 加载文件路径 - 添加数据完整性验证\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadFilePaths = async ()=>{\n            if (associatedFiles.length === 0) {\n                setFilePaths(new Map());\n                return;\n            }\n            const newFilePaths = new Map();\n            const validFileIds = [];\n            const invalidFileIds = [];\n            for (const fileId of associatedFiles){\n                // 数据完整性验证：检查fileId是否有效\n                if (!fileId || typeof fileId !== \"string\" || fileId.trim().length === 0) {\n                    console.warn(\"⚠️ 发现无效的文件ID:\", fileId);\n                    invalidFileIds.push(fileId);\n                    continue;\n                }\n                try {\n                    const filePath = await findFilePathById(fileId);\n                    // 验证文件路径是否有效\n                    if (filePath && !filePath.includes(\"未知文件\")) {\n                        newFilePaths.set(fileId, filePath);\n                        validFileIds.push(fileId);\n                    } else {\n                        console.warn(\"⚠️ 文件不存在或无法访问:\", fileId);\n                        invalidFileIds.push(fileId);\n                    }\n                } catch (error) {\n                    console.error(\"❌ 获取文件路径失败:\", fileId, error);\n                    invalidFileIds.push(fileId);\n                }\n            }\n            setFilePaths(newFilePaths);\n            // 如果发现无效文件，自动清理\n            if (invalidFileIds.length > 0) {\n                console.log(\"\\uD83E\\uDDF9 发现\", invalidFileIds.length, \"个无效文件，自动清理:\", invalidFileIds);\n                // 通知父组件更新文件关联，移除无效文件\n                if (validFileIds.length !== associatedFiles.length) {\n                    try {\n                        await chatHistoryService.saveCurrentFileAssociations(validFileIds);\n                        console.log(\"✅ 已自动清理无效文件关联\");\n                    } catch (error) {\n                        console.error(\"❌ 清理无效文件关联失败:\", error);\n                    }\n                }\n            }\n        };\n        loadFilePaths();\n    }, [\n        associatedFiles,\n        findFilePathById,\n        chatHistoryService\n    ]);\n    // 🔧 AI响应完成处理 - 使用防抖机制减少重复触发\n    const lastSavedContentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isComplete && responseContent && responseContent !== lastSavedContentRef.current) {\n            lastSavedContentRef.current = responseContent;\n            console.log(\"\\uD83E\\uDD16 AI响应完成，准备保存消息:\", {\n                isComplete,\n                contentLength: responseContent.length,\n                contentPreview: responseContent.substring(0, 50) + \"...\"\n            });\n            const assistantMessage = {\n                id: generateMessageId(\"assistant\"),\n                type: \"assistant\",\n                content: responseContent,\n                timestamp: Date.now(),\n                createdAt: Date.now()\n            };\n            setChatHistory((prev)=>{\n                console.log(\"\\uD83D\\uDCCB 当前历史记录数量:\", prev.length);\n                // 🔧 改进重复消息检测：检查最近的消息，而不是所有消息\n                // 只检查最近3条assistant消息，避免误判\n                const recentAssistantMessages = prev.filter((m)=>m.type === \"assistant\").slice(-3) // 只检查最近3条\n                ;\n                const duplicateExists = recentAssistantMessages.some((m)=>{\n                    const contentMatch = m.content === responseContent;\n                    const timeMatch = Math.abs(m.timestamp - assistantMessage.timestamp) < 5000 // 5秒内\n                    ;\n                    console.log(\"\\uD83D\\uDD0D 重复检测:\", {\n                        messageId: m.id.substring(0, 8),\n                        contentMatch,\n                        timeMatch,\n                        timeDiff: Math.abs(m.timestamp - assistantMessage.timestamp)\n                    });\n                    return contentMatch && timeMatch;\n                });\n                if (duplicateExists) {\n                    console.log(\"⚠️ 检测到重复消息，跳过添加\");\n                    return prev;\n                }\n                console.log(\"✅ 添加新的AI响应消息:\", assistantMessage.id.substring(0, 8));\n                const newHistory = [\n                    ...prev,\n                    assistantMessage\n                ];\n                // 异步保存历史记录\n                chatHistoryService.saveCurrentHistory(newHistory).catch((error)=>{\n                    console.error(\"❌ 保存AI响应消息失败:\", error);\n                });\n                // 通知父组件历史记录变化\n                if (onChatHistoryChange) {\n                    onChatHistoryChange(newHistory);\n                }\n                return newHistory;\n            });\n        }\n    }, [\n        isComplete,\n        responseContent,\n        generateMessageId,\n        chatHistoryService,\n        onChatHistoryChange\n    ]);\n    // 暂时禁用文本选择保护\n    // useEffect(() => {\n    //   if (selectionManagerRef.current && aiResponseRef.current) {\n    //     if (isStreaming) {\n    //       selectionManagerRef.current.clearSelection()\n    //     } else if (!selectionManagerRef.current.isSelecting()) {\n    //       selectionManagerRef.current.saveSelection(aiResponseRef.current)\n    //     }\n    //   }\n    // }, [isStreaming])\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 在DOM更新后恢复选择状态\n        if (selectionManagerRef.current && aiResponseRef.current && !isStreaming && isComplete) {\n            setTimeout(()=>{\n                var _selectionManagerRef_current;\n                (_selectionManagerRef_current = selectionManagerRef.current) === null || _selectionManagerRef_current === void 0 ? void 0 : _selectionManagerRef_current.restoreSelection(aiResponseRef.current);\n            }, 50) // 短延迟确保DOM更新完成\n            ;\n        }\n    }, [\n        isStreaming,\n        isComplete\n    ]) // 🔧 关键修复：移除responseContent依赖，避免持续重新渲染\n    ;\n    // 🔧 简化的滚动状态管理 - 参考StreamingResponse的简单模式\n    const scrollStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        shouldAutoScroll: true,\n        lastContentLength: 0\n    });\n    // 🔧 简化的滚动控制 - 参考StreamingResponse的直接滚动方式\n    const scrollToBottom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (chatContainerRef.current) {\n            chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n        }\n    }, []);\n    // 🔧 添加流式响应状态监控 - 移除responseContent.length依赖，避免频繁重渲染\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83C\\uDF0A 流式响应状态变化:\", {\n            isStreaming,\n            isComplete,\n            responseContentLength: responseContentRef.current.length,\n            timestamp: Date.now()\n        });\n    }, [\n        isStreaming,\n        isComplete\n    ]);\n    // 🔧 检测用户是否在底部附近\n    const isUserNearBottom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!chatContainerRef.current) return false;\n        const container = chatContainerRef.current;\n        return container.scrollTop + container.clientHeight >= container.scrollHeight - 100;\n    }, []);\n    // 🔧 聊天历史变化时的滚动控制 - 简化版本，参考StreamingResponse\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (chatContainerRef.current && chatHistory.length > 0) {\n            // 简化逻辑：如果用户在底部或启用自动滚动，直接滚动到底部\n            if (isUserNearBottom() || scrollStateRef.current.shouldAutoScroll) {\n                chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n            }\n        }\n    }, [\n        chatHistory.length,\n        isUserNearBottom\n    ]);\n    // 🔧 流式响应时的实时滚动 - 简化版本，参考StreamingResponse的直接模式\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isStreaming && responseContentRef.current && chatContainerRef.current) {\n            const currentLength = responseContentRef.current.length;\n            // 简化逻辑：内容增加时，如果用户在底部就直接滚动\n            if (currentLength > scrollStateRef.current.lastContentLength) {\n                scrollStateRef.current.lastContentLength = currentLength;\n                if (isUserNearBottom() || scrollStateRef.current.shouldAutoScroll) {\n                    // 直接滚动，无需额外的函数调用\n                    chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n                }\n            }\n        }\n    }, [\n        isStreaming,\n        isUserNearBottom\n    ]) // 🔧 简化依赖项\n    ;\n    // 🔧 用户滚动事件处理 - 智能检测用户滚动意图\n    const handleUserScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (chatContainerRef.current) {\n            // 当用户手动滚动时，根据位置决定是否启用自动滚动\n            scrollStateRef.current.shouldAutoScroll = isUserNearBottom();\n        }\n    }, [\n        isUserNearBottom\n    ]);\n    // 处理会话切换\n    const handleSessionSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (sessionId)=>{\n        try {\n            console.log(\"\\uD83D\\uDD04 开始切换会话:\", sessionId);\n            // 🔧 关键修复：在切换会话前，先清理当前AI响应状态\n            // 这是解决会话切换时AI响应残留问题的核心修复\n            console.log(\"\\uD83E\\uDDF9 清理AI响应状态...\");\n            // 通知父组件清理AI响应状态\n            // 这里需要父组件提供清理回调函数\n            if (onClearAIResponse) {\n                onClearAIResponse();\n            }\n            setCurrentSessionId(sessionId);\n            // 加载选中会话的历史记录\n            const sessionHistory = await chatHistoryService.loadHistory(sessionId);\n            setChatHistory(sessionHistory);\n            // 通知父组件对话历史变化\n            if (onChatHistoryChange) {\n                onChatHistoryChange(sessionHistory);\n            }\n            console.log(\"✅ 会话已切换:\", sessionId, \"历史记录:\", sessionHistory.length, \"条\");\n            console.log(\"✅ AI响应状态已清理\");\n        } catch (error) {\n            console.error(\"❌ 切换会话失败:\", error);\n        }\n    }, [\n        chatHistoryService,\n        onChatHistoryChange,\n        onClearAIResponse\n    ]);\n    // 处理文件关联变化\n    const handleFilesChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (newFiles)=>{\n        try {\n            console.log(\"\\uD83D\\uDD25 ChatInterface - 接收到文件关联变化:\", newFiles);\n            console.log(\"\\uD83D\\uDD25 ChatInterface - 当前associatedFiles:\", associatedFiles);\n            // 保存到ChatHistoryService\n            await chatHistoryService.saveCurrentFileAssociations(newFiles);\n            // 通知父组件\n            console.log(\"\\uD83D\\uDD25 ChatInterface - 调用父组件onFilesChange:\", newFiles);\n            onFilesChange(newFiles);\n            console.log(\"✅ 文件关联已更新:\", newFiles.length, \"个文件\");\n        } catch (error) {\n            console.error(\"❌ 保存文件关联失败:\", error);\n        }\n    }, [\n        chatHistoryService,\n        onFilesChange,\n        associatedFiles\n    ]);\n    // 处理单条消息插入到编辑器\n    const handleInsertMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageContent)=>{\n        console.log(\"\\uD83D\\uDD04 尝试插入消息内容到编辑器:\", messageContent.substring(0, 100) + \"...\");\n        if (onMessageContentInsert) {\n            console.log(\"✅ 使用消息内容插入弹窗\");\n            // 使用专门的消息内容插入接口，会显示插入弹窗\n            onMessageContentInsert(messageContent);\n        } else if (onContentInsert) {\n            console.log(\"✅ 直接使用onContentInsert接口插入内容\");\n            // 直接使用onContentInsert插入特定消息内容\n            onContentInsert(messageContent, {\n                position: \"cursor\",\n                addNewlines: true // 添加新行\n            });\n        } else if (onInsertToEditor) {\n            console.log(\"⚠️ 使用降级插入方法\");\n            // 创建一个临时的插入函数，直接操作编辑器\n            try {\n                // 尝试直接访问编辑器并插入内容\n                const activeEditor = document.querySelector('textarea, [contenteditable=\"true\"]');\n                if (activeEditor) {\n                    if (activeEditor instanceof HTMLTextAreaElement) {\n                        // 处理textarea\n                        const start = activeEditor.selectionStart || 0;\n                        const end = activeEditor.selectionEnd || 0;\n                        const currentValue = activeEditor.value;\n                        const newValue = currentValue.slice(0, start) + \"\\n\" + messageContent + \"\\n\" + currentValue.slice(end);\n                        activeEditor.value = newValue;\n                        activeEditor.selectionStart = activeEditor.selectionEnd = start + messageContent.length + 2;\n                        // 触发change事件\n                        const event = new Event(\"input\", {\n                            bubbles: true\n                        });\n                        activeEditor.dispatchEvent(event);\n                        console.log(\"✅ 直接插入到textarea成功\");\n                    } else if (activeEditor.contentEditable === \"true\") {\n                        // 处理contenteditable元素\n                        const selection = window.getSelection();\n                        if (selection && selection.rangeCount > 0) {\n                            const range = selection.getRangeAt(0);\n                            range.deleteContents();\n                            const textNode = document.createTextNode(\"\\n\" + messageContent + \"\\n\");\n                            range.insertNode(textNode);\n                            // 移动光标到插入内容后\n                            range.setStartAfter(textNode);\n                            range.setEndAfter(textNode);\n                            selection.removeAllRanges();\n                            selection.addRange(range);\n                            console.log(\"✅ 直接插入到contenteditable成功\");\n                        }\n                    }\n                } else {\n                    // 如果找不到编辑器，使用原有方法\n                    console.log(\"⚠️ 未找到编辑器元素，使用原有插入方法\");\n                    onInsertToEditor();\n                }\n            } catch (error) {\n                console.error(\"❌ 直接插入失败，使用原有方法:\", error);\n                onInsertToEditor();\n            }\n        } else {\n            console.warn(\"⚠️ 没有可用的插入接口\");\n        }\n    }, [\n        onMessageContentInsert,\n        onContentInsert,\n        onInsertToEditor\n    ]);\n    // 处理重新生成AI消息\n    const handleRegenerateMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageIndex)=>{\n        // 找到该AI消息前面的用户消息\n        const messages = chatHistory.slice(0, messageIndex);\n        const lastUserMessage = messages.reverse().find((msg)=>msg.type === \"user\");\n        if (lastUserMessage) {\n            console.log(\"\\uD83D\\uDD04 重新生成AI消息，基于用户消息:\", lastUserMessage.content.substring(0, 50));\n            console.log(\"\\uD83D\\uDCCA 历史消息限制：只包含索引 0-\".concat(messageIndex - 1, \" 的消息\"));\n            // 删除从当前AI消息开始的所有后续消息\n            const newHistory = chatHistory.slice(0, messageIndex);\n            setChatHistory(newHistory);\n            // 保存更新后的历史记录\n            chatHistoryService.saveCurrentHistory(newHistory);\n            // 重新发送用户消息，传递目标消息索引以限制历史消息范围\n            onSendMessage(lastUserMessage.content, messageIndex);\n            // 关闭工具面板\n            setActiveMessageTools(null);\n        }\n    }, [\n        chatHistory,\n        onSendMessage,\n        chatHistoryService\n    ]);\n    // 处理编辑用户消息\n    const handleEditMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageId, currentContent)=>{\n        setEditingMessageId(messageId);\n        setEditingContent(currentContent);\n        setActiveMessageTools(null);\n    }, []);\n    // 处理保存编辑的消息\n    const handleSaveEditedMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageId, newContent)=>{\n        const messageIndex = chatHistory.findIndex((msg)=>msg.id === messageId);\n        if (messageIndex === -1) return;\n        // 更新消息内容\n        const updatedMessage = {\n            ...chatHistory[messageIndex],\n            content: newContent,\n            updatedAt: Date.now()\n        };\n        // 删除该消息之后的所有消息\n        const newHistory = [\n            ...chatHistory.slice(0, messageIndex),\n            updatedMessage\n        ];\n        setChatHistory(newHistory);\n        // 保存更新后的历史记录\n        chatHistoryService.saveCurrentHistory(newHistory);\n        // 重新发送编辑后的消息\n        onSendMessage(newContent);\n        // 清除编辑状态\n        setEditingMessageId(null);\n        setEditingContent(\"\");\n        console.log(\"✅ 消息已编辑并重发:\", newContent.substring(0, 50));\n    }, [\n        chatHistory,\n        onSendMessage,\n        chatHistoryService\n    ]);\n    // 处理多项对比\n    const handleMultipleComparison = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageIndex)=>{\n        // 找到该AI消息前面的用户消息\n        const messages = chatHistory.slice(0, messageIndex);\n        const lastUserMessage = messages.reverse().find((msg)=>msg.type === \"user\");\n        if (lastUserMessage) {\n            console.log(\"\\uD83D\\uDD04 开启多项对比，基于用户消息:\", lastUserMessage.content.substring(0, 50));\n            console.log(\"\\uD83C\\uDFAF 记录触发消息索引:\", messageIndex);\n            // 设置对比内容并打开弹窗\n            setComparisonMessageContent(lastUserMessage.content);\n            setComparisonTriggerMessageIndex(messageIndex) // 记录触发的消息索引\n            ;\n            setShowMultipleComparison(true);\n            // 关闭工具面板\n            setActiveMessageTools(null);\n        }\n    }, [\n        chatHistory\n    ]);\n    // 处理多项对比结果选择\n    const handleComparisonResultSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((selectedResult)=>{\n        console.log(\"\\uD83C\\uDFAF 多项对比结果选择，替换最后一条AI消息:\", selectedResult.substring(0, 100) + \"...\");\n        // 替换最后一条AI消息的内容\n        setChatHistory((prev)=>{\n            const newHistory = [\n                ...prev\n            ];\n            // 从后往前找最后一条AI消息\n            for(let i = newHistory.length - 1; i >= 0; i--){\n                if (newHistory[i].type === \"assistant\") {\n                    // 替换这条AI消息的内容\n                    newHistory[i] = {\n                        ...newHistory[i],\n                        content: selectedResult,\n                        timestamp: Date.now() // 更新时间戳\n                    };\n                    console.log(\"✅ 已替换AI消息:\", newHistory[i].id);\n                    break;\n                }\n            }\n            // 保存更新后的历史记录\n            chatHistoryService.saveCurrentHistory(newHistory).catch((error)=>{\n                console.error(\"❌ 保存替换后的消息失败:\", error);\n            });\n            return newHistory;\n        });\n        // 关闭弹窗\n        setShowMultipleComparison(false);\n        setComparisonMessageContent(\"\");\n    }, [\n        chatHistoryService\n    ]);\n    // 处理取消编辑\n    const handleCancelEdit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setEditingMessageId(null);\n        setEditingContent(\"\");\n    }, []);\n    // 获取可用模型列表\n    const loadAvailableModels = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!(currentConfig === null || currentConfig === void 0 ? void 0 : currentConfig.apiKey)) {\n            console.warn(\"⚠️ 没有可用的API配置\");\n            return;\n        }\n        setIsLoadingModels(true);\n        try {\n            const aiService = _services_aiService__WEBPACK_IMPORTED_MODULE_5__.AIService.getInstance();\n            const result = await aiService.getAvailableModels(currentConfig);\n            if (result.success && result.data && result.data.length > 0) {\n                // 转换为模型对象格式\n                const models = result.data.map((modelId)=>({\n                        id: modelId,\n                        name: modelId.toUpperCase().replace(/-/g, \" \"),\n                        description: getModelDescription(modelId)\n                    }));\n                setAvailableModels(models);\n                console.log(\"✅ 成功获取模型列表:\", models.length, \"个模型\");\n            } else {\n                console.warn(\"⚠️ 获取模型列表失败\");\n                setAvailableModels([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取模型列表失败:\", error);\n            setAvailableModels([]);\n        } finally{\n            setIsLoadingModels(false);\n        }\n    }, [\n        currentConfig\n    ]);\n    // 获取模型描述\n    const getModelDescription = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const lowerModelId = modelId.toLowerCase();\n        // GPT系列模型\n        if (lowerModelId.includes(\"gpt-4\")) {\n            if (lowerModelId.includes(\"turbo\")) {\n                return \"更快的GPT-4，性价比更高\";\n            }\n            return \"最强大的模型，适合复杂任务\";\n        } else if (lowerModelId.includes(\"gpt-3.5\")) {\n            return \"快速且经济的选择\";\n        } else if (lowerModelId.includes(\"claude\")) {\n            return \"Anthropic Claude，强大的推理能力\";\n        } else if (lowerModelId.includes(\"gemini\")) {\n            return \"Google Gemini，多模态能力强\";\n        }\n        // 其他模型\n        return \"可用的AI模型: \".concat(modelId);\n    }, []);\n    // 点击外部关闭消息工具面板\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (activeMessageTools) {\n                // 检查点击是否在工具面板外部\n                const target = event.target;\n                if (!target.closest(\".message-tools-panel\") && !target.closest(\".message-tools-button\")) {\n                    setActiveMessageTools(null);\n                }\n            }\n            // 点击外部取消编辑状态（除非点击在编辑区域内）\n            if (editingMessageId) {\n                const target = event.target;\n                if (!target.closest(\"textarea\") && !target.closest(\"button\")) {\n                    handleCancelEdit();\n                }\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        activeMessageTools,\n        editingMessageId,\n        handleCancelEdit\n    ]);\n    // 暴露方法给父组件\n    react__WEBPACK_IMPORTED_MODULE_1___default().useImperativeHandle(ref, ()=>({\n            handleSessionSelect\n        }), [\n        handleSessionSelect\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            (associatedFiles.length > 0 || focusedFile) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 bg-gray-800/20 border-b border-amber-500/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2 cursor-pointer hover:bg-gray-700/20 rounded-md p-1 -m-1 transition-all duration-200\",\n                        onClick: ()=>setIsFileAssociationCollapsed(!isFileAssociationCollapsed),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                width: \"14\",\n                                height: \"14\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                className: \"text-amber-400 transition-transform duration-200 \".concat(isFileAssociationCollapsed ? \"rotate-0\" : \"rotate-90\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1695,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1688,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                width: \"14\",\n                                height: \"14\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                className: \"text-amber-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1698,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1697,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-amber-200 font-handwritten\",\n                                children: \"关联文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1700,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-400\",\n                                children: [\n                                    \"(\",\n                                    focusedFile ? associatedFiles.includes(focusedFile) ? associatedFiles.length : associatedFiles.length + 1 : associatedFiles.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1701,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500 ml-auto\",\n                                children: isFileAssociationCollapsed ? \"点击展开\" : \"点击折叠\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1704,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 1684,\n                        columnNumber: 11\n                    }, this),\n                    !isFileAssociationCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2 mt-2\",\n                        children: (()=>{\n                            // 创建文件显示列表，聚焦文件优先\n                            const allFiles = new Set([\n                                ...focusedFile ? [\n                                    focusedFile\n                                ] : [],\n                                ...associatedFiles\n                            ]);\n                            return Array.from(allFiles).map((fileId, index)=>{\n                                const filePath = filePaths.get(fileId) || \"加载中... (\".concat(fileId.substring(0, 8), \"...)\");\n                                const isFocused = focusedFile === fileId;\n                                const isManuallyAssociated = associatedFiles.includes(fileId);\n                                // 确定文件类型和样式\n                                const fileType = isFocused ? \"auto\" : \"manual\";\n                                const styleConfig = {\n                                    auto: {\n                                        className: \"bg-blue-500/20 text-blue-200 border-blue-500/50\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"12\",\n                                            height: \"12\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1731,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1730,\n                                            columnNumber: 25\n                                        }, this),\n                                        label: \"自动\",\n                                        title: \"当前编辑文件（自动关联）\"\n                                    },\n                                    manual: {\n                                        className: \"bg-green-500/20 text-green-200 border-green-500/50\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"12\",\n                                            height: \"12\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1741,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1740,\n                                            columnNumber: 25\n                                        }, this),\n                                        label: \"手动\",\n                                        title: \"手动关联文件\"\n                                    }\n                                };\n                                const config = styleConfig[fileType];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 px-2 py-1 \".concat(config.className, \" border rounded-md text-xs\"),\n                                    title: config.title,\n                                    children: [\n                                        config.icon,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs opacity-75 font-handwritten\",\n                                            children: config.label\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1758,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono\",\n                                            title: filePath,\n                                            children: filePath\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1759,\n                                            columnNumber: 23\n                                        }, this),\n                                        isManuallyAssociated && !isFocused && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                const newFiles = associatedFiles.filter((id)=>id !== fileId);\n                                                handleFilesChange(newFiles);\n                                            },\n                                            className: \"ml-1 p-0.5 text-green-300 hover:text-red-400 hover:bg-red-500/10 rounded transition-all duration-200\",\n                                            title: \"移除手动关联文件\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"10\",\n                                                height: \"10\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 1772,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1771,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1763,\n                                            columnNumber: 25\n                                        }, this),\n                                        isFocused && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-1 px-1 py-0.5 bg-blue-600/30 text-blue-100 rounded text-xs font-handwritten\",\n                                            children: \"当前\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1779,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, fileId, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1752,\n                                    columnNumber: 21\n                                }, this);\n                            });\n                        })()\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 1710,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 1683,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                className: \"flex-1 overflow-y-auto custom-scrollbar p-4 space-y-4\",\n                onScroll: handleUserScroll,\n                children: [\n                    chatHistory.length === 0 && !isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center h-full text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto mb-4 rounded-lg bg-amber-500/20 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"32\",\n                                    height: \"32\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    className: \"text-amber-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 1802,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1801,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1800,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-handwritten text-amber-200 mb-2\",\n                                children: \"开始对话\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1805,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm font-handwritten\",\n                                children: currentConfig ? \"输入消息开始与AI对话\" : \"请先在配置页面设置API Key\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1808,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 1799,\n                        columnNumber: 11\n                    }, this),\n                    chatHistory.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex \".concat(message.type === \"user\" ? \"justify-end\" : \"justify-start\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-[80%] p-3 rounded-lg \".concat(message.type === \"user\" ? \"bg-blue-500/20 text-blue-200 border border-blue-500/50\" : \"bg-green-500/20 text-green-200 border border-green-500/50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4\",\n                                                        children: message.type === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1831,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1830,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M17.5,12A1.5,1.5 0 0,1 16,10.5A1.5,1.5 0 0,1 17.5,9A1.5,1.5 0 0,1 19,10.5A1.5,1.5 0 0,1 17.5,12M10,10.5C10,11.3 9.3,12 8.5,12S7,11.3 7,10.5C7,9.7 7.7,9 8.5,9S10,9.7 10,10.5M12,14C9.8,14 8,15.8 8,18V20H16V18C16,15.8 14.2,14 12,14M12,4C14.2,4 16,5.8 16,8S14.2,12 12,12S8,10.2 8,8S9.8,4 12,4M12,2A6,6 0 0,0 6,8C6,11.3 8.7,14 12,14S18,11.3 18,8A6,6 0 0,0 12,2Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1835,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1834,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1828,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-handwritten\",\n                                                        children: message.type === \"user\" ? \"你\" : \"AI助手\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1839,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs opacity-60\",\n                                                        children: new Date(message.timestamp).toLocaleTimeString()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1842,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1827,\n                                                columnNumber: 17\n                                            }, this),\n                                            message.type === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveMessageTools(activeMessageTools === message.id ? null : message.id),\n                                                        className: \"message-tools-button p-1 rounded-md bg-blue-400/10 hover:bg-blue-400/20 border border-blue-400/20 hover:border-blue-400/40 transition-all duration-200 text-blue-300 hover:text-blue-200 opacity-90 hover:opacity-100\",\n                                                        title: \"消息工具\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"12\",\n                                                            height: \"12\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1856,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1855,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1850,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    activeMessageTools === message.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"message-tools-panel absolute right-0 top-full mt-1 bg-gray-800 border border-blue-500/30 rounded-lg shadow-lg z-50 min-w-[120px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                handleEditMessage(message.id, message.content);\n                                                            },\n                                                            className: \"w-full px-3 py-2 text-left text-sm text-blue-200 hover:bg-blue-500/20 transition-all duration-200 font-handwritten flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    width: \"14\",\n                                                                    height: \"14\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1870,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 1869,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"编辑重发\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1863,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1862,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1849,\n                                                columnNumber: 19\n                                            }, this),\n                                            message.type === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveMessageTools(activeMessageTools === message.id ? null : message.id),\n                                                        className: \"message-tools-button p-1 rounded-md bg-amber-400/10 hover:bg-amber-400/20 border border-amber-400/20 hover:border-amber-400/40 transition-all duration-200 text-amber-300 hover:text-amber-200 opacity-90 hover:opacity-100\",\n                                                        title: \"消息工具\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"12\",\n                                                            height: \"12\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1888,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1887,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1882,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    activeMessageTools === message.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"message-tools-panel absolute right-0 top-full mt-1 bg-gray-800 border border-green-500/30 rounded-lg shadow-lg z-50 min-w-[140px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    handleInsertMessage(message.content);\n                                                                    setActiveMessageTools(null);\n                                                                },\n                                                                className: \"w-full px-3 py-2 text-left text-sm text-green-200 hover:bg-green-500/20 transition-all duration-200 font-handwritten border-b border-green-500/20 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        width: \"14\",\n                                                                        height: \"14\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 1903,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1902,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"插入到编辑器\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1895,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    const messageIndex = chatHistory.findIndex((msg)=>msg.id === message.id);\n                                                                    if (messageIndex !== -1) {\n                                                                        handleRegenerateMessage(messageIndex);\n                                                                    }\n                                                                },\n                                                                className: \"w-full px-3 py-2 text-left text-sm text-amber-200 hover:bg-amber-500/20 transition-all duration-200 font-handwritten flex items-center gap-2 border-b border-amber-500/20\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        width: \"14\",\n                                                                        height: \"14\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 1917,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1916,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"重新生成\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1907,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    const messageIndex = chatHistory.findIndex((msg)=>msg.id === message.id);\n                                                                    if (messageIndex !== -1) {\n                                                                        handleMultipleComparison(messageIndex);\n                                                                    }\n                                                                },\n                                                                className: \"w-full px-3 py-2 text-left text-sm text-purple-200 hover:bg-purple-500/20 transition-all duration-200 font-handwritten flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        width: \"14\",\n                                                                        height: \"14\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V6.5H5V5H19M19,19H5V8.5H19V19Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 1931,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1930,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"多项对比\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1921,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1894,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1881,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 1826,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: editingMessageId === message.id ? // 编辑模式\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: editingContent,\n                                                    onChange: (e)=>setEditingContent(e.target.value),\n                                                    className: \"w-full p-2 bg-gray-700 border border-blue-500/30 rounded-md text-blue-200 font-handwritten resize-none focus:outline-none focus:border-blue-500/60\",\n                                                    rows: Math.max(2, editingContent.split(\"\\n\").length),\n                                                    placeholder: \"编辑消息内容...\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 1945,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleCancelEdit,\n                                                            className: \"px-3 py-1 text-xs bg-gray-600 hover:bg-gray-500 text-gray-200 rounded-md transition-all duration-200 font-handwritten\",\n                                                            children: \"取消\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1954,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSaveEditedMessage(message.id, editingContent),\n                                                            className: \"px-3 py-1 text-xs bg-blue-600 hover:bg-blue-500 text-white rounded-md transition-all duration-200 font-handwritten\",\n                                                            disabled: !editingContent.trim(),\n                                                            children: \"保存并重发\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1960,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 1953,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1944,\n                                            columnNumber: 19\n                                        }, this) : // 正常显示模式\n                                        renderMessageContentInternal(message.content)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 1941,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1820,\n                                columnNumber: 13\n                            }, this)\n                        }, message.id, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                            lineNumber: 1816,\n                            columnNumber: 11\n                        }, this)),\n                    messageLayers.map((layer)=>{\n                        var _layer_metadata;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full\",\n                            children: layer.type === \"helper-response\" && ((_layer_metadata = layer.metadata) === null || _layer_metadata === void 0 ? void 0 : _layer_metadata.humanBlock) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HelperResponseLayer__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                messageId: layer.id,\n                                humanBlockContent: layer.metadata.humanBlock,\n                                onIntegrationComplete: handleIntegrationComplete,\n                                onMainAIResponse: handleMainAIResponse,\n                                onError: handleHelperError,\n                                className: \"my-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1982,\n                                columnNumber: 15\n                            }, this)\n                        }, layer.id, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                            lineNumber: 1980,\n                            columnNumber: 11\n                        }, this);\n                    }),\n                    (isLoading || isStreaming && !isComplete) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-[80%] p-3 bg-green-500/20 text-green-200 border border-green-500/50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M17.5,12A1.5,1.5 0 0,1 16,10.5A1.5,1.5 0 0,1 17.5,9A1.5,1.5 0 0,1 19,10.5A1.5,1.5 0 0,1 17.5,12M10,10.5C10,11.3 9.3,12 8.5,12S7,11.3 7,10.5C7,9.7 7.7,9 8.5,9S10,9.7 10,10.5M12,14C9.8,14 8,15.8 8,18V20H16V18C16,15.8 14.2,14 12,14M12,4C14.2,4 16,5.8 16,8S14.2,12 12,12S8,10.2 8,8S9.8,4 12,4M12,2A6,6 0 0,0 6,8C6,11.3 8.7,14 12,14S18,11.3 18,8A6,6 0 0,0 12,2Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2001,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2000,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1999,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-handwritten\",\n                                            children: \"AI助手\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2004,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs opacity-60\",\n                                            children: isLoading && !responseContent ? \"正在思考...\" : isStreaming ? \"正在输入...\" : \"刚刚\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2005,\n                                            columnNumber: 17\n                                        }, this),\n                                        isStreaming && onStop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onStop,\n                                            className: \"ml-auto p-1 text-gray-400 hover:text-red-400 hover:bg-red-500/10 rounded transition-all duration-200\",\n                                            title: \"停止生成\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"12\",\n                                                height: \"12\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M6,6H18V18H6V6Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2016,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2015,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2010,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1998,\n                                    columnNumber: 15\n                                }, this),\n                                responseContent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: aiResponseRef,\n                                    className: \"text-sm\",\n                                    children: [\n                                        renderMessageContentInternal(responseContent),\n                                        isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse text-amber-400 ml-1\",\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2027,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2024,\n                                    columnNumber: 17\n                                }, this) : /* 等待动画 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-amber-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-amber-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"0ms\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2034,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-amber-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"150ms\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2035,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-amber-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"300ms\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2036,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2033,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2032,\n                                    columnNumber: 17\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-2 bg-red-500/20 border border-red-500/50 rounded text-red-200 text-xs\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2045,\n                                                columnNumber: 21\n                                            }, this),\n                                            onRetry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onRetry,\n                                                className: \"ml-2 px-2 py-1 bg-red-500/30 hover:bg-red-500/50 rounded text-xs transition-colors\",\n                                                children: \"重试\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2047,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2044,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2043,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                            lineNumber: 1997,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 1996,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 1793,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-800/30 border-t border-amber-500/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: textareaRef,\n                                        value: inputMessage,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyDown,\n                                        placeholder: currentConfig ? \"输入消息...\" : \"请先配置API Key\",\n                                        disabled: !currentConfig || isLoading,\n                                        className: \"w-full min-h-[40px] max-h-[120px] p-3 pr-12 bg-gray-800 border border-gray-600 rounded-lg text-amber-200 placeholder-gray-500 resize-none focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 disabled:opacity-50 transition-all duration-200\",\n                                        rows: 1\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2066,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSend,\n                                        disabled: !inputMessage.trim() || !currentConfig || isLoading,\n                                        className: \"absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-md transition-all duration-200 \".concat(inputMessage.trim() && currentConfig && !isLoading ? \"text-amber-400 hover:text-amber-300 hover:bg-amber-500/20\" : \"text-gray-500 cursor-not-allowed\"),\n                                        title: isLoading ? \"发送中...\" : \"Enter 发送\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"16\",\n                                            height: \"16\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            className: \"animate-spin\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2089,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2088,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"16\",\n                                            height: \"16\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M2,21L23,12L2,3V10L17,12L2,14V21Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2093,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2092,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2078,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2065,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mt-3 px-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4 text-xs text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"12\",\n                                                        height: \"12\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"currentColor\",\n                                                        className: \"opacity-60\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2104,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M6.25,7.72H10V10.5H14V13.5H10V16.25H6.25V13.5H3.5V10.5H6.25V7.72Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2105,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2103,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Shift+Enter 换行\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2102,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"12\",\n                                                        height: \"12\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"currentColor\",\n                                                        className: \"opacity-60\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2111,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M6.25,7.72H17.75V10.5H6.25V7.72Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2112,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2110,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Enter 发送\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2109,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2101,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: [\n                                                    inputMessage.length,\n                                                    \" 字符\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2119,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative tools-panel-container\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowToolsPanel(!showToolsPanel),\n                                                        className: \"group flex items-center gap-1 px-2 py-1 rounded-md text-xs transition-all duration-200 \".concat(showToolsPanel ? \"text-amber-300 bg-amber-500/20 border border-amber-500/40\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 border border-transparent\"),\n                                                        title: \"工具\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                width: \"12\",\n                                                                height: \"12\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: \"currentColor\",\n                                                                className: \"transition-transform duration-200 group-hover:rotate-12\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 2132,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2131,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"工具\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2134,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2123,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    showToolsPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute bottom-full right-0 mb-3 bg-gray-800/95 backdrop-blur-sm border border-amber-500/30 rounded-xl shadow-2xl p-2 z-50 animate-in fade-in-0 zoom-in-95 duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-amber-200 font-medium mb-2 px-2 py-1\",\n                                                                children: \"快捷工具\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2141,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setShowFileAssociation(true);\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        className: \"p-2 bg-blue-500/20 text-blue-200 border border-blue-500/50 rounded-lg hover:bg-blue-500/30 transition-all duration-200\",\n                                                                        title: \"关联文件\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                lineNumber: 2154,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2153,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2145,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setShowMediaUploader(!showMediaUploader);\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        className: \"p-2 bg-purple-500/20 text-purple-200 border border-purple-500/50 rounded-lg hover:bg-purple-500/30 transition-all duration-200\",\n                                                                        title: \"上传媒体文件\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M9,16V10H5L12,3L19,10H15V16H9M5,20V18H19V20H5Z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                lineNumber: 2168,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2167,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2159,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setShowPromptManager(true);\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        disabled: isLoading,\n                                                                        className: \"p-2 rounded-lg transition-all duration-200 \".concat(activePromptConfig ? \"bg-amber-500/30 text-amber-200 border border-amber-500/70\" : \"bg-amber-500/20 text-amber-200 border border-amber-500/50 hover:bg-amber-500/30\"),\n                                                                        title: activePromptConfig ? \"当前配置: \".concat(activePromptConfig.name) : \"管理提示词模板\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                    lineNumber: 2186,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M12,15.39L8.24,17.66L9.23,13.38L5.91,10.5L10.29,10.13L12,6.09L13.71,10.13L18.09,10.5L14.77,13.38L15.76,17.66M22,9.24L14.81,8.63L12,2L9.19,8.63L2,9.24L7.45,13.97L5.82,21L12,17.27L18.18,21L16.54,13.97L22,9.24Z\",\n                                                                                    opacity: \"0.6\",\n                                                                                    transform: \"scale(0.4) translate(24, 24)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                    lineNumber: 2187,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2185,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2173,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            if (onShowAudienceSettings) {\n                                                                                onShowAudienceSettings();\n                                                                            }\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        className: \"p-2 bg-green-500/20 text-green-200 border border-green-500/50 rounded-lg hover:bg-green-500/30 transition-all duration-200\",\n                                                                        title: \"设置受众\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M16,4C18.11,4 19.81,5.69 19.81,7.8C19.81,9.91 18.11,11.6 16,11.6C13.89,11.6 12.2,9.91 12.2,7.8C12.2,5.69 13.89,4 16,4M16,13.4C18.67,13.4 24,14.73 24,17.4V20H8V17.4C8,14.73 13.33,13.4 16,13.4M8.8,11.6C6.69,11.6 5,9.91 5,7.8C5,5.69 6.69,4 8.8,4C9.13,4 9.45,4.05 9.75,4.14C9.28,5.16 9,6.3 9,7.5C9,8.7 9.28,9.84 9.75,10.86C9.45,10.95 9.13,11 8.8,11.6M8.8,13.4C7.12,13.4 3.5,14.26 3.5,17.4V20H6.5V17.4C6.5,16.55 7.45,15.1 8.8,13.4Z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                lineNumber: 2206,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2205,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2195,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2143,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-full right-4 -mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-gray-800 border-r border-b border-amber-500/30 transform rotate-45\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 2213,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2212,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2139,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2122,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2118,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2100,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 2064,\n                        columnNumber: 9\n                    }, this),\n                    showMediaUploader && artworkId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-4 bg-gray-800/50 border border-purple-500/30 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-purple-200\",\n                                        children: \"媒体文件上传\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2226,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowMediaUploader(false),\n                                        className: \"text-gray-400 hover:text-gray-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"16\",\n                                            height: \"16\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2232,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2231,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2227,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2225,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MediaUploader__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                onUploadSuccess: handleMediaUploadSuccess,\n                                onError: handleMediaUploadError,\n                                artworkId: artworkId,\n                                className: \"mb-4\",\n                                maxFiles: 5\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2236,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 2224,\n                        columnNumber: 11\n                    }, this),\n                    uploadedMedia.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowMediaList(!showMediaList),\n                                className: \"flex items-center gap-2 px-3 py-2 bg-green-500/20 text-green-200 border border-green-500/50 rounded-md hover:bg-green-500/30 transition-all duration-200 font-handwritten text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"16\",\n                                        height: \"16\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2254,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2253,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"已上传 \",\n                                            uploadedMedia.length,\n                                            \" 个媒体文件\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"14\",\n                                        height: \"14\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"currentColor\",\n                                        className: \"transition-transform duration-200 \".concat(showMediaList ? \"rotate-180\" : \"\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2264,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2257,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2249,\n                                columnNumber: 13\n                            }, this),\n                            showMediaList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 p-4 bg-gray-800/50 border border-green-500/30 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-3\",\n                                    children: uploadedMedia.map((mediaFile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 p-3 bg-gray-700/50 rounded-lg border border-gray-600/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: mediaFile.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"20\",\n                                                            height: \"20\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            className: \"text-blue-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2282,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2281,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2280,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"20\",\n                                                            height: \"20\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            className: \"text-purple-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2288,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2287,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2286,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2278,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-200 truncate\",\n                                                            children: mediaFile.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2296,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: [\n                                                                mediaFile.mimeType,\n                                                                \" • \",\n                                                                (mediaFile.size / 1024 / 1024).toFixed(2),\n                                                                \" MB\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2299,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: new Date(mediaFile.uploadedAt).toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2302,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2295,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>removeMediaFile(mediaFile.id),\n                                                        className: \"w-8 h-8 bg-red-500/20 text-red-400 rounded-lg hover:bg-red-500/30 transition-colors flex items-center justify-center\",\n                                                        title: \"删除文件\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"14\",\n                                                            height: \"14\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2315,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2314,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2309,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2308,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, mediaFile.id, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2273,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2271,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2270,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 2248,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2063,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SessionManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: showSessionManager,\n                onClose: ()=>setShowSessionManager(false),\n                onSessionSelect: handleSessionSelect,\n                currentSessionId: currentSessionId\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2331,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FileAssociationTreeDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showFileAssociation,\n                onClose: ()=>setShowFileAssociation(false),\n                artworkId: artworkId,\n                onFilesChange: handleFilesChange\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2339,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PromptManagerModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isOpen: showPromptManager,\n                onClose: ()=>setShowPromptManager(false),\n                onConfigSelect: handlePromptConfigSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2347,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultipleComparisonModal__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                isOpen: showMultipleComparison,\n                onClose: ()=>setShowMultipleComparison(false),\n                messageContent: comparisonMessageContent,\n                onResultSelect: handleComparisonResultSelect,\n                onSendMessage: onMultipleComparisonSend || onSendMessage,\n                targetMessageIndex: comparisonTriggerMessageIndex\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2354,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n        lineNumber: 1678,\n        columnNumber: 5\n    }, this);\n}, \"RfxeR6O005vLzWT/JUmeZeKO01c=\")), \"RfxeR6O005vLzWT/JUmeZeKO01c=\");\n_c1 = ChatInterface;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ChatInterface);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatInterface$React.forwardRef\");\n$RefreshReg$(_c1, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AIAssistant/ChatInterface.tsx\n"));

/***/ })

});