# 测试改进的 df 替换工具

## 测试场景

### 1. 精确匹配测试
```df
{
  "file": "test-file.txt",
  "type": "replace",
  "find": "Hello World",
  "content": "Hello Universe"
}
```

### 2. 大小写不敏感测试
```df
{
  "file": "test-file.txt", 
  "type": "replace",
  "find": "hello world",
  "content": "Hello Universe"
}
```

### 3. 空白字符标准化测试
```df
{
  "file": "test-file.txt",
  "type": "replace", 
  "find": "Hello    World\n\n  Test",
  "content": "Hello Universe Test"
}
```

### 4. 不存在内容测试（应该显示建议）
```df
{
  "file": "test-file.txt",
  "type": "replace",
  "find": "NonExistent Content",
  "content": "Replacement"
}
```

## 预期结果

1. **精确匹配**: 直接替换成功
2. **大小写不敏感**: 找到并替换，显示使用了 case-insensitive 策略
3. **空白字符标准化**: 找到并替换，显示使用了 whitespace-normalized 策略  
4. **不存在内容**: 显示错误信息和相似内容建议

## 改进功能

✅ **智能匹配策略**
- 精确匹配
- 忽略大小写匹配
- 空白字符标准化匹配
- 模糊匹配建议

✅ **详细错误信息**
- 显示搜索失败的具体原因
- 提供相似内容建议
- 包含行号和上下文

✅ **统一服务调用**
- 使用 DiffToolService.intelligentStringReplace()
- 移除前端的手动字符串处理
- 保持代码一致性

## 使用说明

现在 df 替换工具会自动：
1. 尝试精确匹配
2. 如果失败，尝试忽略大小写
3. 如果仍失败，尝试标准化空白字符
4. 如果都失败，提供相似内容建议

不需要手动配置任何选项，所有增强功能都是默认启用的。
