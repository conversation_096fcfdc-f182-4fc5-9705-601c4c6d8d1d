"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_hcl_hcl_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/hcl/hcl.js":
/*!**********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/hcl/hcl.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/hcl/hcl.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".hcl\",\n  keywords: [\n    \"var\",\n    \"local\",\n    \"path\",\n    \"for_each\",\n    \"any\",\n    \"string\",\n    \"number\",\n    \"bool\",\n    \"true\",\n    \"false\",\n    \"null\",\n    \"if \",\n    \"else \",\n    \"endif \",\n    \"for \",\n    \"in\",\n    \"endfor\"\n  ],\n  operators: [\n    \"=\",\n    \">=\",\n    \"<=\",\n    \"==\",\n    \"!=\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"&&\",\n    \"||\",\n    \"!\",\n    \"<\",\n    \">\",\n    \"?\",\n    \"...\",\n    \":\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  terraformFunctions: /(abs|ceil|floor|log|max|min|pow|signum|chomp|format|formatlist|indent|join|lower|regex|regexall|replace|split|strrev|substr|title|trimspace|upper|chunklist|coalesce|coalescelist|compact|concat|contains|distinct|element|flatten|index|keys|length|list|lookup|map|matchkeys|merge|range|reverse|setintersection|setproduct|setunion|slice|sort|transpose|values|zipmap|base64decode|base64encode|base64gzip|csvdecode|jsondecode|jsonencode|urlencode|yamldecode|yamlencode|abspath|dirname|pathexpand|basename|file|fileexists|fileset|filebase64|templatefile|formatdate|timeadd|timestamp|base64sha256|base64sha512|bcrypt|filebase64sha256|filebase64sha512|filemd5|filemd1|filesha256|filesha512|md5|rsadecrypt|sha1|sha256|sha512|uuid|uuidv5|cidrhost|cidrnetmask|cidrsubnet|tobool|tolist|tomap|tonumber|toset|tostring)/,\n  terraformMainBlocks: /(module|data|terraform|resource|provider|variable|output|locals)/,\n  tokenizer: {\n    root: [\n      // highlight main blocks\n      [\n        /^@terraformMainBlocks([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)(\\{)/,\n        [\"type\", \"\", \"string\", \"\", \"string\", \"\", \"@brackets\"]\n      ],\n      // highlight all the remaining blocks\n      [\n        /(\\w+[ \\t]+)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)(\\{)/,\n        [\"identifier\", \"\", \"string\", \"\", \"string\", \"\", \"@brackets\"]\n      ],\n      // highlight block\n      [\n        /(\\w+[ \\t]+)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)([ \\t]*)([\\w-]+|\"[\\w-]+\"|)(=)(\\{)/,\n        [\"identifier\", \"\", \"string\", \"\", \"operator\", \"\", \"@brackets\"]\n      ],\n      // terraform general highlight - shared with expressions\n      { include: \"@terraform\" }\n    ],\n    terraform: [\n      // highlight terraform functions\n      [/@terraformFunctions(\\()/, [\"type\", \"@brackets\"]],\n      // all other words are variables or keywords\n      [\n        /[a-zA-Z_]\\w*-*/,\n        // must work with variables such as foo-bar and also with negative numbers\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"variable\"\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      { include: \"@heredoc\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d*\\d+[eE]([\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d[\\d']*/, \"number\"],\n      [/\\d/, \"number\"],\n      [/[;,.]/, \"delimiter\"],\n      // delimiter: after number because of .\\d floats\n      // strings\n      [/\"/, \"string\", \"@string\"],\n      // this will include expressions\n      [/'/, \"invalid\"]\n    ],\n    heredoc: [\n      [/<<[-]*\\s*[\"]?([\\w\\-]+)[\"]?/, { token: \"string.heredoc.delimiter\", next: \"@heredocBody.$1\" }]\n    ],\n    heredocBody: [\n      [\n        /([\\w\\-]+)$/,\n        {\n          cases: {\n            \"$1==$S2\": [\n              {\n                token: \"string.heredoc.delimiter\",\n                next: \"@popall\"\n              }\n            ],\n            \"@default\": \"string.heredoc\"\n          }\n        }\n      ],\n      [/./, \"string.heredoc\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"],\n      [/#.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/\\$\\{/, { token: \"delimiter\", next: \"@stringExpression\" }],\n      [/[^\\\\\"\\$]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@popall\"]\n    ],\n    stringInsideExpression: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    stringExpression: [\n      [/\\}/, { token: \"delimiter\", next: \"@pop\" }],\n      [/\"/, \"string\", \"@stringInsideExpression\"],\n      { include: \"@terraform\" }\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/hcl/hcl.js\n"));

/***/ })

}]);