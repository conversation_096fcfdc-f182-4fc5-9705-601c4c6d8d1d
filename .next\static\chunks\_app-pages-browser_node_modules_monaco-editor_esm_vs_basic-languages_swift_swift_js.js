"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_swift_swift_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/swift/swift.js":
/*!**************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/swift/swift.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/swift/swift.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".swift\",\n  // TODO(owensd): Support the full range of unicode valid identifiers.\n  identifier: /[a-zA-Z_][\\w$]*/,\n  attributes: [\n    \"@GKInspectable\",\n    \"@IBAction\",\n    \"@IBDesignable\",\n    \"@IBInspectable\",\n    \"@IBOutlet\",\n    \"@IBSegueAction\",\n    \"@NSApplicationMain\",\n    \"@NSCopying\",\n    \"@NSManaged\",\n    \"@Sendable\",\n    \"@UIApplicationMain\",\n    \"@autoclosure\",\n    \"@actorIndependent\",\n    \"@asyncHandler\",\n    \"@available\",\n    \"@convention\",\n    \"@derivative\",\n    \"@differentiable\",\n    \"@discardableResult\",\n    \"@dynamicCallable\",\n    \"@dynamicMemberLookup\",\n    \"@escaping\",\n    \"@frozen\",\n    \"@globalActor\",\n    \"@inlinable\",\n    \"@inline\",\n    \"@main\",\n    \"@noDerivative\",\n    \"@nonobjc\",\n    \"@noreturn\",\n    \"@objc\",\n    \"@objcMembers\",\n    \"@preconcurrency\",\n    \"@propertyWrapper\",\n    \"@requires_stored_property_inits\",\n    \"@resultBuilder\",\n    \"@testable\",\n    \"@unchecked\",\n    \"@unknown\",\n    \"@usableFromInline\",\n    \"@warn_unqualified_access\"\n  ],\n  accessmodifiers: [\"open\", \"public\", \"internal\", \"fileprivate\", \"private\"],\n  keywords: [\n    \"#available\",\n    \"#colorLiteral\",\n    \"#column\",\n    \"#dsohandle\",\n    \"#else\",\n    \"#elseif\",\n    \"#endif\",\n    \"#error\",\n    \"#file\",\n    \"#fileID\",\n    \"#fileLiteral\",\n    \"#filePath\",\n    \"#function\",\n    \"#if\",\n    \"#imageLiteral\",\n    \"#keyPath\",\n    \"#line\",\n    \"#selector\",\n    \"#sourceLocation\",\n    \"#warning\",\n    \"Any\",\n    \"Protocol\",\n    \"Self\",\n    \"Type\",\n    \"actor\",\n    \"as\",\n    \"assignment\",\n    \"associatedtype\",\n    \"associativity\",\n    \"async\",\n    \"await\",\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"class\",\n    \"continue\",\n    \"convenience\",\n    \"default\",\n    \"defer\",\n    \"deinit\",\n    \"didSet\",\n    \"do\",\n    \"dynamic\",\n    \"dynamicType\",\n    \"else\",\n    \"enum\",\n    \"extension\",\n    \"fallthrough\",\n    \"false\",\n    \"fileprivate\",\n    \"final\",\n    \"for\",\n    \"func\",\n    \"get\",\n    \"guard\",\n    \"higherThan\",\n    \"if\",\n    \"import\",\n    \"in\",\n    \"indirect\",\n    \"infix\",\n    \"init\",\n    \"inout\",\n    \"internal\",\n    \"is\",\n    \"isolated\",\n    \"lazy\",\n    \"left\",\n    \"let\",\n    \"lowerThan\",\n    \"mutating\",\n    \"nil\",\n    \"none\",\n    \"nonisolated\",\n    \"nonmutating\",\n    \"open\",\n    \"operator\",\n    \"optional\",\n    \"override\",\n    \"postfix\",\n    \"precedence\",\n    \"precedencegroup\",\n    \"prefix\",\n    \"private\",\n    \"protocol\",\n    \"public\",\n    \"repeat\",\n    \"required\",\n    \"rethrows\",\n    \"return\",\n    \"right\",\n    \"safe\",\n    \"self\",\n    \"set\",\n    \"some\",\n    \"static\",\n    \"struct\",\n    \"subscript\",\n    \"super\",\n    \"switch\",\n    \"throw\",\n    \"throws\",\n    \"true\",\n    \"try\",\n    \"typealias\",\n    \"unowned\",\n    \"unsafe\",\n    \"var\",\n    \"weak\",\n    \"where\",\n    \"while\",\n    \"willSet\",\n    \"__consuming\",\n    \"__owned\"\n  ],\n  symbols: /[=(){}\\[\\].,:;@#\\_&\\-<>`?!+*\\\\\\/]/,\n  // Moved . to operatorstart so it can be a delimiter\n  operatorstart: /[\\/=\\-+!*%<>&|^~?\\u00A1-\\u00A7\\u00A9\\u00AB\\u00AC\\u00AE\\u00B0-\\u00B1\\u00B6\\u00BB\\u00BF\\u00D7\\u00F7\\u2016-\\u2017\\u2020-\\u2027\\u2030-\\u203E\\u2041-\\u2053\\u2055-\\u205E\\u2190-\\u23FF\\u2500-\\u2775\\u2794-\\u2BFF\\u2E00-\\u2E7F\\u3001-\\u3003\\u3008-\\u3030]/,\n  operatorend: /[\\u0300-\\u036F\\u1DC0-\\u1DFF\\u20D0-\\u20FF\\uFE00-\\uFE0F\\uFE20-\\uFE2F\\uE0100-\\uE01EF]/,\n  operators: /(@operatorstart)((@operatorstart)|(@operatorend))*/,\n  // TODO(owensd): These are borrowed from C#; need to validate correctness for Swift.\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comment\" },\n      { include: \"@attribute\" },\n      { include: \"@literal\" },\n      { include: \"@keyword\" },\n      { include: \"@invokedmethod\" },\n      { include: \"@symbol\" }\n    ],\n    whitespace: [\n      [/\\s+/, \"white\"],\n      [/\"\"\"/, \"string.quote\", \"@endDblDocString\"]\n    ],\n    endDblDocString: [\n      [/[^\"]+/, \"string\"],\n      [/\\\\\"/, \"string\"],\n      [/\"\"\"/, \"string.quote\", \"@popall\"],\n      [/\"/, \"string\"]\n    ],\n    symbol: [\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [/[.]/, \"delimiter\"],\n      [/@operators/, \"operator\"],\n      [/@symbols/, \"operator\"]\n    ],\n    comment: [\n      [/\\/\\/\\/.*$/, \"comment.doc\"],\n      [/\\/\\*\\*/, \"comment.doc\", \"@commentdocbody\"],\n      [/\\/\\/.*$/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@commentbody\"]\n    ],\n    commentdocbody: [\n      [/\\/\\*/, \"comment\", \"@commentbody\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/\\:[a-zA-Z]+\\:/, \"comment.doc.param\"],\n      [/./, \"comment.doc\"]\n    ],\n    commentbody: [\n      [/\\/\\*/, \"comment\", \"@commentbody\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/./, \"comment\"]\n    ],\n    attribute: [\n      [\n        /@@@identifier/,\n        {\n          cases: {\n            \"@attributes\": \"keyword.control\",\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ],\n    literal: [\n      [/\"/, { token: \"string.quote\", next: \"@stringlit\" }],\n      [/0[b]([01]_?)+/, \"number.binary\"],\n      [/0[o]([0-7]_?)+/, \"number.octal\"],\n      [/0[x]([0-9a-fA-F]_?)+([pP][\\-+](\\d_?)+)?/, \"number.hex\"],\n      [/(\\d_?)*\\.(\\d_?)+([eE][\\-+]?(\\d_?)+)?/, \"number.float\"],\n      [/(\\d_?)+/, \"number\"]\n    ],\n    stringlit: [\n      [/\\\\\\(/, { token: \"operator\", next: \"@interpolatedexpression\" }],\n      [/@escapes/, \"string\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }],\n      [/./, \"string\"]\n    ],\n    interpolatedexpression: [\n      [/\\(/, { token: \"operator\", next: \"@interpolatedexpression\" }],\n      [/\\)/, { token: \"operator\", next: \"@pop\" }],\n      { include: \"@literal\" },\n      { include: \"@keyword\" },\n      { include: \"@symbol\" }\n    ],\n    keyword: [\n      [/`/, { token: \"operator\", next: \"@escapedkeyword\" }],\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"[A-Z][a-zA-Z0-9$]*\": \"type.identifier\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    escapedkeyword: [\n      [/`/, { token: \"operator\", next: \"@pop\" }],\n      [/./, \"identifier\"]\n    ],\n    invokedmethod: [\n      [\n        /([.])(@identifier)/,\n        {\n          cases: {\n            $2: [\"delimeter\", \"type.identifier\"],\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ]\n  }\n};\n\n/*!---------------------------------------------------------------------------------------------\n *  Copyright (C) David Owens II, owensd.io. All rights reserved.\n *--------------------------------------------------------------------------------------------*/\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/swift/swift.js\n"));

/***/ })

}]);