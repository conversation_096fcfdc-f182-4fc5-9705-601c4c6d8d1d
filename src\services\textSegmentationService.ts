/**
 * 文本分割服务
 * 提供智能文本分割的核心算法，支持按字数限制和句子边界进行分割
 */

import { TextSegment, SegmentOptions, SegmentationResult } from '@/types'

export class TextSegmentationService {
  private static instance: TextSegmentationService

  private constructor() {}

  /**
   * 获取文本分割服务单例
   */
  public static getInstance(): TextSegmentationService {
    if (!TextSegmentationService.instance) {
      TextSegmentationService.instance = new TextSegmentationService()
    }
    return TextSegmentationService.instance
  }

  /**
   * 生成UUID
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }

  /**
   * 智能分割文本
   * @param text 要分割的文本
   * @param fileId 文件ID
   * @param options 分割选项
   * @returns 分割结果
   */
  public intelligentSegment(
    text: string, 
    fileId: string, 
    options: SegmentOptions
  ): SegmentationResult {
    const startTime = Date.now()
    
    // 预处理文本
    const processedText = this.preprocessText(text, options)
    
    // 检测语言类型
    const detectedLanguage = this.detectLanguage(processedText, options.language)
    
    // 分割成句子
    const sentences = this.detectSentenceBoundaries(processedText, detectedLanguage)
    
    // 按字数限制分组
    const segments = this.groupSentencesByWordLimit(
      sentences, 
      fileId, 
      detectedLanguage, 
      options
    )
    
    const processingTime = Date.now() - startTime
    const totalWords = segments.reduce((sum, segment) => sum + segment.wordCount, 0)
    
    return {
      segments,
      totalSegments: segments.length,
      totalWords,
      averageWordsPerSegment: segments.length > 0 ? Math.round(totalWords / segments.length) : 0,
      processingTime
    }
  }

  /**
   * 预处理文本
   * @param text 原始文本
   * @param options 分割选项
   * @returns 处理后的文本
   */
  private preprocessText(text: string, options: SegmentOptions): string {
    let processedText = text

    // 保护代码块
    if (options.preserveCodeBlocks) {
      processedText = this.protectCodeBlocks(processedText)
    }

    // 标准化换行符
    processedText = processedText.replace(/\r\n/g, '\n').replace(/\r/g, '\n')

    // 清理多余的空白字符，但保持段落结构
    if (options.preserveParagraphs) {
      // 保持段落分隔和单行换行，但清理行内多余空格
      processedText = processedText
        .split('\n\n')
        .map(paragraph => {
          // 保持单行换行符，只清理连续的空格和制表符
          return paragraph
            .split('\n')
            .map(line => line.replace(/[ \t]+/g, ' ').trim())
            .filter(line => line.length > 0)
            .join('\n')
        })
        .filter(paragraph => paragraph.length > 0)
        .join('\n\n')
    } else {
      // 简单清理空白字符，但保持换行符
      processedText = processedText
        .split('\n')
        .map(line => line.replace(/[ \t]+/g, ' ').trim())
        .filter(line => line.length > 0)
        .join('\n')
    }

    return processedText
  }

  // 存储当前处理的代码块
  private currentCodeBlocks: string[] = []

  /**
   * 保护代码块，避免在代码块内部分割
   * @param text 文本
   * @returns 处理后的文本
   */
  private protectCodeBlocks(text: string): string {
    // 重置代码块存储
    this.currentCodeBlocks = []
    
    // 匹配代码块（```...``` 和 `...`）
    const codeBlockRegex = /```[\s\S]*?```|`[^`\n]*`/g
    
    // 替换代码块为占位符
    const processedText = text.replace(codeBlockRegex, (match) => {
      const placeholder = `__CODE_BLOCK_${this.currentCodeBlocks.length}__`
      this.currentCodeBlocks.push(match)
      return placeholder
    })
    
    return processedText
  }

  /**
   * 恢复代码块
   * @param text 包含占位符的文本
   * @returns 恢复后的文本
   */
  private restoreCodeBlocks(text: string): string {
    // 如果没有代码块或没有占位符，直接返回
    if (this.currentCodeBlocks.length === 0 || !text.includes('__CODE_BLOCK_')) {
      return text
    }

    let restoredText = text
    this.currentCodeBlocks.forEach((codeBlock: string, index: number) => {
      const placeholder = `__CODE_BLOCK_${index}__`
      restoredText = restoredText.replace(new RegExp(placeholder, 'g'), codeBlock)
    })
    
    return restoredText
  }

  /**
   * 检测文本语言
   * @param text 文本
   * @param languageHint 语言提示
   * @returns 检测到的语言
   */
  private detectLanguage(text: string, languageHint: 'zh' | 'en' | 'auto'): 'zh' | 'en' | 'mixed' {
    if (languageHint !== 'auto') {
      return languageHint
    }

    // 统计中文字符和英文单词
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length
    const totalChars = text.length

    // 如果中文字符占比超过30%，认为是中文
    if (chineseChars / totalChars > 0.3) {
      return englishWords > chineseChars * 0.5 ? 'mixed' : 'zh'
    }
    
    // 如果英文单词较多，认为是英文
    if (englishWords > 10) {
      return chineseChars > englishWords * 0.3 ? 'mixed' : 'en'
    }

    // 默认返回混合
    return 'mixed'
  }

  /**
   * 检测句子边界
   * @param text 文本
   * @param language 语言类型
   * @returns 句子数组
   */
  private detectSentenceBoundaries(text: string, language: 'zh' | 'en' | 'mixed'): string[] {
    let sentences: string[] = []

    if (language === 'zh' || language === 'mixed') {
      // 中文句子分割模式
      sentences = this.splitChineseSentences(text)
    } else {
      // 英文句子分割模式
      sentences = this.splitEnglishSentences(text)
    }

    // 清理空句子和过短的句子
    return sentences
      .map(sentence => sentence.trim())
      .filter(sentence => sentence.length > 0)
  }

  /**
   * 分割中文句子
   * @param text 文本
   * @returns 句子数组
   */
  private splitChineseSentences(text: string): string[] {
    // 中文句子结束标点：。！？；
    // 考虑引号、括号等配对标点
    const sentenceEndRegex = /([。！？；](?:["""''）】}]*)?)/g
    
    const sentences: string[] = []
    let lastIndex = 0
    let match

    while ((match = sentenceEndRegex.exec(text)) !== null) {
      const sentence = text.substring(lastIndex, match.index + match[0].length).trim()
      if (sentence.length > 0) {
        sentences.push(sentence)
      }
      lastIndex = match.index + match[0].length
    }

    // 处理最后一部分文本
    if (lastIndex < text.length) {
      const lastSentence = text.substring(lastIndex).trim()
      if (lastSentence.length > 0) {
        sentences.push(lastSentence)
      }
    }

    return sentences
  }

  /**
   * 分割英文句子
   * @param text 文本
   * @returns 句子数组
   */
  private splitEnglishSentences(text: string): string[] {
    // 英文句子结束标点：. ! ? ;
    // 需要考虑缩写词（如 Mr. Dr. etc.）
    const abbreviations = ['Mr', 'Mrs', 'Dr', 'Prof', 'etc', 'vs', 'e.g', 'i.e']
    
    // 先处理缩写词，避免误分割
    let processedText = text
    abbreviations.forEach(abbr => {
      const regex = new RegExp(`\\b${abbr}\\.`, 'gi')
      processedText = processedText.replace(regex, `${abbr}__DOT__`)
    })

    // 分割句子
    const sentenceEndRegex = /([.!?;](?:["')\]]*)?)\s+/g
    const sentences: string[] = []
    let lastIndex = 0
    let match

    while ((match = sentenceEndRegex.exec(processedText)) !== null) {
      const sentence = processedText.substring(lastIndex, match.index + match[1].length).trim()
      if (sentence.length > 0) {
        sentences.push(sentence)
      }
      lastIndex = match.index + match[0].length
    }

    // 处理最后一部分文本
    if (lastIndex < processedText.length) {
      const lastSentence = processedText.substring(lastIndex).trim()
      if (lastSentence.length > 0) {
        sentences.push(lastSentence)
      }
    }

    // 恢复缩写词中的点号
    return sentences.map(sentence => sentence.replace(/__DOT__/g, '.'))
  }

  /**
   * 按字数限制分组句子
   * @param sentences 句子数组
   * @param fileId 文件ID
   * @param language 语言类型
   * @param options 分割选项
   * @returns 文本段落数组
   */
  private groupSentencesByWordLimit(
    sentences: string[], 
    fileId: string, 
    language: 'zh' | 'en' | 'mixed',
    options: SegmentOptions
  ): TextSegment[] {
    const segments: TextSegment[] = []
    let currentSegment = ''
    let currentWordCount = 0
    let currentSentenceCount = 0
    let startIndex = 0

    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i]
      const sentenceWordCount = this.countWords(sentence, language)

      // 如果单个句子就超过最大字数限制，需要强制分割
      if (sentenceWordCount > options.maxWords) {
        // 先保存当前段落（如果有内容）
        if (currentSegment.trim().length > 0) {
          segments.push(this.createTextSegment(
            fileId,
            currentSegment.trim(),
            startIndex,
            startIndex + currentSegment.length,
            currentWordCount,
            currentSentenceCount,
            language
          ))
        }

        // 强制分割长句子
        const forceSplitSegments = this.forceSplitLongSentence(
          sentence, 
          fileId, 
          language, 
          options.maxWords,
          startIndex + currentSegment.length
        )
        segments.push(...forceSplitSegments)

        // 重置当前段落
        currentSegment = ''
        currentWordCount = 0
        currentSentenceCount = 0
        startIndex = startIndex + currentSegment.length + sentence.length
        continue
      }

      // 检查是否可以添加到当前段落
      if (currentWordCount + sentenceWordCount <= options.maxWords) {
        // 可以添加
        if (currentSegment.length > 0) {
          currentSegment += ' '
        }
        currentSegment += sentence
        currentWordCount += sentenceWordCount
        currentSentenceCount++
      } else {
        // 不能添加，需要创建新段落
        if (currentSegment.trim().length > 0) {
          segments.push(this.createTextSegment(
            fileId,
            currentSegment.trim(),
            startIndex,
            startIndex + currentSegment.length,
            currentWordCount,
            currentSentenceCount,
            language
          ))
        }

        // 开始新段落
        startIndex = startIndex + currentSegment.length
        currentSegment = sentence
        currentWordCount = sentenceWordCount
        currentSentenceCount = 1
      }
    }

    // 处理最后一个段落
    if (currentSegment.trim().length > 0) {
      // 检查是否满足最小字数要求
      if (!options.minWords || currentWordCount >= options.minWords) {
        segments.push(this.createTextSegment(
          fileId,
          currentSegment.trim(),
          startIndex,
          startIndex + currentSegment.length,
          currentWordCount,
          currentSentenceCount,
          language
        ))
      } else if (segments.length > 0) {
        // 如果不满足最小字数，合并到上一个段落
        const lastSegment = segments[segments.length - 1]
        lastSegment.content += ' ' + currentSegment.trim()
        lastSegment.endIndex = startIndex + currentSegment.length
        lastSegment.wordCount += currentWordCount
        lastSegment.sentenceCount += currentSentenceCount
      } else {
        // 如果是唯一的段落，即使不满足最小字数也要保留
        segments.push(this.createTextSegment(
          fileId,
          currentSegment.trim(),
          startIndex,
          startIndex + currentSegment.length,
          currentWordCount,
          currentSentenceCount,
          language
        ))
      }
    }

    return segments
  }

  /**
   * 强制分割过长的句子
   * @param sentence 长句子
   * @param fileId 文件ID
   * @param language 语言类型
   * @param maxWords 最大字数
   * @param baseIndex 基础索引
   * @returns 分割后的段落数组
   */
  private forceSplitLongSentence(
    sentence: string, 
    fileId: string, 
    language: 'zh' | 'en' | 'mixed',
    maxWords: number,
    baseIndex: number
  ): TextSegment[] {
    const segments: TextSegment[] = []
    const words = language === 'zh' ? sentence.split('') : sentence.split(/\s+/)
    
    let currentChunk = ''
    let currentIndex = baseIndex

    for (let i = 0; i < words.length; i += maxWords) {
      const chunk = words.slice(i, i + maxWords)
      const chunkText = language === 'zh' ? chunk.join('') : chunk.join(' ')
      const wordCount = this.countWords(chunkText, language)
      
      segments.push(this.createTextSegment(
        fileId,
        chunkText,
        currentIndex,
        currentIndex + chunkText.length,
        wordCount,
        1, // 强制分割的片段算作1个句子
        language
      ))
      
      currentIndex += chunkText.length
    }

    return segments
  }

  /**
   * 统计字数
   * @param text 文本
   * @param language 语言类型
   * @returns 字数
   */
  private countWords(text: string, language: 'zh' | 'en' | 'mixed'): number {
    if (language === 'zh') {
      // 中文按字符数计算
      return text.replace(/\s/g, '').length
    } else if (language === 'en') {
      // 英文按单词数计算
      return text.trim().split(/\s+/).filter(word => word.length > 0).length
    } else {
      // 混合语言：中文字符数 + 英文单词数
      const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length
      const englishWords = (text.match(/[a-zA-Z]+/g) || []).length
      return chineseChars + englishWords
    }
  }

  /**
   * 创建文本段落对象
   * @param fileId 文件ID
   * @param content 内容
   * @param startIndex 起始索引
   * @param endIndex 结束索引
   * @param wordCount 字数
   * @param sentenceCount 句子数
   * @param language 语言类型
   * @returns 文本段落对象
   */
  private createTextSegment(
    fileId: string,
    content: string,
    startIndex: number,
    endIndex: number,
    wordCount: number,
    sentenceCount: number,
    language: 'zh' | 'en' | 'mixed'
  ): TextSegment {
    // 恢复代码块（如果有）
    const restoredContent = this.restoreCodeBlocks(content)
    
    return {
      id: this.generateUUID(),
      fileId,
      content: restoredContent,
      startIndex,
      endIndex,
      wordCount,
      sentenceCount,
      language,
      createdAt: Date.now()
    }
  }

  /**
   * 为文本内容添加句子引入标记
   * @param content 原始内容
   * @param language 语言类型
   * @returns 添加标记后的内容
   */
  public addSentenceMarkers(content: string, language: 'zh' | 'en' | 'mixed' = 'auto'): string {
    // 检测语言类型
    const detectedLanguage = language === 'auto' ? this.detectLanguage(content, 'auto') : language
    
    // 分割成句子
    const sentences = this.detectSentenceBoundaries(content, detectedLanguage)
    
    // 为每个句子添加引入标记
    const markedSentences = sentences.map(sentence => {
      const trimmedSentence = sentence.trim()
      if (trimmedSentence.length === 0) {
        return ''
      }
      return `${trimmedSentence}`
    }).filter(sentence => sentence.length > 0)
    
    // 用换行符连接句子，作为句子间的分隔标记
    return markedSentences.join('\n\n')
  }

  /**
   * 按字数分割文本（简单模式）
   * @param text 文本
   * @param fileId 文件ID
   * @param maxWords 最大字数
   * @returns 文本段落数组
   */
  public segmentByWordCount(text: string, fileId: string, maxWords: number = 500): TextSegment[] {
    const options: SegmentOptions = {
      maxWords,
      language: 'auto',
      preserveParagraphs: false,
      preserveCodeBlocks: false
    }

    const result = this.intelligentSegment(text, fileId, options)
    return result.segments
  }
}