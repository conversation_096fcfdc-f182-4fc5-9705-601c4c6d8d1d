"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_services_fontPersistenceService_ts";
exports.ids = ["_ssr_src_services_fontPersistenceService_ts"];
exports.modules = {

/***/ "(ssr)/./src/services/fontPersistenceService.ts":
/*!************************************************!*\
  !*** ./src/services/fontPersistenceService.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FontPersistenceService: () => (/* binding */ FontPersistenceService)\n/* harmony export */ });\n/* harmony import */ var _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database/DatabaseService */ \"(ssr)/./src/services/database/DatabaseService.ts\");\n/**\r\n * 字体持久化服务\r\n * 管理字体应用状态的持久化、恢复和历史记录\r\n */ \nclass FontPersistenceService {\n    constructor(){\n        this.configStoreName = \"font-configs\";\n        this.historyStoreName = \"font-history\";\n        this.localStorageKey = \"font-active-config\";\n        this.maxHistoryRecords = 50;\n        this.dbService = _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__.DatabaseService.getInstance();\n    }\n    /**\r\n   * 获取字体持久化服务单例\r\n   */ static getInstance() {\n        if (!FontPersistenceService.instance) {\n            FontPersistenceService.instance = new FontPersistenceService();\n        }\n        return FontPersistenceService.instance;\n    }\n    /**\r\n   * 生成UUID\r\n   */ generateUUID() {\n        return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n            const r = Math.random() * 16 | 0;\n            const v = c === \"x\" ? r : r & 0x3 | 0x8;\n            return v.toString(16);\n        });\n    }\n    /**\r\n   * 创建错误对象\r\n   */ createError(type, message, fontId, details) {\n        return {\n            type,\n            message,\n            fontId,\n            timestamp: Date.now(),\n            details\n        };\n    }\n    /**\r\n   * 保存字体应用配置\r\n   */ async saveApplicationConfig(config) {\n        try {\n            console.log(\"\\uD83D\\uDCBE 保存字体应用配置:\", config.fontFamily);\n            // 1. 先清除之前的活跃配置\n            await this.clearActiveConfigs();\n            // 2. 设置当前配置为活跃状态\n            config.isActive = true;\n            config.appliedAt = Date.now();\n            // 3. 保存到 IndexedDB\n            const dbResult = await this.dbService.add(this.configStoreName, config);\n            if (!dbResult.success) {\n                throw new Error(dbResult.error || \"保存到IndexedDB失败\");\n            }\n            // 4. 同步保存到 localStorage（快速访问）\n            const quickConfig = {\n                id: config.id,\n                fontId: config.fontId,\n                fontFamily: config.fontFamily,\n                appliedAt: config.appliedAt,\n                scope: config.scope\n            };\n            localStorage.setItem(this.localStorageKey, JSON.stringify(quickConfig));\n            // 5. 更新历史记录\n            await this.updateHistory(config);\n            console.log(\"✅ 字体应用配置保存成功\");\n            return {\n                success: true\n            };\n        } catch (error) {\n            const errorMessage = `保存字体应用配置失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取当前活跃的字体配置\r\n   */ async getActiveConfig() {\n        try {\n            // 1. 优先从 localStorage 读取（快速访问）\n            const quickConfigStr = localStorage.getItem(this.localStorageKey);\n            if (quickConfigStr) {\n                const quickConfig = JSON.parse(quickConfigStr);\n                // 2. 从 IndexedDB 获取完整配置\n                const fullConfigResult = await this.dbService.get(this.configStoreName, quickConfig.id);\n                if (fullConfigResult.success && fullConfigResult.data) {\n                    return {\n                        success: true,\n                        data: fullConfigResult.data\n                    };\n                }\n            }\n            // 3. 降级方案：直接从 IndexedDB 查找活跃配置\n            const allConfigsResult = await this.dbService.getAll(this.configStoreName);\n            if (allConfigsResult.success && allConfigsResult.data) {\n                const activeConfig = allConfigsResult.data.find((config)=>config.isActive);\n                return {\n                    success: true,\n                    data: activeConfig || null\n                };\n            }\n            return {\n                success: true,\n                data: null\n            };\n        } catch (error) {\n            const errorMessage = `获取活跃字体配置失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 恢复字体应用状态\r\n   */ async restoreFontApplication() {\n        try {\n            console.log(\"\\uD83D\\uDD04 开始恢复字体应用状态...\");\n            const configResult = await this.getActiveConfig();\n            if (!configResult.success) {\n                throw new Error(configResult.error || \"获取活跃配置失败\");\n            }\n            if (!configResult.data) {\n                console.log(\"\\uD83D\\uDCDD 没有找到活跃的字体配置\");\n                return {\n                    success: true,\n                    data: false\n                };\n            }\n            const config = configResult.data;\n            console.log(\"\\uD83C\\uDFAF 找到活跃配置:\", config.fontFamily);\n            // 应用字体到页面\n            await this.applyFontToPage(config);\n            console.log(\"✅ 字体应用状态恢复成功\");\n            return {\n                success: true,\n                data: true\n            };\n        } catch (error) {\n            const errorMessage = `恢复字体应用状态失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 清除字体应用状态\r\n   */ async clearApplicationState() {\n        try {\n            console.log(\"\\uD83E\\uDDF9 清除字体应用状态...\");\n            // 1. 清除 localStorage\n            localStorage.removeItem(this.localStorageKey);\n            // 2. 清除 IndexedDB 中的活跃配置\n            await this.clearActiveConfigs();\n            // 3. 重置页面字体为默认\n            this.resetPageFont();\n            console.log(\"✅ 字体应用状态已清除\");\n            return {\n                success: true\n            };\n        } catch (error) {\n            const errorMessage = `清除字体应用状态失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取字体应用历史\r\n   */ async getApplicationHistory(limit = 20) {\n        try {\n            const result = await this.dbService.getAll(this.historyStoreName);\n            if (result.success && result.data) {\n                // 按应用时间倒序排列，并限制数量\n                const sortedHistory = result.data.sort((a, b)=>b.appliedAt - a.appliedAt).slice(0, limit);\n                return {\n                    success: true,\n                    data: sortedHistory\n                };\n            }\n            return {\n                success: true,\n                data: []\n            };\n        } catch (error) {\n            const errorMessage = `获取字体应用历史失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 清除所有活跃配置\r\n   */ async clearActiveConfigs() {\n        const allConfigsResult = await this.dbService.getAll(this.configStoreName);\n        if (allConfigsResult.success && allConfigsResult.data) {\n            const activeConfigs = allConfigsResult.data.filter((config)=>config.isActive);\n            for (const config of activeConfigs){\n                config.isActive = false;\n                await this.dbService.put(this.configStoreName, config);\n            }\n        }\n    }\n    /**\r\n   * 更新历史记录\r\n   */ async updateHistory(config) {\n        try {\n            // 检查是否已存在相同字体的历史记录\n            const historyResult = await this.getApplicationHistory();\n            if (historyResult.success && historyResult.data) {\n                const existingHistory = historyResult.data.find((h)=>h.fontId === config.fontId);\n                if (existingHistory) {\n                    // 更新现有记录\n                    existingHistory.appliedAt = config.appliedAt;\n                    existingHistory.config = {\n                        ...config\n                    };\n                    await this.dbService.put(this.historyStoreName, existingHistory);\n                } else {\n                    // 创建新的历史记录\n                    const historyRecord = {\n                        id: this.generateUUID(),\n                        fontId: config.fontId,\n                        fontName: config.fontFamily,\n                        fontFamily: config.fontFamily,\n                        appliedAt: config.appliedAt,\n                        duration: 0,\n                        config: {\n                            ...config\n                        }\n                    };\n                    await this.dbService.add(this.historyStoreName, historyRecord);\n                }\n            }\n            // 清理过多的历史记录\n            await this.cleanupHistory();\n        } catch (error) {\n            console.warn(\"更新历史记录失败:\", error);\n        }\n    }\n    /**\r\n   * 清理过多的历史记录\r\n   */ async cleanupHistory() {\n        try {\n            const historyResult = await this.getApplicationHistory(this.maxHistoryRecords + 10);\n            if (historyResult.success && historyResult.data && historyResult.data.length > this.maxHistoryRecords) {\n                const sortedHistory = historyResult.data.sort((a, b)=>b.appliedAt - a.appliedAt);\n                const toDelete = sortedHistory.slice(this.maxHistoryRecords);\n                for (const record of toDelete){\n                    await this.dbService.delete(this.historyStoreName, record.id);\n                }\n                console.log(`🧹 清理了 ${toDelete.length} 条过期历史记录`);\n            }\n        } catch (error) {\n            console.warn(\"清理历史记录失败:\", error);\n        }\n    }\n    /**\r\n   * 应用字体到页面\r\n   */ async applyFontToPage(config) {\n        try {\n            // 首先需要确保字体文件已加载到CSS\n            const { FontService } = await __webpack_require__.e(/*! import() */ \"_ssr_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./fontService */ \"(ssr)/./src/services/fontService.ts\"));\n            const fontService = FontService.getInstance();\n            // 加载字体到CSS\n            const loadResult = await fontService.loadFontToCSS(config.fontId);\n            if (!loadResult.success) {\n                throw new Error(`字体加载失败: ${loadResult.error}`);\n            }\n            // 等待字体加载完成\n            await document.fonts.ready;\n            // 更新CSS变量\n            document.documentElement.style.setProperty(\"--font-family-applied\", `'${config.fontFamily}', sans-serif`);\n            document.documentElement.style.setProperty(\"--font-family-handwritten\", `'${config.fontFamily}', cursive, var(--font-family-primary)`);\n            // 根据应用范围应用字体\n            if (config.scope.type === \"global\") {\n                // 全局应用 - 确保使用用户上传的字体\n                const elementsToApply = [\n                    \"body\",\n                    \".font-applied\",\n                    \".font-handwritten\",\n                    \"h1, h2, h3, h4, h5, h6\",\n                    \".artwork-title\",\n                    \".card-title\",\n                    \".welcome-content\",\n                    \".btn\",\n                    \"p, span, div, label, input, textarea\",\n                    \".handdrawn-text\",\n                    \".font-primary\"\n                ];\n                elementsToApply.forEach((selector)=>{\n                    try {\n                        const elements = document.querySelectorAll(selector);\n                        elements.forEach((element)=>{\n                            element.style.fontFamily = `'${config.fontFamily}', sans-serif`;\n                            // 添加重要性标记确保样式生效\n                            element.style.setProperty(\"font-family\", `'${config.fontFamily}', sans-serif`, \"important\");\n                        });\n                    } catch (e) {\n                    // 忽略选择器错误\n                    }\n                });\n                // 强制应用到body元素\n                document.body.style.setProperty(\"font-family\", `'${config.fontFamily}', sans-serif`, \"important\");\n                // 强制触发重排以确保字体生效\n                document.body.offsetHeight;\n            } else if (config.scope.type === \"selective\" && config.scope.selectors) {\n                // 选择性应用\n                config.scope.selectors.forEach((selector)=>{\n                    try {\n                        const elements = document.querySelectorAll(selector);\n                        elements.forEach((element)=>{\n                            element.style.setProperty(\"font-family\", `'${config.fontFamily}', sans-serif`, \"important\");\n                        });\n                    } catch (e) {\n                        console.warn(`选择器应用失败: ${selector}`, e);\n                    }\n                });\n            }\n            console.log(\"✅ 用户上传的字体已应用到页面:\", config.fontFamily);\n        } catch (error) {\n            console.error(\"❌ 应用字体到页面失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 重置页面字体为默认\r\n   */ resetPageFont() {\n        try {\n            // 重置CSS变量\n            document.documentElement.style.setProperty(\"--font-family-applied\", \"var(--font-family-primary)\");\n            document.documentElement.style.setProperty(\"--font-family-handwritten\", \"var(--font-family-handwritten)\");\n            // 重置body字体\n            document.body.style.fontFamily = \"\";\n            // 清除所有手动设置的字体样式\n            const elementsWithFont = document.querySelectorAll('[style*=\"font-family\"]');\n            elementsWithFont.forEach((element)=>{\n                element.style.fontFamily = \"\";\n            });\n            console.log(\"✅ 页面字体已重置为默认\");\n        } catch (error) {\n            console.error(\"❌ 重置页面字体失败:\", error);\n        }\n    }\n    /**\r\n   * 创建默认的字体应用配置\r\n   */ createDefaultConfig(fontId, fontFamily) {\n        return {\n            id: this.generateUUID(),\n            fontId,\n            fontFamily,\n            appliedAt: Date.now(),\n            scope: {\n                type: \"global\"\n            },\n            isActive: true,\n            settings: {\n                fallbackFonts: [\n                    \"Arial\",\n                    \"sans-serif\"\n                ],\n                loadTimeout: 5000,\n                enableSmoothing: true,\n                preloadOnStartup: true\n            }\n        };\n    }\n    /**\r\n   * 获取字体持久化统计信息\r\n   */ async getStatistics() {\n        try {\n            const [configsResult, historyResult] = await Promise.all([\n                this.dbService.getAll(this.configStoreName),\n                this.dbService.getAll(this.historyStoreName)\n            ]);\n            const configs = configsResult.success ? configsResult.data || [] : [];\n            const history = historyResult.success ? historyResult.data || [] : [];\n            const stats = {\n                totalConfigs: configs.length,\n                activeConfigs: configs.filter((c)=>c.isActive).length,\n                historyCount: history.length,\n                lastAppliedAt: history.length > 0 ? Math.max(...history.map((h)=>h.appliedAt)) : undefined\n            };\n            return {\n                success: true,\n                data: stats\n            };\n        } catch (error) {\n            const errorMessage = `获取统计信息失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/fontPersistenceService.ts\n");

/***/ })

};
;