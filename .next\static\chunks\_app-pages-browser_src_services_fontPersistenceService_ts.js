"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_services_fontPersistenceService_ts"],{

/***/ "(app-pages-browser)/./src/services/fontPersistenceService.ts":
/*!************************************************!*\
  !*** ./src/services/fontPersistenceService.ts ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FontPersistenceService: function() { return /* binding */ FontPersistenceService; }\n/* harmony export */ });\n/* harmony import */ var _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database/DatabaseService */ \"(app-pages-browser)/./src/services/database/DatabaseService.ts\");\n/**\r\n * 字体持久化服务\r\n * 管理字体应用状态的持久化、恢复和历史记录\r\n */ \nclass FontPersistenceService {\n    /**\r\n   * 获取字体持久化服务单例\r\n   */ static getInstance() {\n        if (!FontPersistenceService.instance) {\n            FontPersistenceService.instance = new FontPersistenceService();\n        }\n        return FontPersistenceService.instance;\n    }\n    /**\r\n   * 生成UUID\r\n   */ generateUUID() {\n        return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n            const r = Math.random() * 16 | 0;\n            const v = c === \"x\" ? r : r & 0x3 | 0x8;\n            return v.toString(16);\n        });\n    }\n    /**\r\n   * 创建错误对象\r\n   */ createError(type, message, fontId, details) {\n        return {\n            type,\n            message,\n            fontId,\n            timestamp: Date.now(),\n            details\n        };\n    }\n    /**\r\n   * 保存字体应用配置\r\n   */ async saveApplicationConfig(config) {\n        try {\n            console.log(\"\\uD83D\\uDCBE 保存字体应用配置:\", config.fontFamily);\n            // 1. 先清除之前的活跃配置\n            await this.clearActiveConfigs();\n            // 2. 设置当前配置为活跃状态\n            config.isActive = true;\n            config.appliedAt = Date.now();\n            // 3. 保存到 IndexedDB\n            const dbResult = await this.dbService.add(this.configStoreName, config);\n            if (!dbResult.success) {\n                throw new Error(dbResult.error || \"保存到IndexedDB失败\");\n            }\n            // 4. 同步保存到 localStorage（快速访问）\n            const quickConfig = {\n                id: config.id,\n                fontId: config.fontId,\n                fontFamily: config.fontFamily,\n                appliedAt: config.appliedAt,\n                scope: config.scope\n            };\n            localStorage.setItem(this.localStorageKey, JSON.stringify(quickConfig));\n            // 5. 更新历史记录\n            await this.updateHistory(config);\n            console.log(\"✅ 字体应用配置保存成功\");\n            return {\n                success: true\n            };\n        } catch (error) {\n            const errorMessage = \"保存字体应用配置失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取当前活跃的字体配置\r\n   */ async getActiveConfig() {\n        try {\n            // 1. 优先从 localStorage 读取（快速访问）\n            const quickConfigStr = localStorage.getItem(this.localStorageKey);\n            if (quickConfigStr) {\n                const quickConfig = JSON.parse(quickConfigStr);\n                // 2. 从 IndexedDB 获取完整配置\n                const fullConfigResult = await this.dbService.get(this.configStoreName, quickConfig.id);\n                if (fullConfigResult.success && fullConfigResult.data) {\n                    return {\n                        success: true,\n                        data: fullConfigResult.data\n                    };\n                }\n            }\n            // 3. 降级方案：直接从 IndexedDB 查找活跃配置\n            const allConfigsResult = await this.dbService.getAll(this.configStoreName);\n            if (allConfigsResult.success && allConfigsResult.data) {\n                const activeConfig = allConfigsResult.data.find((config)=>config.isActive);\n                return {\n                    success: true,\n                    data: activeConfig || null\n                };\n            }\n            return {\n                success: true,\n                data: null\n            };\n        } catch (error) {\n            const errorMessage = \"获取活跃字体配置失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 恢复字体应用状态\r\n   */ async restoreFontApplication() {\n        try {\n            console.log(\"\\uD83D\\uDD04 开始恢复字体应用状态...\");\n            const configResult = await this.getActiveConfig();\n            if (!configResult.success) {\n                throw new Error(configResult.error || \"获取活跃配置失败\");\n            }\n            if (!configResult.data) {\n                console.log(\"\\uD83D\\uDCDD 没有找到活跃的字体配置\");\n                return {\n                    success: true,\n                    data: false\n                };\n            }\n            const config = configResult.data;\n            console.log(\"\\uD83C\\uDFAF 找到活跃配置:\", config.fontFamily);\n            // 应用字体到页面\n            await this.applyFontToPage(config);\n            console.log(\"✅ 字体应用状态恢复成功\");\n            return {\n                success: true,\n                data: true\n            };\n        } catch (error) {\n            const errorMessage = \"恢复字体应用状态失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 清除字体应用状态\r\n   */ async clearApplicationState() {\n        try {\n            console.log(\"\\uD83E\\uDDF9 清除字体应用状态...\");\n            // 1. 清除 localStorage\n            localStorage.removeItem(this.localStorageKey);\n            // 2. 清除 IndexedDB 中的活跃配置\n            await this.clearActiveConfigs();\n            // 3. 重置页面字体为默认\n            this.resetPageFont();\n            console.log(\"✅ 字体应用状态已清除\");\n            return {\n                success: true\n            };\n        } catch (error) {\n            const errorMessage = \"清除字体应用状态失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取字体应用历史\r\n   */ async getApplicationHistory() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20;\n        try {\n            const result = await this.dbService.getAll(this.historyStoreName);\n            if (result.success && result.data) {\n                // 按应用时间倒序排列，并限制数量\n                const sortedHistory = result.data.sort((a, b)=>b.appliedAt - a.appliedAt).slice(0, limit);\n                return {\n                    success: true,\n                    data: sortedHistory\n                };\n            }\n            return {\n                success: true,\n                data: []\n            };\n        } catch (error) {\n            const errorMessage = \"获取字体应用历史失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 清除所有活跃配置\r\n   */ async clearActiveConfigs() {\n        const allConfigsResult = await this.dbService.getAll(this.configStoreName);\n        if (allConfigsResult.success && allConfigsResult.data) {\n            const activeConfigs = allConfigsResult.data.filter((config)=>config.isActive);\n            for (const config of activeConfigs){\n                config.isActive = false;\n                await this.dbService.put(this.configStoreName, config);\n            }\n        }\n    }\n    /**\r\n   * 更新历史记录\r\n   */ async updateHistory(config) {\n        try {\n            // 检查是否已存在相同字体的历史记录\n            const historyResult = await this.getApplicationHistory();\n            if (historyResult.success && historyResult.data) {\n                const existingHistory = historyResult.data.find((h)=>h.fontId === config.fontId);\n                if (existingHistory) {\n                    // 更新现有记录\n                    existingHistory.appliedAt = config.appliedAt;\n                    existingHistory.config = {\n                        ...config\n                    };\n                    await this.dbService.put(this.historyStoreName, existingHistory);\n                } else {\n                    // 创建新的历史记录\n                    const historyRecord = {\n                        id: this.generateUUID(),\n                        fontId: config.fontId,\n                        fontName: config.fontFamily,\n                        fontFamily: config.fontFamily,\n                        appliedAt: config.appliedAt,\n                        duration: 0,\n                        config: {\n                            ...config\n                        }\n                    };\n                    await this.dbService.add(this.historyStoreName, historyRecord);\n                }\n            }\n            // 清理过多的历史记录\n            await this.cleanupHistory();\n        } catch (error) {\n            console.warn(\"更新历史记录失败:\", error);\n        }\n    }\n    /**\r\n   * 清理过多的历史记录\r\n   */ async cleanupHistory() {\n        try {\n            const historyResult = await this.getApplicationHistory(this.maxHistoryRecords + 10);\n            if (historyResult.success && historyResult.data && historyResult.data.length > this.maxHistoryRecords) {\n                const sortedHistory = historyResult.data.sort((a, b)=>b.appliedAt - a.appliedAt);\n                const toDelete = sortedHistory.slice(this.maxHistoryRecords);\n                for (const record of toDelete){\n                    await this.dbService.delete(this.historyStoreName, record.id);\n                }\n                console.log(\"\\uD83E\\uDDF9 清理了 \".concat(toDelete.length, \" 条过期历史记录\"));\n            }\n        } catch (error) {\n            console.warn(\"清理历史记录失败:\", error);\n        }\n    }\n    /**\r\n   * 应用字体到页面\r\n   */ async applyFontToPage(config) {\n        try {\n            // 首先需要确保字体文件已加载到CSS\n            const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n            const fontService = FontService.getInstance();\n            // 加载字体到CSS\n            const loadResult = await fontService.loadFontToCSS(config.fontId);\n            if (!loadResult.success) {\n                throw new Error(\"字体加载失败: \".concat(loadResult.error));\n            }\n            // 等待字体加载完成\n            await document.fonts.ready;\n            // 更新CSS变量\n            document.documentElement.style.setProperty(\"--font-family-applied\", \"'\".concat(config.fontFamily, \"', sans-serif\"));\n            document.documentElement.style.setProperty(\"--font-family-handwritten\", \"'\".concat(config.fontFamily, \"', cursive, var(--font-family-primary)\"));\n            // 根据应用范围应用字体\n            if (config.scope.type === \"global\") {\n                // 全局应用 - 确保使用用户上传的字体\n                const elementsToApply = [\n                    \"body\",\n                    \".font-applied\",\n                    \".font-handwritten\",\n                    \"h1, h2, h3, h4, h5, h6\",\n                    \".artwork-title\",\n                    \".card-title\",\n                    \".welcome-content\",\n                    \".btn\",\n                    \"p, span, div, label, input, textarea\",\n                    \".handdrawn-text\",\n                    \".font-primary\"\n                ];\n                elementsToApply.forEach((selector)=>{\n                    try {\n                        const elements = document.querySelectorAll(selector);\n                        elements.forEach((element)=>{\n                            element.style.fontFamily = \"'\".concat(config.fontFamily, \"', sans-serif\");\n                            // 添加重要性标记确保样式生效\n                            element.style.setProperty(\"font-family\", \"'\".concat(config.fontFamily, \"', sans-serif\"), \"important\");\n                        });\n                    } catch (e) {\n                    // 忽略选择器错误\n                    }\n                });\n                // 强制应用到body元素\n                document.body.style.setProperty(\"font-family\", \"'\".concat(config.fontFamily, \"', sans-serif\"), \"important\");\n                // 强制触发重排以确保字体生效\n                document.body.offsetHeight;\n            } else if (config.scope.type === \"selective\" && config.scope.selectors) {\n                // 选择性应用\n                config.scope.selectors.forEach((selector)=>{\n                    try {\n                        const elements = document.querySelectorAll(selector);\n                        elements.forEach((element)=>{\n                            element.style.setProperty(\"font-family\", \"'\".concat(config.fontFamily, \"', sans-serif\"), \"important\");\n                        });\n                    } catch (e) {\n                        console.warn(\"选择器应用失败: \".concat(selector), e);\n                    }\n                });\n            }\n            console.log(\"✅ 用户上传的字体已应用到页面:\", config.fontFamily);\n        } catch (error) {\n            console.error(\"❌ 应用字体到页面失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 重置页面字体为默认\r\n   */ resetPageFont() {\n        try {\n            // 重置CSS变量\n            document.documentElement.style.setProperty(\"--font-family-applied\", \"var(--font-family-primary)\");\n            document.documentElement.style.setProperty(\"--font-family-handwritten\", \"var(--font-family-handwritten)\");\n            // 重置body字体\n            document.body.style.fontFamily = \"\";\n            // 清除所有手动设置的字体样式\n            const elementsWithFont = document.querySelectorAll('[style*=\"font-family\"]');\n            elementsWithFont.forEach((element)=>{\n                element.style.fontFamily = \"\";\n            });\n            console.log(\"✅ 页面字体已重置为默认\");\n        } catch (error) {\n            console.error(\"❌ 重置页面字体失败:\", error);\n        }\n    }\n    /**\r\n   * 创建默认的字体应用配置\r\n   */ createDefaultConfig(fontId, fontFamily) {\n        return {\n            id: this.generateUUID(),\n            fontId,\n            fontFamily,\n            appliedAt: Date.now(),\n            scope: {\n                type: \"global\"\n            },\n            isActive: true,\n            settings: {\n                fallbackFonts: [\n                    \"Arial\",\n                    \"sans-serif\"\n                ],\n                loadTimeout: 5000,\n                enableSmoothing: true,\n                preloadOnStartup: true\n            }\n        };\n    }\n    /**\r\n   * 获取字体持久化统计信息\r\n   */ async getStatistics() {\n        try {\n            const [configsResult, historyResult] = await Promise.all([\n                this.dbService.getAll(this.configStoreName),\n                this.dbService.getAll(this.historyStoreName)\n            ]);\n            const configs = configsResult.success ? configsResult.data || [] : [];\n            const history = historyResult.success ? historyResult.data || [] : [];\n            const stats = {\n                totalConfigs: configs.length,\n                activeConfigs: configs.filter((c)=>c.isActive).length,\n                historyCount: history.length,\n                lastAppliedAt: history.length > 0 ? Math.max(...history.map((h)=>h.appliedAt)) : undefined\n            };\n            return {\n                success: true,\n                data: stats\n            };\n        } catch (error) {\n            const errorMessage = \"获取统计信息失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    constructor(){\n        this.configStoreName = \"font-configs\";\n        this.historyStoreName = \"font-history\";\n        this.localStorageKey = \"font-active-config\";\n        this.maxHistoryRecords = 50;\n        this.dbService = _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__.DatabaseService.getInstance();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/fontPersistenceService.ts\n"));

/***/ })

}]);