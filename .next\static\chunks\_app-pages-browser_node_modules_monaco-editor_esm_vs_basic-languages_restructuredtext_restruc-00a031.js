"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_restructuredtext_restruc-00a031"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js ***!
  \************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/restructuredtext/restructuredtext.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"(\", close: \")\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*<!--\\\\s*#?region\\\\b.*-->\"),\n      end: new RegExp(\"^\\\\s*<!--\\\\s*#?endregion\\\\b.*-->\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".rst\",\n  control: /[\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n  escapes: /\\\\(?:@control)/,\n  empty: [\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"link\",\n    \"meta\",\n    \"param\"\n  ],\n  alphanumerics: /[A-Za-z0-9]/,\n  simpleRefNameWithoutBq: /(?:@alphanumerics[-_+:.]*@alphanumerics)+|(?:@alphanumerics+)/,\n  simpleRefName: /(?:`@phrase`|@simpleRefNameWithoutBq)/,\n  phrase: /@simpleRefNameWithoutBq(?:\\s@simpleRefNameWithoutBq)*/,\n  citationName: /[A-Za-z][A-Za-z0-9-_.]*/,\n  blockLiteralStart: /(?:[!\"#$%&'()*+,-./:;<=>?@\\[\\]^_`{|}~]|[\\s])/,\n  precedingChars: /(?:[ -:/'\"<([{])/,\n  followingChars: /(?:[ -.,:;!?/'\")\\]}>]|$)/,\n  punctuation: /(=|-|~|`|#|\"|\\^|\\+|\\*|:|\\.|'|_|\\+)/,\n  tokenizer: {\n    root: [\n      //sections\n      [/^(@punctuation{3,}$){1,1}?/, \"keyword\"],\n      //line-blocks\n      //No rules on it\n      //bullet-lists\n      [/^\\s*([\\*\\-+‣•]|[a-zA-Z0-9]+\\.|\\([a-zA-Z0-9]+\\)|[a-zA-Z0-9]+\\))\\s/, \"keyword\"],\n      //literal-blocks\n      [/([ ]::)\\s*$/, \"keyword\", \"@blankLineOfLiteralBlocks\"],\n      [/(::)\\s*$/, \"keyword\", \"@blankLineOfLiteralBlocks\"],\n      { include: \"@tables\" },\n      { include: \"@explicitMarkupBlocks\" },\n      { include: \"@inlineMarkup\" }\n    ],\n    explicitMarkupBlocks: [\n      //citations\n      { include: \"@citations\" },\n      //footnotes\n      { include: \"@footnotes\" },\n      //directives\n      [\n        /^(\\.\\.\\s)(@simpleRefName)(::\\s)(.*)$/,\n        [{ token: \"\", next: \"subsequentLines\" }, \"keyword\", \"\", \"\"]\n      ],\n      //hyperlink-targets\n      [\n        /^(\\.\\.)(\\s+)(_)(@simpleRefName)(:)(\\s+)(.*)/,\n        [{ token: \"\", next: \"hyperlinks\" }, \"\", \"\", \"string.link\", \"\", \"\", \"string.link\"]\n      ],\n      //anonymous-hyperlinks\n      [\n        /^((?:(?:\\.\\.)(?:\\s+))?)(__)(:)(\\s+)(.*)/,\n        [{ token: \"\", next: \"subsequentLines\" }, \"\", \"\", \"\", \"string.link\"]\n      ],\n      [/^(__\\s+)(.+)/, [\"\", \"string.link\"]],\n      //substitution-definitions\n      [\n        /^(\\.\\.)( \\|)([^| ]+[^|]*[^| ]*)(\\| )(@simpleRefName)(:: .*)/,\n        [{ token: \"\", next: \"subsequentLines\" }, \"\", \"string.link\", \"\", \"keyword\", \"\"],\n        \"@rawBlocks\"\n      ],\n      [/(\\|)([^| ]+[^|]*[^| ]*)(\\|_{0,2})/, [\"\", \"string.link\", \"\"]],\n      //comments\n      [/^(\\.\\.)([ ].*)$/, [{ token: \"\", next: \"@comments\" }, \"comment\"]]\n    ],\n    inlineMarkup: [\n      { include: \"@citationsReference\" },\n      { include: \"@footnotesReference\" },\n      //hyperlink-references\n      [/(@simpleRefName)(_{1,2})/, [\"string.link\", \"\"]],\n      //embedded-uris-and-aliases\n      [/(`)([^<`]+\\s+)(<)(.*)(>)(`)(_)/, [\"\", \"string.link\", \"\", \"string.link\", \"\", \"\", \"\"]],\n      //emphasis\n      [/\\*\\*([^\\\\*]|\\*(?!\\*))+\\*\\*/, \"strong\"],\n      [/\\*[^*]+\\*/, \"emphasis\"],\n      //inline-literals\n      [/(``)((?:[^`]|\\`(?!`))+)(``)/, [\"\", \"keyword\", \"\"]],\n      [/(__\\s+)(.+)/, [\"\", \"keyword\"]],\n      //interpreted-text\n      [/(:)((?:@simpleRefNameWithoutBq)?)(:`)([^`]+)(`)/, [\"\", \"keyword\", \"\", \"\", \"\"]],\n      [/(`)([^`]+)(`:)((?:@simpleRefNameWithoutBq)?)(:)/, [\"\", \"\", \"\", \"keyword\", \"\"]],\n      [/(`)([^`]+)(`)/, \"\"],\n      //inline-internal-targets\n      [/(_`)(@phrase)(`)/, [\"\", \"string.link\", \"\"]]\n    ],\n    citations: [\n      [\n        /^(\\.\\.\\s+\\[)((?:@citationName))(\\]\\s+)(.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\", \"\"]\n      ]\n    ],\n    citationsReference: [[/(\\[)(@citationName)(\\]_)/, [\"\", \"string.link\", \"\"]]],\n    footnotes: [\n      [\n        /^(\\.\\.\\s+\\[)((?:[0-9]+))(\\]\\s+.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\"]\n      ],\n      [\n        /^(\\.\\.\\s+\\[)((?:#@simpleRefName?))(\\]\\s+)(.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\", \"\"]\n      ],\n      [\n        /^(\\.\\.\\s+\\[)((?:\\*))(\\]\\s+)(.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\", \"\"]\n      ]\n    ],\n    footnotesReference: [\n      [/(\\[)([0-9]+)(\\])(_)/, [\"\", \"string.link\", \"\", \"\"]],\n      [/(\\[)(#@simpleRefName?)(\\])(_)/, [\"\", \"string.link\", \"\", \"\"]],\n      [/(\\[)(\\*)(\\])(_)/, [\"\", \"string.link\", \"\", \"\"]]\n    ],\n    blankLineOfLiteralBlocks: [\n      [/^$/, \"\", \"@subsequentLinesOfLiteralBlocks\"],\n      [/^.*$/, \"\", \"@pop\"]\n    ],\n    subsequentLinesOfLiteralBlocks: [\n      [/(@blockLiteralStart+)(.*)/, [\"keyword\", \"\"]],\n      [/^(?!blockLiteralStart)/, \"\", \"@popall\"]\n    ],\n    subsequentLines: [\n      [/^[\\s]+.*/, \"\"],\n      [/^(?!\\s)/, \"\", \"@pop\"]\n    ],\n    hyperlinks: [\n      [/^[\\s]+.*/, \"string.link\"],\n      [/^(?!\\s)/, \"\", \"@pop\"]\n    ],\n    comments: [\n      [/^[\\s]+.*/, \"comment\"],\n      [/^(?!\\s)/, \"\", \"@pop\"]\n    ],\n    tables: [\n      [/\\+-[+-]+/, \"keyword\"],\n      [/\\+=[+=]+/, \"keyword\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js\n"));

/***/ })

}]);