/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[3]!./src/components/MindMapRenderer/styles/drag-enhancements.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/**
 * 拖拽增强样式
 * 为思维导图拖拽功能提供丰富的视觉效果
 */

/* 拖拽克隆节点增强样式 */
.drag-clone-enhanced {
  /* 基础样式 */
  position: relative;
  z-index: 9999;
  
  /* 阴影效果 */
  box-shadow: 
    0 8px 32px rgba(255, 215, 0, 0.4),
    0 4px 16px rgba(255, 215, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  
  /* 边框效果 */
  border: 2px solid #FFD700;
  border-radius: 8px;
  
  /* 背景效果 */
  background: linear-gradient(135deg, 
    rgba(255, 215, 0, 0.1) 0%, 
    rgba(255, 215, 0, 0.05) 100%);
  
  /* 动画过渡 */
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  /* GPU加速 */
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* 拖拽克隆节点悬停效果 */
.drag-clone-enhanced:hover {
  box-shadow: 
    0 12px 48px rgba(255, 215, 0, 0.5),
    0 6px 24px rgba(255, 215, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  
  border-color: #FFA500;
  transform: translateZ(0) scale(1.02);
}

/* 拖拽开始动画 */
@keyframes dragStartPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 16px rgba(255, 215, 0, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 8px 32px rgba(255, 215, 0, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 6px 24px rgba(255, 215, 0, 0.4);
  }
}

/* 拖拽结束动画 */
@keyframes dragEndBounce {
  0% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  60% {
    transform: scale(0.95);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 拖拽轨迹效果 */
.drag-trail {
  position: absolute;
  pointer-events: none;
  z-index: 9998;
  
  /* 轨迹样式 */
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, #FFD700 0%, transparent 70%);
  border-radius: 50%;
  
  /* 动画效果 */
  animation: trailFade 0.8s ease-out forwards;
}

@keyframes trailFade {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.3);
  }
}

/* 拖拽位置指示器增强 */
.drag-placeholder-enhanced {
  /* 基础样式 */
  position: relative;
  
  /* 发光效果 */
  box-shadow: 
    0 0 10px rgba(255, 215, 0, 0.6),
    0 0 20px rgba(255, 215, 0, 0.4),
    0 0 30px rgba(255, 215, 0, 0.2);
  
  /* 动画效果 */
  animation: placeholderPulse 1.5s ease-in-out infinite;
}

@keyframes placeholderPulse {
  0%, 100% {
    opacity: 0.7;
    transform: scaleY(1);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.1);
  }
}

/* 拖拽目标高亮 */
.drag-target-highlight {
  /* 边框高亮 */
  border: 2px solid #32CD32 !important;
  border-radius: 6px;
  
  /* 背景高亮 */
  background: linear-gradient(135deg, 
    rgba(50, 205, 50, 0.1) 0%, 
    rgba(50, 205, 50, 0.05) 100%) !important;
  
  /* 发光效果 */
  box-shadow: 
    0 0 15px rgba(50, 205, 50, 0.4),
    0 0 30px rgba(50, 205, 50, 0.2);
  
  /* 动画过渡 */
  transition: all 0.3s ease;
}

/* 拖拽禁止状态 */
.drag-forbidden {
  cursor: not-allowed !important;
  
  /* 红色边框 */
  border: 2px solid #FF6B6B !important;
  
  /* 红色背景 */
  background: linear-gradient(135deg, 
    rgba(255, 107, 107, 0.1) 0%, 
    rgba(255, 107, 107, 0.05) 100%) !important;
  
  /* 抖动动画 */
  animation: dragForbiddenShake 0.5s ease-in-out;
}

@keyframes dragForbiddenShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* 拖拽节点透明度过渡 */
.drag-node-fading {
  transition: opacity 0.2s ease-out;
}

/* 拖拽容器样式 */
.mind-map-drag-container {
  position: relative;
  overflow: visible;
}

/* 拖拽遮罩层 */
.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.02);
  pointer-events: none;
  z-index: 9997;
  
  /* 渐变显示 */
  opacity: 0;
  transition: opacity 0.3s ease;
}

.drag-overlay.active {
  opacity: 1;
}

/* 拖拽提示信息 */
.drag-hint {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  
  /* 样式 */
  background: rgba(255, 215, 0, 0.95);
  color: #000;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
  
  /* 阴影 */
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
  
  /* 动画 */
  animation: dragHintSlideIn 0.3s ease-out;
  
  /* 层级 */
  z-index: 10000;
  pointer-events: none;
}

@keyframes dragHintSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 拖拽统计信息 */
.drag-stats {
  position: absolute;
  bottom: 10px;
  right: 10px;
  
  /* 样式 */
  background: rgba(0, 0, 0, 0.8);
  color: #FFD700;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 11px;
  font-family: monospace;
  
  /* 层级 */
  z-index: 10000;
  pointer-events: none;
  
  /* 动画 */
  opacity: 0;
  transition: opacity 0.3s ease;
}

.drag-stats.visible {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .drag-clone-enhanced {
    box-shadow: 0 4px 16px rgba(255, 215, 0, 0.3);
  }
  
  .drag-hint {
    font-size: 11px;
    padding: 6px 12px;
  }
  
  .drag-stats {
    font-size: 10px;
    padding: 4px 8px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .drag-clone-enhanced {
    background: linear-gradient(135deg, 
      rgba(255, 215, 0, 0.15) 0%, 
      rgba(255, 215, 0, 0.08) 100%);
  }
  
  .drag-target-highlight {
    background: linear-gradient(135deg, 
      rgba(50, 205, 50, 0.15) 0%, 
      rgba(50, 205, 50, 0.08) 100%) !important;
  }
  
  .drag-forbidden {
    background: linear-gradient(135deg, 
      rgba(255, 107, 107, 0.15) 0%, 
      rgba(255, 107, 107, 0.08) 100%) !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .drag-clone-enhanced {
    border-width: 3px;
    box-shadow: 0 0 0 2px #000, 0 8px 32px rgba(255, 215, 0, 0.6);
  }
  
  .drag-target-highlight {
    border-width: 3px !important;
    box-shadow: 0 0 0 2px #000, 0 0 15px rgba(50, 205, 50, 0.6);
  }
  
  .drag-forbidden {
    border-width: 3px !important;
    box-shadow: 0 0 0 2px #000, 0 0 15px rgba(255, 107, 107, 0.6);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .drag-clone-enhanced,
  .drag-placeholder-enhanced,
  .drag-target-highlight,
  .drag-forbidden,
  .drag-node-fading,
  .drag-overlay,
  .drag-hint,
  .drag-stats {
    animation: none !important;
    transition: none !important;
  }
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!./src/components/FolderTreeDialog/TreeView.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
.TreeView_treeView__xNxgv {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.TreeView_treeNode__O5DIB {
  display: flex;
  flex-direction: column;
}

.TreeView_treeNodeContent__GmwuF {
  display: flex;
  cursor: pointer;
  align-items: center;
  gap: 0.5rem;
  border-radius: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.TreeView_treeNodeContent__GmwuF:hover {
  background-color: rgb(245 158 11 / 0.1);
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-shadow-color: rgb(245 158 11 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.TreeView_treeNodeContent__GmwuF {
  border-width: 1px;
  border-color: transparent;
}

.TreeView_treeNodeContent__GmwuF:hover {
  border-color: rgb(245 158 11 / 0.2);
}

.TreeView_treeNodeContent__GmwuF {
  min-height: 36px;
  position: relative;
}

.TreeView_expandButton__AzRy_ {
  display: flex;
  height: 1.5rem;
  width: 1.5rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  border-width: 1px;
  border-color: rgb(245 158 11 / 0.3);
  background-color: rgb(245 158 11 / 0.1);
}

.TreeView_expandButton__AzRy_:hover {
  border-color: rgb(245 158 11 / 0.5);
  background-color: rgb(245 158 11 / 0.2);
}

.TreeView_expandButton__AzRy_ {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}

.TreeView_expandButton__AzRy_:hover {
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}

.TreeView_expandButton__AzRy_.TreeView_hasChildren__dP1Na {
  cursor: pointer;
}

.TreeView_expandButton__AzRy_.TreeView_hasChildren__dP1Na:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-shadow-color: rgb(245 158 11 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.TreeView_expandButton__AzRy_.TreeView_noChildren__T86Nj {
  cursor: default;
  border-color: transparent;
  background-color: transparent;
  opacity: 0.3;
}

.TreeView_nodeIcon__3aT2B {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}

.TreeView_nodeName__aUSM1 {
  flex: 1 1 0%;
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.TreeView_childNodes__qrEgJ {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.TreeView_treeCheckbox__blKaw {
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
  border-width: 2px;
  border-color: rgb(245 158 11 / 0.5);
  background-color: rgb(31 41 55 / 0.5);
}

.TreeView_treeCheckbox__blKaw:checked {
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}

.TreeView_treeCheckbox__blKaw:hover {
  border-color: rgb(245 158 11 / 0.7);
  background-color: rgb(245 158 11 / 0.1);
}

.TreeView_treeCheckbox__blKaw:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(245 158 11 / 0.5);
  --tw-ring-offset-width: 0px;
}

.TreeView_treeCheckbox__blKaw {
  cursor: pointer;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  position: relative;
}

.TreeView_treeCheckbox__blKaw:checked::after {
  content: '✓';
  position: absolute;
  inset: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

/* 搜索结果样式 */
.TreeView_searchResult__j_l_v {
  border-color: rgb(245 158 11 / 0.4);
  background-color: rgb(245 158 11 / 0.2);
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-shadow-color: rgb(245 158 11 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
  animation: TreeView_pulse__x1kLS 2s infinite;
  position: relative;
}

.TreeView_searchResult__j_l_v::before {
  content: '';
  position: absolute;
  left: 0px;
  top: 0px;
  bottom: 0px;
  width: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
  animation: TreeView_glow__dsuzL 2s infinite;
}

.TreeView_searchResultText__SZhiG {
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(253 230 138 / var(--tw-text-opacity, 1));
}

.TreeView_searchResultHighlight__tFQpT {
  border-radius: 0.25rem;
  background-color: rgb(245 158 11 / 0.4);
  padding-left: 0.125rem;
  padding-right: 0.125rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.TreeView_searchResultsSection___Za_K {
  margin-bottom: 1rem;
  border-bottom-width: 1px;
  border-color: rgb(245 158 11 / 0.3);
  padding-bottom: 0.5rem;
}

.TreeView_searchResultsTitle__92BbO {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}

@keyframes TreeView_pulse__x1kLS {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(245, 158, 11, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

@keyframes TreeView_glow__dsuzL {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}
