"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/services/editorDiffService.ts":
/*!*******************************************!*\
  !*** ./src/services/editorDiffService.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditorDiffCalculator: function() { return /* binding */ EditorDiffCalculator; }\n/* harmony export */ });\n/* harmony import */ var diff__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! diff */ \"(app-pages-browser)/./node_modules/diff/lib/index.mjs\");\n/**\r\n * 编辑器差异计算服务\r\n * 扩展现有的DiffToolService，为编辑器diff对比功能提供专用方法\r\n */ \nclass EditorDiffCalculator {\n    /**\r\n   * 获取编辑器差异计算服务单例\r\n   */ static getInstance() {\n        if (!EditorDiffCalculator.instance) {\n            EditorDiffCalculator.instance = new EditorDiffCalculator();\n        }\n        return EditorDiffCalculator.instance;\n    }\n    /**\r\n   * 计算编辑器专用的diff数据\r\n   * 复用现有的文件获取和diff计算逻辑\r\n   */ async calculateEditorDiff(artworkId, filePath, modifiedContent) {\n        let operation = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : \"replace\";\n        try {\n            // 1. 检查文件是否存在\n            const { FileTreeService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\"));\n            const fileTreeService = FileTreeService.getInstance();\n            const fileExists = await fileTreeService.checkFileExists(artworkId, filePath);\n            let originalContent = \"\";\n            let fileId = \"\";\n            if (fileExists) {\n                // 文件存在，获取原始内容\n                const fileTreeResult = await this.getFileByPath(artworkId, filePath);\n                if (!fileTreeResult.success || !fileTreeResult.data) {\n                    return {\n                        success: false,\n                        error: \"无法获取文件内容: \".concat(fileTreeResult.error)\n                    };\n                }\n                originalContent = fileTreeResult.data.content || \"\";\n                fileId = fileTreeResult.data.id;\n            } else {\n                // 文件不存在，原始内容为空\n                originalContent = \"\";\n                fileId = \"new-file-\".concat(Date.now());\n            }\n            // 2. 根据操作类型生成最终的修改内容\n            let finalModifiedContent = modifiedContent;\n            if (operation === \"append\" && originalContent) {\n                finalModifiedContent = originalContent + \"\\n\" + modifiedContent;\n            } else if (operation === \"replace\" && modifiedContent.includes(\"|||\")) {\n                // 处理正则替换格式：pattern|||replacement\n                const [pattern, replacement] = modifiedContent.split(\"|||\");\n                try {\n                    const regex = new RegExp(pattern, \"g\");\n                    finalModifiedContent = originalContent.replace(regex, replacement);\n                } catch (error) {\n                    return {\n                        success: false,\n                        error: \"正则表达式错误: \".concat(error instanceof Error ? error.message : String(error))\n                    };\n                }\n            }\n            // 3. 使用现有的diff计算逻辑\n            const diffStats = this.calculateDiffStats(originalContent, finalModifiedContent);\n            // 4. 生成react-diff-view兼容的数据\n            const reactDiffData = this.generateReactDiffViewData(originalContent, finalModifiedContent, filePath);\n            // 5. 构建EditorDiffData\n            const editorDiffData = {\n                // 复用现有的DiffStats字段\n                additions: diffStats.additions,\n                deletions: diffStats.deletions,\n                modifications: diffStats.modifications,\n                totalChanges: diffStats.totalChanges,\n                // 新增编辑器专用字段\n                hunks: reactDiffData.hunks,\n                oldSource: originalContent,\n                newSource: finalModifiedContent,\n                filePath,\n                operation\n            };\n            return {\n                success: true,\n                data: editorDiffData\n            };\n        } catch (error) {\n            const errorMessage = \"计算编辑器diff失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 生成react-diff-view兼容的数据格式\r\n   * 使用diff库生成hunks数据\r\n   */ generateReactDiffViewData(original, modified, filePath) {\n        try {\n            // 使用diff库生成patch\n            const patch = (0,diff__WEBPACK_IMPORTED_MODULE_0__.createPatch)(filePath, original, modified, \"\", \"\");\n            // 解析patch为结构化数据\n            const parsedDiff = (0,diff__WEBPACK_IMPORTED_MODULE_0__.parsePatch)(patch);\n            if (parsedDiff.length === 0) {\n                // 没有差异的情况\n                return {\n                    hunks: [],\n                    oldRevision: \"original\",\n                    newRevision: \"modified\",\n                    type: \"modify\"\n                };\n            }\n            const file = parsedDiff[0];\n            // 转换hunks格式\n            const hunks = file.hunks.map((hunk)=>{\n                var _hunk_changes;\n                console.log(\"\\uD83D\\uDD0D 处理hunk:\", {\n                    oldStart: hunk.oldStart,\n                    oldLines: hunk.oldLines,\n                    newStart: hunk.newStart,\n                    newLines: hunk.newLines,\n                    changesCount: ((_hunk_changes = hunk.changes) === null || _hunk_changes === void 0 ? void 0 : _hunk_changes.length) || 0,\n                    hasChanges: !!hunk.changes,\n                    hunkKeys: Object.keys(hunk)\n                });\n                return {\n                    oldStart: hunk.oldStart,\n                    oldLines: hunk.oldLines,\n                    newStart: hunk.newStart,\n                    newLines: hunk.newLines,\n                    changes: hunk.changes.map((change)=>{\n                        var _change_value;\n                        const changeType = change.type === \"add\" ? \"insert\" : change.type === \"del\" ? \"delete\" : \"normal\";\n                        const oldLineNumber = change.type !== \"add\" ? change.ln1 : undefined;\n                        const newLineNumber = change.type !== \"del\" ? change.ln2 : undefined;\n                        console.log(\"\\uD83D\\uDD0D 处理change:\", {\n                            originalType: change.type,\n                            mappedType: changeType,\n                            oldLine: oldLineNumber,\n                            newLine: newLineNumber,\n                            content: ((_change_value = change.value) === null || _change_value === void 0 ? void 0 : _change_value.substring(0, 20)) || \"\"\n                        });\n                        return {\n                            type: changeType,\n                            oldLineNumber,\n                            newLineNumber,\n                            content: change.value || \"\",\n                            isNormal: change.type === \"normal\",\n                            // react-diff-view兼容字段\n                            ln1: oldLineNumber,\n                            ln2: newLineNumber,\n                            value: change.value || \"\"\n                        };\n                    }),\n                    content: hunk.changes.map((c)=>c.value || \"\").join(\"\")\n                };\n            });\n            return {\n                hunks,\n                oldRevision: \"original\",\n                newRevision: \"modified\",\n                type: this.detectChangeType(original, modified)\n            };\n        } catch (error) {\n            console.error(\"❌ 生成react-diff-view数据失败:\", error);\n            // 返回空的diff数据\n            return {\n                hunks: [],\n                oldRevision: \"original\",\n                newRevision: \"modified\",\n                type: \"modify\"\n            };\n        }\n    }\n    /**\r\n   * 查找下一个差异位置\r\n   * 用于差异导航功能\r\n   */ findNextDiff(hunks, currentLine) {\n        for (const hunk of hunks){\n            // 查找第一个大于当前行的差异\n            const diffLine = Math.min(hunk.oldStart, hunk.newStart);\n            if (diffLine > currentLine) {\n                return diffLine;\n            }\n        }\n        return null; // 没有找到下一个差异\n    }\n    /**\r\n   * 查找上一个差异位置\r\n   * 用于差异导航功能\r\n   */ findPrevDiff(hunks, currentLine) {\n        // 从后往前查找\n        for(let i = hunks.length - 1; i >= 0; i--){\n            const hunk = hunks[i];\n            const diffLine = Math.min(hunk.oldStart, hunk.newStart);\n            if (diffLine < currentLine) {\n                return diffLine;\n            }\n        }\n        return null; // 没有找到上一个差异\n    }\n    /**\r\n   * 计算差异统计信息\r\n   * 复用现有逻辑但适配新的数据格式\r\n   */ calculateDiffStats(original, modified) {\n        const originalLines = original.split(\"\\n\");\n        const modifiedLines = modified.split(\"\\n\");\n        let additions = 0;\n        let deletions = 0;\n        let modifications = 0;\n        const maxLines = Math.max(originalLines.length, modifiedLines.length);\n        for(let i = 0; i < maxLines; i++){\n            const originalLine = originalLines[i];\n            const modifiedLine = modifiedLines[i];\n            if (originalLine === undefined) {\n                additions++;\n            } else if (modifiedLine === undefined) {\n                deletions++;\n            } else if (originalLine !== modifiedLine) {\n                modifications++;\n            }\n        }\n        return {\n            additions,\n            deletions,\n            modifications,\n            totalChanges: additions + deletions + modifications\n        };\n    }\n    /**\r\n   * 根据文件路径获取文件信息\r\n   * 辅助方法，用于获取文件内容\r\n   */ async getFileByPath(artworkId, filePath) {\n        try {\n            // 直接使用FileTreeService实例\n            const { FileTreeService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\"));\n            const fileTreeService = FileTreeService.getInstance();\n            // 获取文件树\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (!fileTreeResult.success || !fileTreeResult.data) {\n                return {\n                    success: false,\n                    error: \"无法获取文件树\"\n                };\n            }\n            // 在文件树中查找目标文件\n            const findFileInTree = (node, targetPath)=>{\n                // 增强的路径标准化函数\n                const normalizePath = (path)=>{\n                    if (!path) return \"\";\n                    // 移除开头的斜杠，统一分隔符，但保持大小写（中文路径敏感）\n                    return path.replace(/^\\/+/, \"\").replace(/\\\\/g, \"/\").replace(/\\/+/g, \"/\");\n                };\n                const normalizedTarget = normalizePath(targetPath);\n                const normalizedNodePath = normalizePath(node.path || \"\");\n                const normalizedNodeName = normalizePath(node.name || \"\");\n                console.log(\"\\uD83D\\uDD0D 查找文件详情:\", {\n                    targetPath,\n                    normalizedTarget,\n                    nodeName: node.name,\n                    nodePath: node.path,\n                    normalizedNodePath,\n                    normalizedNodeName,\n                    nodeType: node.type\n                });\n                // 🔧 排除根目录：根目录不应该被匹配为文件\n                if (node.name === \"root\" || node.path === \"/\" || normalizedNodePath === \"\") {\n                    console.log(\"⏭️ 跳过根目录，继续搜索子节点\");\n                    // 直接搜索子节点，不匹配根目录本身\n                    if (node.children && Array.isArray(node.children)) {\n                        for (const child of node.children){\n                            const found = findFileInTree(child, targetPath);\n                            if (found) return found;\n                        }\n                    }\n                    return null;\n                }\n                // 🔧 只匹配文件类型的节点，排除文件夹\n                const isFile = node.type === \"file\" || !node.children || node.children.length === 0;\n                // 1. 精确路径匹配（仅限文件）\n                if (isFile && normalizedNodePath === normalizedTarget) {\n                    console.log(\"✅ 精确路径匹配（文件）:\", normalizedNodePath);\n                    return node;\n                }\n                // 2. 文件名匹配（仅限文件）\n                if (isFile && normalizedNodeName === normalizedTarget) {\n                    console.log(\"✅ 文件名匹配（文件）:\", normalizedNodeName);\n                    return node;\n                }\n                // 3. 路径末尾匹配（仅限文件）\n                const targetParts = normalizedTarget.split(\"/\").filter((p)=>p);\n                const nodeParts = normalizedNodePath.split(\"/\").filter((p)=>p);\n                if (isFile && targetParts.length <= nodeParts.length) {\n                    const nodePathSuffix = nodeParts.slice(-targetParts.length).join(\"/\");\n                    if (nodePathSuffix === normalizedTarget) {\n                        console.log(\"✅ 路径后缀匹配（文件）:\", {\n                            nodePathSuffix,\n                            normalizedTarget\n                        });\n                        return node;\n                    }\n                }\n                // 4. 文件名部分匹配（仅限文件）\n                const targetFileName = targetParts[targetParts.length - 1];\n                if (isFile && targetFileName && normalizedNodeName === targetFileName) {\n                    console.log(\"✅ 文件名部分匹配（文件）:\", {\n                        targetFileName,\n                        normalizedNodeName\n                    });\n                    return node;\n                }\n                // 5. 模糊匹配（仅限文件）\n                if (isFile && (normalizedNodePath.includes(normalizedTarget) || normalizedTarget.includes(normalizedNodePath))) {\n                    console.log(\"✅ 模糊路径匹配（文件）:\", {\n                        normalizedNodePath,\n                        normalizedTarget\n                    });\n                    return node;\n                }\n                // 递归检查子节点\n                if (node.children && Array.isArray(node.children)) {\n                    for (const child of node.children){\n                        const found = findFileInTree(child, targetPath);\n                        if (found) return found;\n                    }\n                }\n                return null;\n            };\n            const targetFile = findFileInTree(fileTreeResult.data, filePath);\n            if (!targetFile) {\n                return {\n                    success: false,\n                    error: \"文件不存在\"\n                };\n            }\n            // 获取完整的文件数据\n            const fileResult = await fileTreeService.getFile(targetFile.id);\n            if (!fileResult.success || !fileResult.data) {\n                return {\n                    success: false,\n                    error: \"无法获取文件内容\"\n                };\n            }\n            return {\n                success: true,\n                data: fileResult.data\n            };\n        } catch (error) {\n            return {\n                success: false,\n                error: \"获取文件失败: \".concat(error instanceof Error ? error.message : String(error))\n            };\n        }\n    }\n    /**\r\n   * 检测变更类型\r\n   * 用于react-diff-view的type字段\r\n   */ detectChangeType(original, modified) {\n        if (!original && modified) {\n            return \"add\";\n        } else if (original && !modified) {\n            return \"delete\";\n        } else {\n            return \"modify\";\n        }\n    }\n    /**\r\n   * 处理diff请求\r\n   * 统一的diff请求处理入口\r\n   */ async processDiffRequest(artworkId, diffRequest) {\n        return this.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n    }\n    /**\r\n   * 获取文件内容\r\n   * 公开方法，供其他组件使用\r\n   */ async getFileContent(artworkId, filePath) {\n        try {\n            const fileResult = await this.getFileByPath(artworkId, filePath);\n            if (fileResult.success && fileResult.data) {\n                return fileResult.data.content || \"\";\n            }\n            return \"\";\n        } catch (error) {\n            console.error(\"❌ 获取文件内容失败:\", error);\n            return \"\";\n        }\n    }\n    constructor(){\n    // 不再继承DiffToolService，独立实现\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/editorDiffService.ts\n"));

/***/ })

});