"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_sparql_sparql_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/sparql/sparql.js":
/*!****************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/sparql/sparql.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/sparql/sparql.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"'\", close: \"'\", notIn: [\"string\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".rq\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.angle\", open: \"<\", close: \">\" }\n  ],\n  keywords: [\n    \"add\",\n    \"as\",\n    \"asc\",\n    \"ask\",\n    \"base\",\n    \"by\",\n    \"clear\",\n    \"construct\",\n    \"copy\",\n    \"create\",\n    \"data\",\n    \"delete\",\n    \"desc\",\n    \"describe\",\n    \"distinct\",\n    \"drop\",\n    \"false\",\n    \"filter\",\n    \"from\",\n    \"graph\",\n    \"group\",\n    \"having\",\n    \"in\",\n    \"insert\",\n    \"limit\",\n    \"load\",\n    \"minus\",\n    \"move\",\n    \"named\",\n    \"not\",\n    \"offset\",\n    \"optional\",\n    \"order\",\n    \"prefix\",\n    \"reduced\",\n    \"select\",\n    \"service\",\n    \"silent\",\n    \"to\",\n    \"true\",\n    \"undef\",\n    \"union\",\n    \"using\",\n    \"values\",\n    \"where\",\n    \"with\"\n  ],\n  builtinFunctions: [\n    \"a\",\n    \"abs\",\n    \"avg\",\n    \"bind\",\n    \"bnode\",\n    \"bound\",\n    \"ceil\",\n    \"coalesce\",\n    \"concat\",\n    \"contains\",\n    \"count\",\n    \"datatype\",\n    \"day\",\n    \"encode_for_uri\",\n    \"exists\",\n    \"floor\",\n    \"group_concat\",\n    \"hours\",\n    \"if\",\n    \"iri\",\n    \"isblank\",\n    \"isiri\",\n    \"isliteral\",\n    \"isnumeric\",\n    \"isuri\",\n    \"lang\",\n    \"langmatches\",\n    \"lcase\",\n    \"max\",\n    \"md5\",\n    \"min\",\n    \"minutes\",\n    \"month\",\n    \"now\",\n    \"rand\",\n    \"regex\",\n    \"replace\",\n    \"round\",\n    \"sameterm\",\n    \"sample\",\n    \"seconds\",\n    \"sha1\",\n    \"sha256\",\n    \"sha384\",\n    \"sha512\",\n    \"str\",\n    \"strafter\",\n    \"strbefore\",\n    \"strdt\",\n    \"strends\",\n    \"strlang\",\n    \"strlen\",\n    \"strstarts\",\n    \"struuid\",\n    \"substr\",\n    \"sum\",\n    \"timezone\",\n    \"tz\",\n    \"ucase\",\n    \"uri\",\n    \"uuid\",\n    \"year\"\n  ],\n  // describe tokens\n  ignoreCase: true,\n  tokenizer: {\n    root: [\n      // resource indicators\n      [/<[^\\s\\u00a0>]*>?/, \"tag\"],\n      // strings\n      { include: \"@strings\" },\n      // line comment\n      [/#.*/, \"comment\"],\n      // special chars with special meaning\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[;,.]/, \"delimiter\"],\n      // (prefixed) name\n      [/[_\\w\\d]+:(\\.(?=[\\w_\\-\\\\%])|[:\\w_-]|\\\\[-\\\\_~.!$&'()*+,;=/?#@%]|%[a-f\\d][a-f\\d])*/, \"tag\"],\n      [/:(\\.(?=[\\w_\\-\\\\%])|[:\\w_-]|\\\\[-\\\\_~.!$&'()*+,;=/?#@%]|%[a-f\\d][a-f\\d])+/, \"tag\"],\n      // identifiers, builtinFunctions and keywords\n      [\n        /[$?]?[_\\w\\d]+/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword\" },\n            \"@builtinFunctions\": { token: \"predefined.sql\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // operators\n      [/\\^\\^/, \"operator.sql\"],\n      [/\\^[*+\\-<>=&|^\\/!?]*/, \"operator.sql\"],\n      [/[*+\\-<>=&|\\/!?]/, \"operator.sql\"],\n      // symbol\n      [/@[a-z\\d\\-]*/, \"metatag.html\"],\n      // whitespaces\n      [/\\s+/, \"white\"]\n    ],\n    strings: [\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-terminated single-quoted string\n      [/'$/, \"string.sql\", \"@pop\"],\n      [/'/, \"string.sql\", \"@stringBody\"],\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-terminated single-quoted string\n      [/\"$/, \"string.sql\", \"@pop\"],\n      [/\"/, \"string.sql\", \"@dblStringBody\"]\n    ],\n    // single-quoted strings\n    stringBody: [\n      [/[^\\\\']+/, \"string.sql\"],\n      [/\\\\./, \"string.escape\"],\n      [/'/, \"string.sql\", \"@pop\"]\n    ],\n    // double-quoted strings\n    dblStringBody: [\n      [/[^\\\\\"]+/, \"string.sql\"],\n      [/\\\\./, \"string.escape\"],\n      [/\"/, \"string.sql\", \"@pop\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/sparql/sparql.js\n"));

/***/ })

}]);