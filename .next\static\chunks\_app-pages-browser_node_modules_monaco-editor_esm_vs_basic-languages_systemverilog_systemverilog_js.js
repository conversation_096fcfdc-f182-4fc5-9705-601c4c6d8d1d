"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_systemverilog_systemverilog_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/systemverilog/systemverilog.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/systemverilog/systemverilog.js ***!
  \******************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/systemverilog/systemverilog.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"begin\", \"end\"],\n    [\"case\", \"endcase\"],\n    [\"casex\", \"endcase\"],\n    [\"casez\", \"endcase\"],\n    [\"checker\", \"endchecker\"],\n    [\"class\", \"endclass\"],\n    [\"clocking\", \"endclocking\"],\n    [\"config\", \"endconfig\"],\n    [\"function\", \"endfunction\"],\n    [\"generate\", \"endgenerate\"],\n    [\"group\", \"endgroup\"],\n    [\"interface\", \"endinterface\"],\n    [\"module\", \"endmodule\"],\n    [\"package\", \"endpackage\"],\n    [\"primitive\", \"endprimitive\"],\n    [\"program\", \"endprogram\"],\n    [\"property\", \"endproperty\"],\n    [\"specify\", \"endspecify\"],\n    [\"sequence\", \"endsequence\"],\n    [\"table\", \"endtable\"],\n    [\"task\", \"endtask\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    offSide: false,\n    markers: {\n      start: new RegExp(\n        \"^(?:\\\\s*|.*(?!\\\\/[\\\\/\\\\*])[^\\\\w])(?:begin|case(x|z)?|class|clocking|config|covergroup|function|generate|interface|module|package|primitive|property|program|sequence|specify|table|task)\\\\b\"\n      ),\n      end: new RegExp(\n        \"^(?:\\\\s*|.*(?!\\\\/[\\\\/\\\\*])[^\\\\w])(?:end|endcase|endclass|endclocking|endconfig|endgroup|endfunction|endgenerate|endinterface|endmodule|endpackage|endprimitive|endproperty|endprogram|endsequence|endspecify|endtable|endtask)\\\\b\"\n      )\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".sv\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.angle\", open: \"<\", close: \">\" }\n  ],\n  keywords: [\n    \"accept_on\",\n    \"alias\",\n    \"always\",\n    \"always_comb\",\n    \"always_ff\",\n    \"always_latch\",\n    \"and\",\n    \"assert\",\n    \"assign\",\n    \"assume\",\n    \"automatic\",\n    \"before\",\n    \"begin\",\n    \"bind\",\n    \"bins\",\n    \"binsof\",\n    \"bit\",\n    \"break\",\n    \"buf\",\n    \"bufif0\",\n    \"bufif1\",\n    \"byte\",\n    \"case\",\n    \"casex\",\n    \"casez\",\n    \"cell\",\n    \"chandle\",\n    \"checker\",\n    \"class\",\n    \"clocking\",\n    \"cmos\",\n    \"config\",\n    \"const\",\n    \"constraint\",\n    \"context\",\n    \"continue\",\n    \"cover\",\n    \"covergroup\",\n    \"coverpoint\",\n    \"cross\",\n    \"deassign\",\n    \"default\",\n    \"defparam\",\n    \"design\",\n    \"disable\",\n    \"dist\",\n    \"do\",\n    \"edge\",\n    \"else\",\n    \"end\",\n    \"endcase\",\n    \"endchecker\",\n    \"endclass\",\n    \"endclocking\",\n    \"endconfig\",\n    \"endfunction\",\n    \"endgenerate\",\n    \"endgroup\",\n    \"endinterface\",\n    \"endmodule\",\n    \"endpackage\",\n    \"endprimitive\",\n    \"endprogram\",\n    \"endproperty\",\n    \"endspecify\",\n    \"endsequence\",\n    \"endtable\",\n    \"endtask\",\n    \"enum\",\n    \"event\",\n    \"eventually\",\n    \"expect\",\n    \"export\",\n    \"extends\",\n    \"extern\",\n    \"final\",\n    \"first_match\",\n    \"for\",\n    \"force\",\n    \"foreach\",\n    \"forever\",\n    \"fork\",\n    \"forkjoin\",\n    \"function\",\n    \"generate\",\n    \"genvar\",\n    \"global\",\n    \"highz0\",\n    \"highz1\",\n    \"if\",\n    \"iff\",\n    \"ifnone\",\n    \"ignore_bins\",\n    \"illegal_bins\",\n    \"implements\",\n    \"implies\",\n    \"import\",\n    \"incdir\",\n    \"include\",\n    \"initial\",\n    \"inout\",\n    \"input\",\n    \"inside\",\n    \"instance\",\n    \"int\",\n    \"integer\",\n    \"interconnect\",\n    \"interface\",\n    \"intersect\",\n    \"join\",\n    \"join_any\",\n    \"join_none\",\n    \"large\",\n    \"let\",\n    \"liblist\",\n    \"library\",\n    \"local\",\n    \"localparam\",\n    \"logic\",\n    \"longint\",\n    \"macromodule\",\n    \"matches\",\n    \"medium\",\n    \"modport\",\n    \"module\",\n    \"nand\",\n    \"negedge\",\n    \"nettype\",\n    \"new\",\n    \"nexttime\",\n    \"nmos\",\n    \"nor\",\n    \"noshowcancelled\",\n    \"not\",\n    \"notif0\",\n    \"notif1\",\n    \"null\",\n    \"or\",\n    \"output\",\n    \"package\",\n    \"packed\",\n    \"parameter\",\n    \"pmos\",\n    \"posedge\",\n    \"primitive\",\n    \"priority\",\n    \"program\",\n    \"property\",\n    \"protected\",\n    \"pull0\",\n    \"pull1\",\n    \"pulldown\",\n    \"pullup\",\n    \"pulsestyle_ondetect\",\n    \"pulsestyle_onevent\",\n    \"pure\",\n    \"rand\",\n    \"randc\",\n    \"randcase\",\n    \"randsequence\",\n    \"rcmos\",\n    \"real\",\n    \"realtime\",\n    \"ref\",\n    \"reg\",\n    \"reject_on\",\n    \"release\",\n    \"repeat\",\n    \"restrict\",\n    \"return\",\n    \"rnmos\",\n    \"rpmos\",\n    \"rtran\",\n    \"rtranif0\",\n    \"rtranif1\",\n    \"s_always\",\n    \"s_eventually\",\n    \"s_nexttime\",\n    \"s_until\",\n    \"s_until_with\",\n    \"scalared\",\n    \"sequence\",\n    \"shortint\",\n    \"shortreal\",\n    \"showcancelled\",\n    \"signed\",\n    \"small\",\n    \"soft\",\n    \"solve\",\n    \"specify\",\n    \"specparam\",\n    \"static\",\n    \"string\",\n    \"strong\",\n    \"strong0\",\n    \"strong1\",\n    \"struct\",\n    \"super\",\n    \"supply0\",\n    \"supply1\",\n    \"sync_accept_on\",\n    \"sync_reject_on\",\n    \"table\",\n    \"tagged\",\n    \"task\",\n    \"this\",\n    \"throughout\",\n    \"time\",\n    \"timeprecision\",\n    \"timeunit\",\n    \"tran\",\n    \"tranif0\",\n    \"tranif1\",\n    \"tri\",\n    \"tri0\",\n    \"tri1\",\n    \"triand\",\n    \"trior\",\n    \"trireg\",\n    \"type\",\n    \"typedef\",\n    \"union\",\n    \"unique\",\n    \"unique0\",\n    \"unsigned\",\n    \"until\",\n    \"until_with\",\n    \"untyped\",\n    \"use\",\n    \"uwire\",\n    \"var\",\n    \"vectored\",\n    \"virtual\",\n    \"void\",\n    \"wait\",\n    \"wait_order\",\n    \"wand\",\n    \"weak\",\n    \"weak0\",\n    \"weak1\",\n    \"while\",\n    \"wildcard\",\n    \"wire\",\n    \"with\",\n    \"within\",\n    \"wor\",\n    \"xnor\",\n    \"xor\"\n  ],\n  builtin_gates: [\n    \"and\",\n    \"nand\",\n    \"nor\",\n    \"or\",\n    \"xor\",\n    \"xnor\",\n    \"buf\",\n    \"not\",\n    \"bufif0\",\n    \"bufif1\",\n    \"notif1\",\n    \"notif0\",\n    \"cmos\",\n    \"nmos\",\n    \"pmos\",\n    \"rcmos\",\n    \"rnmos\",\n    \"rpmos\",\n    \"tran\",\n    \"tranif1\",\n    \"tranif0\",\n    \"rtran\",\n    \"rtranif1\",\n    \"rtranif0\"\n  ],\n  operators: [\n    // assignment operators\n    \"=\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"%=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"<<=\",\n    \">>+\",\n    \"<<<=\",\n    \">>>=\",\n    // conditional expression\n    \"?\",\n    \":\",\n    // Unary operators\n    \"+\",\n    \"-\",\n    \"!\",\n    \"~\",\n    \"&\",\n    \"~&\",\n    \"|\",\n    \"~|\",\n    \"^\",\n    \"~^\",\n    \"^~\",\n    //binary operators\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"==\",\n    \"!=\",\n    \"===\",\n    \"!==\",\n    \"==?\",\n    \"!=?\",\n    \"&&\",\n    \"||\",\n    \"**\",\n    \"<\",\n    \"<=\",\n    \">\",\n    \">=\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \">>\",\n    \"<<\",\n    \">>>\",\n    \"<<<\",\n    // increment or decrement operator\n    \"++\",\n    \"--\",\n    //binary logical operator\n    \"->\",\n    \"<->\",\n    // binary set membership operator\n    \"inside\",\n    // binary distrubution operator\n    \"dist\",\n    \"::\",\n    \"+:\",\n    \"-:\",\n    \"*>\",\n    \"&&&\",\n    \"|->\",\n    \"|=>\",\n    \"#=#\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%#]+/,\n  escapes: /%%|\\\\(?:[antvf\\\\\"']|x[0-9A-Fa-f]{1,2}|[0-7]{1,3})/,\n  identifier: /(?:[a-zA-Z_][a-zA-Z0-9_$\\.]*|\\\\\\S+ )/,\n  systemcall: /[$][a-zA-Z0-9_]+/,\n  timeunits: /s|ms|us|ns|ps|fs/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // module instances\n      [\n        /^(\\s*)(@identifier)/,\n        [\n          \"\",\n          {\n            cases: {\n              \"@builtin_gates\": {\n                token: \"keyword.$2\",\n                next: \"@module_instance\"\n              },\n              table: {\n                token: \"keyword.$2\",\n                next: \"@table\"\n              },\n              \"@keywords\": { token: \"keyword.$2\" },\n              \"@default\": {\n                token: \"identifier\",\n                next: \"@module_instance\"\n              }\n            }\n          }\n        ]\n      ],\n      // include statements\n      [/^\\s*`include/, { token: \"keyword.directive.include\", next: \"@include\" }],\n      // Preprocessor directives\n      [/^\\s*`\\s*\\w+/, \"keyword\"],\n      // identifiers and keywords\n      { include: \"@identifier_or_keyword\" },\n      // whitespace and comments\n      { include: \"@whitespace\" },\n      // (* attributes *).\n      [/\\(\\*.*\\*\\)/, \"annotation\"],\n      // Systemcall\n      [/@systemcall/, \"variable.predefined\"],\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      { include: \"@numbers\" },\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      { include: \"@strings\" }\n    ],\n    identifier_or_keyword: [\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/\\d+?[\\d_]*(?:\\.[\\d_]+)?[eE][\\-+]?\\d+/, \"number.float\"],\n      [/\\d+?[\\d_]*\\.[\\d_]+(?:\\s*@timeunits)?/, \"number.float\"],\n      [/(?:\\d+?[\\d_]*\\s*)?'[sS]?[dD]\\s*[0-9xXzZ?]+?[0-9xXzZ?_]*/, \"number\"],\n      [/(?:\\d+?[\\d_]*\\s*)?'[sS]?[bB]\\s*[0-1xXzZ?]+?[0-1xXzZ?_]*/, \"number.binary\"],\n      [/(?:\\d+?[\\d_]*\\s*)?'[sS]?[oO]\\s*[0-7xXzZ?]+?[0-7xXzZ?_]*/, \"number.octal\"],\n      [/(?:\\d+?[\\d_]*\\s*)?'[sS]?[hH]\\s*[0-9a-fA-FxXzZ?]+?[0-9a-fA-FxXzZ?_]*/, \"number.hex\"],\n      [/1step/, \"number\"],\n      [/[\\dxXzZ]+?[\\dxXzZ_]*(?:\\s*@timeunits)?/, \"number\"],\n      [/'[01xXzZ]+/, \"number\"]\n    ],\n    module_instance: [\n      { include: \"@whitespace\" },\n      [/(#?)(\\()/, [\"\", { token: \"@brackets\", next: \"@port_connection\" }]],\n      [/@identifier\\s*[;={}\\[\\],]/, { token: \"@rematch\", next: \"@pop\" }],\n      [/@symbols|[;={}\\[\\],]/, { token: \"@rematch\", next: \"@pop\" }],\n      [/@identifier/, \"type\"],\n      [/;/, \"delimiter\", \"@pop\"]\n    ],\n    port_connection: [\n      { include: \"@identifier_or_keyword\" },\n      { include: \"@whitespace\" },\n      [/@systemcall/, \"variable.predefined\"],\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      [/[,]/, \"delimiter\"],\n      [/\\(/, \"@brackets\", \"@port_connection\"],\n      [/\\)/, \"@brackets\", \"@pop\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    strings: [\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    include: [\n      [\n        /(\\s*)(\")([\\w*\\/*]*)(.\\w*)(\")/,\n        [\n          \"\",\n          \"string.include.identifier\",\n          \"string.include.identifier\",\n          \"string.include.identifier\",\n          { token: \"string.include.identifier\", next: \"@pop\" }\n        ]\n      ],\n      [\n        /(\\s*)(<)([\\w*\\/*]*)(.\\w*)(>)/,\n        [\n          \"\",\n          \"string.include.identifier\",\n          \"string.include.identifier\",\n          \"string.include.identifier\",\n          { token: \"string.include.identifier\", next: \"@pop\" }\n        ]\n      ]\n    ],\n    table: [\n      { include: \"@whitespace\" },\n      [/[()]/, \"@brackets\"],\n      [/[:;]/, \"delimiter\"],\n      [/[01\\-*?xXbBrRfFpPnN]/, \"variable.predefined\"],\n      [\"endtable\", \"keyword.endtable\", \"@pop\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/systemverilog/systemverilog.js\n"));

/***/ })

}]);