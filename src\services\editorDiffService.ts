/**
 * 编辑器差异计算服务
 * 扩展现有的DiffToolService，为编辑器diff对比功能提供专用方法
 */

import { parseDiff, createPatch } from 'diff';
import { 
  EditorDiffData, 
  DiffHunk, 
  ReactDiffViewData,
  DiffRequest 
} from '@/types/diff';
import { DatabaseResult, DiffStats } from '@/types';

export class EditorDiffCalculator {
  private static instance: EditorDiffCalculator;

  private constructor() {
    // 不再继承DiffToolService，独立实现
  }

  /**
   * 获取编辑器差异计算服务单例
   */
  public static getInstance(): EditorDiffCalculator {
    if (!EditorDiffCalculator.instance) {
      EditorDiffCalculator.instance = new EditorDiffCalculator();
    }
    return EditorDiffCalculator.instance;
  }

  /**
   * 计算编辑器专用的diff数据
   * 复用现有的文件获取和diff计算逻辑
   */
  public async calculateEditorDiff(
    artworkId: string,
    filePath: string,
    modifiedContent: string,
    operation: 'append' | 'replace' = 'replace'
  ): Promise<DatabaseResult<EditorDiffData>> {
    try {
      // 1. 检查文件是否存在
      const { FileTreeService } = await import('./fileTreeService');
      const fileTreeService = FileTreeService.getInstance();
      const fileExists = await fileTreeService.checkFileExists(artworkId, filePath);
      let originalContent = '';
      let fileId = '';

      if (fileExists) {
        // 文件存在，获取原始内容
        const fileTreeResult = await this.getFileByPath(artworkId, filePath);
        if (!fileTreeResult.success || !fileTreeResult.data) {
          return {
            success: false,
            error: `无法获取文件内容: ${fileTreeResult.error}`
          };
        }
        originalContent = fileTreeResult.data.content || '';
        fileId = fileTreeResult.data.id;
      } else {
        // 文件不存在，原始内容为空
        originalContent = '';
        fileId = `new-file-${Date.now()}`;
      }

      // 2. 根据操作类型生成最终的修改内容
      let finalModifiedContent = modifiedContent;
      if (operation === 'append' && originalContent) {
        finalModifiedContent = originalContent + '\n' + modifiedContent;
      } else if (operation === 'replace' && modifiedContent.includes('|||')) {
        // 处理正则替换格式：pattern|||replacement
        const [pattern, replacement] = modifiedContent.split('|||');
        try {
          const regex = new RegExp(pattern, 'g');
          finalModifiedContent = originalContent.replace(regex, replacement);
        } catch (error) {
          return {
            success: false,
            error: `正则表达式错误: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      }

      // 3. 使用现有的diff计算逻辑
      const diffStats = this.calculateDiffStats(originalContent, finalModifiedContent);

      // 4. 生成react-diff-view兼容的数据
      const reactDiffData = this.generateReactDiffViewData(
        originalContent,
        finalModifiedContent,
        filePath
      );

      // 5. 构建EditorDiffData
      const editorDiffData: EditorDiffData = {
        // 复用现有的DiffStats字段
        additions: diffStats.additions,
        deletions: diffStats.deletions,
        modifications: diffStats.modifications,
        totalChanges: diffStats.totalChanges,
        
        // 新增编辑器专用字段
        hunks: reactDiffData.hunks,
        oldSource: originalContent,
        newSource: finalModifiedContent,
        filePath,
        operation
      };

      return { success: true, data: editorDiffData };

    } catch (error) {
      const errorMessage = `计算编辑器diff失败: ${error instanceof Error ? error.message : String(error)}`;
      console.error('❌', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 生成react-diff-view兼容的数据格式
   * 使用diff库生成hunks数据
   */
  public generateReactDiffViewData(
    original: string,
    modified: string,
    filePath: string
  ): ReactDiffViewData {
    try {
      // 使用diff库生成patch
      const patch = createPatch(filePath, original, modified, '', '');
      
      // 解析patch为结构化数据
      const parsedDiff = parseDiff(patch);
      
      if (parsedDiff.length === 0) {
        // 没有差异的情况
        return {
          hunks: [],
          oldRevision: 'original',
          newRevision: 'modified',
          type: 'modify'
        };
      }

      const file = parsedDiff[0];
      
      // 转换hunks格式
      const hunks: DiffHunk[] = file.hunks.map((hunk: any) => {
        console.log('🔍 处理hunk:', {
          oldStart: hunk.oldStart,
          oldLines: hunk.oldLines,
          newStart: hunk.newStart,
          newLines: hunk.newLines,
          changesCount: hunk.changes.length
        });

        return {
          oldStart: hunk.oldStart,
          oldLines: hunk.oldLines,
          newStart: hunk.newStart,
          newLines: hunk.newLines,
          changes: hunk.changes.map((change: any) => {
            const changeType = change.type === 'add' ? 'insert' : 
                             change.type === 'del' ? 'delete' : 'normal';
            const oldLineNumber = change.type !== 'add' ? change.ln1 : undefined;
            const newLineNumber = change.type !== 'del' ? change.ln2 : undefined;
            
            console.log('🔍 处理change:', {
              originalType: change.type,
              mappedType: changeType,
              oldLine: oldLineNumber,
              newLine: newLineNumber,
              content: change.value?.substring(0, 20) || ''
            });
            
            return {
              type: changeType as 'normal' | 'insert' | 'delete',
              oldLineNumber,
              newLineNumber,
              content: change.value || '',
              isNormal: change.type === 'normal',
              // react-diff-view兼容字段
              ln1: oldLineNumber,
              ln2: newLineNumber,
              value: change.value || ''
            };
          }),
          content: hunk.changes.map((c: any) => c.value || '').join('')
        };
      });

      return {
        hunks,
        oldRevision: 'original',
        newRevision: 'modified',
        type: this.detectChangeType(original, modified)
      };

    } catch (error) {
      console.error('❌ 生成react-diff-view数据失败:', error);
      // 返回空的diff数据
      return {
        hunks: [],
        oldRevision: 'original',
        newRevision: 'modified',
        type: 'modify'
      };
    }
  }

  /**
   * 查找下一个差异位置
   * 用于差异导航功能
   */
  public findNextDiff(hunks: DiffHunk[], currentLine: number): number | null {
    for (const hunk of hunks) {
      // 查找第一个大于当前行的差异
      const diffLine = Math.min(hunk.oldStart, hunk.newStart);
      if (diffLine > currentLine) {
        return diffLine;
      }
    }
    return null; // 没有找到下一个差异
  }

  /**
   * 查找上一个差异位置
   * 用于差异导航功能
   */
  public findPrevDiff(hunks: DiffHunk[], currentLine: number): number | null {
    // 从后往前查找
    for (let i = hunks.length - 1; i >= 0; i--) {
      const hunk = hunks[i];
      const diffLine = Math.min(hunk.oldStart, hunk.newStart);
      if (diffLine < currentLine) {
        return diffLine;
      }
    }
    return null; // 没有找到上一个差异
  }

  /**
   * 计算差异统计信息
   * 复用现有逻辑但适配新的数据格式
   */
  public calculateDiffStats(original: string, modified: string): DiffStats {
    const originalLines = original.split('\n');
    const modifiedLines = modified.split('\n');
    
    let additions = 0;
    let deletions = 0;
    let modifications = 0;

    const maxLines = Math.max(originalLines.length, modifiedLines.length);
    
    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[i];
      const modifiedLine = modifiedLines[i];

      if (originalLine === undefined) {
        additions++;
      } else if (modifiedLine === undefined) {
        deletions++;
      } else if (originalLine !== modifiedLine) {
        modifications++;
      }
    }

    return {
      additions,
      deletions,
      modifications,
      totalChanges: additions + deletions + modifications
    };
  }

  /**
   * 根据文件路径获取文件信息
   * 辅助方法，用于获取文件内容
   */
  private async getFileByPath(artworkId: string, filePath: string): Promise<DatabaseResult<any>> {
    try {
      // 直接使用FileTreeService实例
      const { FileTreeService } = await import('./fileTreeService');
      const fileTreeService = FileTreeService.getInstance();
      
      // 获取文件树
      const fileTreeResult = await fileTreeService.getFileTree(artworkId);
      if (!fileTreeResult.success || !fileTreeResult.data) {
        return { success: false, error: '无法获取文件树' };
      }

      // 在文件树中查找目标文件
      const findFileInTree = (node: any, targetPath: string): any => {
        // 增强的路径标准化函数
        const normalizePath = (path: string): string => {
          if (!path) return '';
          // 移除开头的斜杠，统一分隔符，但保持大小写（中文路径敏感）
          return path.replace(/^\/+/, '').replace(/\\/g, '/').replace(/\/+/g, '/');
        };
        
        const normalizedTarget = normalizePath(targetPath);
        const normalizedNodePath = normalizePath(node.path || '');
        const normalizedNodeName = normalizePath(node.name || '');
        
        console.log('🔍 查找文件详情:', { 
          targetPath, 
          normalizedTarget,
          nodeName: node.name,
          nodePath: node.path,
          normalizedNodePath,
          normalizedNodeName,
          nodeType: node.type
        });
        
        // 🔧 排除根目录：根目录不应该被匹配为文件
        if (node.name === 'root' || node.path === '/' || normalizedNodePath === '') {
          console.log('⏭️ 跳过根目录，继续搜索子节点');
          // 直接搜索子节点，不匹配根目录本身
          if (node.children && Array.isArray(node.children)) {
            for (const child of node.children) {
              const found = findFileInTree(child, targetPath);
              if (found) return found;
            }
          }
          return null;
        }
        
        // 🔧 只匹配文件类型的节点，排除文件夹
        const isFile = node.type === 'file' || (!node.children || node.children.length === 0);
        
        // 1. 精确路径匹配（仅限文件）
        if (isFile && normalizedNodePath === normalizedTarget) {
          console.log('✅ 精确路径匹配（文件）:', normalizedNodePath);
          return node;
        }
        
        // 2. 文件名匹配（仅限文件）
        if (isFile && normalizedNodeName === normalizedTarget) {
          console.log('✅ 文件名匹配（文件）:', normalizedNodeName);
          return node;
        }
        
        // 3. 路径末尾匹配（仅限文件）
        const targetParts = normalizedTarget.split('/').filter(p => p);
        const nodeParts = normalizedNodePath.split('/').filter(p => p);
        
        if (isFile && targetParts.length <= nodeParts.length) {
          const nodePathSuffix = nodeParts.slice(-targetParts.length).join('/');
          if (nodePathSuffix === normalizedTarget) {
            console.log('✅ 路径后缀匹配（文件）:', { nodePathSuffix, normalizedTarget });
            return node;
          }
        }
        
        // 4. 文件名部分匹配（仅限文件）
        const targetFileName = targetParts[targetParts.length - 1];
        if (isFile && targetFileName && normalizedNodeName === targetFileName) {
          console.log('✅ 文件名部分匹配（文件）:', { targetFileName, normalizedNodeName });
          return node;
        }
        
        // 5. 模糊匹配（仅限文件）
        if (isFile && (normalizedNodePath.includes(normalizedTarget) || normalizedTarget.includes(normalizedNodePath))) {
          console.log('✅ 模糊路径匹配（文件）:', { normalizedNodePath, normalizedTarget });
          return node;
        }
        
        // 递归检查子节点
        if (node.children && Array.isArray(node.children)) {
          for (const child of node.children) {
            const found = findFileInTree(child, targetPath);
            if (found) return found;
          }
        }
        
        return null;
      };

      const targetFile = findFileInTree(fileTreeResult.data, filePath);
      
      if (!targetFile) {
        return { success: false, error: '文件不存在' };
      }

      // 获取完整的文件数据
      const fileResult = await fileTreeService.getFile(targetFile.id);
      if (!fileResult.success || !fileResult.data) {
        return { success: false, error: '无法获取文件内容' };
      }

      return { success: true, data: fileResult.data };

    } catch (error) {
      return { 
        success: false, 
        error: `获取文件失败: ${error instanceof Error ? error.message : String(error)}` 
      };
    }
  }

  /**
   * 检测变更类型
   * 用于react-diff-view的type字段
   */
  private detectChangeType(original: string, modified: string): 'add' | 'delete' | 'modify' | 'rename' | 'copy' {
    if (!original && modified) {
      return 'add';
    } else if (original && !modified) {
      return 'delete';
    } else {
      return 'modify';
    }
  }

  /**
   * 处理diff请求
   * 统一的diff请求处理入口
   */
  public async processDiffRequest(
    artworkId: string,
    diffRequest: DiffRequest
  ): Promise<DatabaseResult<EditorDiffData>> {
    return this.calculateEditorDiff(
      artworkId,
      diffRequest.filePath,
      diffRequest.content,
      diffRequest.operation
    );
  }

  /**
   * 获取文件内容
   * 公开方法，供其他组件使用
   */
  public async getFileContent(artworkId: string, filePath: string): Promise<string> {
    try {
      const fileResult = await this.getFileByPath(artworkId, filePath);
      if (fileResult.success && fileResult.data) {
        return fileResult.data.content || '';
      }
      return '';
    } catch (error) {
      console.error('❌ 获取文件内容失败:', error);
      return '';
    }
  }
}