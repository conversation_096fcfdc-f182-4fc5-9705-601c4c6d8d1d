"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/services/editorDiffService.ts":
/*!*******************************************!*\
  !*** ./src/services/editorDiffService.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditorDiffCalculator: function() { return /* binding */ EditorDiffCalculator; }\n/* harmony export */ });\n/* harmony import */ var diff__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! diff */ \"(app-pages-browser)/./node_modules/diff/lib/index.mjs\");\n/**\r\n * 编辑器差异计算服务\r\n * 扩展现有的DiffToolService，为编辑器diff对比功能提供专用方法\r\n */ \nclass EditorDiffCalculator {\n    /**\r\n   * 获取编辑器差异计算服务单例\r\n   */ static getInstance() {\n        if (!EditorDiffCalculator.instance) {\n            EditorDiffCalculator.instance = new EditorDiffCalculator();\n        }\n        return EditorDiffCalculator.instance;\n    }\n    /**\r\n   * 计算编辑器专用的diff数据\r\n   * 复用现有的文件获取和diff计算逻辑\r\n   */ async calculateEditorDiff(artworkId, filePath, modifiedContent) {\n        let operation = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : \"replace\";\n        try {\n            // 1. 检查文件是否存在\n            const { FileTreeService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\"));\n            const fileTreeService = FileTreeService.getInstance();\n            const fileExists = await fileTreeService.checkFileExists(artworkId, filePath);\n            let originalContent = \"\";\n            let fileId = \"\";\n            if (fileExists) {\n                // 文件存在，获取原始内容\n                const fileTreeResult = await this.getFileByPath(artworkId, filePath);\n                if (!fileTreeResult.success || !fileTreeResult.data) {\n                    return {\n                        success: false,\n                        error: \"无法获取文件内容: \".concat(fileTreeResult.error)\n                    };\n                }\n                originalContent = fileTreeResult.data.content || \"\";\n                fileId = fileTreeResult.data.id;\n            } else {\n                // 文件不存在，原始内容为空\n                originalContent = \"\";\n                fileId = \"new-file-\".concat(Date.now());\n            }\n            // 2. 根据操作类型生成最终的修改内容\n            let finalModifiedContent = modifiedContent;\n            if (operation === \"append\" && originalContent) {\n                finalModifiedContent = originalContent + \"\\n\" + modifiedContent;\n            } else if (operation === \"replace\" && modifiedContent.includes(\"|||\")) {\n                // 处理正则替换格式：pattern|||replacement\n                const [pattern, replacement] = modifiedContent.split(\"|||\");\n                try {\n                    const regex = new RegExp(pattern, \"g\");\n                    finalModifiedContent = originalContent.replace(regex, replacement);\n                } catch (error) {\n                    return {\n                        success: false,\n                        error: \"正则表达式错误: \".concat(error instanceof Error ? error.message : String(error))\n                    };\n                }\n            }\n            // 3. 使用现有的diff计算逻辑\n            const diffStats = this.calculateDiffStats(originalContent, finalModifiedContent);\n            // 4. 生成react-diff-view兼容的数据\n            const reactDiffData = this.generateReactDiffViewData(originalContent, finalModifiedContent, filePath);\n            // 5. 构建EditorDiffData\n            const editorDiffData = {\n                // 复用现有的DiffStats字段\n                additions: diffStats.additions,\n                deletions: diffStats.deletions,\n                modifications: diffStats.modifications,\n                totalChanges: diffStats.totalChanges,\n                // 新增编辑器专用字段\n                hunks: reactDiffData.hunks,\n                oldSource: originalContent,\n                newSource: finalModifiedContent,\n                filePath,\n                operation\n            };\n            return {\n                success: true,\n                data: editorDiffData\n            };\n        } catch (error) {\n            const errorMessage = \"计算编辑器diff失败: \".concat(error instanceof Error ? error.message : String(error));\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 生成react-diff-view兼容的数据格式\r\n   * 使用diff库生成hunks数据\r\n   */ generateReactDiffViewData(original, modified, filePath) {\n        try {\n            // 使用diff库生成patch\n            const patch = (0,diff__WEBPACK_IMPORTED_MODULE_0__.createPatch)(filePath, original, modified, \"\", \"\");\n            // 解析patch为结构化数据\n            const parsedDiff = (0,diff__WEBPACK_IMPORTED_MODULE_0__.parsePatch)(patch);\n            if (parsedDiff.length === 0) {\n                // 没有差异的情况\n                return {\n                    hunks: [],\n                    oldRevision: \"original\",\n                    newRevision: \"modified\",\n                    type: \"modify\"\n                };\n            }\n            const file = parsedDiff[0];\n            // 转换hunks格式\n            const hunks = file.hunks.map((hunk)=>{\n                var _hunk_changes, _hunk_lines;\n                console.log(\"\\uD83D\\uDD0D 处理hunk:\", {\n                    oldStart: hunk.oldStart,\n                    oldLines: hunk.oldLines,\n                    newStart: hunk.newStart,\n                    newLines: hunk.newLines,\n                    changesCount: ((_hunk_changes = hunk.changes) === null || _hunk_changes === void 0 ? void 0 : _hunk_changes.length) || 0,\n                    linesCount: ((_hunk_lines = hunk.lines) === null || _hunk_lines === void 0 ? void 0 : _hunk_lines.length) || 0,\n                    hasChanges: !!hunk.changes,\n                    hasLines: !!hunk.lines,\n                    hunkKeys: Object.keys(hunk)\n                });\n                // 使用lines属性而不是changes属性（diff库的实际数据结构）\n                const changes = (hunk.lines || []).map((line, index)=>{\n                    // 解析line格式：第一个字符表示操作类型\n                    const firstChar = line.charAt(0);\n                    const content = line.substring(1);\n                    let changeType = \"normal\";\n                    if (firstChar === \"+\") {\n                        changeType = \"insert\";\n                    } else if (firstChar === \"-\") {\n                        changeType = \"delete\";\n                    }\n                    console.log(\"\\uD83D\\uDD0D 处理line:\", {\n                        index,\n                        firstChar,\n                        changeType,\n                        content: content.substring(0, 20) + \"...\"\n                    });\n                    return {\n                        type: changeType,\n                        content: content,\n                        // react-diff-view兼容字段\n                        ln1: changeType !== \"insert\" ? hunk.oldStart + index : undefined,\n                        ln2: changeType !== \"delete\" ? hunk.newStart + index : undefined,\n                        value: content\n                    };\n                });\n                return {\n                    oldStart: hunk.oldStart,\n                    oldLines: hunk.oldLines,\n                    newStart: hunk.newStart,\n                    newLines: hunk.newLines,\n                    content: changes.map((c)=>c.value || \"\").join(\"\")\n                };\n            });\n            return {\n                hunks,\n                oldRevision: \"original\",\n                newRevision: \"modified\",\n                type: this.detectChangeType(original, modified)\n            };\n        } catch (error) {\n            console.error(\"❌ 生成react-diff-view数据失败:\", error);\n            // 返回空的diff数据\n            return {\n                hunks: [],\n                oldRevision: \"original\",\n                newRevision: \"modified\",\n                type: \"modify\"\n            };\n        }\n    }\n    /**\r\n   * 查找下一个差异位置\r\n   * 用于差异导航功能\r\n   */ findNextDiff(hunks, currentLine) {\n        for (const hunk of hunks){\n            // 查找第一个大于当前行的差异\n            const diffLine = Math.min(hunk.oldStart, hunk.newStart);\n            if (diffLine > currentLine) {\n                return diffLine;\n            }\n        }\n        return null; // 没有找到下一个差异\n    }\n    /**\r\n   * 查找上一个差异位置\r\n   * 用于差异导航功能\r\n   */ findPrevDiff(hunks, currentLine) {\n        // 从后往前查找\n        for(let i = hunks.length - 1; i >= 0; i--){\n            const hunk = hunks[i];\n            const diffLine = Math.min(hunk.oldStart, hunk.newStart);\n            if (diffLine < currentLine) {\n                return diffLine;\n            }\n        }\n        return null; // 没有找到上一个差异\n    }\n    /**\r\n   * 计算差异统计信息\r\n   * 复用现有逻辑但适配新的数据格式\r\n   */ calculateDiffStats(original, modified) {\n        const originalLines = original.split(\"\\n\");\n        const modifiedLines = modified.split(\"\\n\");\n        let additions = 0;\n        let deletions = 0;\n        let modifications = 0;\n        const maxLines = Math.max(originalLines.length, modifiedLines.length);\n        for(let i = 0; i < maxLines; i++){\n            const originalLine = originalLines[i];\n            const modifiedLine = modifiedLines[i];\n            if (originalLine === undefined) {\n                additions++;\n            } else if (modifiedLine === undefined) {\n                deletions++;\n            } else if (originalLine !== modifiedLine) {\n                modifications++;\n            }\n        }\n        return {\n            additions,\n            deletions,\n            modifications,\n            totalChanges: additions + deletions + modifications\n        };\n    }\n    /**\r\n   * 根据文件路径获取文件信息\r\n   * 辅助方法，用于获取文件内容\r\n   */ async getFileByPath(artworkId, filePath) {\n        try {\n            // 直接使用FileTreeService实例\n            const { FileTreeService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\"));\n            const fileTreeService = FileTreeService.getInstance();\n            // 获取文件树\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (!fileTreeResult.success || !fileTreeResult.data) {\n                return {\n                    success: false,\n                    error: \"无法获取文件树\"\n                };\n            }\n            // 在文件树中查找目标文件\n            const findFileInTree = (node, targetPath)=>{\n                // 增强的路径标准化函数\n                const normalizePath = (path)=>{\n                    if (!path) return \"\";\n                    // 移除开头的斜杠，统一分隔符，但保持大小写（中文路径敏感）\n                    return path.replace(/^\\/+/, \"\").replace(/\\\\/g, \"/\").replace(/\\/+/g, \"/\");\n                };\n                const normalizedTarget = normalizePath(targetPath);\n                const normalizedNodePath = normalizePath(node.path || \"\");\n                const normalizedNodeName = normalizePath(node.name || \"\");\n                console.log(\"\\uD83D\\uDD0D 查找文件详情:\", {\n                    targetPath,\n                    normalizedTarget,\n                    nodeName: node.name,\n                    nodePath: node.path,\n                    normalizedNodePath,\n                    normalizedNodeName,\n                    nodeType: node.type\n                });\n                // 🔧 排除根目录：根目录不应该被匹配为文件\n                if (node.name === \"root\" || node.path === \"/\" || normalizedNodePath === \"\") {\n                    console.log(\"⏭️ 跳过根目录，继续搜索子节点\");\n                    // 直接搜索子节点，不匹配根目录本身\n                    if (node.children && Array.isArray(node.children)) {\n                        for (const child of node.children){\n                            const found = findFileInTree(child, targetPath);\n                            if (found) return found;\n                        }\n                    }\n                    return null;\n                }\n                // 🔧 只匹配文件类型的节点，排除文件夹\n                const isFile = node.type === \"file\" || !node.children || node.children.length === 0;\n                // 1. 精确路径匹配（仅限文件）\n                if (isFile && normalizedNodePath === normalizedTarget) {\n                    console.log(\"✅ 精确路径匹配（文件）:\", normalizedNodePath);\n                    return node;\n                }\n                // 2. 文件名匹配（仅限文件）\n                if (isFile && normalizedNodeName === normalizedTarget) {\n                    console.log(\"✅ 文件名匹配（文件）:\", normalizedNodeName);\n                    return node;\n                }\n                // 3. 路径末尾匹配（仅限文件）\n                const targetParts = normalizedTarget.split(\"/\").filter((p)=>p);\n                const nodeParts = normalizedNodePath.split(\"/\").filter((p)=>p);\n                if (isFile && targetParts.length <= nodeParts.length) {\n                    const nodePathSuffix = nodeParts.slice(-targetParts.length).join(\"/\");\n                    if (nodePathSuffix === normalizedTarget) {\n                        console.log(\"✅ 路径后缀匹配（文件）:\", {\n                            nodePathSuffix,\n                            normalizedTarget\n                        });\n                        return node;\n                    }\n                }\n                // 4. 文件名部分匹配（仅限文件）\n                const targetFileName = targetParts[targetParts.length - 1];\n                if (isFile && targetFileName && normalizedNodeName === targetFileName) {\n                    console.log(\"✅ 文件名部分匹配（文件）:\", {\n                        targetFileName,\n                        normalizedNodeName\n                    });\n                    return node;\n                }\n                // 5. 模糊匹配（仅限文件）\n                if (isFile && (normalizedNodePath.includes(normalizedTarget) || normalizedTarget.includes(normalizedNodePath))) {\n                    console.log(\"✅ 模糊路径匹配（文件）:\", {\n                        normalizedNodePath,\n                        normalizedTarget\n                    });\n                    return node;\n                }\n                // 递归检查子节点\n                if (node.children && Array.isArray(node.children)) {\n                    for (const child of node.children){\n                        const found = findFileInTree(child, targetPath);\n                        if (found) return found;\n                    }\n                }\n                return null;\n            };\n            const targetFile = findFileInTree(fileTreeResult.data, filePath);\n            if (!targetFile) {\n                return {\n                    success: false,\n                    error: \"文件不存在\"\n                };\n            }\n            // 获取完整的文件数据\n            const fileResult = await fileTreeService.getFile(targetFile.id);\n            if (!fileResult.success || !fileResult.data) {\n                return {\n                    success: false,\n                    error: \"无法获取文件内容\"\n                };\n            }\n            return {\n                success: true,\n                data: fileResult.data\n            };\n        } catch (error) {\n            return {\n                success: false,\n                error: \"获取文件失败: \".concat(error instanceof Error ? error.message : String(error))\n            };\n        }\n    }\n    /**\r\n   * 检测变更类型\r\n   * 用于react-diff-view的type字段\r\n   */ detectChangeType(original, modified) {\n        if (!original && modified) {\n            return \"add\";\n        } else if (original && !modified) {\n            return \"delete\";\n        } else {\n            return \"modify\";\n        }\n    }\n    /**\r\n   * 处理diff请求\r\n   * 统一的diff请求处理入口\r\n   */ async processDiffRequest(artworkId, diffRequest) {\n        return this.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n    }\n    /**\r\n   * 获取文件内容\r\n   * 公开方法，供其他组件使用\r\n   */ async getFileContent(artworkId, filePath) {\n        try {\n            const fileResult = await this.getFileByPath(artworkId, filePath);\n            if (fileResult.success && fileResult.data) {\n                return fileResult.data.content || \"\";\n            }\n            return \"\";\n        } catch (error) {\n            console.error(\"❌ 获取文件内容失败:\", error);\n            return \"\";\n        }\n    }\n    constructor(){\n    // 不再继承DiffToolService，独立实现\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/editorDiffService.ts\n"));

/***/ })

});