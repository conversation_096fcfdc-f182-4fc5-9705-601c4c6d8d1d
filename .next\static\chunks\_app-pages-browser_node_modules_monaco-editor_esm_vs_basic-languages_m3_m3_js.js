"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_m3_m3_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/m3/m3.js":
/*!********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/m3/m3.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/m3/m3.ts\nvar conf = {\n  comments: {\n    blockComment: [\"(*\", \"*)\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"(*\", close: \"*)\" },\n    { open: \"<*\", close: \"*>\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".m3\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  keywords: [\n    \"AND\",\n    \"ANY\",\n    \"ARRAY\",\n    \"AS\",\n    \"BEGIN\",\n    \"BITS\",\n    \"BRANDED\",\n    \"BY\",\n    \"CASE\",\n    \"CONST\",\n    \"DIV\",\n    \"DO\",\n    \"ELSE\",\n    \"ELSIF\",\n    \"END\",\n    \"EVAL\",\n    \"EXCEPT\",\n    \"EXCEPTION\",\n    \"EXIT\",\n    \"EXPORTS\",\n    \"FINALLY\",\n    \"FOR\",\n    \"FROM\",\n    \"GENERIC\",\n    \"IF\",\n    \"IMPORT\",\n    \"IN\",\n    \"INTERFACE\",\n    \"LOCK\",\n    \"LOOP\",\n    \"METHODS\",\n    \"MOD\",\n    \"MODULE\",\n    \"NOT\",\n    \"OBJECT\",\n    \"OF\",\n    \"OR\",\n    \"OVERRIDES\",\n    \"PROCEDURE\",\n    \"RAISE\",\n    \"RAISES\",\n    \"READONLY\",\n    \"RECORD\",\n    \"REF\",\n    \"REPEAT\",\n    \"RETURN\",\n    \"REVEAL\",\n    \"SET\",\n    \"THEN\",\n    \"TO\",\n    \"TRY\",\n    \"TYPE\",\n    \"TYPECASE\",\n    \"UNSAFE\",\n    \"UNTIL\",\n    \"UNTRACED\",\n    \"VALUE\",\n    \"VAR\",\n    \"WHILE\",\n    \"WITH\"\n  ],\n  reservedConstNames: [\n    \"ABS\",\n    \"ADR\",\n    \"ADRSIZE\",\n    \"BITSIZE\",\n    \"BYTESIZE\",\n    \"CEILING\",\n    \"DEC\",\n    \"DISPOSE\",\n    \"FALSE\",\n    \"FIRST\",\n    \"FLOAT\",\n    \"FLOOR\",\n    \"INC\",\n    \"ISTYPE\",\n    \"LAST\",\n    \"LOOPHOLE\",\n    \"MAX\",\n    \"MIN\",\n    \"NARROW\",\n    \"NEW\",\n    \"NIL\",\n    \"NUMBER\",\n    \"ORD\",\n    \"ROUND\",\n    \"SUBARRAY\",\n    \"TRUE\",\n    \"TRUNC\",\n    \"TYPECODE\",\n    \"VAL\"\n  ],\n  reservedTypeNames: [\n    \"ADDRESS\",\n    \"ANY\",\n    \"BOOLEAN\",\n    \"CARDINAL\",\n    \"CHAR\",\n    \"EXTENDED\",\n    \"INTEGER\",\n    \"LONGCARD\",\n    \"LONGINT\",\n    \"LONGREAL\",\n    \"MUTEX\",\n    \"NULL\",\n    \"REAL\",\n    \"REFANY\",\n    \"ROOT\",\n    \"TEXT\"\n  ],\n  operators: [\"+\", \"-\", \"*\", \"/\", \"&\", \"^\", \".\"],\n  relations: [\"=\", \"#\", \"<\", \"<=\", \">\", \">=\", \"<:\", \":\"],\n  delimiters: [\"|\", \"..\", \"=>\", \",\", \";\", \":=\"],\n  symbols: /[>=<#.,:;+\\-*/&^]+/,\n  escapes: /\\\\(?:[\\\\fnrt\"']|[0-7]{3})/,\n  tokenizer: {\n    root: [\n      // Identifiers and keywords\n      [/_\\w*/, \"invalid\"],\n      [\n        /[a-zA-Z][a-zA-Z0-9_]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@reservedConstNames\": { token: \"constant.reserved.$0\" },\n            \"@reservedTypeNames\": { token: \"type.reserved.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // Whitespace\n      { include: \"@whitespace\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      // Integer- and real literals\n      [/[0-9]+\\.[0-9]+(?:[DdEeXx][\\+\\-]?[0-9]+)?/, \"number.float\"],\n      [/[0-9]+(?:\\_[0-9a-fA-F]+)?L?/, \"number\"],\n      // Operators, relations, and delimiters\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"operators\",\n            \"@relations\": \"operators\",\n            \"@delimiters\": \"delimiter\",\n            \"@default\": \"invalid\"\n          }\n        }\n      ],\n      // Character literals\n      [/'[^\\\\']'/, \"string.char\"],\n      [/(')(@escapes)(')/, [\"string.char\", \"string.escape\", \"string.char\"]],\n      [/'/, \"invalid\"],\n      // Text literals\n      [/\"([^\"\\\\]|\\\\.)*$/, \"invalid\"],\n      [/\"/, \"string.text\", \"@text\"]\n    ],\n    text: [\n      [/[^\\\\\"]+/, \"string.text\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"invalid\"],\n      [/\"/, \"string.text\", \"@pop\"]\n    ],\n    comment: [\n      [/\\(\\*/, \"comment\", \"@push\"],\n      [/\\*\\)/, \"comment\", \"@pop\"],\n      [/./, \"comment\"]\n    ],\n    pragma: [\n      [/<\\*/, \"keyword.pragma\", \"@push\"],\n      [/\\*>/, \"keyword.pragma\", \"@pop\"],\n      [/./, \"keyword.pragma\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\(\\*/, \"comment\", \"@comment\"],\n      [/<\\*/, \"keyword.pragma\", \"@pragma\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/m3/m3.js\n"));

/***/ })

}]);