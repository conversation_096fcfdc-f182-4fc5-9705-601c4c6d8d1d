"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_ecl_ecl_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/ecl/ecl.js":
/*!**********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/ecl/ecl.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/ecl/ecl.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".ecl\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  pounds: [\n    \"append\",\n    \"break\",\n    \"declare\",\n    \"demangle\",\n    \"end\",\n    \"for\",\n    \"getdatatype\",\n    \"if\",\n    \"inmodule\",\n    \"loop\",\n    \"mangle\",\n    \"onwarning\",\n    \"option\",\n    \"set\",\n    \"stored\",\n    \"uniquename\"\n  ].join(\"|\"),\n  keywords: [\n    \"__compressed__\",\n    \"after\",\n    \"all\",\n    \"and\",\n    \"any\",\n    \"as\",\n    \"atmost\",\n    \"before\",\n    \"beginc\",\n    \"best\",\n    \"between\",\n    \"case\",\n    \"cluster\",\n    \"compressed\",\n    \"compression\",\n    \"const\",\n    \"counter\",\n    \"csv\",\n    \"default\",\n    \"descend\",\n    \"embed\",\n    \"encoding\",\n    \"encrypt\",\n    \"end\",\n    \"endc\",\n    \"endembed\",\n    \"endmacro\",\n    \"enum\",\n    \"escape\",\n    \"except\",\n    \"exclusive\",\n    \"expire\",\n    \"export\",\n    \"extend\",\n    \"fail\",\n    \"few\",\n    \"fileposition\",\n    \"first\",\n    \"flat\",\n    \"forward\",\n    \"from\",\n    \"full\",\n    \"function\",\n    \"functionmacro\",\n    \"group\",\n    \"grouped\",\n    \"heading\",\n    \"hole\",\n    \"ifblock\",\n    \"import\",\n    \"in\",\n    \"inner\",\n    \"interface\",\n    \"internal\",\n    \"joined\",\n    \"keep\",\n    \"keyed\",\n    \"last\",\n    \"left\",\n    \"limit\",\n    \"linkcounted\",\n    \"literal\",\n    \"little_endian\",\n    \"load\",\n    \"local\",\n    \"locale\",\n    \"lookup\",\n    \"lzw\",\n    \"macro\",\n    \"many\",\n    \"maxcount\",\n    \"maxlength\",\n    \"min skew\",\n    \"module\",\n    \"mofn\",\n    \"multiple\",\n    \"named\",\n    \"namespace\",\n    \"nocase\",\n    \"noroot\",\n    \"noscan\",\n    \"nosort\",\n    \"not\",\n    \"noxpath\",\n    \"of\",\n    \"onfail\",\n    \"only\",\n    \"opt\",\n    \"or\",\n    \"outer\",\n    \"overwrite\",\n    \"packed\",\n    \"partition\",\n    \"penalty\",\n    \"physicallength\",\n    \"pipe\",\n    \"prefetch\",\n    \"quote\",\n    \"record\",\n    \"repeat\",\n    \"retry\",\n    \"return\",\n    \"right\",\n    \"right1\",\n    \"right2\",\n    \"rows\",\n    \"rowset\",\n    \"scan\",\n    \"scope\",\n    \"self\",\n    \"separator\",\n    \"service\",\n    \"shared\",\n    \"skew\",\n    \"skip\",\n    \"smart\",\n    \"soapaction\",\n    \"sql\",\n    \"stable\",\n    \"store\",\n    \"terminator\",\n    \"thor\",\n    \"threshold\",\n    \"timelimit\",\n    \"timeout\",\n    \"token\",\n    \"transform\",\n    \"trim\",\n    \"type\",\n    \"unicodeorder\",\n    \"unordered\",\n    \"unsorted\",\n    \"unstable\",\n    \"update\",\n    \"use\",\n    \"validate\",\n    \"virtual\",\n    \"whole\",\n    \"width\",\n    \"wild\",\n    \"within\",\n    \"wnotrim\",\n    \"xml\",\n    \"xpath\"\n  ],\n  functions: [\n    \"abs\",\n    \"acos\",\n    \"aggregate\",\n    \"allnodes\",\n    \"apply\",\n    \"ascii\",\n    \"asin\",\n    \"assert\",\n    \"asstring\",\n    \"atan\",\n    \"atan2\",\n    \"ave\",\n    \"build\",\n    \"buildindex\",\n    \"case\",\n    \"catch\",\n    \"choose\",\n    \"choosen\",\n    \"choosesets\",\n    \"clustersize\",\n    \"combine\",\n    \"correlation\",\n    \"cos\",\n    \"cosh\",\n    \"count\",\n    \"covariance\",\n    \"cron\",\n    \"dataset\",\n    \"dedup\",\n    \"define\",\n    \"denormalize\",\n    \"dictionary\",\n    \"distribute\",\n    \"distributed\",\n    \"distribution\",\n    \"ebcdic\",\n    \"enth\",\n    \"error\",\n    \"evaluate\",\n    \"event\",\n    \"eventextra\",\n    \"eventname\",\n    \"exists\",\n    \"exp\",\n    \"fail\",\n    \"failcode\",\n    \"failmessage\",\n    \"fetch\",\n    \"fromunicode\",\n    \"fromxml\",\n    \"getenv\",\n    \"getisvalid\",\n    \"global\",\n    \"graph\",\n    \"group\",\n    \"hash\",\n    \"hash32\",\n    \"hash64\",\n    \"hashcrc\",\n    \"hashmd5\",\n    \"having\",\n    \"httpcall\",\n    \"httpheader\",\n    \"if\",\n    \"iff\",\n    \"index\",\n    \"intformat\",\n    \"isvalid\",\n    \"iterate\",\n    \"join\",\n    \"keydiff\",\n    \"keypatch\",\n    \"keyunicode\",\n    \"length\",\n    \"library\",\n    \"limit\",\n    \"ln\",\n    \"loadxml\",\n    \"local\",\n    \"log\",\n    \"loop\",\n    \"map\",\n    \"matched\",\n    \"matchlength\",\n    \"matchposition\",\n    \"matchtext\",\n    \"matchunicode\",\n    \"max\",\n    \"merge\",\n    \"mergejoin\",\n    \"min\",\n    \"nofold\",\n    \"nolocal\",\n    \"nonempty\",\n    \"normalize\",\n    \"nothor\",\n    \"notify\",\n    \"output\",\n    \"parallel\",\n    \"parse\",\n    \"pipe\",\n    \"power\",\n    \"preload\",\n    \"process\",\n    \"project\",\n    \"pull\",\n    \"random\",\n    \"range\",\n    \"rank\",\n    \"ranked\",\n    \"realformat\",\n    \"recordof\",\n    \"regexfind\",\n    \"regexreplace\",\n    \"regroup\",\n    \"rejected\",\n    \"rollup\",\n    \"round\",\n    \"roundup\",\n    \"row\",\n    \"rowdiff\",\n    \"sample\",\n    \"sequential\",\n    \"set\",\n    \"sin\",\n    \"sinh\",\n    \"sizeof\",\n    \"soapcall\",\n    \"sort\",\n    \"sorted\",\n    \"sqrt\",\n    \"stepped\",\n    \"stored\",\n    \"sum\",\n    \"table\",\n    \"tan\",\n    \"tanh\",\n    \"thisnode\",\n    \"topn\",\n    \"tounicode\",\n    \"toxml\",\n    \"transfer\",\n    \"transform\",\n    \"trim\",\n    \"truncate\",\n    \"typeof\",\n    \"ungroup\",\n    \"unicodeorder\",\n    \"variance\",\n    \"wait\",\n    \"which\",\n    \"workunit\",\n    \"xmldecode\",\n    \"xmlencode\",\n    \"xmltext\",\n    \"xmlunicode\"\n  ],\n  typesint: [\"integer\", \"unsigned\"].join(\"|\"),\n  typesnum: [\"data\", \"qstring\", \"string\", \"unicode\", \"utf8\", \"varstring\", \"varunicode\"],\n  typesone: [\n    \"ascii\",\n    \"big_endian\",\n    \"boolean\",\n    \"data\",\n    \"decimal\",\n    \"ebcdic\",\n    \"grouped\",\n    \"integer\",\n    \"linkcounted\",\n    \"pattern\",\n    \"qstring\",\n    \"real\",\n    \"record\",\n    \"rule\",\n    \"set of\",\n    \"streamed\",\n    \"string\",\n    \"token\",\n    \"udecimal\",\n    \"unicode\",\n    \"unsigned\",\n    \"utf8\",\n    \"varstring\",\n    \"varunicode\"\n  ].join(\"|\"),\n  operators: [\"+\", \"-\", \"/\", \":=\", \"<\", \"<>\", \"=\", \">\", \"\\\\\", \"and\", \"in\", \"not\", \"or\"],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  // escape sequences\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      [/@typesint[4|8]/, \"type\"],\n      [/#(@pounds)/, \"type\"],\n      [/@typesone/, \"type\"],\n      [\n        /[a-zA-Z_$][\\w-$]*/,\n        {\n          cases: {\n            \"@functions\": \"keyword.function\",\n            \"@keywords\": \"keyword\",\n            \"@operators\": \"operator\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/[0-9_]*\\.[0-9_]+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F_]+/, \"number.hex\"],\n      [/0[bB][01]+/, \"number.hex\"],\n      // binary: use same theme style as hex\n      [/[0-9_]+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/\"/, \"string\", \"@string\"],\n      // characters\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    whitespace: [\n      [/[ \\t\\v\\f\\r\\n]+/, \"\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, \"string\", \"@pop\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/ecl/ecl.js\n"));

/***/ })

}]);