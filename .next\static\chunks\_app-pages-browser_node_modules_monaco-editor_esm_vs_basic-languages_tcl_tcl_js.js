"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_tcl_tcl_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/tcl/tcl.js":
/*!**********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/tcl/tcl.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/tcl/tcl.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  tokenPostfix: \".tcl\",\n  specialFunctions: [\n    \"set\",\n    \"unset\",\n    \"rename\",\n    \"variable\",\n    \"proc\",\n    \"coroutine\",\n    \"foreach\",\n    \"incr\",\n    \"append\",\n    \"lappend\",\n    \"linsert\",\n    \"lreplace\"\n  ],\n  mainFunctions: [\n    \"if\",\n    \"then\",\n    \"elseif\",\n    \"else\",\n    \"case\",\n    \"switch\",\n    \"while\",\n    \"for\",\n    \"break\",\n    \"continue\",\n    \"return\",\n    \"package\",\n    \"namespace\",\n    \"catch\",\n    \"exit\",\n    \"eval\",\n    \"expr\",\n    \"uplevel\",\n    \"upvar\"\n  ],\n  builtinFunctions: [\n    \"file\",\n    \"info\",\n    \"concat\",\n    \"join\",\n    \"lindex\",\n    \"list\",\n    \"llength\",\n    \"lrange\",\n    \"lsearch\",\n    \"lsort\",\n    \"split\",\n    \"array\",\n    \"parray\",\n    \"binary\",\n    \"format\",\n    \"regexp\",\n    \"regsub\",\n    \"scan\",\n    \"string\",\n    \"subst\",\n    \"dict\",\n    \"cd\",\n    \"clock\",\n    \"exec\",\n    \"glob\",\n    \"pid\",\n    \"pwd\",\n    \"close\",\n    \"eof\",\n    \"fblocked\",\n    \"fconfigure\",\n    \"fcopy\",\n    \"fileevent\",\n    \"flush\",\n    \"gets\",\n    \"open\",\n    \"puts\",\n    \"read\",\n    \"seek\",\n    \"socket\",\n    \"tell\",\n    \"interp\",\n    \"after\",\n    \"auto_execok\",\n    \"auto_load\",\n    \"auto_mkindex\",\n    \"auto_reset\",\n    \"bgerror\",\n    \"error\",\n    \"global\",\n    \"history\",\n    \"load\",\n    \"source\",\n    \"time\",\n    \"trace\",\n    \"unknown\",\n    \"unset\",\n    \"update\",\n    \"vwait\",\n    \"winfo\",\n    \"wm\",\n    \"bind\",\n    \"event\",\n    \"pack\",\n    \"place\",\n    \"grid\",\n    \"font\",\n    \"bell\",\n    \"clipboard\",\n    \"destroy\",\n    \"focus\",\n    \"grab\",\n    \"lower\",\n    \"option\",\n    \"raise\",\n    \"selection\",\n    \"send\",\n    \"tk\",\n    \"tkwait\",\n    \"tk_bisque\",\n    \"tk_focusNext\",\n    \"tk_focusPrev\",\n    \"tk_focusFollowsMouse\",\n    \"tk_popup\",\n    \"tk_setPalette\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  brackets: [\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" }\n  ],\n  escapes: /\\\\(?:[abfnrtv\\\\\"'\\[\\]\\{\\};\\$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  variables: /(?:\\$+(?:(?:\\:\\:?)?[a-zA-Z_]\\w*)+)/,\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@specialFunctions\": {\n              token: \"keyword.flow\",\n              next: \"@specialFunc\"\n            },\n            \"@mainFunctions\": \"keyword\",\n            \"@builtinFunctions\": \"variable\",\n            \"@default\": \"operator.scss\"\n          }\n        }\n      ],\n      [/\\s+\\-+(?!\\d|\\.)\\w*|{\\*}/, \"metatag\"],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"operator\"],\n      [/\\$+(?:\\:\\:)?\\{/, { token: \"identifier\", next: \"@nestedVariable\" }],\n      [/@variables/, \"type.identifier\"],\n      [/\\.(?!\\d|\\.)[\\w\\-]*/, \"operator.sql\"],\n      // numbers\n      [/\\d+(\\.\\d+)?/, \"number\"],\n      [/\\d+/, \"number\"],\n      // delimiter\n      [/;/, \"delimiter\"],\n      // strings\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@dstring\" }],\n      [/'/, { token: \"string.quote\", bracket: \"@open\", next: \"@sstring\" }]\n    ],\n    dstring: [\n      [/\\[/, { token: \"@brackets\", next: \"@nestedCall\" }],\n      [/\\$+(?:\\:\\:)?\\{/, { token: \"identifier\", next: \"@nestedVariable\" }],\n      [/@variables/, \"type.identifier\"],\n      [/[^\\\\$\\[\\]\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    sstring: [\n      [/\\[/, { token: \"@brackets\", next: \"@nestedCall\" }],\n      [/\\$+(?:\\:\\:)?\\{/, { token: \"identifier\", next: \"@nestedVariable\" }],\n      [/@variables/, \"type.identifier\"],\n      [/[^\\\\$\\[\\]']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/#.*\\\\$/, { token: \"comment\", next: \"@newlineComment\" }],\n      [/#.*(?!\\\\)$/, \"comment\"]\n    ],\n    newlineComment: [\n      [/.*\\\\$/, \"comment\"],\n      [/.*(?!\\\\)$/, { token: \"comment\", next: \"@pop\" }]\n    ],\n    nestedVariable: [\n      [/[^\\{\\}\\$]+/, \"type.identifier\"],\n      [/\\}/, { token: \"identifier\", next: \"@pop\" }]\n    ],\n    nestedCall: [\n      [/\\[/, { token: \"@brackets\", next: \"@nestedCall\" }],\n      [/\\]/, { token: \"@brackets\", next: \"@pop\" }],\n      { include: \"root\" }\n    ],\n    specialFunc: [\n      [/\"/, { token: \"string\", next: \"@dstring\" }],\n      [/'/, { token: \"string\", next: \"@sstring\" }],\n      [/\\S+/, { token: \"type\", next: \"@pop\" }]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/tcl/tcl.js\n"));

/***/ })

}]);