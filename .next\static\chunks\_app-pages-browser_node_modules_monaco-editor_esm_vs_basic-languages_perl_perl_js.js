"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_perl_perl_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/perl/perl.js":
/*!************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/perl/perl.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/perl/perl.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".perl\",\n  brackets: [\n    { token: \"delimiter.bracket\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  // https://learn.perl.org/docs/keywords.html\n  // Perl syntax\n  keywords: [\n    \"__DATA__\",\n    \"else\",\n    \"lock\",\n    \"__END__\",\n    \"elsif\",\n    \"lt\",\n    \"__FILE__\",\n    \"eq\",\n    \"__LINE__\",\n    \"exp\",\n    \"ne\",\n    \"sub\",\n    \"__PACKAGE__\",\n    \"for\",\n    \"no\",\n    \"and\",\n    \"foreach\",\n    \"or\",\n    \"unless\",\n    \"cmp\",\n    \"ge\",\n    \"package\",\n    \"until\",\n    \"continue\",\n    \"gt\",\n    \"while\",\n    \"CORE\",\n    \"if\",\n    \"xor\",\n    \"do\",\n    \"le\",\n    \"__DIE__\",\n    \"__WARN__\"\n  ],\n  // Perl functions\n  builtinFunctions: [\n    \"-A\",\n    \"END\",\n    \"length\",\n    \"setpgrp\",\n    \"-B\",\n    \"endgrent\",\n    \"link\",\n    \"setpriority\",\n    \"-b\",\n    \"endhostent\",\n    \"listen\",\n    \"setprotoent\",\n    \"-C\",\n    \"endnetent\",\n    \"local\",\n    \"setpwent\",\n    \"-c\",\n    \"endprotoent\",\n    \"localtime\",\n    \"setservent\",\n    \"-d\",\n    \"endpwent\",\n    \"log\",\n    \"setsockopt\",\n    \"-e\",\n    \"endservent\",\n    \"lstat\",\n    \"shift\",\n    \"-f\",\n    \"eof\",\n    \"map\",\n    \"shmctl\",\n    \"-g\",\n    \"eval\",\n    \"mkdir\",\n    \"shmget\",\n    \"-k\",\n    \"exec\",\n    \"msgctl\",\n    \"shmread\",\n    \"-l\",\n    \"exists\",\n    \"msgget\",\n    \"shmwrite\",\n    \"-M\",\n    \"exit\",\n    \"msgrcv\",\n    \"shutdown\",\n    \"-O\",\n    \"fcntl\",\n    \"msgsnd\",\n    \"sin\",\n    \"-o\",\n    \"fileno\",\n    \"my\",\n    \"sleep\",\n    \"-p\",\n    \"flock\",\n    \"next\",\n    \"socket\",\n    \"-r\",\n    \"fork\",\n    \"not\",\n    \"socketpair\",\n    \"-R\",\n    \"format\",\n    \"oct\",\n    \"sort\",\n    \"-S\",\n    \"formline\",\n    \"open\",\n    \"splice\",\n    \"-s\",\n    \"getc\",\n    \"opendir\",\n    \"split\",\n    \"-T\",\n    \"getgrent\",\n    \"ord\",\n    \"sprintf\",\n    \"-t\",\n    \"getgrgid\",\n    \"our\",\n    \"sqrt\",\n    \"-u\",\n    \"getgrnam\",\n    \"pack\",\n    \"srand\",\n    \"-w\",\n    \"gethostbyaddr\",\n    \"pipe\",\n    \"stat\",\n    \"-W\",\n    \"gethostbyname\",\n    \"pop\",\n    \"state\",\n    \"-X\",\n    \"gethostent\",\n    \"pos\",\n    \"study\",\n    \"-x\",\n    \"getlogin\",\n    \"print\",\n    \"substr\",\n    \"-z\",\n    \"getnetbyaddr\",\n    \"printf\",\n    \"symlink\",\n    \"abs\",\n    \"getnetbyname\",\n    \"prototype\",\n    \"syscall\",\n    \"accept\",\n    \"getnetent\",\n    \"push\",\n    \"sysopen\",\n    \"alarm\",\n    \"getpeername\",\n    \"quotemeta\",\n    \"sysread\",\n    \"atan2\",\n    \"getpgrp\",\n    \"rand\",\n    \"sysseek\",\n    \"AUTOLOAD\",\n    \"getppid\",\n    \"read\",\n    \"system\",\n    \"BEGIN\",\n    \"getpriority\",\n    \"readdir\",\n    \"syswrite\",\n    \"bind\",\n    \"getprotobyname\",\n    \"readline\",\n    \"tell\",\n    \"binmode\",\n    \"getprotobynumber\",\n    \"readlink\",\n    \"telldir\",\n    \"bless\",\n    \"getprotoent\",\n    \"readpipe\",\n    \"tie\",\n    \"break\",\n    \"getpwent\",\n    \"recv\",\n    \"tied\",\n    \"caller\",\n    \"getpwnam\",\n    \"redo\",\n    \"time\",\n    \"chdir\",\n    \"getpwuid\",\n    \"ref\",\n    \"times\",\n    \"CHECK\",\n    \"getservbyname\",\n    \"rename\",\n    \"truncate\",\n    \"chmod\",\n    \"getservbyport\",\n    \"require\",\n    \"uc\",\n    \"chomp\",\n    \"getservent\",\n    \"reset\",\n    \"ucfirst\",\n    \"chop\",\n    \"getsockname\",\n    \"return\",\n    \"umask\",\n    \"chown\",\n    \"getsockopt\",\n    \"reverse\",\n    \"undef\",\n    \"chr\",\n    \"glob\",\n    \"rewinddir\",\n    \"UNITCHECK\",\n    \"chroot\",\n    \"gmtime\",\n    \"rindex\",\n    \"unlink\",\n    \"close\",\n    \"goto\",\n    \"rmdir\",\n    \"unpack\",\n    \"closedir\",\n    \"grep\",\n    \"say\",\n    \"unshift\",\n    \"connect\",\n    \"hex\",\n    \"scalar\",\n    \"untie\",\n    \"cos\",\n    \"index\",\n    \"seek\",\n    \"use\",\n    \"crypt\",\n    \"INIT\",\n    \"seekdir\",\n    \"utime\",\n    \"dbmclose\",\n    \"int\",\n    \"select\",\n    \"values\",\n    \"dbmopen\",\n    \"ioctl\",\n    \"semctl\",\n    \"vec\",\n    \"defined\",\n    \"join\",\n    \"semget\",\n    \"wait\",\n    \"delete\",\n    \"keys\",\n    \"semop\",\n    \"waitpid\",\n    \"DESTROY\",\n    \"kill\",\n    \"send\",\n    \"wantarray\",\n    \"die\",\n    \"last\",\n    \"setgrent\",\n    \"warn\",\n    \"dump\",\n    \"lc\",\n    \"sethostent\",\n    \"write\",\n    \"each\",\n    \"lcfirst\",\n    \"setnetent\"\n  ],\n  // File handlers\n  builtinFileHandlers: [\"ARGV\", \"STDERR\", \"STDOUT\", \"ARGVOUT\", \"STDIN\", \"ENV\"],\n  // Perl variables\n  builtinVariables: [\n    \"$!\",\n    \"$^RE_TRIE_MAXBUF\",\n    \"$LAST_REGEXP_CODE_RESULT\",\n    '$\"',\n    \"$^S\",\n    \"$LIST_SEPARATOR\",\n    \"$#\",\n    \"$^T\",\n    \"$MATCH\",\n    \"$$\",\n    \"$^TAINT\",\n    \"$MULTILINE_MATCHING\",\n    \"$%\",\n    \"$^UNICODE\",\n    \"$NR\",\n    \"$&\",\n    \"$^UTF8LOCALE\",\n    \"$OFMT\",\n    \"$'\",\n    \"$^V\",\n    \"$OFS\",\n    \"$(\",\n    \"$^W\",\n    \"$ORS\",\n    \"$)\",\n    \"$^WARNING_BITS\",\n    \"$OS_ERROR\",\n    \"$*\",\n    \"$^WIDE_SYSTEM_CALLS\",\n    \"$OSNAME\",\n    \"$+\",\n    \"$^X\",\n    \"$OUTPUT_AUTO_FLUSH\",\n    \"$,\",\n    \"$_\",\n    \"$OUTPUT_FIELD_SEPARATOR\",\n    \"$-\",\n    \"$`\",\n    \"$OUTPUT_RECORD_SEPARATOR\",\n    \"$.\",\n    \"$a\",\n    \"$PERL_VERSION\",\n    \"$/\",\n    \"$ACCUMULATOR\",\n    \"$PERLDB\",\n    \"$0\",\n    \"$ARG\",\n    \"$PID\",\n    \"$:\",\n    \"$ARGV\",\n    \"$POSTMATCH\",\n    \"$;\",\n    \"$b\",\n    \"$PREMATCH\",\n    \"$<\",\n    \"$BASETIME\",\n    \"$PROCESS_ID\",\n    \"$=\",\n    \"$CHILD_ERROR\",\n    \"$PROGRAM_NAME\",\n    \"$>\",\n    \"$COMPILING\",\n    \"$REAL_GROUP_ID\",\n    \"$?\",\n    \"$DEBUGGING\",\n    \"$REAL_USER_ID\",\n    \"$@\",\n    \"$EFFECTIVE_GROUP_ID\",\n    \"$RS\",\n    \"$[\",\n    \"$EFFECTIVE_USER_ID\",\n    \"$SUBSCRIPT_SEPARATOR\",\n    \"$\\\\\",\n    \"$EGID\",\n    \"$SUBSEP\",\n    \"$]\",\n    \"$ERRNO\",\n    \"$SYSTEM_FD_MAX\",\n    \"$^\",\n    \"$EUID\",\n    \"$UID\",\n    \"$^A\",\n    \"$EVAL_ERROR\",\n    \"$WARNING\",\n    \"$^C\",\n    \"$EXCEPTIONS_BEING_CAUGHT\",\n    \"$|\",\n    \"$^CHILD_ERROR_NATIVE\",\n    \"$EXECUTABLE_NAME\",\n    \"$~\",\n    \"$^D\",\n    \"$EXTENDED_OS_ERROR\",\n    \"%!\",\n    \"$^E\",\n    \"$FORMAT_FORMFEED\",\n    \"%^H\",\n    \"$^ENCODING\",\n    \"$FORMAT_LINE_BREAK_CHARACTERS\",\n    \"%ENV\",\n    \"$^F\",\n    \"$FORMAT_LINES_LEFT\",\n    \"%INC\",\n    \"$^H\",\n    \"$FORMAT_LINES_PER_PAGE\",\n    \"%OVERLOAD\",\n    \"$^I\",\n    \"$FORMAT_NAME\",\n    \"%SIG\",\n    \"$^L\",\n    \"$FORMAT_PAGE_NUMBER\",\n    \"@+\",\n    \"$^M\",\n    \"$FORMAT_TOP_NAME\",\n    \"@-\",\n    \"$^N\",\n    \"$GID\",\n    \"@_\",\n    \"$^O\",\n    \"$INPLACE_EDIT\",\n    \"@ARGV\",\n    \"$^OPEN\",\n    \"$INPUT_LINE_NUMBER\",\n    \"@INC\",\n    \"$^P\",\n    \"$INPUT_RECORD_SEPARATOR\",\n    \"@LAST_MATCH_START\",\n    \"$^R\",\n    \"$LAST_MATCH_END\",\n    \"$^RE_DEBUG_FLAGS\",\n    \"$LAST_PAREN_MATCH\"\n  ],\n  // operators\n  symbols: /[:+\\-\\^*$&%@=<>!?|\\/~\\.]/,\n  quoteLikeOps: [\"qr\", \"m\", \"s\", \"q\", \"qq\", \"qx\", \"qw\", \"tr\", \"y\"],\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      [\n        /[a-zA-Z\\-_][\\w\\-_]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@builtinFunctions\": \"type.identifier\",\n            \"@builtinFileHandlers\": \"variable.predefined\",\n            \"@quoteLikeOps\": {\n              token: \"@rematch\",\n              next: \"quotedConstructs\"\n            },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // Perl variables\n      [\n        /[\\$@%][*@#?\\+\\-\\$!\\w\\\\\\^><~:;\\.]+/,\n        {\n          cases: {\n            \"@builtinVariables\": \"variable.predefined\",\n            \"@default\": \"variable\"\n          }\n        }\n      ],\n      { include: \"@strings\" },\n      { include: \"@dblStrings\" },\n      // Perl Doc\n      { include: \"@perldoc\" },\n      // Here Doc\n      { include: \"@heredoc\" },\n      [/[{}\\[\\]()]/, \"@brackets\"],\n      // RegExp\n      [/[\\/](?:(?:\\[(?:\\\\]|[^\\]])+\\])|(?:\\\\\\/|[^\\]\\/]))*[\\/]\\w*\\s*(?=[).,;]|$)/, \"regexp\"],\n      [/@symbols/, \"operators\"],\n      { include: \"@numbers\" },\n      [/[,;]/, \"delimiter\"]\n    ],\n    whitespace: [\n      [/\\s+/, \"white\"],\n      [/(^#!.*$)/, \"metatag\"],\n      [/(^#.*$)/, \"comment\"]\n    ],\n    numbers: [\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F_]*[0-9a-fA-F]/, \"number.hex\"],\n      [/\\d+/, \"number\"]\n    ],\n    // Single quote string\n    strings: [[/'/, \"string\", \"@stringBody\"]],\n    stringBody: [\n      [/'/, \"string\", \"@popall\"],\n      [/\\\\'/, \"string.escape\"],\n      [/./, \"string\"]\n    ],\n    // Double quote string\n    dblStrings: [[/\"/, \"string\", \"@dblStringBody\"]],\n    dblStringBody: [\n      [/\"/, \"string\", \"@popall\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      { include: \"@variables\" },\n      [/./, \"string\"]\n    ],\n    // Quoted constructs\n    // Percent strings in Ruby are similar to quote-like operators in Perl.\n    // This is adapted from pstrings in ../ruby/ruby.ts.\n    quotedConstructs: [\n      [/(q|qw|tr|y)\\s*\\(/, { token: \"string.delim\", switchTo: \"@qstring.(.)\" }],\n      [/(q|qw|tr|y)\\s*\\[/, { token: \"string.delim\", switchTo: \"@qstring.[.]\" }],\n      [/(q|qw|tr|y)\\s*\\{/, { token: \"string.delim\", switchTo: \"@qstring.{.}\" }],\n      [/(q|qw|tr|y)\\s*</, { token: \"string.delim\", switchTo: \"@qstring.<.>\" }],\n      [/(q|qw|tr|y)#/, { token: \"string.delim\", switchTo: \"@qstring.#.#\" }],\n      [/(q|qw|tr|y)\\s*([^A-Za-z0-9#\\s])/, { token: \"string.delim\", switchTo: \"@qstring.$2.$2\" }],\n      [/(q|qw|tr|y)\\s+(\\w)/, { token: \"string.delim\", switchTo: \"@qstring.$2.$2\" }],\n      [/(qr|m|s)\\s*\\(/, { token: \"regexp.delim\", switchTo: \"@qregexp.(.)\" }],\n      [/(qr|m|s)\\s*\\[/, { token: \"regexp.delim\", switchTo: \"@qregexp.[.]\" }],\n      [/(qr|m|s)\\s*\\{/, { token: \"regexp.delim\", switchTo: \"@qregexp.{.}\" }],\n      [/(qr|m|s)\\s*</, { token: \"regexp.delim\", switchTo: \"@qregexp.<.>\" }],\n      [/(qr|m|s)#/, { token: \"regexp.delim\", switchTo: \"@qregexp.#.#\" }],\n      [/(qr|m|s)\\s*([^A-Za-z0-9_#\\s])/, { token: \"regexp.delim\", switchTo: \"@qregexp.$2.$2\" }],\n      [/(qr|m|s)\\s+(\\w)/, { token: \"regexp.delim\", switchTo: \"@qregexp.$2.$2\" }],\n      [/(qq|qx)\\s*\\(/, { token: \"string.delim\", switchTo: \"@qqstring.(.)\" }],\n      [/(qq|qx)\\s*\\[/, { token: \"string.delim\", switchTo: \"@qqstring.[.]\" }],\n      [/(qq|qx)\\s*\\{/, { token: \"string.delim\", switchTo: \"@qqstring.{.}\" }],\n      [/(qq|qx)\\s*</, { token: \"string.delim\", switchTo: \"@qqstring.<.>\" }],\n      [/(qq|qx)#/, { token: \"string.delim\", switchTo: \"@qqstring.#.#\" }],\n      [/(qq|qx)\\s*([^A-Za-z0-9#\\s])/, { token: \"string.delim\", switchTo: \"@qqstring.$2.$2\" }],\n      [/(qq|qx)\\s+(\\w)/, { token: \"string.delim\", switchTo: \"@qqstring.$2.$2\" }]\n    ],\n    // Non-expanded quoted string\n    // qstring<open>.<close>\n    //  open = open delimiter\n    //  close = close delimiter\n    qstring: [\n      [/\\\\./, \"string.escape\"],\n      [\n        /./,\n        {\n          cases: {\n            \"$#==$S3\": { token: \"string.delim\", next: \"@pop\" },\n            \"$#==$S2\": { token: \"string.delim\", next: \"@push\" },\n            // nested delimiters\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    // Quoted regexp\n    // qregexp.<open>.<close>\n    //  open = open delimiter\n    //  close = close delimiter\n    qregexp: [\n      { include: \"@variables\" },\n      [/\\\\./, \"regexp.escape\"],\n      [\n        /./,\n        {\n          cases: {\n            \"$#==$S3\": {\n              token: \"regexp.delim\",\n              next: \"@regexpModifiers\"\n            },\n            \"$#==$S2\": { token: \"regexp.delim\", next: \"@push\" },\n            // nested delimiters\n            \"@default\": \"regexp\"\n          }\n        }\n      ]\n    ],\n    regexpModifiers: [[/[msixpodualngcer]+/, { token: \"regexp.modifier\", next: \"@popall\" }]],\n    // Expanded quoted string\n    // qqstring.<open>.<close>\n    //  open = open delimiter\n    //  close = close delimiter\n    qqstring: [{ include: \"@variables\" }, { include: \"@qstring\" }],\n    heredoc: [\n      [/<<\\s*['\"`]?([\\w\\-]+)['\"`]?/, { token: \"string.heredoc.delimiter\", next: \"@heredocBody.$1\" }]\n    ],\n    heredocBody: [\n      [\n        /^([\\w\\-]+)$/,\n        {\n          cases: {\n            \"$1==$S2\": [\n              {\n                token: \"string.heredoc.delimiter\",\n                next: \"@popall\"\n              }\n            ],\n            \"@default\": \"string.heredoc\"\n          }\n        }\n      ],\n      [/./, \"string.heredoc\"]\n    ],\n    perldoc: [[/^=\\w/, \"comment.doc\", \"@perldocBody\"]],\n    perldocBody: [\n      [/^=cut\\b/, \"type.identifier\", \"@popall\"],\n      [/./, \"comment.doc\"]\n    ],\n    variables: [\n      [/\\$\\w+/, \"variable\"],\n      // scalar\n      [/@\\w+/, \"variable\"],\n      // array\n      [/%\\w+/, \"variable\"]\n      // key/value\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/perl/perl.js\n"));

/***/ })

}]);