"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_objective-c_objective-c_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/objective-c/objective-c.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/objective-c/objective-c.js ***!
  \**************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/objective-c/objective-c.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".objective-c\",\n  keywords: [\n    \"#import\",\n    \"#include\",\n    \"#define\",\n    \"#else\",\n    \"#endif\",\n    \"#if\",\n    \"#ifdef\",\n    \"#ifndef\",\n    \"#ident\",\n    \"#undef\",\n    \"@class\",\n    \"@defs\",\n    \"@dynamic\",\n    \"@encode\",\n    \"@end\",\n    \"@implementation\",\n    \"@interface\",\n    \"@package\",\n    \"@private\",\n    \"@protected\",\n    \"@property\",\n    \"@protocol\",\n    \"@public\",\n    \"@selector\",\n    \"@synthesize\",\n    \"__declspec\",\n    \"assign\",\n    \"auto\",\n    \"BOOL\",\n    \"break\",\n    \"bycopy\",\n    \"byref\",\n    \"case\",\n    \"char\",\n    \"Class\",\n    \"const\",\n    \"copy\",\n    \"continue\",\n    \"default\",\n    \"do\",\n    \"double\",\n    \"else\",\n    \"enum\",\n    \"extern\",\n    \"FALSE\",\n    \"false\",\n    \"float\",\n    \"for\",\n    \"goto\",\n    \"if\",\n    \"in\",\n    \"int\",\n    \"id\",\n    \"inout\",\n    \"IMP\",\n    \"long\",\n    \"nil\",\n    \"nonatomic\",\n    \"NULL\",\n    \"oneway\",\n    \"out\",\n    \"private\",\n    \"public\",\n    \"protected\",\n    \"readwrite\",\n    \"readonly\",\n    \"register\",\n    \"return\",\n    \"SEL\",\n    \"self\",\n    \"short\",\n    \"signed\",\n    \"sizeof\",\n    \"static\",\n    \"struct\",\n    \"super\",\n    \"switch\",\n    \"typedef\",\n    \"TRUE\",\n    \"true\",\n    \"union\",\n    \"unsigned\",\n    \"volatile\",\n    \"void\",\n    \"while\"\n  ],\n  decpart: /\\d(_?\\d)*/,\n  decimal: /0|@decpart/,\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      [/[,:;]/, \"delimiter\"],\n      [/[{}\\[\\]()<>]/, \"@brackets\"],\n      [\n        /[a-zA-Z@#]\\w*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~,]|and\\\\b|or\\\\b|not\\\\b]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [\".\", \"comment\"]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*(_?[0-9a-fA-F])*/, \"number.hex\"],\n      [\n        /@decimal((\\.@decpart)?([eE][\\-+]?@decpart)?)[fF]*/,\n        {\n          cases: {\n            \"(\\\\d)*\": \"number\",\n            $0: \"number.float\"\n          }\n        }\n      ]\n    ],\n    // Recognize strings, including those broken across lines with \\ (but not without)\n    strings: [\n      [/'$/, \"string.escape\", \"@popall\"],\n      [/'/, \"string.escape\", \"@stringBody\"],\n      [/\"$/, \"string.escape\", \"@popall\"],\n      [/\"/, \"string.escape\", \"@dblStringBody\"]\n    ],\n    stringBody: [\n      [/[^\\\\']+$/, \"string\", \"@popall\"],\n      [/[^\\\\']+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    dblStringBody: [\n      [/[^\\\\\"]+$/, \"string\", \"@popall\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/objective-c/objective-c.js\n"));

/***/ })

}]);