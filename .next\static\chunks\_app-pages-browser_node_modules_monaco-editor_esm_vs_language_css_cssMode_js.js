"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_language_css_cssMode_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/language/css/cssMode.js":
/*!*******************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/language/css/cssMode.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompletionAdapter: function() { return /* binding */ CompletionAdapter; },\n/* harmony export */   DefinitionAdapter: function() { return /* binding */ DefinitionAdapter; },\n/* harmony export */   DiagnosticsAdapter: function() { return /* binding */ DiagnosticsAdapter; },\n/* harmony export */   DocumentColorAdapter: function() { return /* binding */ DocumentColorAdapter; },\n/* harmony export */   DocumentFormattingEditProvider: function() { return /* binding */ DocumentFormattingEditProvider; },\n/* harmony export */   DocumentHighlightAdapter: function() { return /* binding */ DocumentHighlightAdapter; },\n/* harmony export */   DocumentLinkAdapter: function() { return /* binding */ DocumentLinkAdapter; },\n/* harmony export */   DocumentRangeFormattingEditProvider: function() { return /* binding */ DocumentRangeFormattingEditProvider; },\n/* harmony export */   DocumentSymbolAdapter: function() { return /* binding */ DocumentSymbolAdapter; },\n/* harmony export */   FoldingRangeAdapter: function() { return /* binding */ FoldingRangeAdapter; },\n/* harmony export */   HoverAdapter: function() { return /* binding */ HoverAdapter; },\n/* harmony export */   ReferenceAdapter: function() { return /* binding */ ReferenceAdapter; },\n/* harmony export */   RenameAdapter: function() { return /* binding */ RenameAdapter; },\n/* harmony export */   SelectionRangeAdapter: function() { return /* binding */ SelectionRangeAdapter; },\n/* harmony export */   WorkerManager: function() { return /* binding */ WorkerManager; },\n/* harmony export */   fromPosition: function() { return /* binding */ fromPosition; },\n/* harmony export */   fromRange: function() { return /* binding */ fromRange; },\n/* harmony export */   setupMode: function() { return /* binding */ setupMode; },\n/* harmony export */   toRange: function() { return /* binding */ toRange; },\n/* harmony export */   toTextEdit: function() { return /* binding */ toTextEdit; }\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/editor.api.js?ce9f\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/language/css/workerManager.ts\nvar STOP_WHEN_IDLE_FOR = 2 * 60 * 1e3;\nvar WorkerManager = class {\n  constructor(defaults) {\n    this._defaults = defaults;\n    this._worker = null;\n    this._client = null;\n    this._idleCheckInterval = window.setInterval(() => this._checkIfIdle(), 30 * 1e3);\n    this._lastUsedTime = 0;\n    this._configChangeListener = this._defaults.onDidChange(() => this._stopWorker());\n  }\n  _stopWorker() {\n    if (this._worker) {\n      this._worker.dispose();\n      this._worker = null;\n    }\n    this._client = null;\n  }\n  dispose() {\n    clearInterval(this._idleCheckInterval);\n    this._configChangeListener.dispose();\n    this._stopWorker();\n  }\n  _checkIfIdle() {\n    if (!this._worker) {\n      return;\n    }\n    let timePassedSinceLastUsed = Date.now() - this._lastUsedTime;\n    if (timePassedSinceLastUsed > STOP_WHEN_IDLE_FOR) {\n      this._stopWorker();\n    }\n  }\n  _getClient() {\n    this._lastUsedTime = Date.now();\n    if (!this._client) {\n      this._worker = monaco_editor_core_exports.editor.createWebWorker({\n        // module that exports the create() method and returns a `CSSWorker` instance\n        moduleId: \"vs/language/css/cssWorker\",\n        label: this._defaults.languageId,\n        // passed in to the create() method\n        createData: {\n          options: this._defaults.options,\n          languageId: this._defaults.languageId\n        }\n      });\n      this._client = this._worker.getProxy();\n    }\n    return this._client;\n  }\n  getLanguageServiceWorker(...resources) {\n    let _client;\n    return this._getClient().then((client) => {\n      _client = client;\n    }).then((_) => {\n      if (this._worker) {\n        return this._worker.withSyncedResources(resources);\n      }\n    }).then((_) => _client);\n  }\n};\n\n// node_modules/vscode-languageserver-types/lib/esm/main.js\nvar DocumentUri;\n(function(DocumentUri2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  DocumentUri2.is = is;\n})(DocumentUri || (DocumentUri = {}));\nvar URI;\n(function(URI2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  URI2.is = is;\n})(URI || (URI = {}));\nvar integer;\n(function(integer2) {\n  integer2.MIN_VALUE = -2147483648;\n  integer2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && integer2.MIN_VALUE <= value && value <= integer2.MAX_VALUE;\n  }\n  integer2.is = is;\n})(integer || (integer = {}));\nvar uinteger;\n(function(uinteger2) {\n  uinteger2.MIN_VALUE = 0;\n  uinteger2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && uinteger2.MIN_VALUE <= value && value <= uinteger2.MAX_VALUE;\n  }\n  uinteger2.is = is;\n})(uinteger || (uinteger = {}));\nvar Position;\n(function(Position3) {\n  function create(line, character) {\n    if (line === Number.MAX_VALUE) {\n      line = uinteger.MAX_VALUE;\n    }\n    if (character === Number.MAX_VALUE) {\n      character = uinteger.MAX_VALUE;\n    }\n    return { line, character };\n  }\n  Position3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n  }\n  Position3.is = is;\n})(Position || (Position = {}));\nvar Range;\n(function(Range3) {\n  function create(one, two, three, four) {\n    if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n      return { start: Position.create(one, two), end: Position.create(three, four) };\n    } else if (Position.is(one) && Position.is(two)) {\n      return { start: one, end: two };\n    } else {\n      throw new Error(`Range#create called with invalid arguments[${one}, ${two}, ${three}, ${four}]`);\n    }\n  }\n  Range3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n  }\n  Range3.is = is;\n})(Range || (Range = {}));\nvar Location;\n(function(Location2) {\n  function create(uri, range) {\n    return { uri, range };\n  }\n  Location2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n  }\n  Location2.is = is;\n})(Location || (Location = {}));\nvar LocationLink;\n(function(LocationLink2) {\n  function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n    return { targetUri, targetRange, targetSelectionRange, originSelectionRange };\n  }\n  LocationLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri) && Range.is(candidate.targetSelectionRange) && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n  }\n  LocationLink2.is = is;\n})(LocationLink || (LocationLink = {}));\nvar Color;\n(function(Color2) {\n  function create(red, green, blue, alpha) {\n    return {\n      red,\n      green,\n      blue,\n      alpha\n    };\n  }\n  Color2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.numberRange(candidate.red, 0, 1) && Is.numberRange(candidate.green, 0, 1) && Is.numberRange(candidate.blue, 0, 1) && Is.numberRange(candidate.alpha, 0, 1);\n  }\n  Color2.is = is;\n})(Color || (Color = {}));\nvar ColorInformation;\n(function(ColorInformation2) {\n  function create(range, color) {\n    return {\n      range,\n      color\n    };\n  }\n  ColorInformation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && Color.is(candidate.color);\n  }\n  ColorInformation2.is = is;\n})(ColorInformation || (ColorInformation = {}));\nvar ColorPresentation;\n(function(ColorPresentation2) {\n  function create(label, textEdit, additionalTextEdits) {\n    return {\n      label,\n      textEdit,\n      additionalTextEdits\n    };\n  }\n  ColorPresentation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate)) && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n  }\n  ColorPresentation2.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\nvar FoldingRangeKind;\n(function(FoldingRangeKind2) {\n  FoldingRangeKind2.Comment = \"comment\";\n  FoldingRangeKind2.Imports = \"imports\";\n  FoldingRangeKind2.Region = \"region\";\n})(FoldingRangeKind || (FoldingRangeKind = {}));\nvar FoldingRange;\n(function(FoldingRange2) {\n  function create(startLine, endLine, startCharacter, endCharacter, kind, collapsedText) {\n    const result = {\n      startLine,\n      endLine\n    };\n    if (Is.defined(startCharacter)) {\n      result.startCharacter = startCharacter;\n    }\n    if (Is.defined(endCharacter)) {\n      result.endCharacter = endCharacter;\n    }\n    if (Is.defined(kind)) {\n      result.kind = kind;\n    }\n    if (Is.defined(collapsedText)) {\n      result.collapsedText = collapsedText;\n    }\n    return result;\n  }\n  FoldingRange2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine) && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter)) && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter)) && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n  }\n  FoldingRange2.is = is;\n})(FoldingRange || (FoldingRange = {}));\nvar DiagnosticRelatedInformation;\n(function(DiagnosticRelatedInformation2) {\n  function create(location, message) {\n    return {\n      location,\n      message\n    };\n  }\n  DiagnosticRelatedInformation2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n  }\n  DiagnosticRelatedInformation2.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\nvar DiagnosticSeverity;\n(function(DiagnosticSeverity2) {\n  DiagnosticSeverity2.Error = 1;\n  DiagnosticSeverity2.Warning = 2;\n  DiagnosticSeverity2.Information = 3;\n  DiagnosticSeverity2.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\nvar DiagnosticTag;\n(function(DiagnosticTag2) {\n  DiagnosticTag2.Unnecessary = 1;\n  DiagnosticTag2.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\nvar CodeDescription;\n(function(CodeDescription2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.href);\n  }\n  CodeDescription2.is = is;\n})(CodeDescription || (CodeDescription = {}));\nvar Diagnostic;\n(function(Diagnostic2) {\n  function create(range, message, severity, code, source, relatedInformation) {\n    let result = { range, message };\n    if (Is.defined(severity)) {\n      result.severity = severity;\n    }\n    if (Is.defined(code)) {\n      result.code = code;\n    }\n    if (Is.defined(source)) {\n      result.source = source;\n    }\n    if (Is.defined(relatedInformation)) {\n      result.relatedInformation = relatedInformation;\n    }\n    return result;\n  }\n  Diagnostic2.create = create;\n  function is(value) {\n    var _a;\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && Is.string(candidate.message) && (Is.number(candidate.severity) || Is.undefined(candidate.severity)) && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code)) && (Is.undefined(candidate.codeDescription) || Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)) && (Is.string(candidate.source) || Is.undefined(candidate.source)) && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n  }\n  Diagnostic2.is = is;\n})(Diagnostic || (Diagnostic = {}));\nvar Command;\n(function(Command2) {\n  function create(title, command, ...args) {\n    let result = { title, command };\n    if (Is.defined(args) && args.length > 0) {\n      result.arguments = args;\n    }\n    return result;\n  }\n  Command2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n  }\n  Command2.is = is;\n})(Command || (Command = {}));\nvar TextEdit;\n(function(TextEdit2) {\n  function replace(range, newText) {\n    return { range, newText };\n  }\n  TextEdit2.replace = replace;\n  function insert(position, newText) {\n    return { range: { start: position, end: position }, newText };\n  }\n  TextEdit2.insert = insert;\n  function del(range) {\n    return { range, newText: \"\" };\n  }\n  TextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.newText) && Range.is(candidate.range);\n  }\n  TextEdit2.is = is;\n})(TextEdit || (TextEdit = {}));\nvar ChangeAnnotation;\n(function(ChangeAnnotation2) {\n  function create(label, needsConfirmation, description) {\n    const result = { label };\n    if (needsConfirmation !== void 0) {\n      result.needsConfirmation = needsConfirmation;\n    }\n    if (description !== void 0) {\n      result.description = description;\n    }\n    return result;\n  }\n  ChangeAnnotation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  ChangeAnnotation2.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nvar ChangeAnnotationIdentifier;\n(function(ChangeAnnotationIdentifier2) {\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate);\n  }\n  ChangeAnnotationIdentifier2.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nvar AnnotatedTextEdit;\n(function(AnnotatedTextEdit2) {\n  function replace(range, newText, annotation) {\n    return { range, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.replace = replace;\n  function insert(position, newText, annotation) {\n    return { range: { start: position, end: position }, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.insert = insert;\n  function del(range, annotation) {\n    return { range, newText: \"\", annotationId: annotation };\n  }\n  AnnotatedTextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  AnnotatedTextEdit2.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\nvar TextDocumentEdit;\n(function(TextDocumentEdit2) {\n  function create(textDocument, edits) {\n    return { textDocument, edits };\n  }\n  TextDocumentEdit2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument) && Array.isArray(candidate.edits);\n  }\n  TextDocumentEdit2.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nvar CreateFile;\n(function(CreateFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"create\",\n      uri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  CreateFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"create\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  CreateFile2.is = is;\n})(CreateFile || (CreateFile = {}));\nvar RenameFile;\n(function(RenameFile2) {\n  function create(oldUri, newUri, options, annotation) {\n    let result = {\n      kind: \"rename\",\n      oldUri,\n      newUri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  RenameFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"rename\" && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  RenameFile2.is = is;\n})(RenameFile || (RenameFile = {}));\nvar DeleteFile;\n(function(DeleteFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"delete\",\n      uri\n    };\n    if (options !== void 0 && (options.recursive !== void 0 || options.ignoreIfNotExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  DeleteFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"delete\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.recursive === void 0 || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === void 0 || Is.boolean(candidate.options.ignoreIfNotExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  DeleteFile2.is = is;\n})(DeleteFile || (DeleteFile = {}));\nvar WorkspaceEdit;\n(function(WorkspaceEdit2) {\n  function is(value) {\n    let candidate = value;\n    return candidate && (candidate.changes !== void 0 || candidate.documentChanges !== void 0) && (candidate.documentChanges === void 0 || candidate.documentChanges.every((change) => {\n      if (Is.string(change.kind)) {\n        return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n      } else {\n        return TextDocumentEdit.is(change);\n      }\n    }));\n  }\n  WorkspaceEdit2.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nvar TextDocumentIdentifier;\n(function(TextDocumentIdentifier2) {\n  function create(uri) {\n    return { uri };\n  }\n  TextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri);\n  }\n  TextDocumentIdentifier2.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\nvar VersionedTextDocumentIdentifier;\n(function(VersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  VersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n  }\n  VersionedTextDocumentIdentifier2.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\nvar OptionalVersionedTextDocumentIdentifier;\n(function(OptionalVersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  OptionalVersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n  }\n  OptionalVersionedTextDocumentIdentifier2.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\nvar TextDocumentItem;\n(function(TextDocumentItem2) {\n  function create(uri, languageId, version, text) {\n    return { uri, languageId, version, text };\n  }\n  TextDocumentItem2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n  }\n  TextDocumentItem2.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\nvar MarkupKind;\n(function(MarkupKind2) {\n  MarkupKind2.PlainText = \"plaintext\";\n  MarkupKind2.Markdown = \"markdown\";\n  function is(value) {\n    const candidate = value;\n    return candidate === MarkupKind2.PlainText || candidate === MarkupKind2.Markdown;\n  }\n  MarkupKind2.is = is;\n})(MarkupKind || (MarkupKind = {}));\nvar MarkupContent;\n(function(MarkupContent2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n  }\n  MarkupContent2.is = is;\n})(MarkupContent || (MarkupContent = {}));\nvar CompletionItemKind;\n(function(CompletionItemKind2) {\n  CompletionItemKind2.Text = 1;\n  CompletionItemKind2.Method = 2;\n  CompletionItemKind2.Function = 3;\n  CompletionItemKind2.Constructor = 4;\n  CompletionItemKind2.Field = 5;\n  CompletionItemKind2.Variable = 6;\n  CompletionItemKind2.Class = 7;\n  CompletionItemKind2.Interface = 8;\n  CompletionItemKind2.Module = 9;\n  CompletionItemKind2.Property = 10;\n  CompletionItemKind2.Unit = 11;\n  CompletionItemKind2.Value = 12;\n  CompletionItemKind2.Enum = 13;\n  CompletionItemKind2.Keyword = 14;\n  CompletionItemKind2.Snippet = 15;\n  CompletionItemKind2.Color = 16;\n  CompletionItemKind2.File = 17;\n  CompletionItemKind2.Reference = 18;\n  CompletionItemKind2.Folder = 19;\n  CompletionItemKind2.EnumMember = 20;\n  CompletionItemKind2.Constant = 21;\n  CompletionItemKind2.Struct = 22;\n  CompletionItemKind2.Event = 23;\n  CompletionItemKind2.Operator = 24;\n  CompletionItemKind2.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\nvar InsertTextFormat;\n(function(InsertTextFormat2) {\n  InsertTextFormat2.PlainText = 1;\n  InsertTextFormat2.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\nvar CompletionItemTag;\n(function(CompletionItemTag2) {\n  CompletionItemTag2.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\nvar InsertReplaceEdit;\n(function(InsertReplaceEdit2) {\n  function create(newText, insert, replace) {\n    return { newText, insert, replace };\n  }\n  InsertReplaceEdit2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n  }\n  InsertReplaceEdit2.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\nvar InsertTextMode;\n(function(InsertTextMode2) {\n  InsertTextMode2.asIs = 1;\n  InsertTextMode2.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\nvar CompletionItemLabelDetails;\n(function(CompletionItemLabelDetails2) {\n  function is(value) {\n    const candidate = value;\n    return candidate && (Is.string(candidate.detail) || candidate.detail === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  CompletionItemLabelDetails2.is = is;\n})(CompletionItemLabelDetails || (CompletionItemLabelDetails = {}));\nvar CompletionItem;\n(function(CompletionItem2) {\n  function create(label) {\n    return { label };\n  }\n  CompletionItem2.create = create;\n})(CompletionItem || (CompletionItem = {}));\nvar CompletionList;\n(function(CompletionList2) {\n  function create(items, isIncomplete) {\n    return { items: items ? items : [], isIncomplete: !!isIncomplete };\n  }\n  CompletionList2.create = create;\n})(CompletionList || (CompletionList = {}));\nvar MarkedString;\n(function(MarkedString2) {\n  function fromPlainText(plainText) {\n    return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\");\n  }\n  MarkedString2.fromPlainText = fromPlainText;\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate) || Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value);\n  }\n  MarkedString2.is = is;\n})(MarkedString || (MarkedString = {}));\nvar Hover;\n(function(Hover2) {\n  function is(value) {\n    let candidate = value;\n    return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) || MarkedString.is(candidate.contents) || Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === void 0 || Range.is(value.range));\n  }\n  Hover2.is = is;\n})(Hover || (Hover = {}));\nvar ParameterInformation;\n(function(ParameterInformation2) {\n  function create(label, documentation) {\n    return documentation ? { label, documentation } : { label };\n  }\n  ParameterInformation2.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\nvar SignatureInformation;\n(function(SignatureInformation2) {\n  function create(label, documentation, ...parameters) {\n    let result = { label };\n    if (Is.defined(documentation)) {\n      result.documentation = documentation;\n    }\n    if (Is.defined(parameters)) {\n      result.parameters = parameters;\n    } else {\n      result.parameters = [];\n    }\n    return result;\n  }\n  SignatureInformation2.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\nvar DocumentHighlightKind;\n(function(DocumentHighlightKind2) {\n  DocumentHighlightKind2.Text = 1;\n  DocumentHighlightKind2.Read = 2;\n  DocumentHighlightKind2.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\nvar DocumentHighlight;\n(function(DocumentHighlight2) {\n  function create(range, kind) {\n    let result = { range };\n    if (Is.number(kind)) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  DocumentHighlight2.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\nvar SymbolKind;\n(function(SymbolKind2) {\n  SymbolKind2.File = 1;\n  SymbolKind2.Module = 2;\n  SymbolKind2.Namespace = 3;\n  SymbolKind2.Package = 4;\n  SymbolKind2.Class = 5;\n  SymbolKind2.Method = 6;\n  SymbolKind2.Property = 7;\n  SymbolKind2.Field = 8;\n  SymbolKind2.Constructor = 9;\n  SymbolKind2.Enum = 10;\n  SymbolKind2.Interface = 11;\n  SymbolKind2.Function = 12;\n  SymbolKind2.Variable = 13;\n  SymbolKind2.Constant = 14;\n  SymbolKind2.String = 15;\n  SymbolKind2.Number = 16;\n  SymbolKind2.Boolean = 17;\n  SymbolKind2.Array = 18;\n  SymbolKind2.Object = 19;\n  SymbolKind2.Key = 20;\n  SymbolKind2.Null = 21;\n  SymbolKind2.EnumMember = 22;\n  SymbolKind2.Struct = 23;\n  SymbolKind2.Event = 24;\n  SymbolKind2.Operator = 25;\n  SymbolKind2.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\nvar SymbolTag;\n(function(SymbolTag2) {\n  SymbolTag2.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nvar SymbolInformation;\n(function(SymbolInformation2) {\n  function create(name, kind, range, uri, containerName) {\n    let result = {\n      name,\n      kind,\n      location: { uri, range }\n    };\n    if (containerName) {\n      result.containerName = containerName;\n    }\n    return result;\n  }\n  SymbolInformation2.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nvar WorkspaceSymbol;\n(function(WorkspaceSymbol2) {\n  function create(name, kind, uri, range) {\n    return range !== void 0 ? { name, kind, location: { uri, range } } : { name, kind, location: { uri } };\n  }\n  WorkspaceSymbol2.create = create;\n})(WorkspaceSymbol || (WorkspaceSymbol = {}));\nvar DocumentSymbol;\n(function(DocumentSymbol2) {\n  function create(name, detail, kind, range, selectionRange, children) {\n    let result = {\n      name,\n      detail,\n      kind,\n      range,\n      selectionRange\n    };\n    if (children !== void 0) {\n      result.children = children;\n    }\n    return result;\n  }\n  DocumentSymbol2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.name) && Is.number(candidate.kind) && Range.is(candidate.range) && Range.is(candidate.selectionRange) && (candidate.detail === void 0 || Is.string(candidate.detail)) && (candidate.deprecated === void 0 || Is.boolean(candidate.deprecated)) && (candidate.children === void 0 || Array.isArray(candidate.children)) && (candidate.tags === void 0 || Array.isArray(candidate.tags));\n  }\n  DocumentSymbol2.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\nvar CodeActionKind;\n(function(CodeActionKind2) {\n  CodeActionKind2.Empty = \"\";\n  CodeActionKind2.QuickFix = \"quickfix\";\n  CodeActionKind2.Refactor = \"refactor\";\n  CodeActionKind2.RefactorExtract = \"refactor.extract\";\n  CodeActionKind2.RefactorInline = \"refactor.inline\";\n  CodeActionKind2.RefactorRewrite = \"refactor.rewrite\";\n  CodeActionKind2.Source = \"source\";\n  CodeActionKind2.SourceOrganizeImports = \"source.organizeImports\";\n  CodeActionKind2.SourceFixAll = \"source.fixAll\";\n})(CodeActionKind || (CodeActionKind = {}));\nvar CodeActionTriggerKind;\n(function(CodeActionTriggerKind2) {\n  CodeActionTriggerKind2.Invoked = 1;\n  CodeActionTriggerKind2.Automatic = 2;\n})(CodeActionTriggerKind || (CodeActionTriggerKind = {}));\nvar CodeActionContext;\n(function(CodeActionContext2) {\n  function create(diagnostics, only, triggerKind) {\n    let result = { diagnostics };\n    if (only !== void 0 && only !== null) {\n      result.only = only;\n    }\n    if (triggerKind !== void 0 && triggerKind !== null) {\n      result.triggerKind = triggerKind;\n    }\n    return result;\n  }\n  CodeActionContext2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is) && (candidate.only === void 0 || Is.typedArray(candidate.only, Is.string)) && (candidate.triggerKind === void 0 || candidate.triggerKind === CodeActionTriggerKind.Invoked || candidate.triggerKind === CodeActionTriggerKind.Automatic);\n  }\n  CodeActionContext2.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nvar CodeAction;\n(function(CodeAction2) {\n  function create(title, kindOrCommandOrEdit, kind) {\n    let result = { title };\n    let checkKind = true;\n    if (typeof kindOrCommandOrEdit === \"string\") {\n      checkKind = false;\n      result.kind = kindOrCommandOrEdit;\n    } else if (Command.is(kindOrCommandOrEdit)) {\n      result.command = kindOrCommandOrEdit;\n    } else {\n      result.edit = kindOrCommandOrEdit;\n    }\n    if (checkKind && kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  CodeAction2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.title) && (candidate.diagnostics === void 0 || Is.typedArray(candidate.diagnostics, Diagnostic.is)) && (candidate.kind === void 0 || Is.string(candidate.kind)) && (candidate.edit !== void 0 || candidate.command !== void 0) && (candidate.command === void 0 || Command.is(candidate.command)) && (candidate.isPreferred === void 0 || Is.boolean(candidate.isPreferred)) && (candidate.edit === void 0 || WorkspaceEdit.is(candidate.edit));\n  }\n  CodeAction2.is = is;\n})(CodeAction || (CodeAction = {}));\nvar CodeLens;\n(function(CodeLens2) {\n  function create(range, data) {\n    let result = { range };\n    if (Is.defined(data)) {\n      result.data = data;\n    }\n    return result;\n  }\n  CodeLens2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n  }\n  CodeLens2.is = is;\n})(CodeLens || (CodeLens = {}));\nvar FormattingOptions;\n(function(FormattingOptions2) {\n  function create(tabSize, insertSpaces) {\n    return { tabSize, insertSpaces };\n  }\n  FormattingOptions2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n  }\n  FormattingOptions2.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\nvar DocumentLink;\n(function(DocumentLink2) {\n  function create(range, target, data) {\n    return { range, target, data };\n  }\n  DocumentLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n  }\n  DocumentLink2.is = is;\n})(DocumentLink || (DocumentLink = {}));\nvar SelectionRange;\n(function(SelectionRange2) {\n  function create(range, parent) {\n    return { range, parent };\n  }\n  SelectionRange2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (candidate.parent === void 0 || SelectionRange2.is(candidate.parent));\n  }\n  SelectionRange2.is = is;\n})(SelectionRange || (SelectionRange = {}));\nvar SemanticTokenTypes;\n(function(SemanticTokenTypes2) {\n  SemanticTokenTypes2[\"namespace\"] = \"namespace\";\n  SemanticTokenTypes2[\"type\"] = \"type\";\n  SemanticTokenTypes2[\"class\"] = \"class\";\n  SemanticTokenTypes2[\"enum\"] = \"enum\";\n  SemanticTokenTypes2[\"interface\"] = \"interface\";\n  SemanticTokenTypes2[\"struct\"] = \"struct\";\n  SemanticTokenTypes2[\"typeParameter\"] = \"typeParameter\";\n  SemanticTokenTypes2[\"parameter\"] = \"parameter\";\n  SemanticTokenTypes2[\"variable\"] = \"variable\";\n  SemanticTokenTypes2[\"property\"] = \"property\";\n  SemanticTokenTypes2[\"enumMember\"] = \"enumMember\";\n  SemanticTokenTypes2[\"event\"] = \"event\";\n  SemanticTokenTypes2[\"function\"] = \"function\";\n  SemanticTokenTypes2[\"method\"] = \"method\";\n  SemanticTokenTypes2[\"macro\"] = \"macro\";\n  SemanticTokenTypes2[\"keyword\"] = \"keyword\";\n  SemanticTokenTypes2[\"modifier\"] = \"modifier\";\n  SemanticTokenTypes2[\"comment\"] = \"comment\";\n  SemanticTokenTypes2[\"string\"] = \"string\";\n  SemanticTokenTypes2[\"number\"] = \"number\";\n  SemanticTokenTypes2[\"regexp\"] = \"regexp\";\n  SemanticTokenTypes2[\"operator\"] = \"operator\";\n  SemanticTokenTypes2[\"decorator\"] = \"decorator\";\n})(SemanticTokenTypes || (SemanticTokenTypes = {}));\nvar SemanticTokenModifiers;\n(function(SemanticTokenModifiers2) {\n  SemanticTokenModifiers2[\"declaration\"] = \"declaration\";\n  SemanticTokenModifiers2[\"definition\"] = \"definition\";\n  SemanticTokenModifiers2[\"readonly\"] = \"readonly\";\n  SemanticTokenModifiers2[\"static\"] = \"static\";\n  SemanticTokenModifiers2[\"deprecated\"] = \"deprecated\";\n  SemanticTokenModifiers2[\"abstract\"] = \"abstract\";\n  SemanticTokenModifiers2[\"async\"] = \"async\";\n  SemanticTokenModifiers2[\"modification\"] = \"modification\";\n  SemanticTokenModifiers2[\"documentation\"] = \"documentation\";\n  SemanticTokenModifiers2[\"defaultLibrary\"] = \"defaultLibrary\";\n})(SemanticTokenModifiers || (SemanticTokenModifiers = {}));\nvar SemanticTokens;\n(function(SemanticTokens2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.resultId === void 0 || typeof candidate.resultId === \"string\") && Array.isArray(candidate.data) && (candidate.data.length === 0 || typeof candidate.data[0] === \"number\");\n  }\n  SemanticTokens2.is = is;\n})(SemanticTokens || (SemanticTokens = {}));\nvar InlineValueText;\n(function(InlineValueText2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  InlineValueText2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.string(candidate.text);\n  }\n  InlineValueText2.is = is;\n})(InlineValueText || (InlineValueText = {}));\nvar InlineValueVariableLookup;\n(function(InlineValueVariableLookup2) {\n  function create(range, variableName, caseSensitiveLookup) {\n    return { range, variableName, caseSensitiveLookup };\n  }\n  InlineValueVariableLookup2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.boolean(candidate.caseSensitiveLookup) && (Is.string(candidate.variableName) || candidate.variableName === void 0);\n  }\n  InlineValueVariableLookup2.is = is;\n})(InlineValueVariableLookup || (InlineValueVariableLookup = {}));\nvar InlineValueEvaluatableExpression;\n(function(InlineValueEvaluatableExpression2) {\n  function create(range, expression) {\n    return { range, expression };\n  }\n  InlineValueEvaluatableExpression2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && (Is.string(candidate.expression) || candidate.expression === void 0);\n  }\n  InlineValueEvaluatableExpression2.is = is;\n})(InlineValueEvaluatableExpression || (InlineValueEvaluatableExpression = {}));\nvar InlineValueContext;\n(function(InlineValueContext2) {\n  function create(frameId, stoppedLocation) {\n    return { frameId, stoppedLocation };\n  }\n  InlineValueContext2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.defined(candidate) && Range.is(value.stoppedLocation);\n  }\n  InlineValueContext2.is = is;\n})(InlineValueContext || (InlineValueContext = {}));\nvar InlayHintKind;\n(function(InlayHintKind2) {\n  InlayHintKind2.Type = 1;\n  InlayHintKind2.Parameter = 2;\n  function is(value) {\n    return value === 1 || value === 2;\n  }\n  InlayHintKind2.is = is;\n})(InlayHintKind || (InlayHintKind = {}));\nvar InlayHintLabelPart;\n(function(InlayHintLabelPart2) {\n  function create(value) {\n    return { value };\n  }\n  InlayHintLabelPart2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.location === void 0 || Location.is(candidate.location)) && (candidate.command === void 0 || Command.is(candidate.command));\n  }\n  InlayHintLabelPart2.is = is;\n})(InlayHintLabelPart || (InlayHintLabelPart = {}));\nvar InlayHint;\n(function(InlayHint2) {\n  function create(position, label, kind) {\n    const result = { position, label };\n    if (kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  InlayHint2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.position) && (Is.string(candidate.label) || Is.typedArray(candidate.label, InlayHintLabelPart.is)) && (candidate.kind === void 0 || InlayHintKind.is(candidate.kind)) && candidate.textEdits === void 0 || Is.typedArray(candidate.textEdits, TextEdit.is) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.paddingLeft === void 0 || Is.boolean(candidate.paddingLeft)) && (candidate.paddingRight === void 0 || Is.boolean(candidate.paddingRight));\n  }\n  InlayHint2.is = is;\n})(InlayHint || (InlayHint = {}));\nvar StringValue;\n(function(StringValue2) {\n  function createSnippet(value) {\n    return { kind: \"snippet\", value };\n  }\n  StringValue2.createSnippet = createSnippet;\n})(StringValue || (StringValue = {}));\nvar InlineCompletionItem;\n(function(InlineCompletionItem2) {\n  function create(insertText, filterText, range, command) {\n    return { insertText, filterText, range, command };\n  }\n  InlineCompletionItem2.create = create;\n})(InlineCompletionItem || (InlineCompletionItem = {}));\nvar InlineCompletionList;\n(function(InlineCompletionList2) {\n  function create(items) {\n    return { items };\n  }\n  InlineCompletionList2.create = create;\n})(InlineCompletionList || (InlineCompletionList = {}));\nvar InlineCompletionTriggerKind;\n(function(InlineCompletionTriggerKind2) {\n  InlineCompletionTriggerKind2.Invoked = 0;\n  InlineCompletionTriggerKind2.Automatic = 1;\n})(InlineCompletionTriggerKind || (InlineCompletionTriggerKind = {}));\nvar SelectedCompletionInfo;\n(function(SelectedCompletionInfo2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  SelectedCompletionInfo2.create = create;\n})(SelectedCompletionInfo || (SelectedCompletionInfo = {}));\nvar InlineCompletionContext;\n(function(InlineCompletionContext2) {\n  function create(triggerKind, selectedCompletionInfo) {\n    return { triggerKind, selectedCompletionInfo };\n  }\n  InlineCompletionContext2.create = create;\n})(InlineCompletionContext || (InlineCompletionContext = {}));\nvar WorkspaceFolder;\n(function(WorkspaceFolder2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && URI.is(candidate.uri) && Is.string(candidate.name);\n  }\n  WorkspaceFolder2.is = is;\n})(WorkspaceFolder || (WorkspaceFolder = {}));\nvar TextDocument;\n(function(TextDocument2) {\n  function create(uri, languageId, version, content) {\n    return new FullTextDocument(uri, languageId, version, content);\n  }\n  TextDocument2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount) && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n  }\n  TextDocument2.is = is;\n  function applyEdits(document, edits) {\n    let text = document.getText();\n    let sortedEdits = mergeSort(edits, (a, b) => {\n      let diff = a.range.start.line - b.range.start.line;\n      if (diff === 0) {\n        return a.range.start.character - b.range.start.character;\n      }\n      return diff;\n    });\n    let lastModifiedOffset = text.length;\n    for (let i = sortedEdits.length - 1; i >= 0; i--) {\n      let e = sortedEdits[i];\n      let startOffset = document.offsetAt(e.range.start);\n      let endOffset = document.offsetAt(e.range.end);\n      if (endOffset <= lastModifiedOffset) {\n        text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n      } else {\n        throw new Error(\"Overlapping edit\");\n      }\n      lastModifiedOffset = startOffset;\n    }\n    return text;\n  }\n  TextDocument2.applyEdits = applyEdits;\n  function mergeSort(data, compare) {\n    if (data.length <= 1) {\n      return data;\n    }\n    const p = data.length / 2 | 0;\n    const left = data.slice(0, p);\n    const right = data.slice(p);\n    mergeSort(left, compare);\n    mergeSort(right, compare);\n    let leftIdx = 0;\n    let rightIdx = 0;\n    let i = 0;\n    while (leftIdx < left.length && rightIdx < right.length) {\n      let ret = compare(left[leftIdx], right[rightIdx]);\n      if (ret <= 0) {\n        data[i++] = left[leftIdx++];\n      } else {\n        data[i++] = right[rightIdx++];\n      }\n    }\n    while (leftIdx < left.length) {\n      data[i++] = left[leftIdx++];\n    }\n    while (rightIdx < right.length) {\n      data[i++] = right[rightIdx++];\n    }\n    return data;\n  }\n})(TextDocument || (TextDocument = {}));\nvar FullTextDocument = class {\n  constructor(uri, languageId, version, content) {\n    this._uri = uri;\n    this._languageId = languageId;\n    this._version = version;\n    this._content = content;\n    this._lineOffsets = void 0;\n  }\n  get uri() {\n    return this._uri;\n  }\n  get languageId() {\n    return this._languageId;\n  }\n  get version() {\n    return this._version;\n  }\n  getText(range) {\n    if (range) {\n      let start = this.offsetAt(range.start);\n      let end = this.offsetAt(range.end);\n      return this._content.substring(start, end);\n    }\n    return this._content;\n  }\n  update(event, version) {\n    this._content = event.text;\n    this._version = version;\n    this._lineOffsets = void 0;\n  }\n  getLineOffsets() {\n    if (this._lineOffsets === void 0) {\n      let lineOffsets = [];\n      let text = this._content;\n      let isLineStart = true;\n      for (let i = 0; i < text.length; i++) {\n        if (isLineStart) {\n          lineOffsets.push(i);\n          isLineStart = false;\n        }\n        let ch = text.charAt(i);\n        isLineStart = ch === \"\\r\" || ch === \"\\n\";\n        if (ch === \"\\r\" && i + 1 < text.length && text.charAt(i + 1) === \"\\n\") {\n          i++;\n        }\n      }\n      if (isLineStart && text.length > 0) {\n        lineOffsets.push(text.length);\n      }\n      this._lineOffsets = lineOffsets;\n    }\n    return this._lineOffsets;\n  }\n  positionAt(offset) {\n    offset = Math.max(Math.min(offset, this._content.length), 0);\n    let lineOffsets = this.getLineOffsets();\n    let low = 0, high = lineOffsets.length;\n    if (high === 0) {\n      return Position.create(0, offset);\n    }\n    while (low < high) {\n      let mid = Math.floor((low + high) / 2);\n      if (lineOffsets[mid] > offset) {\n        high = mid;\n      } else {\n        low = mid + 1;\n      }\n    }\n    let line = low - 1;\n    return Position.create(line, offset - lineOffsets[line]);\n  }\n  offsetAt(position) {\n    let lineOffsets = this.getLineOffsets();\n    if (position.line >= lineOffsets.length) {\n      return this._content.length;\n    } else if (position.line < 0) {\n      return 0;\n    }\n    let lineOffset = lineOffsets[position.line];\n    let nextLineOffset = position.line + 1 < lineOffsets.length ? lineOffsets[position.line + 1] : this._content.length;\n    return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n  }\n  get lineCount() {\n    return this.getLineOffsets().length;\n  }\n};\nvar Is;\n(function(Is2) {\n  const toString = Object.prototype.toString;\n  function defined(value) {\n    return typeof value !== \"undefined\";\n  }\n  Is2.defined = defined;\n  function undefined2(value) {\n    return typeof value === \"undefined\";\n  }\n  Is2.undefined = undefined2;\n  function boolean(value) {\n    return value === true || value === false;\n  }\n  Is2.boolean = boolean;\n  function string(value) {\n    return toString.call(value) === \"[object String]\";\n  }\n  Is2.string = string;\n  function number(value) {\n    return toString.call(value) === \"[object Number]\";\n  }\n  Is2.number = number;\n  function numberRange(value, min, max) {\n    return toString.call(value) === \"[object Number]\" && min <= value && value <= max;\n  }\n  Is2.numberRange = numberRange;\n  function integer2(value) {\n    return toString.call(value) === \"[object Number]\" && -2147483648 <= value && value <= 2147483647;\n  }\n  Is2.integer = integer2;\n  function uinteger2(value) {\n    return toString.call(value) === \"[object Number]\" && 0 <= value && value <= 2147483647;\n  }\n  Is2.uinteger = uinteger2;\n  function func(value) {\n    return toString.call(value) === \"[object Function]\";\n  }\n  Is2.func = func;\n  function objectLiteral(value) {\n    return value !== null && typeof value === \"object\";\n  }\n  Is2.objectLiteral = objectLiteral;\n  function typedArray(value, check) {\n    return Array.isArray(value) && value.every(check);\n  }\n  Is2.typedArray = typedArray;\n})(Is || (Is = {}));\n\n// src/language/common/lspLanguageFeatures.ts\nvar DiagnosticsAdapter = class {\n  constructor(_languageId, _worker, configChangeEvent) {\n    this._languageId = _languageId;\n    this._worker = _worker;\n    this._disposables = [];\n    this._listener = /* @__PURE__ */ Object.create(null);\n    const onModelAdd = (model) => {\n      let modeId = model.getLanguageId();\n      if (modeId !== this._languageId) {\n        return;\n      }\n      let handle;\n      this._listener[model.uri.toString()] = model.onDidChangeContent(() => {\n        window.clearTimeout(handle);\n        handle = window.setTimeout(() => this._doValidate(model.uri, modeId), 500);\n      });\n      this._doValidate(model.uri, modeId);\n    };\n    const onModelRemoved = (model) => {\n      monaco_editor_core_exports.editor.setModelMarkers(model, this._languageId, []);\n      let uriStr = model.uri.toString();\n      let listener = this._listener[uriStr];\n      if (listener) {\n        listener.dispose();\n        delete this._listener[uriStr];\n      }\n    };\n    this._disposables.push(monaco_editor_core_exports.editor.onDidCreateModel(onModelAdd));\n    this._disposables.push(monaco_editor_core_exports.editor.onWillDisposeModel(onModelRemoved));\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        onModelRemoved(event.model);\n        onModelAdd(event.model);\n      })\n    );\n    this._disposables.push(\n      configChangeEvent((_) => {\n        monaco_editor_core_exports.editor.getModels().forEach((model) => {\n          if (model.getLanguageId() === this._languageId) {\n            onModelRemoved(model);\n            onModelAdd(model);\n          }\n        });\n      })\n    );\n    this._disposables.push({\n      dispose: () => {\n        monaco_editor_core_exports.editor.getModels().forEach(onModelRemoved);\n        for (let key in this._listener) {\n          this._listener[key].dispose();\n        }\n      }\n    });\n    monaco_editor_core_exports.editor.getModels().forEach(onModelAdd);\n  }\n  dispose() {\n    this._disposables.forEach((d) => d && d.dispose());\n    this._disposables.length = 0;\n  }\n  _doValidate(resource, languageId) {\n    this._worker(resource).then((worker) => {\n      return worker.doValidation(resource.toString());\n    }).then((diagnostics) => {\n      const markers = diagnostics.map((d) => toDiagnostics(resource, d));\n      let model = monaco_editor_core_exports.editor.getModel(resource);\n      if (model && model.getLanguageId() === languageId) {\n        monaco_editor_core_exports.editor.setModelMarkers(model, languageId, markers);\n      }\n    }).then(void 0, (err) => {\n      console.error(err);\n    });\n  }\n};\nfunction toSeverity(lsSeverity) {\n  switch (lsSeverity) {\n    case DiagnosticSeverity.Error:\n      return monaco_editor_core_exports.MarkerSeverity.Error;\n    case DiagnosticSeverity.Warning:\n      return monaco_editor_core_exports.MarkerSeverity.Warning;\n    case DiagnosticSeverity.Information:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n    case DiagnosticSeverity.Hint:\n      return monaco_editor_core_exports.MarkerSeverity.Hint;\n    default:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n  }\n}\nfunction toDiagnostics(resource, diag) {\n  let code = typeof diag.code === \"number\" ? String(diag.code) : diag.code;\n  return {\n    severity: toSeverity(diag.severity),\n    startLineNumber: diag.range.start.line + 1,\n    startColumn: diag.range.start.character + 1,\n    endLineNumber: diag.range.end.line + 1,\n    endColumn: diag.range.end.character + 1,\n    message: diag.message,\n    code,\n    source: diag.source\n  };\n}\nvar CompletionAdapter = class {\n  constructor(_worker, _triggerCharacters) {\n    this._worker = _worker;\n    this._triggerCharacters = _triggerCharacters;\n  }\n  get triggerCharacters() {\n    return this._triggerCharacters;\n  }\n  provideCompletionItems(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.doComplete(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      const wordInfo = model.getWordUntilPosition(position);\n      const wordRange = new monaco_editor_core_exports.Range(\n        position.lineNumber,\n        wordInfo.startColumn,\n        position.lineNumber,\n        wordInfo.endColumn\n      );\n      const items = info.items.map((entry) => {\n        const item = {\n          label: entry.label,\n          insertText: entry.insertText || entry.label,\n          sortText: entry.sortText,\n          filterText: entry.filterText,\n          documentation: entry.documentation,\n          detail: entry.detail,\n          command: toCommand(entry.command),\n          range: wordRange,\n          kind: toCompletionItemKind(entry.kind)\n        };\n        if (entry.textEdit) {\n          if (isInsertReplaceEdit(entry.textEdit)) {\n            item.range = {\n              insert: toRange(entry.textEdit.insert),\n              replace: toRange(entry.textEdit.replace)\n            };\n          } else {\n            item.range = toRange(entry.textEdit.range);\n          }\n          item.insertText = entry.textEdit.newText;\n        }\n        if (entry.additionalTextEdits) {\n          item.additionalTextEdits = entry.additionalTextEdits.map(toTextEdit);\n        }\n        if (entry.insertTextFormat === InsertTextFormat.Snippet) {\n          item.insertTextRules = monaco_editor_core_exports.languages.CompletionItemInsertTextRule.InsertAsSnippet;\n        }\n        return item;\n      });\n      return {\n        isIncomplete: info.isIncomplete,\n        suggestions: items\n      };\n    });\n  }\n};\nfunction fromPosition(position) {\n  if (!position) {\n    return void 0;\n  }\n  return { character: position.column - 1, line: position.lineNumber - 1 };\n}\nfunction fromRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return {\n    start: {\n      line: range.startLineNumber - 1,\n      character: range.startColumn - 1\n    },\n    end: { line: range.endLineNumber - 1, character: range.endColumn - 1 }\n  };\n}\nfunction toRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return new monaco_editor_core_exports.Range(\n    range.start.line + 1,\n    range.start.character + 1,\n    range.end.line + 1,\n    range.end.character + 1\n  );\n}\nfunction isInsertReplaceEdit(edit) {\n  return typeof edit.insert !== \"undefined\" && typeof edit.replace !== \"undefined\";\n}\nfunction toCompletionItemKind(kind) {\n  const mItemKind = monaco_editor_core_exports.languages.CompletionItemKind;\n  switch (kind) {\n    case CompletionItemKind.Text:\n      return mItemKind.Text;\n    case CompletionItemKind.Method:\n      return mItemKind.Method;\n    case CompletionItemKind.Function:\n      return mItemKind.Function;\n    case CompletionItemKind.Constructor:\n      return mItemKind.Constructor;\n    case CompletionItemKind.Field:\n      return mItemKind.Field;\n    case CompletionItemKind.Variable:\n      return mItemKind.Variable;\n    case CompletionItemKind.Class:\n      return mItemKind.Class;\n    case CompletionItemKind.Interface:\n      return mItemKind.Interface;\n    case CompletionItemKind.Module:\n      return mItemKind.Module;\n    case CompletionItemKind.Property:\n      return mItemKind.Property;\n    case CompletionItemKind.Unit:\n      return mItemKind.Unit;\n    case CompletionItemKind.Value:\n      return mItemKind.Value;\n    case CompletionItemKind.Enum:\n      return mItemKind.Enum;\n    case CompletionItemKind.Keyword:\n      return mItemKind.Keyword;\n    case CompletionItemKind.Snippet:\n      return mItemKind.Snippet;\n    case CompletionItemKind.Color:\n      return mItemKind.Color;\n    case CompletionItemKind.File:\n      return mItemKind.File;\n    case CompletionItemKind.Reference:\n      return mItemKind.Reference;\n  }\n  return mItemKind.Property;\n}\nfunction toTextEdit(textEdit) {\n  if (!textEdit) {\n    return void 0;\n  }\n  return {\n    range: toRange(textEdit.range),\n    text: textEdit.newText\n  };\n}\nfunction toCommand(c) {\n  return c && c.command === \"editor.action.triggerSuggest\" ? { id: c.command, title: c.title, arguments: c.arguments } : void 0;\n}\nvar HoverAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideHover(model, position, token) {\n    let resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.doHover(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      return {\n        range: toRange(info.range),\n        contents: toMarkedStringArray(info.contents)\n      };\n    });\n  }\n};\nfunction isMarkupContent(thing) {\n  return thing && typeof thing === \"object\" && typeof thing.kind === \"string\";\n}\nfunction toMarkdownString(entry) {\n  if (typeof entry === \"string\") {\n    return {\n      value: entry\n    };\n  }\n  if (isMarkupContent(entry)) {\n    if (entry.kind === \"plaintext\") {\n      return {\n        value: entry.value.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\")\n      };\n    }\n    return {\n      value: entry.value\n    };\n  }\n  return { value: \"```\" + entry.language + \"\\n\" + entry.value + \"\\n```\\n\" };\n}\nfunction toMarkedStringArray(contents) {\n  if (!contents) {\n    return void 0;\n  }\n  if (Array.isArray(contents)) {\n    return contents.map(toMarkdownString);\n  }\n  return [toMarkdownString(contents)];\n}\nvar DocumentHighlightAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentHighlights(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentHighlights(resource.toString(), fromPosition(position))).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map((entry) => {\n        return {\n          range: toRange(entry.range),\n          kind: toDocumentHighlightKind(entry.kind)\n        };\n      });\n    });\n  }\n};\nfunction toDocumentHighlightKind(kind) {\n  switch (kind) {\n    case DocumentHighlightKind.Read:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Read;\n    case DocumentHighlightKind.Write:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Write;\n    case DocumentHighlightKind.Text:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n  }\n  return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n}\nvar DefinitionAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDefinition(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.findDefinition(resource.toString(), fromPosition(position));\n    }).then((definition) => {\n      if (!definition) {\n        return;\n      }\n      return [toLocation(definition)];\n    });\n  }\n};\nfunction toLocation(location) {\n  return {\n    uri: monaco_editor_core_exports.Uri.parse(location.uri),\n    range: toRange(location.range)\n  };\n}\nvar ReferenceAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideReferences(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.findReferences(resource.toString(), fromPosition(position));\n    }).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map(toLocation);\n    });\n  }\n};\nvar RenameAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideRenameEdits(model, position, newName, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.doRename(resource.toString(), fromPosition(position), newName);\n    }).then((edit) => {\n      return toWorkspaceEdit(edit);\n    });\n  }\n};\nfunction toWorkspaceEdit(edit) {\n  if (!edit || !edit.changes) {\n    return void 0;\n  }\n  let resourceEdits = [];\n  for (let uri in edit.changes) {\n    const _uri = monaco_editor_core_exports.Uri.parse(uri);\n    for (let e of edit.changes[uri]) {\n      resourceEdits.push({\n        resource: _uri,\n        versionId: void 0,\n        textEdit: {\n          range: toRange(e.range),\n          text: e.newText\n        }\n      });\n    }\n  }\n  return {\n    edits: resourceEdits\n  };\n}\nvar DocumentSymbolAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentSymbols(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentSymbols(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return items.map((item) => {\n        if (isDocumentSymbol(item)) {\n          return toDocumentSymbol(item);\n        }\n        return {\n          name: item.name,\n          detail: \"\",\n          containerName: item.containerName,\n          kind: toSymbolKind(item.kind),\n          range: toRange(item.location.range),\n          selectionRange: toRange(item.location.range),\n          tags: []\n        };\n      });\n    });\n  }\n};\nfunction isDocumentSymbol(symbol) {\n  return \"children\" in symbol;\n}\nfunction toDocumentSymbol(symbol) {\n  return {\n    name: symbol.name,\n    detail: symbol.detail ?? \"\",\n    kind: toSymbolKind(symbol.kind),\n    range: toRange(symbol.range),\n    selectionRange: toRange(symbol.selectionRange),\n    tags: symbol.tags ?? [],\n    children: (symbol.children ?? []).map((item) => toDocumentSymbol(item))\n  };\n}\nfunction toSymbolKind(kind) {\n  let mKind = monaco_editor_core_exports.languages.SymbolKind;\n  switch (kind) {\n    case SymbolKind.File:\n      return mKind.File;\n    case SymbolKind.Module:\n      return mKind.Module;\n    case SymbolKind.Namespace:\n      return mKind.Namespace;\n    case SymbolKind.Package:\n      return mKind.Package;\n    case SymbolKind.Class:\n      return mKind.Class;\n    case SymbolKind.Method:\n      return mKind.Method;\n    case SymbolKind.Property:\n      return mKind.Property;\n    case SymbolKind.Field:\n      return mKind.Field;\n    case SymbolKind.Constructor:\n      return mKind.Constructor;\n    case SymbolKind.Enum:\n      return mKind.Enum;\n    case SymbolKind.Interface:\n      return mKind.Interface;\n    case SymbolKind.Function:\n      return mKind.Function;\n    case SymbolKind.Variable:\n      return mKind.Variable;\n    case SymbolKind.Constant:\n      return mKind.Constant;\n    case SymbolKind.String:\n      return mKind.String;\n    case SymbolKind.Number:\n      return mKind.Number;\n    case SymbolKind.Boolean:\n      return mKind.Boolean;\n    case SymbolKind.Array:\n      return mKind.Array;\n  }\n  return mKind.Function;\n}\nvar DocumentLinkAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideLinks(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentLinks(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return {\n        links: items.map((item) => ({\n          range: toRange(item.range),\n          url: item.target\n        }))\n      };\n    });\n  }\n};\nvar DocumentFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentFormattingEdits(model, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.format(resource.toString(), null, fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nvar DocumentRangeFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n    this.canFormatMultipleRanges = false;\n  }\n  provideDocumentRangeFormattingEdits(model, range, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.format(resource.toString(), fromRange(range), fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nfunction fromFormattingOptions(options) {\n  return {\n    tabSize: options.tabSize,\n    insertSpaces: options.insertSpaces\n  };\n}\nvar DocumentColorAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentColors(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentColors(resource.toString())).then((infos) => {\n      if (!infos) {\n        return;\n      }\n      return infos.map((item) => ({\n        color: item.color,\n        range: toRange(item.range)\n      }));\n    });\n  }\n  provideColorPresentations(model, info, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker) => worker.getColorPresentations(resource.toString(), info.color, fromRange(info.range))\n    ).then((presentations) => {\n      if (!presentations) {\n        return;\n      }\n      return presentations.map((presentation) => {\n        let item = {\n          label: presentation.label\n        };\n        if (presentation.textEdit) {\n          item.textEdit = toTextEdit(presentation.textEdit);\n        }\n        if (presentation.additionalTextEdits) {\n          item.additionalTextEdits = presentation.additionalTextEdits.map(toTextEdit);\n        }\n        return item;\n      });\n    });\n  }\n};\nvar FoldingRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideFoldingRanges(model, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.getFoldingRanges(resource.toString(), context)).then((ranges) => {\n      if (!ranges) {\n        return;\n      }\n      return ranges.map((range) => {\n        const result = {\n          start: range.startLine + 1,\n          end: range.endLine + 1\n        };\n        if (typeof range.kind !== \"undefined\") {\n          result.kind = toFoldingRangeKind(range.kind);\n        }\n        return result;\n      });\n    });\n  }\n};\nfunction toFoldingRangeKind(kind) {\n  switch (kind) {\n    case FoldingRangeKind.Comment:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Comment;\n    case FoldingRangeKind.Imports:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Imports;\n    case FoldingRangeKind.Region:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Region;\n  }\n  return void 0;\n}\nvar SelectionRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideSelectionRanges(model, positions, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker) => worker.getSelectionRanges(\n        resource.toString(),\n        positions.map(fromPosition)\n      )\n    ).then((selectionRanges) => {\n      if (!selectionRanges) {\n        return;\n      }\n      return selectionRanges.map((selectionRange) => {\n        const result = [];\n        while (selectionRange) {\n          result.push({ range: toRange(selectionRange.range) });\n          selectionRange = selectionRange.parent;\n        }\n        return result;\n      });\n    });\n  }\n};\n\n// src/language/css/cssMode.ts\nfunction setupMode(defaults) {\n  const disposables = [];\n  const providers = [];\n  const client = new WorkerManager(defaults);\n  disposables.push(client);\n  const worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  function registerProviders() {\n    const { languageId, modeConfiguration } = defaults;\n    disposeAll(providers);\n    if (modeConfiguration.completionItems) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCompletionItemProvider(\n          languageId,\n          new CompletionAdapter(worker, [\"/\", \"-\", \":\"])\n        )\n      );\n    }\n    if (modeConfiguration.hovers) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerHoverProvider(languageId, new HoverAdapter(worker))\n      );\n    }\n    if (modeConfiguration.documentHighlights) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentHighlightProvider(\n          languageId,\n          new DocumentHighlightAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.definitions) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDefinitionProvider(\n          languageId,\n          new DefinitionAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.references) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerReferenceProvider(\n          languageId,\n          new ReferenceAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.documentSymbols) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentSymbolProvider(\n          languageId,\n          new DocumentSymbolAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.rename) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerRenameProvider(languageId, new RenameAdapter(worker))\n      );\n    }\n    if (modeConfiguration.colors) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerColorProvider(\n          languageId,\n          new DocumentColorAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.foldingRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerFoldingRangeProvider(\n          languageId,\n          new FoldingRangeAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.diagnostics) {\n      providers.push(\n        new DiagnosticsAdapter(languageId, worker, defaults.onDidChange)\n      );\n    }\n    if (modeConfiguration.selectionRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerSelectionRangeProvider(\n          languageId,\n          new SelectionRangeAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.documentFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentFormattingEditProvider(\n          languageId,\n          new DocumentFormattingEditProvider(worker)\n        )\n      );\n    }\n    if (modeConfiguration.documentRangeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(\n          languageId,\n          new DocumentRangeFormattingEditProvider(worker)\n        )\n      );\n    }\n  }\n  registerProviders();\n  disposables.push(asDisposable(providers));\n  return asDisposable(disposables);\n}\nfunction asDisposable(disposables) {\n  return { dispose: () => disposeAll(disposables) };\n}\nfunction disposeAll(disposables) {\n  while (disposables.length) {\n    disposables.pop().dispose();\n  }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/language/css/cssMode.js\n"));

/***/ })

}]);