/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5C%E6%96%B0%E5%86%99%E4%BD%9C%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E6%96%B0%E5%86%99%E4%BD%9C&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5C%E6%96%B0%E5%86%99%E4%BD%9C%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E6%96%B0%E5%86%99%E4%BD%9C&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\新写作\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5C%E6%96%B0%E5%86%99%E4%BD%9C%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E6%96%B0%E5%86%99%E4%BD%9C&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Ccontexts%5C%5CAudienceContext.tsx%22%2C%22ids%22%3A%5B%22AudienceProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Ccontexts%5C%5CPersonaContext.tsx%22%2C%22ids%22%3A%5B%22PersonaProvider%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Ccontexts%5C%5CAudienceContext.tsx%22%2C%22ids%22%3A%5B%22AudienceProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Ccontexts%5C%5CPersonaContext.tsx%22%2C%22ids%22%3A%5B%22PersonaProvider%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AudienceContext.tsx */ \"(ssr)/./src/contexts/AudienceContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/PersonaContext.tsx */ \"(ssr)/./src/contexts/PersonaContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUMlRTYlOTYlQjAlRTUlODYlOTklRTQlQkQlOUMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDJUU2JTk2JUIwJUU1JTg2JTk5JUU0JUJEJTlDJTVDJTVDc3JjJTVDJTVDY29udGV4dHMlNUMlNUNBdWRpZW5jZUNvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXVkaWVuY2VQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDJUU2JTk2JUIwJUU1JTg2JTk5JUU0JUJEJTlDJTVDJTVDc3JjJTVDJTVDY29udGV4dHMlNUMlNUNQZXJzb25hQ29udGV4dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQZXJzb25hUHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdMQUFzSDtBQUN0SDtBQUNBLDhLQUFvSCIsInNvdXJjZXMiOlsid2VicGFjazovL2FydHdvcmstcGxhdGZvcm0vP2Q1YzEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdWRpZW5jZVByb3ZpZGVyXCJdICovIFwiRDpcXFxc5paw5YaZ5L2cXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxBdWRpZW5jZUNvbnRleHQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJQZXJzb25hUHJvdmlkZXJcIl0gKi8gXCJEOlxcXFzmlrDlhpnkvZxcXFxcc3JjXFxcXGNvbnRleHRzXFxcXFBlcnNvbmFDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Ccontexts%5C%5CAudienceContext.tsx%22%2C%22ids%22%3A%5B%22AudienceProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5C%E6%96%B0%E5%86%99%E4%BD%9C%5C%5Csrc%5C%5Ccontexts%5C%5CPersonaContext.tsx%22%2C%22ids%22%3A%5B%22PersonaProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AudienceContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/AudienceContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudienceProvider: () => (/* binding */ AudienceProvider),\n/* harmony export */   audienceEventBus: () => (/* binding */ audienceEventBus),\n/* harmony export */   useAudience: () => (/* binding */ useAudience)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_audienceService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/audienceService */ \"(ssr)/./src/services/audienceService.ts\");\n/**\r\n * 受众全局状态管理Context\r\n * 提供受众的实时状态管理和事件通知机制\r\n */ /* __next_internal_client_entry_do_not_use__ AudienceProvider,useAudience,audienceEventBus auto */ \n\n\n// Context创建\nconst AudienceContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\n// 事件总线类\nclass AudienceEventBus {\n    subscribe(eventType, callback) {\n        if (!this.listeners.has(eventType)) {\n            this.listeners.set(eventType, []);\n        }\n        const callbacks = this.listeners.get(eventType);\n        callbacks.push(callback);\n        // 返回取消订阅函数\n        return ()=>{\n            const index = callbacks.indexOf(callback);\n            if (index > -1) {\n                callbacks.splice(index, 1);\n            }\n        };\n    }\n    emit(event) {\n        const callbacks = this.listeners.get(event.type) || [];\n        const allCallbacks = this.listeners.get(\"*\") || [];\n        // 触发特定类型的监听器\n        callbacks.forEach((callback)=>{\n            try {\n                callback(event);\n            } catch (error) {\n                console.error(\"受众事件回调执行失败:\", error);\n            }\n        });\n        // 触发通用监听器\n        allCallbacks.forEach((callback)=>{\n            try {\n                callback(event);\n            } catch (error) {\n                console.error(\"受众事件回调执行失败:\", error);\n            }\n        });\n    }\n    clear() {\n        this.listeners.clear();\n    }\n    constructor(){\n        this.listeners = new Map();\n    }\n}\n// 全局事件总线实例\nconst audienceEventBus = new AudienceEventBus();\n// Provider组件\nconst AudienceProvider = ({ children, initialAudience })=>{\n    const [currentAudience, setCurrentAudience] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialAudience || null);\n    const [allAudiences, setAllAudiences] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const audienceService = _services_audienceService__WEBPACK_IMPORTED_MODULE_2__.AudienceService.getInstance();\n    const initializingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 发出受众变化事件\n    const emitAudienceChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((type, audience, previousAudience, error)=>{\n        // 避免重复触发相同的事件\n        if (type === \"activate\" && audience && previousAudience && audience.id === previousAudience.id) {\n            return; // 如果是相同的受众，不触发事件\n        }\n        const event = {\n            type,\n            audience,\n            previousAudience,\n            error,\n            timestamp: Date.now()\n        };\n        audienceEventBus.emit(event);\n        // 只在开发环境下输出详细日志\n        if (true) {\n            console.log(\"\\uD83C\\uDFAF 受众事件:\", {\n                type,\n                audienceName: audience?.name,\n                error\n            });\n        }\n    }, []);\n    // 初始化加载当前激活的受众和所有受众列表\n    const initializeAudience = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (initializingRef.current) return;\n        initializingRef.current = true;\n        setIsLoading(true);\n        setError(null);\n        const startTime = Date.now();\n        console.log(\"\\uD83D\\uDE80 开始初始化受众Context...\");\n        try {\n            // 首先尝试初始化受众系统（包含完整的错误处理和重试机制）\n            const initResult = await audienceService.initializeAudienceSystem();\n            if (!initResult.success) {\n                throw new Error(initResult.error || \"受众系统初始化失败\");\n            }\n            console.log(\"✅ 受众系统初始化成功:\", {\n                isFirstTime: initResult.isFirstTime,\n                initializationTime: initResult.initializationTime,\n                defaultAudienceId: initResult.defaultAudienceId,\n                createdAudiences: initResult.createdAudiences?.length || 0\n            });\n            // 获取当前激活的受众\n            const activeResult = await audienceService.getActiveAudience();\n            if (activeResult.success && activeResult.data) {\n                const previousAudience = currentAudience;\n                setCurrentAudience(activeResult.data);\n                emitAudienceChange(\"activate\", activeResult.data, previousAudience || undefined);\n                console.log(\"✅ 激活受众加载成功:\", activeResult.data.name);\n            } else {\n                console.warn(\"⚠️ 未找到激活的受众\");\n            }\n            // 获取所有受众列表\n            const allResult = await audienceService.getAllAudiences();\n            if (allResult.success && allResult.data) {\n                setAllAudiences(allResult.data);\n                console.log(\"✅ 受众列表加载成功，共\", allResult.data.length, \"个受众\");\n            } else {\n                console.warn(\"⚠️ 受众列表加载失败:\", allResult.error);\n            }\n            const totalTime = Date.now() - startTime;\n            console.log(\"✅ 受众Context初始化完成，总耗时:\", totalTime, \"ms\");\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"加载受众失败\";\n            console.error(\"❌ 受众Context初始化失败:\", errorMessage);\n            setError(errorMessage);\n            emitAudienceChange(\"error\", undefined, currentAudience || undefined, errorMessage);\n            // 即使初始化失败，也尝试加载现有数据\n            try {\n                console.log(\"\\uD83D\\uDD04 尝试加载现有受众数据...\");\n                const fallbackResult = await audienceService.getAllAudiences();\n                if (fallbackResult.success && fallbackResult.data && fallbackResult.data.length > 0) {\n                    setAllAudiences(fallbackResult.data);\n                    // 尝试找到激活的受众\n                    const activeAudience = fallbackResult.data.find((audience)=>audience.isActive);\n                    if (activeAudience) {\n                        setCurrentAudience(activeAudience);\n                        console.log(\"✅ 降级模式：加载到激活受众\", activeAudience.name);\n                    }\n                }\n            } catch (fallbackError) {\n                console.error(\"❌ 降级模式也失败了:\", fallbackError);\n            }\n        } finally{\n            setIsLoading(false);\n            initializingRef.current = false;\n        }\n    }, [\n        currentAudience,\n        audienceService,\n        emitAudienceChange\n    ]);\n    // 创建受众\n    const createAudience = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (audienceData)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await audienceService.createAudience(audienceData);\n            if (result.success && result.data) {\n                // 更新受众列表\n                setAllAudiences((prev)=>[\n                        result.data,\n                        ...prev\n                    ]);\n                emitAudienceChange(\"create\", result.data);\n                console.log(\"✅ 受众创建成功:\", result.data.name);\n            } else {\n                throw new Error(result.error || \"创建受众失败\");\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"创建受众失败\";\n            setError(errorMessage);\n            emitAudienceChange(\"error\", undefined, currentAudience, errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        audienceService,\n        emitAudienceChange,\n        currentAudience\n    ]);\n    // 更新受众\n    const updateAudience = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (audienceId, updates)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await audienceService.updateAudience(audienceId, updates);\n            if (result.success) {\n                // 获取更新后的受众信息\n                const updatedResult = await audienceService.getAudience(audienceId);\n                if (updatedResult.success && updatedResult.data) {\n                    const updatedAudience = updatedResult.data;\n                    // 更新受众列表\n                    setAllAudiences((prev)=>prev.map((audience)=>audience.id === audienceId ? updatedAudience : audience));\n                    // 如果更新的是当前激活受众，也更新当前状态\n                    if (currentAudience && currentAudience.id === audienceId) {\n                        setCurrentAudience(updatedAudience);\n                    }\n                    emitAudienceChange(\"update\", updatedAudience, currentAudience);\n                    console.log(\"✅ 受众更新成功:\", audienceId);\n                }\n            } else {\n                throw new Error(result.error || \"更新受众失败\");\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"更新受众失败\";\n            setError(errorMessage);\n            emitAudienceChange(\"error\", undefined, currentAudience, errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        audienceService,\n        emitAudienceChange,\n        currentAudience\n    ]);\n    // 删除受众\n    const deleteAudience = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (audienceId)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // 先获取要删除的受众信息\n            const audienceResult = await audienceService.getAudience(audienceId);\n            const audienceToDelete = audienceResult.success ? audienceResult.data : null;\n            const result = await audienceService.deleteAudience(audienceId);\n            if (result.success) {\n                // 更新受众列表\n                setAllAudiences((prev)=>prev.filter((audience)=>audience.id !== audienceId));\n                // 如果删除的是当前激活受众，清除当前状态\n                if (currentAudience && currentAudience.id === audienceId) {\n                    setCurrentAudience(null);\n                }\n                emitAudienceChange(\"delete\", audienceToDelete || undefined, currentAudience);\n                console.log(\"✅ 受众删除成功:\", audienceId);\n            } else {\n                throw new Error(result.error || \"删除受众失败\");\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"删除受众失败\";\n            setError(errorMessage);\n            emitAudienceChange(\"error\", undefined, currentAudience, errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        audienceService,\n        emitAudienceChange,\n        currentAudience\n    ]);\n    // 激活受众\n    const activateAudience = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (audienceId)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // 先获取受众信息\n            const audienceResult = await audienceService.getAudience(audienceId);\n            if (!audienceResult.success || !audienceResult.data) {\n                throw new Error(\"受众不存在\");\n            }\n            // 激活受众\n            const result = await audienceService.setActiveAudience(audienceId);\n            if (result.success) {\n                const previousAudience = currentAudience;\n                setCurrentAudience(audienceResult.data);\n                // 更新受众列表中的激活状态\n                setAllAudiences((prev)=>prev.map((audience)=>({\n                            ...audience,\n                            isActive: audience.id === audienceId\n                        })));\n                emitAudienceChange(\"activate\", audienceResult.data, previousAudience);\n                console.log(\"✅ 受众激活成功:\", audienceResult.data.name);\n            } else {\n                throw new Error(result.error || \"激活受众失败\");\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"激活受众失败\";\n            setError(errorMessage);\n            emitAudienceChange(\"error\", undefined, currentAudience, errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        audienceService,\n        emitAudienceChange,\n        currentAudience\n    ]);\n    // 刷新受众数据\n    const refreshAudiences = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        await initializeAudience();\n    }, [\n        initializeAudience\n    ]);\n    // 事件订阅\n    const onAudienceChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((callback)=>{\n        return audienceEventBus.subscribe(\"*\", callback);\n    }, []);\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!initialAudience) {\n            initializeAudience();\n        }\n        // 清理函数\n        return ()=>{\n            audienceEventBus.clear();\n        };\n    }, [\n        initialAudience,\n        initializeAudience\n    ]);\n    const contextValue = {\n        currentAudience,\n        allAudiences,\n        isLoading,\n        error,\n        createAudience,\n        updateAudience,\n        deleteAudience,\n        activateAudience,\n        refreshAudiences,\n        onAudienceChange\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AudienceContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\新写作\\\\src\\\\contexts\\\\AudienceContext.tsx\",\n        lineNumber: 375,\n        columnNumber: 5\n    }, undefined);\n};\n// Hook for using AudienceContext\nconst useAudience = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AudienceContext);\n    if (!context) {\n        throw new Error(\"useAudience must be used within an AudienceProvider\");\n    }\n    return context;\n};\n// 导出事件总线供高级用法\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AudienceContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/PersonaContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/PersonaContext.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PersonaProvider: () => (/* binding */ PersonaProvider),\n/* harmony export */   personaEventBus: () => (/* binding */ personaEventBus),\n/* harmony export */   usePersona: () => (/* binding */ usePersona)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_personaService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/personaService */ \"(ssr)/./src/services/personaService.ts\");\n/**\r\n * 人设全局状态管理Context\r\n * 提供人设的实时状态管理和事件通知机制\r\n */ /* __next_internal_client_entry_do_not_use__ PersonaProvider,usePersona,personaEventBus auto */ \n\n\n// Context创建\nconst PersonaContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\n// 事件总线类\nclass PersonaEventBus {\n    subscribe(eventType, callback) {\n        if (!this.listeners.has(eventType)) {\n            this.listeners.set(eventType, []);\n        }\n        const callbacks = this.listeners.get(eventType);\n        callbacks.push(callback);\n        // 返回取消订阅函数\n        return ()=>{\n            const index = callbacks.indexOf(callback);\n            if (index > -1) {\n                callbacks.splice(index, 1);\n            }\n        };\n    }\n    emit(event) {\n        const callbacks = this.listeners.get(event.type) || [];\n        const allCallbacks = this.listeners.get(\"*\") || [];\n        // 触发特定类型的监听器\n        callbacks.forEach((callback)=>{\n            try {\n                callback(event);\n            } catch (error) {\n                console.error(\"人设事件回调执行失败:\", error);\n            }\n        });\n        // 触发通用监听器\n        allCallbacks.forEach((callback)=>{\n            try {\n                callback(event);\n            } catch (error) {\n                console.error(\"人设事件回调执行失败:\", error);\n            }\n        });\n    }\n    clear() {\n        this.listeners.clear();\n    }\n    constructor(){\n        this.listeners = new Map();\n    }\n}\n// 全局事件总线实例\nconst personaEventBus = new PersonaEventBus();\n// Provider组件\nconst PersonaProvider = ({ children, initialPersona })=>{\n    const [currentPersona, setCurrentPersona] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialPersona || null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const personaService = _services_personaService__WEBPACK_IMPORTED_MODULE_2__.PersonaService.getInstance();\n    const initializingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 发出人设变化事件\n    const emitPersonaChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((type, persona, previousPersona, error)=>{\n        // 避免重复触发相同的事件\n        if (type === \"activate\" && persona && previousPersona && persona.id === previousPersona.id) {\n            return; // 如果是相同的人设，不触发事件\n        }\n        const event = {\n            type,\n            persona,\n            previousPersona,\n            error,\n            timestamp: Date.now()\n        };\n        personaEventBus.emit(event);\n        // 只在开发环境下输出详细日志\n        if (true) {\n            console.log(\"\\uD83C\\uDFAD 人设事件:\", {\n                type,\n                personaName: persona?.name,\n                error\n            });\n        }\n    }, []);\n    // 初始化加载当前激活的人设\n    const initializePersona = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (initializingRef.current) return;\n        initializingRef.current = true;\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await personaService.getActivePersona();\n            if (result.success && result.data) {\n                const previousPersona = currentPersona;\n                setCurrentPersona(result.data);\n                emitPersonaChange(\"activate\", result.data, previousPersona);\n            } else {\n                // 如果没有激活的人设，尝试初始化系统\n                try {\n                    await personaService.initializePersonaSystem();\n                    const retryResult = await personaService.getActivePersona();\n                    if (retryResult.success && retryResult.data) {\n                        const previousPersona = currentPersona;\n                        setCurrentPersona(retryResult.data);\n                        emitPersonaChange(\"activate\", retryResult.data, previousPersona);\n                    }\n                } catch (initError) {\n                    console.warn(\"人设系统初始化失败:\", initError);\n                }\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"加载人设失败\";\n            setError(errorMessage);\n            emitPersonaChange(\"error\", undefined, currentPersona, errorMessage);\n        } finally{\n            setIsLoading(false);\n            initializingRef.current = false;\n        }\n    }, [\n        currentPersona,\n        personaService,\n        emitPersonaChange\n    ]);\n    // 选择人设（立即生效）\n    const selectPersona = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (persona)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // 立即更新状态，提供即时反馈\n            const previousPersona = currentPersona;\n            setCurrentPersona(persona);\n            // 异步激活人设\n            const result = await personaService.setActivePersona(persona.id);\n            if (result.success) {\n                emitPersonaChange(\"select\", persona, previousPersona);\n                console.log(\"✅ 人设选择成功:\", persona.name);\n            } else {\n                // 如果激活失败，回滚状态\n                setCurrentPersona(previousPersona);\n                throw new Error(result.error || \"激活人设失败\");\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"选择人设失败\";\n            setError(errorMessage);\n            emitPersonaChange(\"error\", undefined, currentPersona, errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        currentPersona,\n        personaService,\n        emitPersonaChange\n    ]);\n    // 激活人设（通过ID）\n    const activatePersona = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (personaId)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            // 先获取人设信息\n            const personaResult = await personaService.getPersona(personaId);\n            if (!personaResult.success || !personaResult.data) {\n                throw new Error(\"人设不存在\");\n            }\n            // 激活人设\n            const result = await personaService.setActivePersona(personaId);\n            if (result.success) {\n                const previousPersona = currentPersona;\n                setCurrentPersona(personaResult.data);\n                emitPersonaChange(\"activate\", personaResult.data, previousPersona);\n            } else {\n                throw new Error(result.error || \"激活人设失败\");\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"激活人设失败\";\n            setError(errorMessage);\n            emitPersonaChange(\"error\", undefined, currentPersona, errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        currentPersona,\n        personaService,\n        emitPersonaChange\n    ]);\n    // 刷新当前人设\n    const refreshPersona = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        await initializePersona();\n    }, [\n        initializePersona\n    ]);\n    // 清除人设\n    const clearPersona = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const previousPersona = currentPersona;\n        setCurrentPersona(null);\n        setError(null);\n        emitPersonaChange(\"clear\", undefined, previousPersona);\n    }, [\n        currentPersona,\n        emitPersonaChange\n    ]);\n    // 事件订阅\n    const onPersonaChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((callback)=>{\n        return personaEventBus.subscribe(\"*\", callback);\n    }, []);\n    // 初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!initialPersona) {\n            initializePersona();\n        }\n        // 清理函数\n        return ()=>{\n            personaEventBus.clear();\n        };\n    }, [\n        initialPersona,\n        initializePersona\n    ]);\n    const contextValue = {\n        currentPersona,\n        isLoading,\n        error,\n        selectPersona,\n        activatePersona,\n        refreshPersona,\n        clearPersona,\n        onPersonaChange\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PersonaContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\新写作\\\\src\\\\contexts\\\\PersonaContext.tsx\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, undefined);\n};\n// Hook for using PersonaContext\nconst usePersona = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PersonaContext);\n    if (!context) {\n        throw new Error(\"usePersona must be used within a PersonaProvider\");\n    }\n    return context;\n};\n// 导出事件总线供高级用法\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/PersonaContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/services/audienceService.ts":
/*!*****************************************!*\
  !*** ./src/services/audienceService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudienceService: () => (/* binding */ AudienceService)\n/* harmony export */ });\n/* harmony import */ var _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database/DatabaseService */ \"(ssr)/./src/services/database/DatabaseService.ts\");\n/**\r\n * 受众管理服务\r\n * 提供受众的创建、存储、管理功能，用于AI助手的动态受众设置\r\n */ \nclass AudienceService {\n    constructor(){\n        this.storeName = \"audiences\";\n        // 受众变化事件监听器\n        this.audienceChangeListeners = [];\n        this.dbService = _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__.DatabaseService.getInstance();\n    }\n    /**\r\n   * 获取受众服务单例\r\n   */ static getInstance() {\n        if (!AudienceService.instance) {\n            AudienceService.instance = new AudienceService();\n        }\n        return AudienceService.instance;\n    }\n    /**\r\n   * 创建受众\r\n   */ async createAudience(audienceData) {\n        try {\n            const now = Date.now();\n            const audienceId = `audience-${now}-${Math.random().toString(36).substring(2, 9)}`;\n            const audience = {\n                ...audienceData,\n                id: audienceId,\n                createdAt: now,\n                updatedAt: now\n            };\n            const result = await this.dbService.add(this.storeName, audience);\n            if (result.success) {\n                console.log(\"✅ 受众创建成功:\", audienceData.name);\n                return {\n                    success: true,\n                    data: audience\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = `创建受众失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取受众\r\n   */ async getAudience(audienceId) {\n        try {\n            const result = await this.dbService.get(this.storeName, audienceId);\n            return result;\n        } catch (error) {\n            const errorMessage = `获取受众失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取所有受众\r\n   */ async getAllAudiences() {\n        try {\n            const result = await this.dbService.getAll(this.storeName);\n            if (result.success && result.data) {\n                // 按创建时间排序，最新的在前\n                const sortedAudiences = result.data.sort((a, b)=>b.createdAt - a.createdAt);\n                return {\n                    success: true,\n                    data: sortedAudiences\n                };\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = `获取所有受众失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 更新受众\r\n   */ async updateAudience(audienceId, updates) {\n        try {\n            // 获取现有受众\n            const existingResult = await this.dbService.get(this.storeName, audienceId);\n            if (!existingResult.success || !existingResult.data) {\n                return {\n                    success: false,\n                    error: \"受众不存在\"\n                };\n            }\n            const existingAudience = existingResult.data;\n            // 合并更新\n            const updatedAudience = {\n                ...existingAudience,\n                ...updates,\n                id: audienceId,\n                updatedAt: Date.now()\n            };\n            const result = await this.dbService.put(this.storeName, updatedAudience);\n            if (result.success) {\n                console.log(\"✅ 受众更新成功:\", audienceId);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = `更新受众失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 删除受众\r\n   */ async deleteAudience(audienceId) {\n        try {\n            const result = await this.dbService.delete(this.storeName, audienceId);\n            if (result.success) {\n                console.log(\"✅ 受众删除成功:\", audienceId);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = `删除受众失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取活跃受众\r\n   */ async getActiveAudience() {\n        try {\n            // 获取所有受众，然后筛选活跃的受众\n            const result = await this.dbService.getAll(this.storeName);\n            if (result.success && result.data) {\n                // 查找活跃受众\n                const activeAudiences = result.data.filter((audience)=>audience.isActive);\n                if (activeAudiences.length > 0) {\n                    // 如果有多个活跃受众，返回最新的一个\n                    const activeAudience = activeAudiences.sort((a, b)=>b.updatedAt - a.updatedAt)[0];\n                    return {\n                        success: true,\n                        data: activeAudience\n                    };\n                }\n            }\n            return {\n                success: true,\n                data: null\n            };\n        } catch (error) {\n            const errorMessage = `获取活跃受众失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 设置活跃受众\r\n   */ async setActiveAudience(audienceId) {\n        try {\n            // 首先将所有受众设为非活跃\n            const allAudiencesResult = await this.dbService.getAll(this.storeName);\n            if (allAudiencesResult.success && allAudiencesResult.data) {\n                for (const audience of allAudiencesResult.data){\n                    if (audience.isActive) {\n                        await this.dbService.put(this.storeName, {\n                            ...audience,\n                            isActive: false,\n                            updatedAt: Date.now()\n                        });\n                    }\n                }\n            }\n            // 设置指定受众为活跃\n            const result = await this.updateAudience(audienceId, {\n                isActive: true\n            });\n            if (result.success) {\n                console.log(\"✅ 活跃受众设置成功:\", audienceId);\n                // 获取新激活的受众并通知监听器\n                const activeAudienceResult = await this.getAudience(audienceId);\n                if (activeAudienceResult.success && activeAudienceResult.data) {\n                    this.notifyAudienceChangeListeners(activeAudienceResult.data);\n                }\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = `设置活跃受众失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 订阅受众变化事件\r\n   */ subscribeToAudienceChange(listener) {\n        this.audienceChangeListeners.push(listener);\n        console.log(\"✅ 受众变化监听器已添加，当前监听器数量:\", this.audienceChangeListeners.length);\n    }\n    /**\r\n   * 取消订阅受众变化事件\r\n   */ unsubscribeFromAudienceChange(listener) {\n        this.audienceChangeListeners = this.audienceChangeListeners.filter((l)=>l !== listener);\n        console.log(\"✅ 受众变化监听器已移除，当前监听器数量:\", this.audienceChangeListeners.length);\n    }\n    /**\r\n   * 通知所有监听器受众已变化\r\n   */ notifyAudienceChangeListeners(audience) {\n        console.log(\"\\uD83D\\uDD14 通知受众变化:\", audience?.name || \"无\", \"监听器数量:\", this.audienceChangeListeners.length);\n        for (const listener of this.audienceChangeListeners){\n            try {\n                listener(audience);\n            } catch (error) {\n                console.error(\"❌ 受众变化监听器执行失败:\", error);\n            }\n        }\n    }\n    /**\r\n   * 清除活跃受众（设置所有受众为非活跃）\r\n   */ async clearActiveAudience() {\n        try {\n            // 将所有受众设为非活跃\n            const allAudiencesResult = await this.dbService.getAll(this.storeName);\n            if (allAudiencesResult.success && allAudiencesResult.data) {\n                for (const audience of allAudiencesResult.data){\n                    if (audience.isActive) {\n                        await this.dbService.put(this.storeName, {\n                            ...audience,\n                            isActive: false,\n                            updatedAt: Date.now()\n                        });\n                    }\n                }\n            }\n            // 通知监听器受众已清除\n            this.notifyAudienceChangeListeners(null);\n            console.log(\"✅ 活跃受众已清除\");\n            return {\n                success: true,\n                data: true\n            };\n        } catch (error) {\n            const errorMessage = `清除活跃受众失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 创建默认受众\r\n   */ async createDefaultAudience() {\n        const defaultAudience = {\n            name: \"通用受众\",\n            description: \"适用于一般场景的通用受众，提供平衡的回应风格，既不过于正式也不过于随意。\",\n            isActive: true\n        };\n        const result = await this.createAudience(defaultAudience);\n        if (result.success && result.data) {\n            return {\n                success: true,\n                data: result.data.id\n            };\n        }\n        return {\n            success: false,\n            error: result.error\n        };\n    }\n    /**\r\n   * 创建默认受众配置列表\r\n   */ async createDefaultAudiences() {\n        try {\n            const defaultAudiences = [\n                {\n                    name: \"网文快餐\",\n                    description: \"面向网络文学读者，偏好快节奏、情节紧凑的内容风格，语言生动有趣，富有想象力。\",\n                    isActive: true\n                },\n                {\n                    name: \"学术研究\",\n                    description: \"面向学术研究人员，需要严谨、专业的表达方式，注重逻辑性和准确性，使用规范的学术语言。\",\n                    isActive: false\n                },\n                {\n                    name: \"商务沟通\",\n                    description: \"面向商务场景，注重效率和专业性的沟通风格，语言简洁明了，重点突出。\",\n                    isActive: false\n                },\n                {\n                    name: \"创意写作\",\n                    description: \"面向创意写作者，鼓励想象力和创新表达，语言富有感染力和艺术性。\",\n                    isActive: false\n                }\n            ];\n            const createdAudienceIds = [];\n            for (const audienceData of defaultAudiences){\n                const result = await this.createAudience(audienceData);\n                if (result.success && result.data) {\n                    createdAudienceIds.push(result.data.id);\n                }\n            }\n            console.log(\"✅ 默认受众创建完成:\", createdAudienceIds.length);\n            return {\n                success: true,\n                data: createdAudienceIds\n            };\n        } catch (error) {\n            const errorMessage = `创建默认受众失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 初始化受众系统\r\n   * 创建默认受众配置，支持重试机制和数据迁移\r\n   */ async initializeAudienceSystem(retryCount = 3) {\n        const startTime = Date.now();\n        console.log(\"\\uD83D\\uDE80 开始初始化受众系统...\");\n        try {\n            // 步骤1: 检查数据库连接\n            await this.ensureDatabaseConnection();\n            // 步骤2: 检查是否已经初始化\n            const existingAudiencesResult = await this.getAllAudiences();\n            if (existingAudiencesResult.success && existingAudiencesResult.data && existingAudiencesResult.data.length > 0) {\n                console.log(\"✅ 受众系统已初始化，发现\", existingAudiencesResult.data.length, \"个受众\");\n                // 检查是否有激活的受众\n                const activeAudience = existingAudiencesResult.data.find((audience)=>audience.isActive);\n                if (!activeAudience && existingAudiencesResult.data.length > 0) {\n                    // 如果没有激活的受众，激活第一个\n                    const firstAudience = existingAudiencesResult.data[0];\n                    await this.setActiveAudience(firstAudience.id);\n                    console.log(\"✅ 自动激活第一个受众:\", firstAudience.name);\n                }\n                const defaultAudienceId = activeAudience?.id || existingAudiencesResult.data[0]?.id;\n                return {\n                    success: true,\n                    defaultAudienceId,\n                    isFirstTime: false,\n                    initializationTime: Date.now() - startTime\n                };\n            }\n            // 步骤3: 执行数据迁移检查\n            await this.performDataMigration();\n            // 步骤4: 创建默认受众配置\n            console.log(\"\\uD83D\\uDCDD 创建默认受众配置...\");\n            const defaultAudiencesResult = await this.createDefaultAudiencesWithRetry(retryCount);\n            if (!defaultAudiencesResult.success || !defaultAudiencesResult.data) {\n                throw new Error(defaultAudiencesResult.error || \"创建默认受众失败\");\n            }\n            const createdAudienceIds = defaultAudiencesResult.data;\n            const defaultAudienceId = createdAudienceIds.length > 0 ? createdAudienceIds[0] : undefined;\n            // 步骤5: 验证初始化结果\n            await this.validateInitialization();\n            const initializationTime = Date.now() - startTime;\n            console.log(\"✅ 受众系统初始化完成，耗时:\", initializationTime, \"ms\");\n            return {\n                success: true,\n                defaultAudienceId,\n                createdAudiences: createdAudienceIds,\n                isFirstTime: true,\n                initializationTime\n            };\n        } catch (error) {\n            const errorMessage = `初始化受众系统失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            // 如果还有重试次数，进行重试\n            if (retryCount > 0) {\n                console.log(`🔄 重试初始化受众系统，剩余重试次数: ${retryCount - 1}`);\n                await this.delay(1000); // 等待1秒后重试\n                return this.initializeAudienceSystem(retryCount - 1);\n            }\n            return {\n                success: false,\n                error: errorMessage,\n                initializationTime: Date.now() - startTime\n            };\n        }\n    }\n    /**\r\n   * 确保数据库连接正常\r\n   */ async ensureDatabaseConnection() {\n        try {\n            // 尝试执行一个简单的数据库操作来测试连接\n            await this.dbService.getAll(this.storeName);\n            console.log(\"✅ 数据库连接正常\");\n        } catch (error) {\n            console.error(\"❌ 数据库连接失败:\", error);\n            throw new Error(\"数据库连接失败，请检查IndexedDB支持\");\n        }\n    }\n    /**\r\n   * 执行数据迁移\r\n   */ async performDataMigration() {\n        try {\n            console.log(\"\\uD83D\\uDD04 检查数据迁移需求...\");\n            // 获取当前数据版本\n            const versionKey = \"audience_data_version\";\n            const currentVersion = localStorage.getItem(versionKey) || \"1.0.0\";\n            const targetVersion = \"1.0.0\";\n            if (currentVersion !== targetVersion) {\n                console.log(`🔄 执行数据迁移: ${currentVersion} -> ${targetVersion}`);\n                // 这里可以添加具体的迁移逻辑\n                // 例如：更新数据结构、添加新字段等\n                // 更新版本号\n                localStorage.setItem(versionKey, targetVersion);\n                console.log(\"✅ 数据迁移完成\");\n            } else {\n                console.log(\"✅ 数据版本匹配，无需迁移\");\n            }\n        } catch (error) {\n            console.warn(\"⚠️ 数据迁移检查失败:\", error);\n        // 迁移失败不应该阻止初始化过程\n        }\n    }\n    /**\r\n   * 带重试机制的默认受众创建\r\n   */ async createDefaultAudiencesWithRetry(retryCount) {\n        try {\n            return await this.createDefaultAudiences();\n        } catch (error) {\n            if (retryCount > 0) {\n                console.log(`🔄 重试创建默认受众，剩余重试次数: ${retryCount - 1}`);\n                await this.delay(500);\n                return this.createDefaultAudiencesWithRetry(retryCount - 1);\n            }\n            throw error;\n        }\n    }\n    /**\r\n   * 验证初始化结果\r\n   */ async validateInitialization() {\n        try {\n            console.log(\"\\uD83D\\uDD0D 验证初始化结果...\");\n            // 检查受众是否创建成功\n            const audiencesResult = await this.getAllAudiences();\n            if (!audiencesResult.success || !audiencesResult.data || audiencesResult.data.length === 0) {\n                throw new Error(\"验证失败：未找到任何受众\");\n            }\n            // 检查是否有激活的受众\n            const activeAudience = audiencesResult.data.find((audience)=>audience.isActive);\n            if (!activeAudience) {\n                throw new Error(\"验证失败：没有激活的受众\");\n            }\n            console.log(\"✅ 初始化验证通过，共\", audiencesResult.data.length, \"个受众，激活受众:\", activeAudience.name);\n        } catch (error) {\n            console.error(\"❌ 初始化验证失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 延迟函数\r\n   */ delay(ms) {\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    /**\r\n   * 清空所有受众\r\n   */ async clearAllAudiences() {\n        try {\n            const result = await this.dbService.clear(this.storeName);\n            if (result.success) {\n                console.log(\"✅ 所有受众已清空\");\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = `清空受众失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/audienceService.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/database/DatabaseService.ts":
/*!**************************************************!*\
  !*** ./src/services/database/DatabaseService.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseService: () => (/* binding */ DatabaseService)\n/* harmony export */ });\n/**\r\n * IndexedDB 数据库服务\r\n * 提供底层数据库操作和连接管理\r\n */ class DatabaseService {\n    static{\n        // 数据库配置\n        this.DB_CONFIG = {\n            name: \"ArtworkPlatformDB\",\n            version: 13,\n            stores: {\n                artworks: {\n                    keyPath: \"id\",\n                    indexes: {\n                        createdAt: {\n                            keyPath: \"createdAt\"\n                        },\n                        updatedAt: {\n                            keyPath: \"updatedAt\"\n                        },\n                        tags: {\n                            keyPath: \"tags\",\n                            unique: false\n                        }\n                    }\n                },\n                fonts: {\n                    keyPath: \"id\",\n                    indexes: {\n                        name: {\n                            keyPath: \"name\"\n                        },\n                        family: {\n                            keyPath: \"family\"\n                        },\n                        uploadedAt: {\n                            keyPath: \"uploadedAt\"\n                        }\n                    }\n                },\n                config: {\n                    keyPath: \"id\",\n                    indexes: {\n                        updatedAt: {\n                            keyPath: \"updatedAt\"\n                        }\n                    }\n                },\n                \"font-configs\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        fontId: {\n                            keyPath: \"fontId\"\n                        },\n                        appliedAt: {\n                            keyPath: \"appliedAt\"\n                        },\n                        isActive: {\n                            keyPath: \"isActive\"\n                        }\n                    }\n                },\n                \"font-history\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        fontId: {\n                            keyPath: \"fontId\"\n                        },\n                        appliedAt: {\n                            keyPath: \"appliedAt\"\n                        },\n                        fontFamily: {\n                            keyPath: \"fontFamily\"\n                        }\n                    }\n                },\n                \"editor-files\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        artworkId: {\n                            keyPath: \"artworkId\"\n                        },\n                        parentId: {\n                            keyPath: \"parentId\"\n                        },\n                        path: {\n                            keyPath: \"path\"\n                        },\n                        type: {\n                            keyPath: \"type\"\n                        },\n                        updatedAt: {\n                            keyPath: \"updatedAt\"\n                        }\n                    }\n                },\n                \"editor-settings\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        updatedAt: {\n                            keyPath: \"updatedAt\"\n                        }\n                    }\n                },\n                \"ai-configs\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        name: {\n                            keyPath: \"name\"\n                        },\n                        isActive: {\n                            keyPath: \"isActive\"\n                        },\n                        createdAt: {\n                            keyPath: \"createdAt\"\n                        },\n                        updatedAt: {\n                            keyPath: \"updatedAt\"\n                        }\n                    }\n                },\n                \"ai-templates\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        name: {\n                            keyPath: \"name\"\n                        },\n                        category: {\n                            keyPath: \"category\"\n                        },\n                        isBuiltIn: {\n                            keyPath: \"isBuiltIn\"\n                        },\n                        createdAt: {\n                            keyPath: \"createdAt\"\n                        },\n                        updatedAt: {\n                            keyPath: \"updatedAt\"\n                        }\n                    }\n                },\n                \"ai-history\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        artworkId: {\n                            keyPath: \"artworkId\"\n                        },\n                        timestamp: {\n                            keyPath: \"timestamp\"\n                        },\n                        model: {\n                            keyPath: \"model\"\n                        }\n                    }\n                },\n                \"personas\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        name: {\n                            keyPath: \"name\"\n                        },\n                        folderId: {\n                            keyPath: \"folderId\"\n                        },\n                        isActive: {\n                            keyPath: \"isActive\"\n                        },\n                        createdAt: {\n                            keyPath: \"createdAt\"\n                        },\n                        updatedAt: {\n                            keyPath: \"updatedAt\"\n                        }\n                    }\n                },\n                \"persona_folders\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        name: {\n                            keyPath: \"name\"\n                        },\n                        createdAt: {\n                            keyPath: \"createdAt\"\n                        },\n                        updatedAt: {\n                            keyPath: \"updatedAt\"\n                        }\n                    }\n                },\n                \"audiences\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        name: {\n                            keyPath: \"name\"\n                        },\n                        isActive: {\n                            keyPath: \"isActive\"\n                        },\n                        createdAt: {\n                            keyPath: \"createdAt\"\n                        },\n                        updatedAt: {\n                            keyPath: \"updatedAt\"\n                        }\n                    }\n                },\n                \"chat-sessions\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        name: {\n                            keyPath: \"name\"\n                        },\n                        createdAt: {\n                            keyPath: \"createdAt\"\n                        },\n                        updatedAt: {\n                            keyPath: \"updatedAt\"\n                        },\n                        isActive: {\n                            keyPath: \"isActive\"\n                        }\n                    }\n                },\n                \"chat-messages\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        sessionId: {\n                            keyPath: \"sessionId\"\n                        },\n                        type: {\n                            keyPath: \"type\"\n                        },\n                        timestamp: {\n                            keyPath: \"timestamp\"\n                        },\n                        createdAt: {\n                            keyPath: \"createdAt\"\n                        }\n                    }\n                },\n                \"message-tokens\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        fileId: {\n                            keyPath: \"fileId\"\n                        },\n                        segmentId: {\n                            keyPath: \"segmentId\"\n                        },\n                        sequence: {\n                            keyPath: \"sequence\"\n                        },\n                        messageType: {\n                            keyPath: \"messageType\"\n                        },\n                        language: {\n                            keyPath: \"language\"\n                        },\n                        timestamp: {\n                            keyPath: \"timestamp\"\n                        },\n                        createdAt: {\n                            keyPath: \"timestamp\"\n                        }\n                    }\n                },\n                \"message-collections\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        fileId: {\n                            keyPath: \"fileId\"\n                        },\n                        fileName: {\n                            keyPath: \"fileName\"\n                        },\n                        createdAt: {\n                            keyPath: \"createdAt\"\n                        },\n                        updatedAt: {\n                            keyPath: \"updatedAt\"\n                        },\n                        version: {\n                            keyPath: \"version\"\n                        }\n                    }\n                },\n                \"text-segments\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        fileId: {\n                            keyPath: \"fileId\"\n                        },\n                        language: {\n                            keyPath: \"language\"\n                        },\n                        wordCount: {\n                            keyPath: \"wordCount\"\n                        },\n                        createdAt: {\n                            keyPath: \"createdAt\"\n                        }\n                    }\n                },\n                \"file-associations\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        fileId: {\n                            keyPath: \"fileId\"\n                        },\n                        fileName: {\n                            keyPath: \"fileName\"\n                        },\n                        fileType: {\n                            keyPath: \"fileType\"\n                        },\n                        createdAt: {\n                            keyPath: \"createdAt\"\n                        },\n                        updatedAt: {\n                            keyPath: \"updatedAt\"\n                        }\n                    }\n                },\n                \"media-files\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        artworkId: {\n                            keyPath: \"artworkId\"\n                        },\n                        type: {\n                            keyPath: \"type\"\n                        },\n                        mimeType: {\n                            keyPath: \"mimeType\"\n                        },\n                        uploadedAt: {\n                            keyPath: \"uploadedAt\"\n                        },\n                        size: {\n                            keyPath: \"size\"\n                        },\n                        name: {\n                            keyPath: \"name\"\n                        }\n                    }\n                },\n                \"media-chunks\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        fileId: {\n                            keyPath: \"fileId\"\n                        },\n                        chunkIndex: {\n                            keyPath: \"chunkIndex\"\n                        },\n                        createdAt: {\n                            keyPath: \"createdAt\"\n                        }\n                    }\n                },\n                \"prompt-templates\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        content: {\n                            keyPath: \"content\"\n                        },\n                        createdAt: {\n                            keyPath: \"createdAt\"\n                        },\n                        updatedAt: {\n                            keyPath: \"updatedAt\"\n                        }\n                    }\n                },\n                \"prompt-configs\": {\n                    keyPath: \"id\",\n                    indexes: {\n                        name: {\n                            keyPath: \"name\"\n                        },\n                        isActive: {\n                            keyPath: \"isActive\"\n                        },\n                        createdAt: {\n                            keyPath: \"createdAt\"\n                        },\n                        updatedAt: {\n                            keyPath: \"updatedAt\"\n                        }\n                    }\n                }\n            }\n        };\n    }\n    constructor(){\n        this.db = null;\n        this.config = DatabaseService.DB_CONFIG;\n    }\n    /**\r\n   * 获取数据库服务单例\r\n   */ static getInstance() {\n        if (!DatabaseService.instance) {\n            DatabaseService.instance = new DatabaseService();\n        }\n        return DatabaseService.instance;\n    }\n    /**\r\n   * 初始化数据库连接\r\n   */ async initialize() {\n        try {\n            if (this.db) {\n                return {\n                    success: true,\n                    data: this.db\n                };\n            }\n            const db = await this.openDatabase();\n            this.db = db;\n            console.log(\"✅ IndexedDB 数据库初始化成功\");\n            return {\n                success: true,\n                data: db\n            };\n        } catch (error) {\n            const errorMessage = `数据库初始化失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 打开数据库连接\r\n   */ openDatabase() {\n        return new Promise((resolve, reject)=>{\n            const request = indexedDB.open(this.config.name, this.config.version);\n            request.onerror = ()=>{\n                reject(new Error(`无法打开数据库: ${request.error?.message}`));\n            };\n            request.onsuccess = ()=>{\n                resolve(request.result);\n            };\n            request.onupgradeneeded = (event)=>{\n                const db = event.target.result;\n                const transaction = event.target.transaction;\n                const oldVersion = event.oldVersion;\n                const newVersion = event.newVersion;\n                console.log(`🔄 数据库升级: ${oldVersion} -> ${newVersion}`);\n                this.createObjectStores(db, transaction, oldVersion);\n            };\n        });\n    }\n    /**\r\n   * 创建对象存储和索引（简化版本）\r\n   */ createObjectStores(db, transaction, oldVersion) {\n        console.log(\"\\uD83D\\uDD04 开始数据库升级...\");\n        console.log(`📊 升级信息: 旧版本 ${oldVersion} -> 新版本 ${this.config.version}`);\n        try {\n            // 简化策略：直接创建缺失的存储，不删除现有存储\n            Object.entries(this.config.stores).forEach(([storeName, storeConfig])=>{\n                if (!db.objectStoreNames.contains(storeName)) {\n                    console.log(`📦 创建新存储: ${storeName}`);\n                    // 创建对象存储\n                    const store = db.createObjectStore(storeName, {\n                        keyPath: storeConfig.keyPath\n                    });\n                    // 创建索引\n                    if (storeConfig.indexes) {\n                        Object.entries(storeConfig.indexes).forEach(([indexName, indexConfig])=>{\n                            try {\n                                store.createIndex(indexName, indexConfig.keyPath, {\n                                    unique: indexConfig.unique || false\n                                });\n                                console.log(`  ✅ 创建索引: ${storeName}.${indexName}`);\n                            } catch (error) {\n                                console.warn(`  ⚠️ 索引创建失败: ${storeName}.${indexName}`, error);\n                            }\n                        });\n                    }\n                } else {\n                    console.log(`✅ 存储已存在: ${storeName}`);\n                }\n            });\n            console.log(\"\\uD83C\\uDF89 数据库升级完成！\");\n        } catch (error) {\n            console.error(\"❌ 数据库升级失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 重新创建存储并恢复数据\r\n   */ recreateStore(db, storeName, storeConfig, backupData) {\n        console.log(`📦 重新创建存储: ${storeName}`);\n        // 创建新存储\n        const store = db.createObjectStore(storeName, {\n            keyPath: storeConfig.keyPath\n        });\n        // 创建所有索引\n        if (storeConfig.indexes) {\n            Object.entries(storeConfig.indexes).forEach(([indexName, indexConfig])=>{\n                try {\n                    store.createIndex(indexName, indexConfig.keyPath, {\n                        unique: indexConfig.unique || false\n                    });\n                    console.log(`  ✅ 创建索引: ${storeName}.${indexName}`);\n                } catch (error) {\n                    console.warn(`  ⚠️ 索引创建失败: ${storeName}.${indexName}`, error);\n                }\n            });\n        }\n        // 如果有备份数据，立即恢复\n        if (backupData && backupData.length > 0) {\n            console.log(`📥 恢复数据到 ${storeName}: ${backupData.length} 条记录`);\n            let restored = 0;\n            backupData.forEach((item)=>{\n                try {\n                    const request = store.add(item);\n                    request.onsuccess = ()=>{\n                        restored++;\n                        if (restored === backupData.length) {\n                            console.log(`✅ ${storeName} 数据恢复完成: ${restored} 条记录`);\n                        }\n                    };\n                    request.onerror = ()=>{\n                        console.warn(`⚠️ 恢复单条数据失败:`, item);\n                    };\n                } catch (error) {\n                    console.warn(`⚠️ 添加数据失败:`, error);\n                }\n            });\n        }\n    }\n    /**\r\n   * 获取数据库连接\r\n   */ async getDatabase() {\n        if (!this.db) {\n            const result = await this.initialize();\n            if (!result.success || !result.data) {\n                throw new Error(result.error || \"数据库连接失败\");\n            }\n        }\n        return this.db;\n    }\n    /**\r\n   * 执行事务操作\r\n   */ async executeTransaction(storeNames, mode, operation) {\n        try {\n            const db = await this.getDatabase();\n            const transaction = db.transaction(storeNames, mode);\n            // 设置事务错误处理\n            transaction.onerror = ()=>{\n                console.error(\"事务执行失败:\", transaction.error);\n            };\n            // 获取对象存储\n            const stores = Array.isArray(storeNames) ? storeNames.map((name)=>transaction.objectStore(name)) : transaction.objectStore(storeNames);\n            const result = await operation(stores);\n            return {\n                success: true,\n                data: result\n            };\n        } catch (error) {\n            const errorMessage = `事务执行失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 添加数据\r\n   */ async add(storeName, data) {\n        return this.executeTransaction(storeName, \"readwrite\", async (store)=>{\n            const request = store.add(data);\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve(String(request.result));\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 获取数据\r\n   */ async get(storeName, key) {\n        return this.executeTransaction(storeName, \"readonly\", async (store)=>{\n            const request = store.get(key);\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve(request.result);\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 获取所有数据\r\n   */ async getAll(storeName) {\n        return this.executeTransaction(storeName, \"readonly\", async (store)=>{\n            const request = store.getAll();\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve(request.result);\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 更新数据\r\n   */ async update(storeName, data) {\n        return this.executeTransaction(storeName, \"readwrite\", async (store)=>{\n            const request = store.put(data);\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve(String(request.result));\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 保存或更新数据 (put操作)\r\n   */ async put(storeName, data) {\n        return this.executeTransaction(storeName, \"readwrite\", async (store)=>{\n            const request = store.put(data);\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve(String(request.result));\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 删除数据\r\n   */ async delete(storeName, key) {\n        return this.executeTransaction(storeName, \"readwrite\", async (store)=>{\n            const request = store.delete(key);\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve();\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 通过索引查询数据\r\n   */ async getByIndex(storeName, indexName, value) {\n        return this.executeTransaction(storeName, \"readonly\", async (store)=>{\n            const index = store.index(indexName);\n            const request = index.getAll(value);\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve(request.result);\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 计数\r\n   */ async count(storeName) {\n        return this.executeTransaction(storeName, \"readonly\", async (store)=>{\n            const request = store.count();\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve(request.result);\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 清空存储\r\n   */ async clear(storeName) {\n        return this.executeTransaction(storeName, \"readwrite\", async (store)=>{\n            const request = store.clear();\n            return new Promise((resolve, reject)=>{\n                request.onsuccess = ()=>resolve();\n                request.onerror = ()=>reject(request.error);\n            });\n        });\n    }\n    /**\r\n   * 关闭数据库连接\r\n   */ close() {\n        if (this.db) {\n            this.db.close();\n            this.db = null;\n            console.log(\"\\uD83D\\uDD12 数据库连接已关闭\");\n        }\n    }\n    /**\r\n   * 检查浏览器是否支持IndexedDB\r\n   */ static isSupported() {\n        return \"indexedDB\" in window;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/database/DatabaseService.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/personaService.ts":
/*!****************************************!*\
  !*** ./src/services/personaService.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PersonaService: () => (/* binding */ PersonaService)\n/* harmony export */ });\n/* harmony import */ var _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database/DatabaseService */ \"(ssr)/./src/services/database/DatabaseService.ts\");\n/**\r\n * 人设管理服务\r\n * 提供人设的创建、存储、管理和变量替换功能\r\n */ \nclass PersonaService {\n    constructor(){\n        this.storeName = \"personas\";\n        // 人设变化事件监听器\n        this.personaChangeListeners = [];\n        // ==================== 文件夹管理功能 ====================\n        this.folderStoreName = \"persona_folders\";\n        this.dbService = _database_DatabaseService__WEBPACK_IMPORTED_MODULE_0__.DatabaseService.getInstance();\n    }\n    /**\r\n   * 获取人设服务单例\r\n   */ static getInstance() {\n        if (!PersonaService.instance) {\n            PersonaService.instance = new PersonaService();\n        }\n        return PersonaService.instance;\n    }\n    /**\r\n   * 创建人设\r\n   */ async createPersona(personaData) {\n        try {\n            const now = Date.now();\n            const personaId = `persona-${now}-${Math.random().toString(36).substr(2, 9)}`;\n            const persona = {\n                ...personaData,\n                id: personaId,\n                createdAt: now,\n                updatedAt: now\n            };\n            const result = await this.dbService.add(this.storeName, persona);\n            if (result.success) {\n                console.log(\"✅ 人设创建成功:\", personaData.name);\n                return {\n                    success: true,\n                    data: persona\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = `创建人设失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取人设\r\n   */ async getPersona(personaId) {\n        try {\n            const result = await this.dbService.get(this.storeName, personaId);\n            return result;\n        } catch (error) {\n            const errorMessage = `获取人设失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取所有人设\r\n   */ async getAllPersonas() {\n        try {\n            const result = await this.dbService.getAll(this.storeName);\n            if (result.success && result.data) {\n                // 按创建时间排序，最新的在前\n                const sortedPersonas = result.data.sort((a, b)=>b.createdAt - a.createdAt);\n                return {\n                    success: true,\n                    data: sortedPersonas\n                };\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = `获取所有人设失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 更新人设\r\n   */ async updatePersona(personaId, updates) {\n        try {\n            // 获取现有人设\n            const existingResult = await this.dbService.get(this.storeName, personaId);\n            if (!existingResult.success || !existingResult.data) {\n                return {\n                    success: false,\n                    error: \"人设不存在\"\n                };\n            }\n            const existingPersona = existingResult.data;\n            // 合并更新\n            const updatedPersona = {\n                ...existingPersona,\n                ...updates,\n                id: personaId,\n                updatedAt: Date.now()\n            };\n            const result = await this.dbService.put(this.storeName, updatedPersona);\n            if (result.success) {\n                console.log(\"✅ 人设更新成功:\", personaId);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = `更新人设失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 删除人设\r\n   */ async deletePersona(personaId) {\n        try {\n            const result = await this.dbService.delete(this.storeName, personaId);\n            if (result.success) {\n                console.log(\"✅ 人设删除成功:\", personaId);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = `删除人设失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取活跃人设\r\n   */ async getActivePersona() {\n        try {\n            // 获取所有人设，然后筛选活跃的人设\n            const result = await this.dbService.getAll(this.storeName);\n            if (result.success && result.data) {\n                // 查找活跃人设\n                const activePersonas = result.data.filter((persona)=>persona.isActive);\n                if (activePersonas.length > 0) {\n                    // 如果有多个活跃人设，返回最新的一个\n                    const activePersona = activePersonas.sort((a, b)=>b.updatedAt - a.updatedAt)[0];\n                    return {\n                        success: true,\n                        data: activePersona\n                    };\n                }\n            }\n            return {\n                success: true,\n                data: null\n            };\n        } catch (error) {\n            const errorMessage = `获取活跃人设失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 设置活跃人设\r\n   */ async setActivePersona(personaId) {\n        try {\n            // 首先将所有人设设为非活跃\n            const allPersonasResult = await this.dbService.getAll(this.storeName);\n            if (allPersonasResult.success && allPersonasResult.data) {\n                for (const persona of allPersonasResult.data){\n                    if (persona.isActive) {\n                        await this.dbService.put(this.storeName, {\n                            ...persona,\n                            isActive: false,\n                            updatedAt: Date.now()\n                        });\n                    }\n                }\n            }\n            // 设置指定人设为活跃\n            const result = await this.updatePersona(personaId, {\n                isActive: true\n            });\n            if (result.success) {\n                console.log(\"✅ 活跃人设设置成功:\", personaId);\n                // 获取新激活的人设并通知监听器\n                const activePersonaResult = await this.getPersona(personaId);\n                if (activePersonaResult.success && activePersonaResult.data) {\n                    this.notifyPersonaChangeListeners(activePersonaResult.data);\n                }\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = `设置活跃人设失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 订阅人设变化事件\r\n   */ subscribeToPersonaChange(listener) {\n        this.personaChangeListeners.push(listener);\n        console.log(\"✅ 人设变化监听器已添加，当前监听器数量:\", this.personaChangeListeners.length);\n    }\n    /**\r\n   * 取消订阅人设变化事件\r\n   */ unsubscribeFromPersonaChange(listener) {\n        this.personaChangeListeners = this.personaChangeListeners.filter((l)=>l !== listener);\n        console.log(\"✅ 人设变化监听器已移除，当前监听器数量:\", this.personaChangeListeners.length);\n    }\n    /**\r\n   * 通知所有监听器人设已变化\r\n   */ notifyPersonaChangeListeners(persona) {\n        console.log(\"\\uD83D\\uDD14 通知人设变化:\", persona?.name || \"无\", \"监听器数量:\", this.personaChangeListeners.length);\n        for (const listener of this.personaChangeListeners){\n            try {\n                listener(persona);\n            } catch (error) {\n                console.error(\"❌ 人设变化监听器执行失败:\", error);\n            }\n        }\n    }\n    /**\r\n   * 清除活跃人设（设置所有人设为非活跃）\r\n   */ async clearActivePersona() {\n        try {\n            // 将所有人设设为非活跃\n            const allPersonasResult = await this.dbService.getAll(this.storeName);\n            if (allPersonasResult.success && allPersonasResult.data) {\n                for (const persona of allPersonasResult.data){\n                    if (persona.isActive) {\n                        await this.dbService.put(this.storeName, {\n                            ...persona,\n                            isActive: false,\n                            updatedAt: Date.now()\n                        });\n                    }\n                }\n            }\n            // 通知监听器人设已清除\n            this.notifyPersonaChangeListeners(null);\n            console.log(\"✅ 活跃人设已清除\");\n            return {\n                success: true,\n                data: true\n            };\n        } catch (error) {\n            const errorMessage = `清除活跃人设失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取人设介绍的前100字（用于变量替换）\r\n   */ getPersonaIntroFirst100Chars(persona) {\n        if (!persona.description) return \"\";\n        return persona.description.substring(0, 100);\n    }\n    /**\r\n   * 替换设计提示词中的变量\r\n   */ replaceDesignPromptVariables(designPrompt, persona) {\n        const personaIntro = this.getPersonaIntroFirst100Chars(persona);\n        return designPrompt.replace(/\\[\\$\\{personaIntro\\}\\]/g, personaIntro);\n    }\n    /**\r\n   * 获取完整的设计提示词（包含变量替换）\r\n   */ async getDesignPromptWithPersona(designPromptTemplate) {\n        try {\n            const activePersonaResult = await this.getActivePersona();\n            if (activePersonaResult.success && activePersonaResult.data) {\n                return this.replaceDesignPromptVariables(designPromptTemplate, activePersonaResult.data);\n            }\n            // 如果没有活跃人设，返回原始模板\n            return designPromptTemplate;\n        } catch (error) {\n            console.error(\"❌ 获取设计提示词失败:\", error);\n            return designPromptTemplate;\n        }\n    }\n    /**\r\n   * 创建默认人设\r\n   */ async createDefaultPersona(folderId) {\n        const defaultPersona = {\n            name: \"默认助手\",\n            description: \"我是一个智能助手，专注于为用户提供高质量的帮助和支持。我具有专业的知识背景，能够理解用户的需求并提供相应的解决方案。我的性格友好、专业、乐于助人。作为一个智能助手，我会尽力帮助用户解决问题，提供准确和有用的信息。\",\n            folderId: folderId || \"default-folder\",\n            isActive: true\n        };\n        const result = await this.createPersona(defaultPersona);\n        if (result.success && result.data) {\n            return {\n                success: true,\n                data: result.data.id\n            };\n        }\n        return {\n            success: false,\n            error: result.error\n        };\n    }\n    /**\r\n   * 清空所有人设\r\n   */ async clearAllPersonas() {\n        try {\n            const result = await this.dbService.clear(this.storeName);\n            if (result.success) {\n                console.log(\"✅ 所有人设已清空\");\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = `清空人设失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 迁移人设数据\r\n   * 将现有的复杂字段内容合并到description字段中\r\n   */ async migratePersonaData() {\n        try {\n            console.log(\"\\uD83D\\uDD04 开始迁移人设数据...\");\n            const result = await this.getAllPersonas();\n            if (!result.success || !result.data) {\n                return {\n                    success: false,\n                    error: \"无法获取现有人设数据\"\n                };\n            }\n            let migratedCount = 0;\n            let skippedCount = 0;\n            for (const persona of result.data){\n                // 检查是否需要迁移（是否包含旧字段）\n                const personaAny = persona;\n                if (this.needsMigration(personaAny)) {\n                    const newDescription = this.mergePersonaFields(personaAny);\n                    // 更新人设数据\n                    const updateResult = await this.updatePersona(persona.id, {\n                        description: newDescription\n                    });\n                    if (updateResult.success) {\n                        migratedCount++;\n                        console.log(`✅ 已迁移人设: ${persona.name}`);\n                    } else {\n                        console.error(`❌ 迁移失败: ${persona.name}`, updateResult.error);\n                    }\n                } else {\n                    skippedCount++;\n                }\n            }\n            console.log(`🎉 人设数据迁移完成: 迁移 ${migratedCount} 个，跳过 ${skippedCount} 个`);\n            return {\n                success: true,\n                data: {\n                    migratedCount,\n                    skippedCount\n                }\n            };\n        } catch (error) {\n            const errorMessage = `迁移人设数据失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 检查人设是否需要迁移\r\n   */ needsMigration(persona) {\n        return !!(persona.role || persona.personality || persona.instructions || persona.personaIntro);\n    }\n    /**\r\n   * 合并人设字段到description中\r\n   */ mergePersonaFields(persona) {\n        let description = persona.description || \"\";\n        // 如果已有description，先保留原有内容\n        if (description) {\n            description += \"\\n\\n\";\n        }\n        // 按逻辑顺序合并字段内容\n        const sections = [];\n        if (persona.role) {\n            sections.push(`**角色定位**: ${persona.role}`);\n        }\n        if (persona.personaIntro) {\n            sections.push(`**人设介绍**: ${persona.personaIntro}`);\n        }\n        if (persona.personality) {\n            sections.push(`**性格特征**: ${persona.personality}`);\n        }\n        if (persona.instructions) {\n            sections.push(`**行为指令**: ${persona.instructions}`);\n        }\n        // 将所有部分合并\n        description += sections.join(\"\\n\\n\");\n        return description.trim();\n    }\n    /**\r\n   * 获取迁移状态\r\n   * 检查是否还有需要迁移的数据\r\n   */ async getMigrationStatus() {\n        try {\n            const result = await this.getAllPersonas();\n            if (!result.success || !result.data) {\n                return {\n                    success: false,\n                    error: \"无法获取人设数据\"\n                };\n            }\n            const totalCount = result.data.length;\n            const needsMigrationCount = result.data.filter((persona)=>this.needsMigration(persona)).length;\n            return {\n                success: true,\n                data: {\n                    needsMigration: needsMigrationCount > 0,\n                    totalCount,\n                    needsMigrationCount\n                }\n            };\n        } catch (error) {\n            const errorMessage = `获取迁移状态失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 创建人设文件夹\r\n   */ async createFolder(folderData) {\n        try {\n            const now = Date.now();\n            const folderId = `folder-${now}-${Math.random().toString(36).substring(2, 9)}`;\n            const folder = {\n                ...folderData,\n                id: folderId,\n                createdAt: now,\n                updatedAt: now\n            };\n            const result = await this.dbService.add(this.folderStoreName, folder);\n            if (result.success) {\n                console.log(\"✅ 人设文件夹创建成功:\", folderData.name);\n                return {\n                    success: true,\n                    data: folder\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = `创建文件夹失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取所有文件夹\r\n   */ async getAllFolders() {\n        try {\n            const result = await this.dbService.getAll(this.folderStoreName);\n            if (result.success && result.data) {\n                // 为每个文件夹计算人设数量\n                const foldersWithCount = await Promise.all(result.data.map(async (folder)=>{\n                    const personaCountResult = await this.getPersonaCountInFolder(folder.id);\n                    return {\n                        ...folder,\n                        personaCount: personaCountResult.success ? personaCountResult.data : 0\n                    };\n                }));\n                // 按创建时间排序，最新的在前\n                const sortedFolders = foldersWithCount.sort((a, b)=>b.createdAt - a.createdAt);\n                return {\n                    success: true,\n                    data: sortedFolders\n                };\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = `获取所有文件夹失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取文件夹中的人设数量\r\n   */ async getPersonaCountInFolder(folderId) {\n        try {\n            const result = await this.getPersonasByFolder(folderId);\n            if (result.success && result.data) {\n                return {\n                    success: true,\n                    data: result.data.length\n                };\n            }\n            return {\n                success: true,\n                data: 0\n            };\n        } catch (error) {\n            const errorMessage = `获取文件夹人设数量失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 获取指定文件夹中的所有人设\r\n   */ async getPersonasByFolder(folderId) {\n        try {\n            const result = await this.dbService.getAll(this.storeName);\n            if (result.success && result.data) {\n                // 筛选指定文件夹的人设\n                const folderPersonas = result.data.filter((persona)=>persona.folderId === folderId);\n                // 按创建时间排序，最新的在前\n                const sortedPersonas = folderPersonas.sort((a, b)=>b.createdAt - a.createdAt);\n                return {\n                    success: true,\n                    data: sortedPersonas\n                };\n            }\n            return {\n                success: true,\n                data: []\n            };\n        } catch (error) {\n            const errorMessage = `获取文件夹人设失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 更新文件夹\r\n   */ async updateFolder(folderId, updates) {\n        try {\n            // 获取现有文件夹\n            const existingResult = await this.dbService.get(this.folderStoreName, folderId);\n            if (!existingResult.success || !existingResult.data) {\n                return {\n                    success: false,\n                    error: \"文件夹不存在\"\n                };\n            }\n            const existingFolder = existingResult.data;\n            // 合并更新\n            const updatedFolder = {\n                ...existingFolder,\n                ...updates,\n                id: folderId,\n                updatedAt: Date.now()\n            };\n            const result = await this.dbService.put(this.folderStoreName, updatedFolder);\n            if (result.success) {\n                console.log(\"✅ 文件夹更新成功:\", folderId);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = `更新文件夹失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 删除文件夹\r\n   */ async deleteFolder(folderId) {\n        try {\n            // 检查文件夹中是否还有人设\n            const personasResult = await this.getPersonasByFolder(folderId);\n            if (personasResult.success && personasResult.data && personasResult.data.length > 0) {\n                return {\n                    success: false,\n                    error: \"文件夹中还有人设，无法删除\"\n                };\n            }\n            const result = await this.dbService.delete(this.folderStoreName, folderId);\n            if (result.success) {\n                console.log(\"✅ 文件夹删除成功:\", folderId);\n                return {\n                    success: true,\n                    data: true\n                };\n            }\n            return {\n                success: false,\n                error: result.error\n            };\n        } catch (error) {\n            const errorMessage = `删除文件夹失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 移动人设到指定文件夹\r\n   */ async movePersonaToFolder(personaId, targetFolderId) {\n        try {\n            // 验证目标文件夹是否存在\n            const folderResult = await this.dbService.get(this.folderStoreName, targetFolderId);\n            if (!folderResult.success || !folderResult.data) {\n                return {\n                    success: false,\n                    error: \"目标文件夹不存在\"\n                };\n            }\n            // 更新人设的文件夹ID\n            const result = await this.updatePersona(personaId, {\n                folderId: targetFolderId\n            });\n            if (result.success) {\n                console.log(\"✅ 人设移动成功:\", personaId, \"->\", targetFolderId);\n            }\n            return result;\n        } catch (error) {\n            const errorMessage = `移动人设失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 创建默认文件夹\r\n   */ async createDefaultFolders() {\n        try {\n            const defaultFolders = [\n                {\n                    name: \"工作助手\"\n                },\n                {\n                    name: \"学习助手\"\n                },\n                {\n                    name: \"生活助手\"\n                },\n                {\n                    name: \"创意助手\"\n                }\n            ];\n            const createdFolderIds = [];\n            for (const folderData of defaultFolders){\n                const result = await this.createFolder(folderData);\n                if (result.success && result.data) {\n                    createdFolderIds.push(result.data.id);\n                }\n            }\n            console.log(\"✅ 默认文件夹创建完成:\", createdFolderIds.length);\n            return {\n                success: true,\n                data: createdFolderIds\n            };\n        } catch (error) {\n            const errorMessage = `创建默认文件夹失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n    /**\r\n   * 初始化人设系统\r\n   * 创建默认文件夹和默认人设\r\n   */ async initializePersonaSystem() {\n        try {\n            // 检查是否已经初始化\n            const foldersResult = await this.getAllFolders();\n            if (foldersResult.success && foldersResult.data && foldersResult.data.length > 0) {\n                console.log(\"✅ 人设系统已初始化\");\n                // 系统已初始化，获取第一个文件夹和第一个人设的ID\n                const firstFolder = foldersResult.data[0];\n                const personasResult = await this.getPersonasByFolder(firstFolder.id);\n                const firstPersonaId = personasResult.success && personasResult.data && personasResult.data.length > 0 ? personasResult.data[0].id : \"no-persona\";\n                return {\n                    success: true,\n                    data: {\n                        folderId: firstFolder.id,\n                        personaId: firstPersonaId\n                    }\n                };\n            }\n            // 创建默认文件夹\n            const defaultFolderResult = await this.createFolder({\n                name: \"默认助手\"\n            });\n            if (!defaultFolderResult.success || !defaultFolderResult.data) {\n                return {\n                    success: false,\n                    error: \"创建默认文件夹失败\"\n                };\n            }\n            const defaultFolderId = defaultFolderResult.data.id;\n            // 创建默认人设\n            const defaultPersona = {\n                name: \"默认助手\",\n                description: \"我是一个智能助手，专注于为用户提供高质量的帮助和支持。我具有专业的知识背景，能够理解用户的需求并提供相应的解决方案。我的性格友好、专业、乐于助人。作为一个智能助手，我会尽力帮助用户解决问题，提供准确和有用的信息。\",\n                folderId: defaultFolderId,\n                isActive: true\n            };\n            const personaResult = await this.createPersona(defaultPersona);\n            if (!personaResult.success || !personaResult.data) {\n                return {\n                    success: false,\n                    error: \"创建默认人设失败\"\n                };\n            }\n            console.log(\"✅ 人设系统初始化完成\");\n            return {\n                success: true,\n                data: {\n                    folderId: defaultFolderId,\n                    personaId: personaResult.data.id\n                }\n            };\n        } catch (error) {\n            const errorMessage = `初始化人设系统失败: ${error instanceof Error ? error.message : String(error)}`;\n            console.error(\"❌\", errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/personaService.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fa0fc6988a5f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXJ0d29yay1wbGF0Zm9ybS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YzRmYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZhMGZjNjk4OGE1ZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_PersonaContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/PersonaContext */ \"(rsc)/./src/contexts/PersonaContext.tsx\");\n/* harmony import */ var _contexts_AudienceContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AudienceContext */ \"(rsc)/./src/contexts/AudienceContext.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"作品展示平台 - 宫崎骏风格创作工具\",\n    description: \"基于宫崎骏手绘风格的本地化作品展示和管理平台\",\n    keywords: [\n        \"作品展示\",\n        \"宫崎骏风格\",\n        \"创作工具\",\n        \"IndexedDB\",\n        \"SVG装饰\"\n    ],\n    authors: [\n        {\n            name: \"Artwork Platform Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"icon\",\n                    type: \"image/svg+xml\",\n                    href: \"/assets/svg/favicon.svg\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"font-primary bg-primary-beige text-black antialiased custom-scrollbar\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_PersonaContext__WEBPACK_IMPORTED_MODULE_2__.PersonaProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AudienceContext__WEBPACK_IMPORTED_MODULE_3__.AudienceProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            id: \"app\",\n                            className: \"min-h-screen flex flex-col custom-scrollbar\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDc0I7QUFFcUM7QUFDRTtBQUV0RCxNQUFNRSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7UUFBQztRQUFRO1FBQVM7UUFBUTtRQUFhO0tBQVE7SUFDekRDLFNBQVM7UUFBQztZQUFFQyxNQUFNO1FBQXdCO0tBQUU7SUFDNUNDLFVBQVU7QUFDWixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7OzBCQUNULDhEQUFDQzswQkFDQyw0RUFBQ0M7b0JBQUtDLEtBQUk7b0JBQU9DLE1BQUs7b0JBQWdCQyxNQUFLOzs7Ozs7Ozs7OzswQkFFN0MsOERBQUNDO2dCQUFLQyxXQUFVOzBCQUNkLDRFQUFDbkIscUVBQWVBOzhCQUNkLDRFQUFDQyx1RUFBZ0JBO2tDQUNmLDRFQUFDbUI7NEJBQUlDLElBQUc7NEJBQU1GLFdBQVU7c0NBQ3JCVDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT2YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcnR3b3JrLXBsYXRmb3JtLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXHJcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcclxuXHJcbmltcG9ydCB7IFBlcnNvbmFQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvUGVyc29uYUNvbnRleHQnXHJcbmltcG9ydCB7IEF1ZGllbmNlUHJvdmlkZXIgfSBmcm9tICdAL2NvbnRleHRzL0F1ZGllbmNlQ29udGV4dCdcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6ICfkvZzlk4HlsZXnpLrlubPlj7AgLSDlrqvltI7pqo/po47moLzliJvkvZzlt6XlhbcnLFxyXG4gIGRlc2NyaXB0aW9uOiAn5Z+65LqO5a6r5bSO6aqP5omL57uY6aOO5qC855qE5pys5Zyw5YyW5L2c5ZOB5bGV56S65ZKM566h55CG5bmz5Y+wJyxcclxuICBrZXl3b3JkczogWyfkvZzlk4HlsZXnpLonLCAn5a6r5bSO6aqP6aOO5qC8JywgJ+WIm+S9nOW3peWFtycsICdJbmRleGVkREInLCAnU1ZH6KOF6aWwJ10sXHJcbiAgYXV0aG9yczogW3sgbmFtZTogJ0FydHdvcmsgUGxhdGZvcm0gVGVhbScgfV0sXHJcbiAgdmlld3BvcnQ6ICd3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MScsXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xyXG4gIGNoaWxkcmVuLFxyXG59OiB7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxyXG59KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxyXG4gICAgICA8aGVhZD5cclxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgdHlwZT1cImltYWdlL3N2Zyt4bWxcIiBocmVmPVwiL2Fzc2V0cy9zdmcvZmF2aWNvbi5zdmdcIiAvPlxyXG4gICAgICA8L2hlYWQ+XHJcbiAgICAgIDxib2R5IGNsYXNzTmFtZT1cImZvbnQtcHJpbWFyeSBiZy1wcmltYXJ5LWJlaWdlIHRleHQtYmxhY2sgYW50aWFsaWFzZWQgY3VzdG9tLXNjcm9sbGJhclwiPlxyXG4gICAgICAgIDxQZXJzb25hUHJvdmlkZXI+XHJcbiAgICAgICAgICA8QXVkaWVuY2VQcm92aWRlcj5cclxuICAgICAgICAgICAgPGRpdiBpZD1cImFwcFwiIGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGZsZXgtY29sIGN1c3RvbS1zY3JvbGxiYXJcIj5cclxuICAgICAgICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9BdWRpZW5jZVByb3ZpZGVyPlxyXG4gICAgICAgIDwvUGVyc29uYVByb3ZpZGVyPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKVxyXG59Il0sIm5hbWVzIjpbIlBlcnNvbmFQcm92aWRlciIsIkF1ZGllbmNlUHJvdmlkZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsImF1dGhvcnMiLCJuYW1lIiwidmlld3BvcnQiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImhlYWQiLCJsaW5rIiwicmVsIiwidHlwZSIsImhyZWYiLCJib2R5IiwiY2xhc3NOYW1lIiwiZGl2IiwiaWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AudienceContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/AudienceContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudienceProvider: () => (/* binding */ e0),\n/* harmony export */   audienceEventBus: () => (/* binding */ e2),\n/* harmony export */   useAudience: () => (/* binding */ e1)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\\新写作\\src\\contexts\\AudienceContext.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\\新写作\\src\\contexts\\AudienceContext.tsx#AudienceProvider`);\n\nconst e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\\新写作\\src\\contexts\\AudienceContext.tsx#useAudience`);\n\nconst e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\\新写作\\src\\contexts\\AudienceContext.tsx#audienceEventBus`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/contexts/AudienceContext.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/PersonaContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/PersonaContext.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PersonaProvider: () => (/* binding */ e0),\n/* harmony export */   personaEventBus: () => (/* binding */ e2),\n/* harmony export */   usePersona: () => (/* binding */ e1)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\\新写作\\src\\contexts\\PersonaContext.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\\新写作\\src\\contexts\\PersonaContext.tsx#PersonaProvider`);\n\nconst e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\\新写作\\src\\contexts\\PersonaContext.tsx#usePersona`);\n\nconst e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\\新写作\\src\\contexts\\PersonaContext.tsx#personaEventBus`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/contexts/PersonaContext.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5C%E6%96%B0%E5%86%99%E4%BD%9C%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C%E6%96%B0%E5%86%99%E4%BD%9C&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();