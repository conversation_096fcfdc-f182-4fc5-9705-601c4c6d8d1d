"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lib0";
exports.ids = ["vendor-chunks/lib0"];
exports.modules = {

/***/ "(ssr)/./node_modules/lib0/mutex.js":
/*!************************************!*\
  !*** ./node_modules/lib0/mutex.js ***!
  \************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMutex: () => (/* binding */ createMutex)\n/* harmony export */ });\n/**\n * Mutual exclude for JavaScript.\n *\n * @module mutex\n */\n\n/**\n * @callback mutex\n * @param {function():void} cb Only executed when this mutex is not in the current stack\n * @param {function():void} [elseCb] Executed when this mutex is in the current stack\n */\n\n/**\n * Creates a mutual exclude function with the following property:\n *\n * ```js\n * const mutex = createMutex()\n * mutex(() => {\n *   // This function is immediately executed\n *   mutex(() => {\n *     // This function is not executed, as the mutex is already active.\n *   })\n * })\n * ```\n *\n * @return {mutex} A mutual exclude function\n * @public\n */\nconst createMutex = () => {\n  let token = true\n  return (f, g) => {\n    if (token) {\n      token = false\n      try {\n        f()\n      } finally {\n        token = true\n      }\n    } else if (g !== undefined) {\n      g()\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbGliMC9tdXRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxpQkFBaUI7QUFDNUIsV0FBVyxpQkFBaUI7QUFDNUI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOLElBQUk7QUFDSjtBQUNBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXJ0d29yay1wbGF0Zm9ybS8uL25vZGVfbW9kdWxlcy9saWIwL211dGV4LmpzP2VhNWYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBNdXR1YWwgZXhjbHVkZSBmb3IgSmF2YVNjcmlwdC5cbiAqXG4gKiBAbW9kdWxlIG11dGV4XG4gKi9cblxuLyoqXG4gKiBAY2FsbGJhY2sgbXV0ZXhcbiAqIEBwYXJhbSB7ZnVuY3Rpb24oKTp2b2lkfSBjYiBPbmx5IGV4ZWN1dGVkIHdoZW4gdGhpcyBtdXRleCBpcyBub3QgaW4gdGhlIGN1cnJlbnQgc3RhY2tcbiAqIEBwYXJhbSB7ZnVuY3Rpb24oKTp2b2lkfSBbZWxzZUNiXSBFeGVjdXRlZCB3aGVuIHRoaXMgbXV0ZXggaXMgaW4gdGhlIGN1cnJlbnQgc3RhY2tcbiAqL1xuXG4vKipcbiAqIENyZWF0ZXMgYSBtdXR1YWwgZXhjbHVkZSBmdW5jdGlvbiB3aXRoIHRoZSBmb2xsb3dpbmcgcHJvcGVydHk6XG4gKlxuICogYGBganNcbiAqIGNvbnN0IG11dGV4ID0gY3JlYXRlTXV0ZXgoKVxuICogbXV0ZXgoKCkgPT4ge1xuICogICAvLyBUaGlzIGZ1bmN0aW9uIGlzIGltbWVkaWF0ZWx5IGV4ZWN1dGVkXG4gKiAgIG11dGV4KCgpID0+IHtcbiAqICAgICAvLyBUaGlzIGZ1bmN0aW9uIGlzIG5vdCBleGVjdXRlZCwgYXMgdGhlIG11dGV4IGlzIGFscmVhZHkgYWN0aXZlLlxuICogICB9KVxuICogfSlcbiAqIGBgYFxuICpcbiAqIEByZXR1cm4ge211dGV4fSBBIG11dHVhbCBleGNsdWRlIGZ1bmN0aW9uXG4gKiBAcHVibGljXG4gKi9cbmV4cG9ydCBjb25zdCBjcmVhdGVNdXRleCA9ICgpID0+IHtcbiAgbGV0IHRva2VuID0gdHJ1ZVxuICByZXR1cm4gKGYsIGcpID0+IHtcbiAgICBpZiAodG9rZW4pIHtcbiAgICAgIHRva2VuID0gZmFsc2VcbiAgICAgIHRyeSB7XG4gICAgICAgIGYoKVxuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgdG9rZW4gPSB0cnVlXG4gICAgICB9XG4gICAgfSBlbHNlIGlmIChnICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIGcoKVxuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/mutex.js\n");

/***/ })

};
;