/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_editor_common_services_sync_recursive_"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services sync recursive ^.*$":
/*!*****************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/editor/common/services/ sync ^.*$ ***!
  \*****************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var map = {
	"./editorBaseApi": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorBaseApi.js",
	"./editorBaseApi.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorBaseApi.js",
	"./editorSimpleWorker": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorSimpleWorker.js",
	"./editorSimpleWorker.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorSimpleWorker.js",
	"./editorWorker": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorWorker.js",
	"./editorWorker.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorWorker.js",
	"./editorWorkerHost": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorWorkerHost.js",
	"./editorWorkerHost.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorWorkerHost.js",
	"./findSectionHeaders": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/findSectionHeaders.js",
	"./findSectionHeaders.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/findSectionHeaders.js",
	"./getIconClasses": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/getIconClasses.js",
	"./getIconClasses.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/getIconClasses.js",
	"./languageFeatureDebounce": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatureDebounce.js",
	"./languageFeatureDebounce.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatureDebounce.js",
	"./languageFeatures": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatures.js",
	"./languageFeatures.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatures.js",
	"./languageFeaturesService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageFeaturesService.js",
	"./languageFeaturesService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageFeaturesService.js",
	"./languageService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageService.js",
	"./languageService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageService.js",
	"./languagesAssociations": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languagesAssociations.js",
	"./languagesAssociations.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languagesAssociations.js",
	"./languagesRegistry": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languagesRegistry.js",
	"./languagesRegistry.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languagesRegistry.js",
	"./markerDecorations": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorations.js",
	"./markerDecorations.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorations.js",
	"./markerDecorationsService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorationsService.js",
	"./markerDecorationsService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorationsService.js",
	"./model": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/model.js",
	"./model.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/model.js",
	"./modelService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/modelService.js",
	"./modelService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/modelService.js",
	"./resolverService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/resolverService.js",
	"./resolverService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/resolverService.js",
	"./semanticTokensDto": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensDto.js",
	"./semanticTokensDto.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensDto.js",
	"./semanticTokensProviderStyling": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensProviderStyling.js",
	"./semanticTokensProviderStyling.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensProviderStyling.js",
	"./semanticTokensStyling": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStyling.js",
	"./semanticTokensStyling.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStyling.js",
	"./semanticTokensStylingService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStylingService.js",
	"./semanticTokensStylingService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStylingService.js",
	"./textModelSync/textModelSync.impl": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.impl.js",
	"./textModelSync/textModelSync.impl.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.impl.js",
	"./textModelSync/textModelSync.protocol": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js",
	"./textModelSync/textModelSync.protocol.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js",
	"./textResourceConfiguration": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/textResourceConfiguration.js",
	"./textResourceConfiguration.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/textResourceConfiguration.js",
	"./treeSitterParserService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/treeSitterParserService.js",
	"./treeSitterParserService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/treeSitterParserService.js",
	"./treeViewsDnd": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDnd.js",
	"./treeViewsDnd.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDnd.js",
	"./treeViewsDndService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDndService.js",
	"./treeViewsDndService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDndService.js",
	"./unicodeTextModelHighlighter": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/unicodeTextModelHighlighter.js",
	"./unicodeTextModelHighlighter.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/unicodeTextModelHighlighter.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/editorBaseApi": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorBaseApi.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/editorBaseApi.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorBaseApi.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/editorSimpleWorker": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorSimpleWorker.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/editorSimpleWorker.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorSimpleWorker.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/editorWorker": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorWorker.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/editorWorker.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorWorker.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/editorWorkerHost": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorWorkerHost.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/editorWorkerHost.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/editorWorkerHost.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/findSectionHeaders": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/findSectionHeaders.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/findSectionHeaders.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/findSectionHeaders.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/getIconClasses": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/getIconClasses.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/getIconClasses.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/getIconClasses.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatureDebounce": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatureDebounce.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatureDebounce.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatureDebounce.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatures": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatures.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatures.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageFeatures.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/languageFeaturesService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageFeaturesService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/languageFeaturesService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageFeaturesService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/languageService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/languageService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languageService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/languagesAssociations": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languagesAssociations.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/languagesAssociations.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languagesAssociations.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/languagesRegistry": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languagesRegistry.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/languagesRegistry.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/languagesRegistry.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorations": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorations.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorations.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorations.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorationsService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorationsService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorationsService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/markerDecorationsService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/model": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/model.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/model.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/model.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/modelService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/modelService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/modelService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/modelService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/resolverService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/resolverService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/resolverService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/resolverService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensDto": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensDto.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensDto.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensDto.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensProviderStyling": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensProviderStyling.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensProviderStyling.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensProviderStyling.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStyling": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStyling.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStyling.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStyling.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStylingService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStylingService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStylingService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/semanticTokensStylingService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.impl": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.impl.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.impl.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.impl.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/textResourceConfiguration": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/textResourceConfiguration.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/textResourceConfiguration.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/textResourceConfiguration.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/treeSitterParserService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/treeSitterParserService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/treeSitterParserService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/treeSitterParserService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDnd": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDnd.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDnd.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDnd.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDndService": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDndService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDndService.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/treeViewsDndService.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/unicodeTextModelHighlighter": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/unicodeTextModelHighlighter.js",
	"node_modules/monaco-editor/esm/vs/editor/common/services/unicodeTextModelHighlighter.js": "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/unicodeTextModelHighlighter.js"
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services sync recursive ^.*$";

/***/ }),

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js ***!
  \**********************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb25hY28tZWRpdG9yL2VzbS92cy9lZGl0b3IvY29tbW9uL3NlcnZpY2VzL3RleHRNb2RlbFN5bmMvdGV4dE1vZGVsU3luYy5wcm90b2NvbC5qcyIsIm1hcHBpbmdzIjoiO0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDVSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbW9uYWNvLWVkaXRvci9lc20vdnMvZWRpdG9yL2NvbW1vbi9zZXJ2aWNlcy90ZXh0TW9kZWxTeW5jL3RleHRNb2RlbFN5bmMucHJvdG9jb2wuanM/MjhmZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICogIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgTGljZW5zZS4gU2VlIExpY2Vuc2UudHh0IGluIHRoZSBwcm9qZWN0IHJvb3QgZm9yIGxpY2Vuc2UgaW5mb3JtYXRpb24uXG4gKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cbmV4cG9ydCB7fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/common/services/textModelSync/textModelSync.protocol.js\n"));

/***/ })

}]);