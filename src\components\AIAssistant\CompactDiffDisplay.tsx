/**
 * 紧凑差异显示组件
 * 用于在AI助手响应中显示文件修改建议
 * 支持折叠/展开状态，显示文件名、修改统计和跳转按钮
 */

'use client'

import React, { useState, useEffect } from 'react'
import { DiffStats } from '@/types'
import { DiffToolService } from '@/services/diffToolService'

interface CompactDiffDisplayProps {
  filePath: string
  operation: 'append' | 'replace'
  content: string
  find?: string
  stats?: DiffStats
  onJumpToEditor?: (filePath: string) => void
  onApplyChanges?: (filePath: string, content: string, operation: string, find?: string) => void
  onCreateFile?: (filePath: string, content: string, operation: string) => void
  onOpenDetailedDiff?: (diffRequest: { filePath: string; operation: 'append' | 'replace'; content: string; previewMode?: boolean }) => void
  fileExists?: boolean
  artworkId?: string
  className?: string
}

export default function CompactDiffDisplay({
  filePath,
  operation,
  content,
  find,
  stats,
  onJumpToEditor,
  onApplyChanges,
  onCreateFile,
  onOpenDetailedDiff,
  fileExists = true,
  artworkId,
  className = ''
}: CompactDiffDisplayProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isCheckingFile, setIsCheckingFile] = useState(false)
  const [actualFileExists, setActualFileExists] = useState(fileExists)

  // 检查文件是否存在
  useEffect(() => {
    const checkFileExists = async () => {
      if (!artworkId) {
        setActualFileExists(fileExists)
        return
      }

      setIsCheckingFile(true)
      try {
        const diffToolService = DiffToolService.getInstance()
        const exists = await diffToolService.checkFileExists(artworkId, filePath)
        setActualFileExists(exists)
      } catch (error) {
        console.error('❌ 检查文件存在性失败:', error)
        setActualFileExists(fileExists)
      } finally {
        setIsCheckingFile(false)
      }
    }

    checkFileExists()
  }, [artworkId, filePath, fileExists])

  // 处理创建文件
  const handleCreateFile = () => {
    if (onCreateFile) {
      onCreateFile(filePath, content, operation)
    }
  }

  // 操作类型的显示文本和样式
  const operationConfig = {
    append: {
      label: '追加',
      color: 'text-green-300',
      bgColor: 'bg-green-500/20',
      borderColor: 'border-green-500/30'
    },
    replace: {
      label: '替换',
      color: 'text-orange-300',
      bgColor: 'bg-orange-500/20',
      borderColor: 'border-orange-500/30'
    }
  }

  // 默认配置，用于处理未知操作类型
  const defaultConfig = {
    label: '修改',
    color: 'text-blue-300',
    bgColor: 'bg-blue-500/20',
    borderColor: 'border-blue-500/30'
  }

  const config = operationConfig[operation] || defaultConfig

  // 处理跳转到编辑器
  const handleJumpToEditor = () => {
    if (onJumpToEditor) {
      onJumpToEditor(filePath)
    }
  }

  // 处理应用修改
  const handleApplyChanges = () => {
    if (onApplyChanges) {
      onApplyChanges(filePath, content, operation, find)
    }
  }

  // 处理打开详细差异对比
  const handleOpenDetailedDiff = () => {
    if (onOpenDetailedDiff) {
      onOpenDetailedDiff({
        filePath,
        operation,
        content,
        previewMode: true
      })
    }
  }

  // 计算内容预览（限制行数）
  const getContentPreview = (content: string, maxLines: number = 3): string => {
    const lines = content.split('\n')
    if (lines.length <= maxLines) {
      return content
    }
    return lines.slice(0, maxLines).join('\n') + `\n... (还有 ${lines.length - maxLines} 行)`
  }

  // 渲染差异预览（用于替换操作）
  const renderDiffPreview = (content: string, findText?: string) => {
    // 🔧 修复：正确处理 JSON 格式的 df 代码块
    // find 和 content 是分离的参数，不是 pattern|||replacement 格式

    if (!findText) {
      // 兼容旧格式：pattern|||replacement
      const parts = content.split('|||')
      if (parts.length >= 2) {
        const [pattern, replacement] = parts
        return renderReplaceDiff(pattern, replacement)
      }

      return (
        <div className="text-red-300">
          <div className="mb-1">⚠️ 替换操作缺少查找内容</div>
          <div className="text-xs text-red-400">请确保 JSON 中包含 find 字段</div>
        </div>
      )
    }

    // 🚀 新格式：使用分离的 find 和 content 参数
    return renderReplaceDiff(findText, content)
  }

  // 渲染替换差异对比
  const renderReplaceDiff = (pattern: string, replacement: string) => {
    return (
      <div className="space-y-3">
        {/* 搜索模式 */}
        <div className="space-y-1">
          <div className="text-xs text-blue-300 font-handwritten">搜索内容：</div>
          <div className="flex items-start gap-2">
            <span className="text-red-300 text-xs mt-1">-</span>
            <div className="text-red-200 bg-red-900/20 px-2 py-1 rounded text-xs font-mono flex-1 whitespace-pre-wrap">
              {pattern}
            </div>
          </div>
        </div>

        {/* 替换内容 */}
        <div className="space-y-1">
          <div className="text-xs text-blue-300 font-handwritten">替换为：</div>
          <div className="flex items-start gap-2">
            <span className="text-green-300 text-xs mt-1">+</span>
            <div className="text-green-200 bg-green-900/20 px-2 py-1 rounded text-xs font-mono flex-1 whitespace-pre-wrap">
              {replacement}
            </div>
          </div>
        </div>

        {/* 操作说明 */}
        <div className="text-xs text-blue-300/70 bg-blue-900/10 px-2 py-1 rounded border border-blue-500/20">
          <div className="flex items-center gap-1">
            <svg className="w-3 h-3" viewBox="0 0 24 24" fill="currentColor">
              <path d="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
            </svg>
            将使用智能匹配算法搜索并替换内容
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`df-compact-display bg-blue-500/10 border border-blue-500/30 rounded-lg p-3 my-2 not-prose ${className}`}>
      {/* 折叠状态头部 */}
      <div 
        className="flex items-center justify-between cursor-pointer hover:bg-blue-500/5 rounded-md p-1 -m-1 transition-all duration-200"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          {/* 文件图标 */}
          <svg className="w-4 h-4 text-blue-400 flex-shrink-0" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
          </svg>
          
          {/* 文件路径 */}
          <span className="text-sm font-handwritten text-blue-200 truncate" title={filePath}>
            {filePath}
          </span>
          
          {/* 文件状态标签 */}
          {isCheckingFile ? (
            <span className="text-xs px-2 py-1 rounded bg-gray-500/20 text-gray-300 border border-gray-500/30 font-handwritten">
              检查中...
            </span>
          ) : !actualFileExists ? (
            <span className="text-xs px-2 py-1 rounded bg-yellow-500/20 text-yellow-300 border border-yellow-500/30 font-handwritten">
              文件不存在
            </span>
          ) : (
            <span className={`text-xs px-2 py-1 rounded ${config.bgColor} ${config.color} ${config.borderColor} border font-handwritten`}>
              {config.label}
            </span>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* 修改统计 */}
          {stats && (stats.additions > 0 || stats.deletions > 0) && (
            <div className="flex items-center gap-1 text-xs">
              {stats.additions > 0 && (
                <span className="text-green-400">+{stats.additions}</span>
              )}
              {stats.deletions > 0 && (
                <span className="text-red-400">-{stats.deletions}</span>
              )}
              {stats.modifications > 0 && (
                <span className="text-orange-400">~{stats.modifications}</span>
              )}
            </div>
          )}
          
          {/* 展开/折叠箭头 */}
          <svg 
            className={`w-4 h-4 text-blue-400 transition-transform duration-200 ${isExpanded ? 'rotate-90' : ''}`}
            viewBox="0 0 24 24" 
            fill="currentColor"
          >
            <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
          </svg>
        </div>
      </div>

      {/* 展开状态内容 */}
      {isExpanded && (
        <div className="mt-3 pt-3 border-t border-blue-500/20">
          {/* 差异对比显示 */}
          <div className="mb-3">
            <div className="text-xs text-blue-300 mb-2 font-handwritten">
              {operation === 'replace' ? '替换差异预览：' : '修改内容预览：'}
            </div>
            {operation === 'replace' ? (
              <div className="text-xs bg-blue-900/20 p-3 rounded-md overflow-x-auto border border-blue-500/20">
                {renderDiffPreview(content, find)}
              </div>
            ) : (
              <pre className="text-xs text-blue-100 bg-blue-900/20 p-3 rounded-md overflow-x-auto whitespace-pre-wrap border border-blue-500/20">
                {isExpanded ? content : getContentPreview(content)}
              </pre>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            {actualFileExists ? (
              <>
                <button
                  onClick={handleJumpToEditor}
                  className="flex items-center gap-1 text-xs px-3 py-1.5 bg-blue-500/20 text-blue-200 border border-blue-500/30 rounded-md hover:bg-blue-500/30 transition-all duration-200 font-handwritten"
                  title="在编辑器中查看文件"
                >
                  <svg className="w-3 h-3" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z" />
                  </svg>
                  跳转编辑器
                </button>
                
                <button
                  onClick={handleApplyChanges}
                  className="flex items-center gap-1 text-xs px-3 py-1.5 bg-green-500/20 text-green-200 border border-green-500/30 rounded-md hover:bg-green-500/30 transition-all duration-200 font-handwritten"
                  title="直接应用此修改到文件"
                >
                  <svg className="w-3 h-3" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z" />
                  </svg>
                  应用修改
                </button>
                
                {/* 详细对比按钮 */}
                {onOpenDetailedDiff && (
                  <button
                    onClick={handleOpenDetailedDiff}
                    className="flex items-center gap-1 text-xs px-3 py-1.5 bg-purple-500/20 text-purple-200 border border-purple-500/30 rounded-md hover:bg-purple-500/30 transition-all duration-200 font-handwritten"
                    title="打开详细差异对比视图"
                  >
                    <svg className="w-3 h-3" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19M6.5,17.5H9.5V16H6.5V17.5M6.5,13H11.5V11.5H6.5V13M6.5,8.5H17.5V7H6.5V8.5Z" />
                    </svg>
                    详细对比
                  </button>
                )}
              </>
            ) : (
              <>
                <button
                  onClick={handleCreateFile}
                  className="flex items-center gap-1 text-xs px-3 py-1.5 bg-yellow-500/20 text-yellow-200 border border-yellow-500/30 rounded-md hover:bg-yellow-500/30 transition-all duration-200 font-handwritten"
                  title="创建文件并应用内容"
                  disabled={isCheckingFile}
                >
                  <svg className="w-3 h-3" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                  </svg>
                  {isCheckingFile ? '检查中...' : '创建文件'}
                </button>
                
                <div className="text-xs text-yellow-300/70 px-2 py-1.5 font-handwritten">
                  文件不存在，将自动创建文件夹结构
                </div>
              </>
            )}
          </div>

          {/* 额外信息 */}
          {stats && (
            <div className="mt-2 pt-2 border-t border-blue-500/10">
              <div className="text-xs text-blue-300/70 font-handwritten">
                统计：共 {stats.totalChanges} 处修改
                {stats.additions > 0 && ` • ${stats.additions} 行新增`}
                {stats.deletions > 0 && ` • ${stats.deletions} 行删除`}
                {stats.modifications > 0 && ` • ${stats.modifications} 行修改`}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}