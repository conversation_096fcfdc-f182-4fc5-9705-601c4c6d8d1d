"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_language_html_htmlMode_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/language/html/htmlMode.js":
/*!*********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/language/html/htmlMode.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompletionAdapter: function() { return /* binding */ CompletionAdapter; },\n/* harmony export */   DefinitionAdapter: function() { return /* binding */ DefinitionAdapter; },\n/* harmony export */   DiagnosticsAdapter: function() { return /* binding */ DiagnosticsAdapter; },\n/* harmony export */   DocumentColorAdapter: function() { return /* binding */ DocumentColorAdapter; },\n/* harmony export */   DocumentFormattingEditProvider: function() { return /* binding */ DocumentFormattingEditProvider; },\n/* harmony export */   DocumentHighlightAdapter: function() { return /* binding */ DocumentHighlightAdapter; },\n/* harmony export */   DocumentLinkAdapter: function() { return /* binding */ DocumentLinkAdapter; },\n/* harmony export */   DocumentRangeFormattingEditProvider: function() { return /* binding */ DocumentRangeFormattingEditProvider; },\n/* harmony export */   DocumentSymbolAdapter: function() { return /* binding */ DocumentSymbolAdapter; },\n/* harmony export */   FoldingRangeAdapter: function() { return /* binding */ FoldingRangeAdapter; },\n/* harmony export */   HoverAdapter: function() { return /* binding */ HoverAdapter; },\n/* harmony export */   ReferenceAdapter: function() { return /* binding */ ReferenceAdapter; },\n/* harmony export */   RenameAdapter: function() { return /* binding */ RenameAdapter; },\n/* harmony export */   SelectionRangeAdapter: function() { return /* binding */ SelectionRangeAdapter; },\n/* harmony export */   WorkerManager: function() { return /* binding */ WorkerManager; },\n/* harmony export */   fromPosition: function() { return /* binding */ fromPosition; },\n/* harmony export */   fromRange: function() { return /* binding */ fromRange; },\n/* harmony export */   setupMode: function() { return /* binding */ setupMode; },\n/* harmony export */   setupMode1: function() { return /* binding */ setupMode1; },\n/* harmony export */   toRange: function() { return /* binding */ toRange; },\n/* harmony export */   toTextEdit: function() { return /* binding */ toTextEdit; }\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/editor.api.js?ce9f\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/language/html/workerManager.ts\nvar STOP_WHEN_IDLE_FOR = 2 * 60 * 1e3;\nvar WorkerManager = class {\n  constructor(defaults) {\n    this._defaults = defaults;\n    this._worker = null;\n    this._client = null;\n    this._idleCheckInterval = window.setInterval(() => this._checkIfIdle(), 30 * 1e3);\n    this._lastUsedTime = 0;\n    this._configChangeListener = this._defaults.onDidChange(() => this._stopWorker());\n  }\n  _stopWorker() {\n    if (this._worker) {\n      this._worker.dispose();\n      this._worker = null;\n    }\n    this._client = null;\n  }\n  dispose() {\n    clearInterval(this._idleCheckInterval);\n    this._configChangeListener.dispose();\n    this._stopWorker();\n  }\n  _checkIfIdle() {\n    if (!this._worker) {\n      return;\n    }\n    let timePassedSinceLastUsed = Date.now() - this._lastUsedTime;\n    if (timePassedSinceLastUsed > STOP_WHEN_IDLE_FOR) {\n      this._stopWorker();\n    }\n  }\n  _getClient() {\n    this._lastUsedTime = Date.now();\n    if (!this._client) {\n      this._worker = monaco_editor_core_exports.editor.createWebWorker({\n        // module that exports the create() method and returns a `HTMLWorker` instance\n        moduleId: \"vs/language/html/htmlWorker\",\n        // passed in to the create() method\n        createData: {\n          languageSettings: this._defaults.options,\n          languageId: this._defaults.languageId\n        },\n        label: this._defaults.languageId\n      });\n      this._client = this._worker.getProxy();\n    }\n    return this._client;\n  }\n  getLanguageServiceWorker(...resources) {\n    let _client;\n    return this._getClient().then((client) => {\n      _client = client;\n    }).then((_) => {\n      if (this._worker) {\n        return this._worker.withSyncedResources(resources);\n      }\n    }).then((_) => _client);\n  }\n};\n\n// node_modules/vscode-languageserver-types/lib/esm/main.js\nvar DocumentUri;\n(function(DocumentUri2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  DocumentUri2.is = is;\n})(DocumentUri || (DocumentUri = {}));\nvar URI;\n(function(URI2) {\n  function is(value) {\n    return typeof value === \"string\";\n  }\n  URI2.is = is;\n})(URI || (URI = {}));\nvar integer;\n(function(integer2) {\n  integer2.MIN_VALUE = -2147483648;\n  integer2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && integer2.MIN_VALUE <= value && value <= integer2.MAX_VALUE;\n  }\n  integer2.is = is;\n})(integer || (integer = {}));\nvar uinteger;\n(function(uinteger2) {\n  uinteger2.MIN_VALUE = 0;\n  uinteger2.MAX_VALUE = 2147483647;\n  function is(value) {\n    return typeof value === \"number\" && uinteger2.MIN_VALUE <= value && value <= uinteger2.MAX_VALUE;\n  }\n  uinteger2.is = is;\n})(uinteger || (uinteger = {}));\nvar Position;\n(function(Position3) {\n  function create(line, character) {\n    if (line === Number.MAX_VALUE) {\n      line = uinteger.MAX_VALUE;\n    }\n    if (character === Number.MAX_VALUE) {\n      character = uinteger.MAX_VALUE;\n    }\n    return { line, character };\n  }\n  Position3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n  }\n  Position3.is = is;\n})(Position || (Position = {}));\nvar Range;\n(function(Range3) {\n  function create(one, two, three, four) {\n    if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n      return { start: Position.create(one, two), end: Position.create(three, four) };\n    } else if (Position.is(one) && Position.is(two)) {\n      return { start: one, end: two };\n    } else {\n      throw new Error(`Range#create called with invalid arguments[${one}, ${two}, ${three}, ${four}]`);\n    }\n  }\n  Range3.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n  }\n  Range3.is = is;\n})(Range || (Range = {}));\nvar Location;\n(function(Location2) {\n  function create(uri, range) {\n    return { uri, range };\n  }\n  Location2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n  }\n  Location2.is = is;\n})(Location || (Location = {}));\nvar LocationLink;\n(function(LocationLink2) {\n  function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n    return { targetUri, targetRange, targetSelectionRange, originSelectionRange };\n  }\n  LocationLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri) && Range.is(candidate.targetSelectionRange) && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n  }\n  LocationLink2.is = is;\n})(LocationLink || (LocationLink = {}));\nvar Color;\n(function(Color2) {\n  function create(red, green, blue, alpha) {\n    return {\n      red,\n      green,\n      blue,\n      alpha\n    };\n  }\n  Color2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.numberRange(candidate.red, 0, 1) && Is.numberRange(candidate.green, 0, 1) && Is.numberRange(candidate.blue, 0, 1) && Is.numberRange(candidate.alpha, 0, 1);\n  }\n  Color2.is = is;\n})(Color || (Color = {}));\nvar ColorInformation;\n(function(ColorInformation2) {\n  function create(range, color) {\n    return {\n      range,\n      color\n    };\n  }\n  ColorInformation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && Color.is(candidate.color);\n  }\n  ColorInformation2.is = is;\n})(ColorInformation || (ColorInformation = {}));\nvar ColorPresentation;\n(function(ColorPresentation2) {\n  function create(label, textEdit, additionalTextEdits) {\n    return {\n      label,\n      textEdit,\n      additionalTextEdits\n    };\n  }\n  ColorPresentation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate)) && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n  }\n  ColorPresentation2.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\nvar FoldingRangeKind;\n(function(FoldingRangeKind2) {\n  FoldingRangeKind2.Comment = \"comment\";\n  FoldingRangeKind2.Imports = \"imports\";\n  FoldingRangeKind2.Region = \"region\";\n})(FoldingRangeKind || (FoldingRangeKind = {}));\nvar FoldingRange;\n(function(FoldingRange2) {\n  function create(startLine, endLine, startCharacter, endCharacter, kind, collapsedText) {\n    const result = {\n      startLine,\n      endLine\n    };\n    if (Is.defined(startCharacter)) {\n      result.startCharacter = startCharacter;\n    }\n    if (Is.defined(endCharacter)) {\n      result.endCharacter = endCharacter;\n    }\n    if (Is.defined(kind)) {\n      result.kind = kind;\n    }\n    if (Is.defined(collapsedText)) {\n      result.collapsedText = collapsedText;\n    }\n    return result;\n  }\n  FoldingRange2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine) && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter)) && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter)) && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n  }\n  FoldingRange2.is = is;\n})(FoldingRange || (FoldingRange = {}));\nvar DiagnosticRelatedInformation;\n(function(DiagnosticRelatedInformation2) {\n  function create(location, message) {\n    return {\n      location,\n      message\n    };\n  }\n  DiagnosticRelatedInformation2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n  }\n  DiagnosticRelatedInformation2.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\nvar DiagnosticSeverity;\n(function(DiagnosticSeverity2) {\n  DiagnosticSeverity2.Error = 1;\n  DiagnosticSeverity2.Warning = 2;\n  DiagnosticSeverity2.Information = 3;\n  DiagnosticSeverity2.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\nvar DiagnosticTag;\n(function(DiagnosticTag2) {\n  DiagnosticTag2.Unnecessary = 1;\n  DiagnosticTag2.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\nvar CodeDescription;\n(function(CodeDescription2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.href);\n  }\n  CodeDescription2.is = is;\n})(CodeDescription || (CodeDescription = {}));\nvar Diagnostic;\n(function(Diagnostic2) {\n  function create(range, message, severity, code, source, relatedInformation) {\n    let result = { range, message };\n    if (Is.defined(severity)) {\n      result.severity = severity;\n    }\n    if (Is.defined(code)) {\n      result.code = code;\n    }\n    if (Is.defined(source)) {\n      result.source = source;\n    }\n    if (Is.defined(relatedInformation)) {\n      result.relatedInformation = relatedInformation;\n    }\n    return result;\n  }\n  Diagnostic2.create = create;\n  function is(value) {\n    var _a;\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && Is.string(candidate.message) && (Is.number(candidate.severity) || Is.undefined(candidate.severity)) && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code)) && (Is.undefined(candidate.codeDescription) || Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)) && (Is.string(candidate.source) || Is.undefined(candidate.source)) && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n  }\n  Diagnostic2.is = is;\n})(Diagnostic || (Diagnostic = {}));\nvar Command;\n(function(Command2) {\n  function create(title, command, ...args) {\n    let result = { title, command };\n    if (Is.defined(args) && args.length > 0) {\n      result.arguments = args;\n    }\n    return result;\n  }\n  Command2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n  }\n  Command2.is = is;\n})(Command || (Command = {}));\nvar TextEdit;\n(function(TextEdit2) {\n  function replace(range, newText) {\n    return { range, newText };\n  }\n  TextEdit2.replace = replace;\n  function insert(position, newText) {\n    return { range: { start: position, end: position }, newText };\n  }\n  TextEdit2.insert = insert;\n  function del(range) {\n    return { range, newText: \"\" };\n  }\n  TextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.newText) && Range.is(candidate.range);\n  }\n  TextEdit2.is = is;\n})(TextEdit || (TextEdit = {}));\nvar ChangeAnnotation;\n(function(ChangeAnnotation2) {\n  function create(label, needsConfirmation, description) {\n    const result = { label };\n    if (needsConfirmation !== void 0) {\n      result.needsConfirmation = needsConfirmation;\n    }\n    if (description !== void 0) {\n      result.description = description;\n    }\n    return result;\n  }\n  ChangeAnnotation2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Is.string(candidate.label) && (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  ChangeAnnotation2.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nvar ChangeAnnotationIdentifier;\n(function(ChangeAnnotationIdentifier2) {\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate);\n  }\n  ChangeAnnotationIdentifier2.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nvar AnnotatedTextEdit;\n(function(AnnotatedTextEdit2) {\n  function replace(range, newText, annotation) {\n    return { range, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.replace = replace;\n  function insert(position, newText, annotation) {\n    return { range: { start: position, end: position }, newText, annotationId: annotation };\n  }\n  AnnotatedTextEdit2.insert = insert;\n  function del(range, annotation) {\n    return { range, newText: \"\", annotationId: annotation };\n  }\n  AnnotatedTextEdit2.del = del;\n  function is(value) {\n    const candidate = value;\n    return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  AnnotatedTextEdit2.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\nvar TextDocumentEdit;\n(function(TextDocumentEdit2) {\n  function create(textDocument, edits) {\n    return { textDocument, edits };\n  }\n  TextDocumentEdit2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument) && Array.isArray(candidate.edits);\n  }\n  TextDocumentEdit2.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nvar CreateFile;\n(function(CreateFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"create\",\n      uri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  CreateFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"create\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  CreateFile2.is = is;\n})(CreateFile || (CreateFile = {}));\nvar RenameFile;\n(function(RenameFile2) {\n  function create(oldUri, newUri, options, annotation) {\n    let result = {\n      kind: \"rename\",\n      oldUri,\n      newUri\n    };\n    if (options !== void 0 && (options.overwrite !== void 0 || options.ignoreIfExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  RenameFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"rename\" && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === void 0 || (candidate.options.overwrite === void 0 || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === void 0 || Is.boolean(candidate.options.ignoreIfExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  RenameFile2.is = is;\n})(RenameFile || (RenameFile = {}));\nvar DeleteFile;\n(function(DeleteFile2) {\n  function create(uri, options, annotation) {\n    let result = {\n      kind: \"delete\",\n      uri\n    };\n    if (options !== void 0 && (options.recursive !== void 0 || options.ignoreIfNotExists !== void 0)) {\n      result.options = options;\n    }\n    if (annotation !== void 0) {\n      result.annotationId = annotation;\n    }\n    return result;\n  }\n  DeleteFile2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && candidate.kind === \"delete\" && Is.string(candidate.uri) && (candidate.options === void 0 || (candidate.options.recursive === void 0 || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === void 0 || Is.boolean(candidate.options.ignoreIfNotExists))) && (candidate.annotationId === void 0 || ChangeAnnotationIdentifier.is(candidate.annotationId));\n  }\n  DeleteFile2.is = is;\n})(DeleteFile || (DeleteFile = {}));\nvar WorkspaceEdit;\n(function(WorkspaceEdit2) {\n  function is(value) {\n    let candidate = value;\n    return candidate && (candidate.changes !== void 0 || candidate.documentChanges !== void 0) && (candidate.documentChanges === void 0 || candidate.documentChanges.every((change) => {\n      if (Is.string(change.kind)) {\n        return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n      } else {\n        return TextDocumentEdit.is(change);\n      }\n    }));\n  }\n  WorkspaceEdit2.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nvar TextDocumentIdentifier;\n(function(TextDocumentIdentifier2) {\n  function create(uri) {\n    return { uri };\n  }\n  TextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri);\n  }\n  TextDocumentIdentifier2.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\nvar VersionedTextDocumentIdentifier;\n(function(VersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  VersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n  }\n  VersionedTextDocumentIdentifier2.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\nvar OptionalVersionedTextDocumentIdentifier;\n(function(OptionalVersionedTextDocumentIdentifier2) {\n  function create(uri, version) {\n    return { uri, version };\n  }\n  OptionalVersionedTextDocumentIdentifier2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n  }\n  OptionalVersionedTextDocumentIdentifier2.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\nvar TextDocumentItem;\n(function(TextDocumentItem2) {\n  function create(uri, languageId, version, text) {\n    return { uri, languageId, version, text };\n  }\n  TextDocumentItem2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n  }\n  TextDocumentItem2.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\nvar MarkupKind;\n(function(MarkupKind2) {\n  MarkupKind2.PlainText = \"plaintext\";\n  MarkupKind2.Markdown = \"markdown\";\n  function is(value) {\n    const candidate = value;\n    return candidate === MarkupKind2.PlainText || candidate === MarkupKind2.Markdown;\n  }\n  MarkupKind2.is = is;\n})(MarkupKind || (MarkupKind = {}));\nvar MarkupContent;\n(function(MarkupContent2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n  }\n  MarkupContent2.is = is;\n})(MarkupContent || (MarkupContent = {}));\nvar CompletionItemKind;\n(function(CompletionItemKind2) {\n  CompletionItemKind2.Text = 1;\n  CompletionItemKind2.Method = 2;\n  CompletionItemKind2.Function = 3;\n  CompletionItemKind2.Constructor = 4;\n  CompletionItemKind2.Field = 5;\n  CompletionItemKind2.Variable = 6;\n  CompletionItemKind2.Class = 7;\n  CompletionItemKind2.Interface = 8;\n  CompletionItemKind2.Module = 9;\n  CompletionItemKind2.Property = 10;\n  CompletionItemKind2.Unit = 11;\n  CompletionItemKind2.Value = 12;\n  CompletionItemKind2.Enum = 13;\n  CompletionItemKind2.Keyword = 14;\n  CompletionItemKind2.Snippet = 15;\n  CompletionItemKind2.Color = 16;\n  CompletionItemKind2.File = 17;\n  CompletionItemKind2.Reference = 18;\n  CompletionItemKind2.Folder = 19;\n  CompletionItemKind2.EnumMember = 20;\n  CompletionItemKind2.Constant = 21;\n  CompletionItemKind2.Struct = 22;\n  CompletionItemKind2.Event = 23;\n  CompletionItemKind2.Operator = 24;\n  CompletionItemKind2.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\nvar InsertTextFormat;\n(function(InsertTextFormat2) {\n  InsertTextFormat2.PlainText = 1;\n  InsertTextFormat2.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\nvar CompletionItemTag;\n(function(CompletionItemTag2) {\n  CompletionItemTag2.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\nvar InsertReplaceEdit;\n(function(InsertReplaceEdit2) {\n  function create(newText, insert, replace) {\n    return { newText, insert, replace };\n  }\n  InsertReplaceEdit2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n  }\n  InsertReplaceEdit2.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\nvar InsertTextMode;\n(function(InsertTextMode2) {\n  InsertTextMode2.asIs = 1;\n  InsertTextMode2.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\nvar CompletionItemLabelDetails;\n(function(CompletionItemLabelDetails2) {\n  function is(value) {\n    const candidate = value;\n    return candidate && (Is.string(candidate.detail) || candidate.detail === void 0) && (Is.string(candidate.description) || candidate.description === void 0);\n  }\n  CompletionItemLabelDetails2.is = is;\n})(CompletionItemLabelDetails || (CompletionItemLabelDetails = {}));\nvar CompletionItem;\n(function(CompletionItem2) {\n  function create(label) {\n    return { label };\n  }\n  CompletionItem2.create = create;\n})(CompletionItem || (CompletionItem = {}));\nvar CompletionList;\n(function(CompletionList2) {\n  function create(items, isIncomplete) {\n    return { items: items ? items : [], isIncomplete: !!isIncomplete };\n  }\n  CompletionList2.create = create;\n})(CompletionList || (CompletionList = {}));\nvar MarkedString;\n(function(MarkedString2) {\n  function fromPlainText(plainText) {\n    return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\");\n  }\n  MarkedString2.fromPlainText = fromPlainText;\n  function is(value) {\n    const candidate = value;\n    return Is.string(candidate) || Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value);\n  }\n  MarkedString2.is = is;\n})(MarkedString || (MarkedString = {}));\nvar Hover;\n(function(Hover2) {\n  function is(value) {\n    let candidate = value;\n    return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) || MarkedString.is(candidate.contents) || Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === void 0 || Range.is(value.range));\n  }\n  Hover2.is = is;\n})(Hover || (Hover = {}));\nvar ParameterInformation;\n(function(ParameterInformation2) {\n  function create(label, documentation) {\n    return documentation ? { label, documentation } : { label };\n  }\n  ParameterInformation2.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\nvar SignatureInformation;\n(function(SignatureInformation2) {\n  function create(label, documentation, ...parameters) {\n    let result = { label };\n    if (Is.defined(documentation)) {\n      result.documentation = documentation;\n    }\n    if (Is.defined(parameters)) {\n      result.parameters = parameters;\n    } else {\n      result.parameters = [];\n    }\n    return result;\n  }\n  SignatureInformation2.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\nvar DocumentHighlightKind;\n(function(DocumentHighlightKind2) {\n  DocumentHighlightKind2.Text = 1;\n  DocumentHighlightKind2.Read = 2;\n  DocumentHighlightKind2.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\nvar DocumentHighlight;\n(function(DocumentHighlight2) {\n  function create(range, kind) {\n    let result = { range };\n    if (Is.number(kind)) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  DocumentHighlight2.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\nvar SymbolKind;\n(function(SymbolKind2) {\n  SymbolKind2.File = 1;\n  SymbolKind2.Module = 2;\n  SymbolKind2.Namespace = 3;\n  SymbolKind2.Package = 4;\n  SymbolKind2.Class = 5;\n  SymbolKind2.Method = 6;\n  SymbolKind2.Property = 7;\n  SymbolKind2.Field = 8;\n  SymbolKind2.Constructor = 9;\n  SymbolKind2.Enum = 10;\n  SymbolKind2.Interface = 11;\n  SymbolKind2.Function = 12;\n  SymbolKind2.Variable = 13;\n  SymbolKind2.Constant = 14;\n  SymbolKind2.String = 15;\n  SymbolKind2.Number = 16;\n  SymbolKind2.Boolean = 17;\n  SymbolKind2.Array = 18;\n  SymbolKind2.Object = 19;\n  SymbolKind2.Key = 20;\n  SymbolKind2.Null = 21;\n  SymbolKind2.EnumMember = 22;\n  SymbolKind2.Struct = 23;\n  SymbolKind2.Event = 24;\n  SymbolKind2.Operator = 25;\n  SymbolKind2.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\nvar SymbolTag;\n(function(SymbolTag2) {\n  SymbolTag2.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nvar SymbolInformation;\n(function(SymbolInformation2) {\n  function create(name, kind, range, uri, containerName) {\n    let result = {\n      name,\n      kind,\n      location: { uri, range }\n    };\n    if (containerName) {\n      result.containerName = containerName;\n    }\n    return result;\n  }\n  SymbolInformation2.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nvar WorkspaceSymbol;\n(function(WorkspaceSymbol2) {\n  function create(name, kind, uri, range) {\n    return range !== void 0 ? { name, kind, location: { uri, range } } : { name, kind, location: { uri } };\n  }\n  WorkspaceSymbol2.create = create;\n})(WorkspaceSymbol || (WorkspaceSymbol = {}));\nvar DocumentSymbol;\n(function(DocumentSymbol2) {\n  function create(name, detail, kind, range, selectionRange, children) {\n    let result = {\n      name,\n      detail,\n      kind,\n      range,\n      selectionRange\n    };\n    if (children !== void 0) {\n      result.children = children;\n    }\n    return result;\n  }\n  DocumentSymbol2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.name) && Is.number(candidate.kind) && Range.is(candidate.range) && Range.is(candidate.selectionRange) && (candidate.detail === void 0 || Is.string(candidate.detail)) && (candidate.deprecated === void 0 || Is.boolean(candidate.deprecated)) && (candidate.children === void 0 || Array.isArray(candidate.children)) && (candidate.tags === void 0 || Array.isArray(candidate.tags));\n  }\n  DocumentSymbol2.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\nvar CodeActionKind;\n(function(CodeActionKind2) {\n  CodeActionKind2.Empty = \"\";\n  CodeActionKind2.QuickFix = \"quickfix\";\n  CodeActionKind2.Refactor = \"refactor\";\n  CodeActionKind2.RefactorExtract = \"refactor.extract\";\n  CodeActionKind2.RefactorInline = \"refactor.inline\";\n  CodeActionKind2.RefactorRewrite = \"refactor.rewrite\";\n  CodeActionKind2.Source = \"source\";\n  CodeActionKind2.SourceOrganizeImports = \"source.organizeImports\";\n  CodeActionKind2.SourceFixAll = \"source.fixAll\";\n})(CodeActionKind || (CodeActionKind = {}));\nvar CodeActionTriggerKind;\n(function(CodeActionTriggerKind2) {\n  CodeActionTriggerKind2.Invoked = 1;\n  CodeActionTriggerKind2.Automatic = 2;\n})(CodeActionTriggerKind || (CodeActionTriggerKind = {}));\nvar CodeActionContext;\n(function(CodeActionContext2) {\n  function create(diagnostics, only, triggerKind) {\n    let result = { diagnostics };\n    if (only !== void 0 && only !== null) {\n      result.only = only;\n    }\n    if (triggerKind !== void 0 && triggerKind !== null) {\n      result.triggerKind = triggerKind;\n    }\n    return result;\n  }\n  CodeActionContext2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is) && (candidate.only === void 0 || Is.typedArray(candidate.only, Is.string)) && (candidate.triggerKind === void 0 || candidate.triggerKind === CodeActionTriggerKind.Invoked || candidate.triggerKind === CodeActionTriggerKind.Automatic);\n  }\n  CodeActionContext2.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nvar CodeAction;\n(function(CodeAction2) {\n  function create(title, kindOrCommandOrEdit, kind) {\n    let result = { title };\n    let checkKind = true;\n    if (typeof kindOrCommandOrEdit === \"string\") {\n      checkKind = false;\n      result.kind = kindOrCommandOrEdit;\n    } else if (Command.is(kindOrCommandOrEdit)) {\n      result.command = kindOrCommandOrEdit;\n    } else {\n      result.edit = kindOrCommandOrEdit;\n    }\n    if (checkKind && kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  CodeAction2.create = create;\n  function is(value) {\n    let candidate = value;\n    return candidate && Is.string(candidate.title) && (candidate.diagnostics === void 0 || Is.typedArray(candidate.diagnostics, Diagnostic.is)) && (candidate.kind === void 0 || Is.string(candidate.kind)) && (candidate.edit !== void 0 || candidate.command !== void 0) && (candidate.command === void 0 || Command.is(candidate.command)) && (candidate.isPreferred === void 0 || Is.boolean(candidate.isPreferred)) && (candidate.edit === void 0 || WorkspaceEdit.is(candidate.edit));\n  }\n  CodeAction2.is = is;\n})(CodeAction || (CodeAction = {}));\nvar CodeLens;\n(function(CodeLens2) {\n  function create(range, data) {\n    let result = { range };\n    if (Is.defined(data)) {\n      result.data = data;\n    }\n    return result;\n  }\n  CodeLens2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n  }\n  CodeLens2.is = is;\n})(CodeLens || (CodeLens = {}));\nvar FormattingOptions;\n(function(FormattingOptions2) {\n  function create(tabSize, insertSpaces) {\n    return { tabSize, insertSpaces };\n  }\n  FormattingOptions2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n  }\n  FormattingOptions2.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\nvar DocumentLink;\n(function(DocumentLink2) {\n  function create(range, target, data) {\n    return { range, target, data };\n  }\n  DocumentLink2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n  }\n  DocumentLink2.is = is;\n})(DocumentLink || (DocumentLink = {}));\nvar SelectionRange;\n(function(SelectionRange2) {\n  function create(range, parent) {\n    return { range, parent };\n  }\n  SelectionRange2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.objectLiteral(candidate) && Range.is(candidate.range) && (candidate.parent === void 0 || SelectionRange2.is(candidate.parent));\n  }\n  SelectionRange2.is = is;\n})(SelectionRange || (SelectionRange = {}));\nvar SemanticTokenTypes;\n(function(SemanticTokenTypes2) {\n  SemanticTokenTypes2[\"namespace\"] = \"namespace\";\n  SemanticTokenTypes2[\"type\"] = \"type\";\n  SemanticTokenTypes2[\"class\"] = \"class\";\n  SemanticTokenTypes2[\"enum\"] = \"enum\";\n  SemanticTokenTypes2[\"interface\"] = \"interface\";\n  SemanticTokenTypes2[\"struct\"] = \"struct\";\n  SemanticTokenTypes2[\"typeParameter\"] = \"typeParameter\";\n  SemanticTokenTypes2[\"parameter\"] = \"parameter\";\n  SemanticTokenTypes2[\"variable\"] = \"variable\";\n  SemanticTokenTypes2[\"property\"] = \"property\";\n  SemanticTokenTypes2[\"enumMember\"] = \"enumMember\";\n  SemanticTokenTypes2[\"event\"] = \"event\";\n  SemanticTokenTypes2[\"function\"] = \"function\";\n  SemanticTokenTypes2[\"method\"] = \"method\";\n  SemanticTokenTypes2[\"macro\"] = \"macro\";\n  SemanticTokenTypes2[\"keyword\"] = \"keyword\";\n  SemanticTokenTypes2[\"modifier\"] = \"modifier\";\n  SemanticTokenTypes2[\"comment\"] = \"comment\";\n  SemanticTokenTypes2[\"string\"] = \"string\";\n  SemanticTokenTypes2[\"number\"] = \"number\";\n  SemanticTokenTypes2[\"regexp\"] = \"regexp\";\n  SemanticTokenTypes2[\"operator\"] = \"operator\";\n  SemanticTokenTypes2[\"decorator\"] = \"decorator\";\n})(SemanticTokenTypes || (SemanticTokenTypes = {}));\nvar SemanticTokenModifiers;\n(function(SemanticTokenModifiers2) {\n  SemanticTokenModifiers2[\"declaration\"] = \"declaration\";\n  SemanticTokenModifiers2[\"definition\"] = \"definition\";\n  SemanticTokenModifiers2[\"readonly\"] = \"readonly\";\n  SemanticTokenModifiers2[\"static\"] = \"static\";\n  SemanticTokenModifiers2[\"deprecated\"] = \"deprecated\";\n  SemanticTokenModifiers2[\"abstract\"] = \"abstract\";\n  SemanticTokenModifiers2[\"async\"] = \"async\";\n  SemanticTokenModifiers2[\"modification\"] = \"modification\";\n  SemanticTokenModifiers2[\"documentation\"] = \"documentation\";\n  SemanticTokenModifiers2[\"defaultLibrary\"] = \"defaultLibrary\";\n})(SemanticTokenModifiers || (SemanticTokenModifiers = {}));\nvar SemanticTokens;\n(function(SemanticTokens2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.resultId === void 0 || typeof candidate.resultId === \"string\") && Array.isArray(candidate.data) && (candidate.data.length === 0 || typeof candidate.data[0] === \"number\");\n  }\n  SemanticTokens2.is = is;\n})(SemanticTokens || (SemanticTokens = {}));\nvar InlineValueText;\n(function(InlineValueText2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  InlineValueText2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.string(candidate.text);\n  }\n  InlineValueText2.is = is;\n})(InlineValueText || (InlineValueText = {}));\nvar InlineValueVariableLookup;\n(function(InlineValueVariableLookup2) {\n  function create(range, variableName, caseSensitiveLookup) {\n    return { range, variableName, caseSensitiveLookup };\n  }\n  InlineValueVariableLookup2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && Is.boolean(candidate.caseSensitiveLookup) && (Is.string(candidate.variableName) || candidate.variableName === void 0);\n  }\n  InlineValueVariableLookup2.is = is;\n})(InlineValueVariableLookup || (InlineValueVariableLookup = {}));\nvar InlineValueEvaluatableExpression;\n(function(InlineValueEvaluatableExpression2) {\n  function create(range, expression) {\n    return { range, expression };\n  }\n  InlineValueEvaluatableExpression2.create = create;\n  function is(value) {\n    const candidate = value;\n    return candidate !== void 0 && candidate !== null && Range.is(candidate.range) && (Is.string(candidate.expression) || candidate.expression === void 0);\n  }\n  InlineValueEvaluatableExpression2.is = is;\n})(InlineValueEvaluatableExpression || (InlineValueEvaluatableExpression = {}));\nvar InlineValueContext;\n(function(InlineValueContext2) {\n  function create(frameId, stoppedLocation) {\n    return { frameId, stoppedLocation };\n  }\n  InlineValueContext2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.defined(candidate) && Range.is(value.stoppedLocation);\n  }\n  InlineValueContext2.is = is;\n})(InlineValueContext || (InlineValueContext = {}));\nvar InlayHintKind;\n(function(InlayHintKind2) {\n  InlayHintKind2.Type = 1;\n  InlayHintKind2.Parameter = 2;\n  function is(value) {\n    return value === 1 || value === 2;\n  }\n  InlayHintKind2.is = is;\n})(InlayHintKind || (InlayHintKind = {}));\nvar InlayHintLabelPart;\n(function(InlayHintLabelPart2) {\n  function create(value) {\n    return { value };\n  }\n  InlayHintLabelPart2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.location === void 0 || Location.is(candidate.location)) && (candidate.command === void 0 || Command.is(candidate.command));\n  }\n  InlayHintLabelPart2.is = is;\n})(InlayHintLabelPart || (InlayHintLabelPart = {}));\nvar InlayHint;\n(function(InlayHint2) {\n  function create(position, label, kind) {\n    const result = { position, label };\n    if (kind !== void 0) {\n      result.kind = kind;\n    }\n    return result;\n  }\n  InlayHint2.create = create;\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && Position.is(candidate.position) && (Is.string(candidate.label) || Is.typedArray(candidate.label, InlayHintLabelPart.is)) && (candidate.kind === void 0 || InlayHintKind.is(candidate.kind)) && candidate.textEdits === void 0 || Is.typedArray(candidate.textEdits, TextEdit.is) && (candidate.tooltip === void 0 || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip)) && (candidate.paddingLeft === void 0 || Is.boolean(candidate.paddingLeft)) && (candidate.paddingRight === void 0 || Is.boolean(candidate.paddingRight));\n  }\n  InlayHint2.is = is;\n})(InlayHint || (InlayHint = {}));\nvar StringValue;\n(function(StringValue2) {\n  function createSnippet(value) {\n    return { kind: \"snippet\", value };\n  }\n  StringValue2.createSnippet = createSnippet;\n})(StringValue || (StringValue = {}));\nvar InlineCompletionItem;\n(function(InlineCompletionItem2) {\n  function create(insertText, filterText, range, command) {\n    return { insertText, filterText, range, command };\n  }\n  InlineCompletionItem2.create = create;\n})(InlineCompletionItem || (InlineCompletionItem = {}));\nvar InlineCompletionList;\n(function(InlineCompletionList2) {\n  function create(items) {\n    return { items };\n  }\n  InlineCompletionList2.create = create;\n})(InlineCompletionList || (InlineCompletionList = {}));\nvar InlineCompletionTriggerKind;\n(function(InlineCompletionTriggerKind2) {\n  InlineCompletionTriggerKind2.Invoked = 0;\n  InlineCompletionTriggerKind2.Automatic = 1;\n})(InlineCompletionTriggerKind || (InlineCompletionTriggerKind = {}));\nvar SelectedCompletionInfo;\n(function(SelectedCompletionInfo2) {\n  function create(range, text) {\n    return { range, text };\n  }\n  SelectedCompletionInfo2.create = create;\n})(SelectedCompletionInfo || (SelectedCompletionInfo = {}));\nvar InlineCompletionContext;\n(function(InlineCompletionContext2) {\n  function create(triggerKind, selectedCompletionInfo) {\n    return { triggerKind, selectedCompletionInfo };\n  }\n  InlineCompletionContext2.create = create;\n})(InlineCompletionContext || (InlineCompletionContext = {}));\nvar WorkspaceFolder;\n(function(WorkspaceFolder2) {\n  function is(value) {\n    const candidate = value;\n    return Is.objectLiteral(candidate) && URI.is(candidate.uri) && Is.string(candidate.name);\n  }\n  WorkspaceFolder2.is = is;\n})(WorkspaceFolder || (WorkspaceFolder = {}));\nvar TextDocument;\n(function(TextDocument2) {\n  function create(uri, languageId, version, content) {\n    return new FullTextDocument(uri, languageId, version, content);\n  }\n  TextDocument2.create = create;\n  function is(value) {\n    let candidate = value;\n    return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount) && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n  }\n  TextDocument2.is = is;\n  function applyEdits(document, edits) {\n    let text = document.getText();\n    let sortedEdits = mergeSort(edits, (a, b) => {\n      let diff = a.range.start.line - b.range.start.line;\n      if (diff === 0) {\n        return a.range.start.character - b.range.start.character;\n      }\n      return diff;\n    });\n    let lastModifiedOffset = text.length;\n    for (let i = sortedEdits.length - 1; i >= 0; i--) {\n      let e = sortedEdits[i];\n      let startOffset = document.offsetAt(e.range.start);\n      let endOffset = document.offsetAt(e.range.end);\n      if (endOffset <= lastModifiedOffset) {\n        text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n      } else {\n        throw new Error(\"Overlapping edit\");\n      }\n      lastModifiedOffset = startOffset;\n    }\n    return text;\n  }\n  TextDocument2.applyEdits = applyEdits;\n  function mergeSort(data, compare) {\n    if (data.length <= 1) {\n      return data;\n    }\n    const p = data.length / 2 | 0;\n    const left = data.slice(0, p);\n    const right = data.slice(p);\n    mergeSort(left, compare);\n    mergeSort(right, compare);\n    let leftIdx = 0;\n    let rightIdx = 0;\n    let i = 0;\n    while (leftIdx < left.length && rightIdx < right.length) {\n      let ret = compare(left[leftIdx], right[rightIdx]);\n      if (ret <= 0) {\n        data[i++] = left[leftIdx++];\n      } else {\n        data[i++] = right[rightIdx++];\n      }\n    }\n    while (leftIdx < left.length) {\n      data[i++] = left[leftIdx++];\n    }\n    while (rightIdx < right.length) {\n      data[i++] = right[rightIdx++];\n    }\n    return data;\n  }\n})(TextDocument || (TextDocument = {}));\nvar FullTextDocument = class {\n  constructor(uri, languageId, version, content) {\n    this._uri = uri;\n    this._languageId = languageId;\n    this._version = version;\n    this._content = content;\n    this._lineOffsets = void 0;\n  }\n  get uri() {\n    return this._uri;\n  }\n  get languageId() {\n    return this._languageId;\n  }\n  get version() {\n    return this._version;\n  }\n  getText(range) {\n    if (range) {\n      let start = this.offsetAt(range.start);\n      let end = this.offsetAt(range.end);\n      return this._content.substring(start, end);\n    }\n    return this._content;\n  }\n  update(event, version) {\n    this._content = event.text;\n    this._version = version;\n    this._lineOffsets = void 0;\n  }\n  getLineOffsets() {\n    if (this._lineOffsets === void 0) {\n      let lineOffsets = [];\n      let text = this._content;\n      let isLineStart = true;\n      for (let i = 0; i < text.length; i++) {\n        if (isLineStart) {\n          lineOffsets.push(i);\n          isLineStart = false;\n        }\n        let ch = text.charAt(i);\n        isLineStart = ch === \"\\r\" || ch === \"\\n\";\n        if (ch === \"\\r\" && i + 1 < text.length && text.charAt(i + 1) === \"\\n\") {\n          i++;\n        }\n      }\n      if (isLineStart && text.length > 0) {\n        lineOffsets.push(text.length);\n      }\n      this._lineOffsets = lineOffsets;\n    }\n    return this._lineOffsets;\n  }\n  positionAt(offset) {\n    offset = Math.max(Math.min(offset, this._content.length), 0);\n    let lineOffsets = this.getLineOffsets();\n    let low = 0, high = lineOffsets.length;\n    if (high === 0) {\n      return Position.create(0, offset);\n    }\n    while (low < high) {\n      let mid = Math.floor((low + high) / 2);\n      if (lineOffsets[mid] > offset) {\n        high = mid;\n      } else {\n        low = mid + 1;\n      }\n    }\n    let line = low - 1;\n    return Position.create(line, offset - lineOffsets[line]);\n  }\n  offsetAt(position) {\n    let lineOffsets = this.getLineOffsets();\n    if (position.line >= lineOffsets.length) {\n      return this._content.length;\n    } else if (position.line < 0) {\n      return 0;\n    }\n    let lineOffset = lineOffsets[position.line];\n    let nextLineOffset = position.line + 1 < lineOffsets.length ? lineOffsets[position.line + 1] : this._content.length;\n    return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n  }\n  get lineCount() {\n    return this.getLineOffsets().length;\n  }\n};\nvar Is;\n(function(Is2) {\n  const toString = Object.prototype.toString;\n  function defined(value) {\n    return typeof value !== \"undefined\";\n  }\n  Is2.defined = defined;\n  function undefined2(value) {\n    return typeof value === \"undefined\";\n  }\n  Is2.undefined = undefined2;\n  function boolean(value) {\n    return value === true || value === false;\n  }\n  Is2.boolean = boolean;\n  function string(value) {\n    return toString.call(value) === \"[object String]\";\n  }\n  Is2.string = string;\n  function number(value) {\n    return toString.call(value) === \"[object Number]\";\n  }\n  Is2.number = number;\n  function numberRange(value, min, max) {\n    return toString.call(value) === \"[object Number]\" && min <= value && value <= max;\n  }\n  Is2.numberRange = numberRange;\n  function integer2(value) {\n    return toString.call(value) === \"[object Number]\" && -2147483648 <= value && value <= 2147483647;\n  }\n  Is2.integer = integer2;\n  function uinteger2(value) {\n    return toString.call(value) === \"[object Number]\" && 0 <= value && value <= 2147483647;\n  }\n  Is2.uinteger = uinteger2;\n  function func(value) {\n    return toString.call(value) === \"[object Function]\";\n  }\n  Is2.func = func;\n  function objectLiteral(value) {\n    return value !== null && typeof value === \"object\";\n  }\n  Is2.objectLiteral = objectLiteral;\n  function typedArray(value, check) {\n    return Array.isArray(value) && value.every(check);\n  }\n  Is2.typedArray = typedArray;\n})(Is || (Is = {}));\n\n// src/language/common/lspLanguageFeatures.ts\nvar DiagnosticsAdapter = class {\n  constructor(_languageId, _worker, configChangeEvent) {\n    this._languageId = _languageId;\n    this._worker = _worker;\n    this._disposables = [];\n    this._listener = /* @__PURE__ */ Object.create(null);\n    const onModelAdd = (model) => {\n      let modeId = model.getLanguageId();\n      if (modeId !== this._languageId) {\n        return;\n      }\n      let handle;\n      this._listener[model.uri.toString()] = model.onDidChangeContent(() => {\n        window.clearTimeout(handle);\n        handle = window.setTimeout(() => this._doValidate(model.uri, modeId), 500);\n      });\n      this._doValidate(model.uri, modeId);\n    };\n    const onModelRemoved = (model) => {\n      monaco_editor_core_exports.editor.setModelMarkers(model, this._languageId, []);\n      let uriStr = model.uri.toString();\n      let listener = this._listener[uriStr];\n      if (listener) {\n        listener.dispose();\n        delete this._listener[uriStr];\n      }\n    };\n    this._disposables.push(monaco_editor_core_exports.editor.onDidCreateModel(onModelAdd));\n    this._disposables.push(monaco_editor_core_exports.editor.onWillDisposeModel(onModelRemoved));\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        onModelRemoved(event.model);\n        onModelAdd(event.model);\n      })\n    );\n    this._disposables.push(\n      configChangeEvent((_) => {\n        monaco_editor_core_exports.editor.getModels().forEach((model) => {\n          if (model.getLanguageId() === this._languageId) {\n            onModelRemoved(model);\n            onModelAdd(model);\n          }\n        });\n      })\n    );\n    this._disposables.push({\n      dispose: () => {\n        monaco_editor_core_exports.editor.getModels().forEach(onModelRemoved);\n        for (let key in this._listener) {\n          this._listener[key].dispose();\n        }\n      }\n    });\n    monaco_editor_core_exports.editor.getModels().forEach(onModelAdd);\n  }\n  dispose() {\n    this._disposables.forEach((d) => d && d.dispose());\n    this._disposables.length = 0;\n  }\n  _doValidate(resource, languageId) {\n    this._worker(resource).then((worker) => {\n      return worker.doValidation(resource.toString());\n    }).then((diagnostics) => {\n      const markers = diagnostics.map((d) => toDiagnostics(resource, d));\n      let model = monaco_editor_core_exports.editor.getModel(resource);\n      if (model && model.getLanguageId() === languageId) {\n        monaco_editor_core_exports.editor.setModelMarkers(model, languageId, markers);\n      }\n    }).then(void 0, (err) => {\n      console.error(err);\n    });\n  }\n};\nfunction toSeverity(lsSeverity) {\n  switch (lsSeverity) {\n    case DiagnosticSeverity.Error:\n      return monaco_editor_core_exports.MarkerSeverity.Error;\n    case DiagnosticSeverity.Warning:\n      return monaco_editor_core_exports.MarkerSeverity.Warning;\n    case DiagnosticSeverity.Information:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n    case DiagnosticSeverity.Hint:\n      return monaco_editor_core_exports.MarkerSeverity.Hint;\n    default:\n      return monaco_editor_core_exports.MarkerSeverity.Info;\n  }\n}\nfunction toDiagnostics(resource, diag) {\n  let code = typeof diag.code === \"number\" ? String(diag.code) : diag.code;\n  return {\n    severity: toSeverity(diag.severity),\n    startLineNumber: diag.range.start.line + 1,\n    startColumn: diag.range.start.character + 1,\n    endLineNumber: diag.range.end.line + 1,\n    endColumn: diag.range.end.character + 1,\n    message: diag.message,\n    code,\n    source: diag.source\n  };\n}\nvar CompletionAdapter = class {\n  constructor(_worker, _triggerCharacters) {\n    this._worker = _worker;\n    this._triggerCharacters = _triggerCharacters;\n  }\n  get triggerCharacters() {\n    return this._triggerCharacters;\n  }\n  provideCompletionItems(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.doComplete(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      const wordInfo = model.getWordUntilPosition(position);\n      const wordRange = new monaco_editor_core_exports.Range(\n        position.lineNumber,\n        wordInfo.startColumn,\n        position.lineNumber,\n        wordInfo.endColumn\n      );\n      const items = info.items.map((entry) => {\n        const item = {\n          label: entry.label,\n          insertText: entry.insertText || entry.label,\n          sortText: entry.sortText,\n          filterText: entry.filterText,\n          documentation: entry.documentation,\n          detail: entry.detail,\n          command: toCommand(entry.command),\n          range: wordRange,\n          kind: toCompletionItemKind(entry.kind)\n        };\n        if (entry.textEdit) {\n          if (isInsertReplaceEdit(entry.textEdit)) {\n            item.range = {\n              insert: toRange(entry.textEdit.insert),\n              replace: toRange(entry.textEdit.replace)\n            };\n          } else {\n            item.range = toRange(entry.textEdit.range);\n          }\n          item.insertText = entry.textEdit.newText;\n        }\n        if (entry.additionalTextEdits) {\n          item.additionalTextEdits = entry.additionalTextEdits.map(toTextEdit);\n        }\n        if (entry.insertTextFormat === InsertTextFormat.Snippet) {\n          item.insertTextRules = monaco_editor_core_exports.languages.CompletionItemInsertTextRule.InsertAsSnippet;\n        }\n        return item;\n      });\n      return {\n        isIncomplete: info.isIncomplete,\n        suggestions: items\n      };\n    });\n  }\n};\nfunction fromPosition(position) {\n  if (!position) {\n    return void 0;\n  }\n  return { character: position.column - 1, line: position.lineNumber - 1 };\n}\nfunction fromRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return {\n    start: {\n      line: range.startLineNumber - 1,\n      character: range.startColumn - 1\n    },\n    end: { line: range.endLineNumber - 1, character: range.endColumn - 1 }\n  };\n}\nfunction toRange(range) {\n  if (!range) {\n    return void 0;\n  }\n  return new monaco_editor_core_exports.Range(\n    range.start.line + 1,\n    range.start.character + 1,\n    range.end.line + 1,\n    range.end.character + 1\n  );\n}\nfunction isInsertReplaceEdit(edit) {\n  return typeof edit.insert !== \"undefined\" && typeof edit.replace !== \"undefined\";\n}\nfunction toCompletionItemKind(kind) {\n  const mItemKind = monaco_editor_core_exports.languages.CompletionItemKind;\n  switch (kind) {\n    case CompletionItemKind.Text:\n      return mItemKind.Text;\n    case CompletionItemKind.Method:\n      return mItemKind.Method;\n    case CompletionItemKind.Function:\n      return mItemKind.Function;\n    case CompletionItemKind.Constructor:\n      return mItemKind.Constructor;\n    case CompletionItemKind.Field:\n      return mItemKind.Field;\n    case CompletionItemKind.Variable:\n      return mItemKind.Variable;\n    case CompletionItemKind.Class:\n      return mItemKind.Class;\n    case CompletionItemKind.Interface:\n      return mItemKind.Interface;\n    case CompletionItemKind.Module:\n      return mItemKind.Module;\n    case CompletionItemKind.Property:\n      return mItemKind.Property;\n    case CompletionItemKind.Unit:\n      return mItemKind.Unit;\n    case CompletionItemKind.Value:\n      return mItemKind.Value;\n    case CompletionItemKind.Enum:\n      return mItemKind.Enum;\n    case CompletionItemKind.Keyword:\n      return mItemKind.Keyword;\n    case CompletionItemKind.Snippet:\n      return mItemKind.Snippet;\n    case CompletionItemKind.Color:\n      return mItemKind.Color;\n    case CompletionItemKind.File:\n      return mItemKind.File;\n    case CompletionItemKind.Reference:\n      return mItemKind.Reference;\n  }\n  return mItemKind.Property;\n}\nfunction toTextEdit(textEdit) {\n  if (!textEdit) {\n    return void 0;\n  }\n  return {\n    range: toRange(textEdit.range),\n    text: textEdit.newText\n  };\n}\nfunction toCommand(c) {\n  return c && c.command === \"editor.action.triggerSuggest\" ? { id: c.command, title: c.title, arguments: c.arguments } : void 0;\n}\nvar HoverAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideHover(model, position, token) {\n    let resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.doHover(resource.toString(), fromPosition(position));\n    }).then((info) => {\n      if (!info) {\n        return;\n      }\n      return {\n        range: toRange(info.range),\n        contents: toMarkedStringArray(info.contents)\n      };\n    });\n  }\n};\nfunction isMarkupContent(thing) {\n  return thing && typeof thing === \"object\" && typeof thing.kind === \"string\";\n}\nfunction toMarkdownString(entry) {\n  if (typeof entry === \"string\") {\n    return {\n      value: entry\n    };\n  }\n  if (isMarkupContent(entry)) {\n    if (entry.kind === \"plaintext\") {\n      return {\n        value: entry.value.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, \"\\\\$&\")\n      };\n    }\n    return {\n      value: entry.value\n    };\n  }\n  return { value: \"```\" + entry.language + \"\\n\" + entry.value + \"\\n```\\n\" };\n}\nfunction toMarkedStringArray(contents) {\n  if (!contents) {\n    return void 0;\n  }\n  if (Array.isArray(contents)) {\n    return contents.map(toMarkdownString);\n  }\n  return [toMarkdownString(contents)];\n}\nvar DocumentHighlightAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentHighlights(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentHighlights(resource.toString(), fromPosition(position))).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map((entry) => {\n        return {\n          range: toRange(entry.range),\n          kind: toDocumentHighlightKind(entry.kind)\n        };\n      });\n    });\n  }\n};\nfunction toDocumentHighlightKind(kind) {\n  switch (kind) {\n    case DocumentHighlightKind.Read:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Read;\n    case DocumentHighlightKind.Write:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Write;\n    case DocumentHighlightKind.Text:\n      return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n  }\n  return monaco_editor_core_exports.languages.DocumentHighlightKind.Text;\n}\nvar DefinitionAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDefinition(model, position, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.findDefinition(resource.toString(), fromPosition(position));\n    }).then((definition) => {\n      if (!definition) {\n        return;\n      }\n      return [toLocation(definition)];\n    });\n  }\n};\nfunction toLocation(location) {\n  return {\n    uri: monaco_editor_core_exports.Uri.parse(location.uri),\n    range: toRange(location.range)\n  };\n}\nvar ReferenceAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideReferences(model, position, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.findReferences(resource.toString(), fromPosition(position));\n    }).then((entries) => {\n      if (!entries) {\n        return;\n      }\n      return entries.map(toLocation);\n    });\n  }\n};\nvar RenameAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideRenameEdits(model, position, newName, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.doRename(resource.toString(), fromPosition(position), newName);\n    }).then((edit) => {\n      return toWorkspaceEdit(edit);\n    });\n  }\n};\nfunction toWorkspaceEdit(edit) {\n  if (!edit || !edit.changes) {\n    return void 0;\n  }\n  let resourceEdits = [];\n  for (let uri in edit.changes) {\n    const _uri = monaco_editor_core_exports.Uri.parse(uri);\n    for (let e of edit.changes[uri]) {\n      resourceEdits.push({\n        resource: _uri,\n        versionId: void 0,\n        textEdit: {\n          range: toRange(e.range),\n          text: e.newText\n        }\n      });\n    }\n  }\n  return {\n    edits: resourceEdits\n  };\n}\nvar DocumentSymbolAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentSymbols(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentSymbols(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return items.map((item) => {\n        if (isDocumentSymbol(item)) {\n          return toDocumentSymbol(item);\n        }\n        return {\n          name: item.name,\n          detail: \"\",\n          containerName: item.containerName,\n          kind: toSymbolKind(item.kind),\n          range: toRange(item.location.range),\n          selectionRange: toRange(item.location.range),\n          tags: []\n        };\n      });\n    });\n  }\n};\nfunction isDocumentSymbol(symbol) {\n  return \"children\" in symbol;\n}\nfunction toDocumentSymbol(symbol) {\n  return {\n    name: symbol.name,\n    detail: symbol.detail ?? \"\",\n    kind: toSymbolKind(symbol.kind),\n    range: toRange(symbol.range),\n    selectionRange: toRange(symbol.selectionRange),\n    tags: symbol.tags ?? [],\n    children: (symbol.children ?? []).map((item) => toDocumentSymbol(item))\n  };\n}\nfunction toSymbolKind(kind) {\n  let mKind = monaco_editor_core_exports.languages.SymbolKind;\n  switch (kind) {\n    case SymbolKind.File:\n      return mKind.File;\n    case SymbolKind.Module:\n      return mKind.Module;\n    case SymbolKind.Namespace:\n      return mKind.Namespace;\n    case SymbolKind.Package:\n      return mKind.Package;\n    case SymbolKind.Class:\n      return mKind.Class;\n    case SymbolKind.Method:\n      return mKind.Method;\n    case SymbolKind.Property:\n      return mKind.Property;\n    case SymbolKind.Field:\n      return mKind.Field;\n    case SymbolKind.Constructor:\n      return mKind.Constructor;\n    case SymbolKind.Enum:\n      return mKind.Enum;\n    case SymbolKind.Interface:\n      return mKind.Interface;\n    case SymbolKind.Function:\n      return mKind.Function;\n    case SymbolKind.Variable:\n      return mKind.Variable;\n    case SymbolKind.Constant:\n      return mKind.Constant;\n    case SymbolKind.String:\n      return mKind.String;\n    case SymbolKind.Number:\n      return mKind.Number;\n    case SymbolKind.Boolean:\n      return mKind.Boolean;\n    case SymbolKind.Array:\n      return mKind.Array;\n  }\n  return mKind.Function;\n}\nvar DocumentLinkAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideLinks(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentLinks(resource.toString())).then((items) => {\n      if (!items) {\n        return;\n      }\n      return {\n        links: items.map((item) => ({\n          range: toRange(item.range),\n          url: item.target\n        }))\n      };\n    });\n  }\n};\nvar DocumentFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentFormattingEdits(model, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.format(resource.toString(), null, fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nvar DocumentRangeFormattingEditProvider = class {\n  constructor(_worker) {\n    this._worker = _worker;\n    this.canFormatMultipleRanges = false;\n  }\n  provideDocumentRangeFormattingEdits(model, range, options, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => {\n      return worker.format(resource.toString(), fromRange(range), fromFormattingOptions(options)).then((edits) => {\n        if (!edits || edits.length === 0) {\n          return;\n        }\n        return edits.map(toTextEdit);\n      });\n    });\n  }\n};\nfunction fromFormattingOptions(options) {\n  return {\n    tabSize: options.tabSize,\n    insertSpaces: options.insertSpaces\n  };\n}\nvar DocumentColorAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideDocumentColors(model, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.findDocumentColors(resource.toString())).then((infos) => {\n      if (!infos) {\n        return;\n      }\n      return infos.map((item) => ({\n        color: item.color,\n        range: toRange(item.range)\n      }));\n    });\n  }\n  provideColorPresentations(model, info, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker) => worker.getColorPresentations(resource.toString(), info.color, fromRange(info.range))\n    ).then((presentations) => {\n      if (!presentations) {\n        return;\n      }\n      return presentations.map((presentation) => {\n        let item = {\n          label: presentation.label\n        };\n        if (presentation.textEdit) {\n          item.textEdit = toTextEdit(presentation.textEdit);\n        }\n        if (presentation.additionalTextEdits) {\n          item.additionalTextEdits = presentation.additionalTextEdits.map(toTextEdit);\n        }\n        return item;\n      });\n    });\n  }\n};\nvar FoldingRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideFoldingRanges(model, context, token) {\n    const resource = model.uri;\n    return this._worker(resource).then((worker) => worker.getFoldingRanges(resource.toString(), context)).then((ranges) => {\n      if (!ranges) {\n        return;\n      }\n      return ranges.map((range) => {\n        const result = {\n          start: range.startLine + 1,\n          end: range.endLine + 1\n        };\n        if (typeof range.kind !== \"undefined\") {\n          result.kind = toFoldingRangeKind(range.kind);\n        }\n        return result;\n      });\n    });\n  }\n};\nfunction toFoldingRangeKind(kind) {\n  switch (kind) {\n    case FoldingRangeKind.Comment:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Comment;\n    case FoldingRangeKind.Imports:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Imports;\n    case FoldingRangeKind.Region:\n      return monaco_editor_core_exports.languages.FoldingRangeKind.Region;\n  }\n  return void 0;\n}\nvar SelectionRangeAdapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  provideSelectionRanges(model, positions, token) {\n    const resource = model.uri;\n    return this._worker(resource).then(\n      (worker) => worker.getSelectionRanges(\n        resource.toString(),\n        positions.map(fromPosition)\n      )\n    ).then((selectionRanges) => {\n      if (!selectionRanges) {\n        return;\n      }\n      return selectionRanges.map((selectionRange) => {\n        const result = [];\n        while (selectionRange) {\n          result.push({ range: toRange(selectionRange.range) });\n          selectionRange = selectionRange.parent;\n        }\n        return result;\n      });\n    });\n  }\n};\n\n// src/language/html/htmlMode.ts\nvar HTMLCompletionAdapter = class extends CompletionAdapter {\n  constructor(worker) {\n    super(worker, [\".\", \":\", \"<\", '\"', \"=\", \"/\"]);\n  }\n};\nfunction setupMode1(defaults) {\n  const client = new WorkerManager(defaults);\n  const worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  let languageId = defaults.languageId;\n  monaco_editor_core_exports.languages.registerCompletionItemProvider(languageId, new HTMLCompletionAdapter(worker));\n  monaco_editor_core_exports.languages.registerHoverProvider(languageId, new HoverAdapter(worker));\n  monaco_editor_core_exports.languages.registerDocumentHighlightProvider(\n    languageId,\n    new DocumentHighlightAdapter(worker)\n  );\n  monaco_editor_core_exports.languages.registerLinkProvider(languageId, new DocumentLinkAdapter(worker));\n  monaco_editor_core_exports.languages.registerFoldingRangeProvider(\n    languageId,\n    new FoldingRangeAdapter(worker)\n  );\n  monaco_editor_core_exports.languages.registerDocumentSymbolProvider(\n    languageId,\n    new DocumentSymbolAdapter(worker)\n  );\n  monaco_editor_core_exports.languages.registerSelectionRangeProvider(\n    languageId,\n    new SelectionRangeAdapter(worker)\n  );\n  monaco_editor_core_exports.languages.registerRenameProvider(languageId, new RenameAdapter(worker));\n  if (languageId === \"html\") {\n    monaco_editor_core_exports.languages.registerDocumentFormattingEditProvider(\n      languageId,\n      new DocumentFormattingEditProvider(worker)\n    );\n    monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(\n      languageId,\n      new DocumentRangeFormattingEditProvider(worker)\n    );\n  }\n}\nfunction setupMode(defaults) {\n  const disposables = [];\n  const providers = [];\n  const client = new WorkerManager(defaults);\n  disposables.push(client);\n  const worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  function registerProviders() {\n    const { languageId, modeConfiguration } = defaults;\n    disposeAll(providers);\n    if (modeConfiguration.completionItems) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCompletionItemProvider(languageId, new HTMLCompletionAdapter(worker))\n      );\n    }\n    if (modeConfiguration.hovers) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerHoverProvider(languageId, new HoverAdapter(worker))\n      );\n    }\n    if (modeConfiguration.documentHighlights) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentHighlightProvider(\n          languageId,\n          new DocumentHighlightAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.links) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerLinkProvider(languageId, new DocumentLinkAdapter(worker))\n      );\n    }\n    if (modeConfiguration.documentSymbols) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentSymbolProvider(\n          languageId,\n          new DocumentSymbolAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.rename) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerRenameProvider(languageId, new RenameAdapter(worker))\n      );\n    }\n    if (modeConfiguration.foldingRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerFoldingRangeProvider(\n          languageId,\n          new FoldingRangeAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.selectionRanges) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerSelectionRangeProvider(\n          languageId,\n          new SelectionRangeAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.documentFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentFormattingEditProvider(\n          languageId,\n          new DocumentFormattingEditProvider(worker)\n        )\n      );\n    }\n    if (modeConfiguration.documentRangeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(\n          languageId,\n          new DocumentRangeFormattingEditProvider(worker)\n        )\n      );\n    }\n  }\n  registerProviders();\n  disposables.push(asDisposable(providers));\n  return asDisposable(disposables);\n}\nfunction asDisposable(disposables) {\n  return { dispose: () => disposeAll(disposables) };\n}\nfunction disposeAll(disposables) {\n  while (disposables.length) {\n    disposables.pop().dispose();\n  }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/language/html/htmlMode.js\n"));

/***/ })

}]);