"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/AIAssistant/ChatInterface.tsx":
/*!******************************************************!*\
  !*** ./src/components/AIAssistant/ChatInterface.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chatHistoryService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chatHistoryService */ \"(app-pages-browser)/./src/services/chatHistoryService.ts\");\n/* harmony import */ var _services_fileTreeService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\");\n/* harmony import */ var _services_diffToolService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/diffToolService */ \"(app-pages-browser)/./src/services/diffToolService.ts\");\n/* harmony import */ var _services_aiService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/aiService */ \"(app-pages-browser)/./src/services/aiService.ts\");\n/* harmony import */ var _utils_pathUtils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/pathUtils */ \"(app-pages-browser)/./src/utils/pathUtils.ts\");\n/* harmony import */ var _utils_SelectionManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/SelectionManager */ \"(app-pages-browser)/./src/utils/SelectionManager.ts\");\n/* harmony import */ var _SessionManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./SessionManager */ \"(app-pages-browser)/./src/components/AIAssistant/SessionManager.tsx\");\n/* harmony import */ var _FileAssociationTreeDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./FileAssociationTreeDialog */ \"(app-pages-browser)/./src/components/AIAssistant/FileAssociationTreeDialog.tsx\");\n/* harmony import */ var _CompactDiffDisplay__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./CompactDiffDisplay */ \"(app-pages-browser)/./src/components/AIAssistant/CompactDiffDisplay.tsx\");\n/* harmony import */ var _MediaUploader__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./MediaUploader */ \"(app-pages-browser)/./src/components/AIAssistant/MediaUploader.tsx\");\n/* harmony import */ var _HelperResponseLayer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./HelperResponseLayer */ \"(app-pages-browser)/./src/components/AIAssistant/HelperResponseLayer.tsx\");\n/* harmony import */ var _PromptManagerModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./PromptManagerModal */ \"(app-pages-browser)/./src/components/AIAssistant/PromptManagerModal.tsx\");\n/* harmony import */ var _MultipleComparisonModal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./MultipleComparisonModal */ \"(app-pages-browser)/./src/components/AIAssistant/MultipleComparisonModal.tsx\");\n/* harmony import */ var _utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/humanBlockParser */ \"(app-pages-browser)/./src/utils/humanBlockParser.ts\");\n/* harmony import */ var _services_promptConfigService__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/services/promptConfigService */ \"(app-pages-browser)/./src/services/promptConfigService.ts\");\n/**\r\n * AI对话界面组件\r\n * 传统的对话形式界面，用于与AI进行实时对话\r\n * 优化版本：解决文本选择被频繁重渲染取消的问题\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n // Renamed to avoid conflict\n\n\n\n\n\n\n\n\n\n\nconst ChatInterface = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c = _s(function ChatInterface(param, ref) {\n    let { currentConfig, onSendMessage, responseContent, isStreaming, isComplete, error, onRetry, onCopy, onStop, onInsertToEditor, onContentInsert, onMessageContentInsert, isLoading, className = \"\", currentPersona, onChatHistoryChange, // 文件关联相关props\n    associatedFiles = [], onFilesChange = ()=>{}, onShowFileAssociation = ()=>{}, // 🔧 AI响应状态清理回调\n    onClearAIResponse, // 🔧 作品ID，用于文件关联\n    artworkId, // 🔧 文件选择回调，用于跳转到编辑器\n    onFileSelect, // 🔧 详细对比回调，用于打开详细差异对比视图\n    onOpenDetailedDiff, // 🔧 聚焦文件状态，由父组件管理\n    focusedFile, // 🔧 媒体文件变化回调\n    onMediaFilesChange, // 🔧 受众设置回调\n    onShowAudienceSettings, // 🔧 会话管理回调\n    onShowSessionManager, // 🔧 多项对比消息构建回调\n    onMultipleComparisonSend } = param;\n    _s();\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [chatHistory, setChatHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSessionManager, setShowSessionManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showFileAssociation, setShowFileAssociation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPromptManager, setShowPromptManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activePromptConfig, setActivePromptConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isFileAssociationCollapsed, setIsFileAssociationCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filePaths, setFilePaths] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [showToolsPanel, setShowToolsPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 消息工具面板状态 - 记录哪条消息的工具面板正在显示\n    const [activeMessageTools, setActiveMessageTools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 编辑消息状态\n    const [editingMessageId, setEditingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingContent, setEditingContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 多项对比弹窗状态\n    const [showMultipleComparison, setShowMultipleComparison] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [comparisonMessageContent, setComparisonMessageContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [comparisonTriggerMessageIndex, setComparisonTriggerMessageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    // 共享状态\n    const [availableModels, setAvailableModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingModels, setIsLoadingModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 单模型生成相关状态\n    const [selectedSingleModel, setSelectedSingleModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [singleModelResults, setSingleModelResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [streamingSingleResults, setStreamingSingleResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [generationCount, setGenerationCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3);\n    // 多模型对比相关状态\n    const [selectedMultiModels, setSelectedMultiModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [multiModelResults, setMultiModelResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [streamingMultiModels, setStreamingMultiModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // 🔧 聚焦文件状态现在由父组件管理，通过props传递\n    // 媒体上传相关状态\n    const [uploadedMedia, setUploadedMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showMediaUploader, setShowMediaUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMediaList, setShowMediaList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 消息层状态管理\n    const [messageLayers, setMessageLayers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pendingIntegratedMessage, setPendingIntegratedMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [processedMessages, setProcessedMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // 🔧 调试监听已移除，避免控制台日志过多\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chatContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const aiResponseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 🔧 响应内容引用，避免useEffect依赖responseContent导致重复渲染\n    const responseContentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    // 🔧 文本选择管理器\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 初始化选择管理器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!selectionManagerRef.current) {\n            selectionManagerRef.current = new _utils_SelectionManager__WEBPACK_IMPORTED_MODULE_7__.SelectionManager();\n        }\n        return ()=>{\n            if (selectionManagerRef.current && aiResponseRef.current) {\n                selectionManagerRef.current.stopListening(aiResponseRef.current);\n            }\n        };\n    }, []);\n    // 点击外部关闭工具面板\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (showToolsPanel) {\n                const target = event.target;\n                if (!target.closest(\".tools-panel-container\")) {\n                    setShowToolsPanel(false);\n                }\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        showToolsPanel\n    ]);\n    // 暂时禁用选择监听\n    // useEffect(() => {\n    //   if (selectionManagerRef.current && aiResponseRef.current) {\n    //     selectionManagerRef.current.startListening(aiResponseRef.current)\n    //   }\n    //   return () => {\n    //     if (selectionManagerRef.current && aiResponseRef.current) {\n    //       selectionManagerRef.current.stopListening(aiResponseRef.current)\n    //     }\n    //   }\n    // }, [])\n    // 🔧 同步responseContent到ref，避免useEffect直接依赖responseContent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        responseContentRef.current = responseContent;\n    }, [\n        responseContent\n    ]);\n    // 🔧 修复重复渲染问题：使用useMemo确保chatHistoryService引用稳定\n    const chatHistoryService = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>_services_chatHistoryService__WEBPACK_IMPORTED_MODULE_2__.ChatHistoryService.getInstance(), []);\n    // 辅助函数：标准化路径\n    const normalizePath = (path)=>{\n        if (!path) return \"\";\n        // 使用导入的工具函数，避免命名冲突\n        return (0,_utils_pathUtils__WEBPACK_IMPORTED_MODULE_6__.normalizePath)(path).toLowerCase();\n    };\n    // 辅助函数：根据路径查找文件ID\n    const findFileIdByPath = (rootNode, targetPath)=>{\n        // 标准化目标路径，确保相对路径和绝对路径都能正确匹配\n        const normalizedTargetPath = normalizePath(targetPath);\n        const searchNode = (node)=>{\n            // 标准化当前节点路径进行比较\n            if (normalizePath(node.path) === normalizedTargetPath) {\n                return node.id;\n            }\n            if (node.children) {\n                for (const child of node.children){\n                    const foundId = searchNode(child);\n                    if (foundId) {\n                        return foundId;\n                    }\n                }\n            }\n            return null;\n        };\n        return searchNode(rootNode);\n    };\n    // 辅助函数：解析替换内容（仅支持JSON格式）\n    const parseReplaceContent = (content, find)=>{\n        // JSON格式：直接使用find和content参数\n        if (find) {\n            console.log(\"✅ 使用JSON格式解析:\", {\n                find,\n                content: content.substring(0, 100) + \"...\"\n            });\n            return [\n                find,\n                content\n            ];\n        }\n        // 如果没有find参数，说明格式不正确\n        console.error(\"❌ 替换操作缺少查找内容\");\n        console.error('\\uD83D\\uDCDD 请使用JSON格式：{\"file\": \"...\", \"type\": \"replace\", \"find\": \"查找内容\", \"content\": \"替换内容\"}');\n        console.error(\"\\uD83D\\uDCDD 实际收到的内容:\", content.substring(0, 200));\n        // 返回错误状态，让调用方处理\n        return [\n            \"\",\n            content\n        ];\n    };\n    // 辅助函数：获取所有文件路径（用于调试）\n    const getAllFilePaths = (rootNode)=>{\n        const paths = [];\n        const collectPaths = (node)=>{\n            if (node.type === \"file\") {\n                paths.push(node.path);\n            }\n            if (node.children) {\n                node.children.forEach(collectPaths);\n            }\n        };\n        collectPaths(rootNode);\n        return paths;\n    };\n    // 辅助函数：根据文件ID查找文件完整路径\n    const findFilePathById = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (fileId)=>{\n        try {\n            if (!artworkId) return \"未知文件 (\".concat(fileId.substring(0, 8), \"...)\");\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_3__.FileTreeService.getInstance();\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (fileTreeResult.success && fileTreeResult.data) {\n                const filePath = getFilePath(fileTreeResult.data, fileId);\n                return filePath || \"未知文件 (\".concat(fileId.substring(0, 8), \"...)\");\n            }\n            return \"未知文件 (\".concat(fileId.substring(0, 8), \"...)\");\n        } catch (error) {\n            console.warn(\"获取文件路径失败:\", error);\n            return \"未知文件 (\".concat(fileId.substring(0, 8), \"...)\");\n        }\n    }, [\n        artworkId\n    ]);\n    // 辅助函数：获取文件的完整路径（不包括作品根节点）\n    const getFilePath = (rootNode, fileId)=>{\n        // 递归搜索文件树，构建路径\n        const buildPath = function(node, targetId) {\n            let currentPath = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n            // 如果找到目标节点，返回当前路径\n            if (node.id === targetId) {\n                return [\n                    ...currentPath,\n                    node.name\n                ];\n            }\n            // 如果有子节点，递归搜索\n            if (node.children) {\n                for (const child of node.children){\n                    const path = buildPath(child, targetId, [\n                        ...currentPath,\n                        node.name\n                    ]);\n                    if (path) {\n                        return path;\n                    }\n                }\n            }\n            return null;\n        };\n        // 从根节点开始搜索，但不包括根节点名称\n        const path = buildPath(rootNode, fileId, []);\n        if (path) {\n            // 移除第一个元素（根节点名称）\n            path.shift();\n            return path.join(\"/\");\n        }\n        return fileId.substring(0, 8) + \"...\"; // 如果找不到路径，返回ID的简短版本\n    };\n    // 递归搜索文件树中的文件名（保留用于兼容性）\n    const searchNodeForName = (node, fileId)=>{\n        if (node.id === fileId) {\n            return node.name;\n        }\n        if (node.children) {\n            for (const child of node.children){\n                const result = searchNodeForName(child, fileId);\n                if (result) return result;\n            }\n        }\n        return null;\n    };\n    // 🔧 聚焦文件状态现在由父组件统一管理，移除子组件中的事件监听\n    // 组件初始化时加载历史记录和当前会话ID\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeSession = async ()=>{\n            try {\n                // 获取当前会话ID\n                const sessionId = chatHistoryService.getCurrentSessionId();\n                setCurrentSessionId(sessionId);\n                // 加载当前会话的历史记录\n                const storedHistory = await chatHistoryService.getCurrentHistory();\n                if (storedHistory.length > 0) {\n                    setChatHistory(storedHistory);\n                    console.log(\"✅ 已从IndexedDB加载对话历史:\", storedHistory.length, \"条消息\");\n                    // 通知父组件对话历史变化\n                    if (onChatHistoryChange) {\n                        onChatHistoryChange(storedHistory);\n                    }\n                }\n            // 文件关联数据由父组件管理，这里不需要重复加载\n            } catch (error) {\n                console.error(\"❌ 加载对话历史失败:\", error);\n            }\n        };\n        initializeSession();\n    }, [\n        chatHistoryService,\n        onChatHistoryChange\n    ]);\n    // 加载激活的提示词配置\n    const loadActivePromptConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const promptConfigService = _services_promptConfigService__WEBPACK_IMPORTED_MODULE_16__.PromptConfigService.getInstance();\n            const result = await promptConfigService.getActiveConfig();\n            if (result.success && result.data) {\n                setActivePromptConfig(result.data);\n            } else {\n                setActivePromptConfig(null);\n            }\n        } catch (error) {\n            console.error(\"加载激活提示词配置失败:\", error);\n            setActivePromptConfig(null);\n        }\n    }, []);\n    // 组件挂载时加载激活配置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadActivePromptConfig();\n    }, [\n        loadActivePromptConfig\n    ]);\n    // 处理提示词配置选择\n    const handlePromptConfigSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setActivePromptConfig(config);\n        console.log(\"\\uD83D\\uDD2E 选择提示词配置:\", config.name);\n        // 重新加载激活配置以确保状态同步\n        setTimeout(()=>{\n            loadActivePromptConfig();\n        }, 100);\n    }, [\n        loadActivePromptConfig\n    ]);\n    // 生成唯一消息ID\n    const generateMessageId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((type)=>{\n        const timestamp = Date.now();\n        const random = Math.random().toString(36).substring(2, 11);\n        return \"\".concat(type, \"-\").concat(timestamp, \"-\").concat(random);\n    }, []);\n    // 添加用户消息到历史记录\n    const addUserMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message)=>{\n        setChatHistory((prev)=>{\n            // 检查消息是否已存在，避免重复添加\n            const exists = prev.some((m)=>m.id === message.id);\n            if (exists) {\n                console.log(\"⚠️ 消息已存在，跳过添加:\", message.content.substring(0, 20));\n                return prev;\n            }\n            const newHistory = [\n                ...prev,\n                message\n            ];\n            console.log(\"✅ 用户消息已添加:\", message.content.substring(0, 20), \"历史长度:\", newHistory.length);\n            // 异步保存到IndexedDB\n            chatHistoryService.saveCurrentHistory(newHistory).catch((error)=>{\n                console.error(\"❌ 保存用户消息失败:\", error);\n            });\n            // 异步通知父组件对话历史变化，避免在渲染过程中同步更新\n            setTimeout(()=>{\n                if (onChatHistoryChange) {\n                    onChatHistoryChange(newHistory);\n                }\n            }, 0);\n            return newHistory;\n        });\n    }, [\n        onChatHistoryChange,\n        chatHistoryService\n    ]);\n    // 处理跳转到编辑器\n    const handleJumpToEditor = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filePath)=>{\n        try {\n            if (!artworkId) return;\n            // 通过文件路径查找文件ID\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_3__.FileTreeService.getInstance();\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (fileTreeResult.success && fileTreeResult.data) {\n                const fileId = findFileIdByPath(fileTreeResult.data, filePath);\n                if (fileId) {\n                    // 触发文件选择事件，通知父组件切换到编辑器\n                    onFileSelect === null || onFileSelect === void 0 ? void 0 : onFileSelect(fileId);\n                    // 可选：设置聚焦状态\n                    const chatHistoryService = _services_chatHistoryService__WEBPACK_IMPORTED_MODULE_2__.ChatHistoryService.getInstance();\n                    await chatHistoryService.setCurrentFocusedFile(fileId);\n                    console.log(\"\\uD83D\\uDD17 跳转到编辑器:\", filePath);\n                } else {\n                    console.warn(\"⚠️ 未找到文件:\", filePath);\n                }\n            }\n        } catch (error) {\n            console.error(\"❌ 跳转到编辑器失败:\", error);\n        }\n    }, [\n        artworkId,\n        onFileSelect\n    ]);\n    // 处理应用diff修改\n    const handleApplyDiff = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filePath, content, operation, find)=>{\n        try {\n            if (!artworkId) return;\n            // 标准化文件路径，确保相对路径和绝对路径都能正确处理\n            const normalizedPath = normalizePath(filePath);\n            console.log(\"\\uD83D\\uDD0D 查找文件:\", {\n                original: filePath,\n                normalized: normalizedPath\n            });\n            const diffToolService = _services_diffToolService__WEBPACK_IMPORTED_MODULE_4__.DiffToolService.getInstance();\n            const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_3__.FileTreeService.getInstance();\n            const fileTreeResult = await fileTreeService.getFileTree(artworkId);\n            if (!fileTreeResult.success || !fileTreeResult.data) {\n                console.error(\"❌ 获取文件树失败\");\n                return;\n            }\n            const fileId = findFileIdByPath(fileTreeResult.data, normalizedPath);\n            if (!fileId) {\n                // 增强错误日志，提供详细的路径匹配调试信息\n                const availablePaths = getAllFilePaths(fileTreeResult.data);\n                console.warn(\"⚠️ 未找到文件:\", {\n                    path: normalizedPath,\n                    originalPath: filePath,\n                    availableFiles: availablePaths.slice(0, 10),\n                    totalFiles: availablePaths.length\n                });\n                return;\n            }\n            let result;\n            if (operation === \"append\") {\n                result = await diffToolService.appendText(fileId, {\n                    content\n                });\n            } else if (operation === \"replace\") {\n                // 解析替换参数\n                const [pattern, replacement] = parseReplaceContent(content, find);\n                if (!pattern) {\n                    console.error(\"❌ 替换操作缺少查找内容\");\n                    return;\n                }\n                // 🚀 使用新的智能字符串替换功能\n                result = await diffToolService.intelligentStringReplace(fileId, pattern, replacement);\n                if (result.success) {\n                    console.log(\"✅ 智能字符串替换成功\");\n                    // 应用差异修改\n                    if (result.data) {\n                        await diffToolService.applyDiff(result.data);\n                    }\n                } else {\n                    console.error(\"❌ 智能字符串替换失败:\", result.error);\n                    // 显示详细错误信息（包含建议）\n                    if (result.error) {\n                        // 这里可以添加用户友好的错误提示UI\n                        console.log(\"\\uD83D\\uDCA1 替换失败详情:\\n\", result.error);\n                    }\n                    return;\n                }\n            }\n            if (result === null || result === void 0 ? void 0 : result.success) {\n                // 对于append操作，需要应用diff；对于replace操作，智能替换已经处理了应用\n                if (operation === \"append\" && result.data) {\n                    await diffToolService.applyDiff(result.data);\n                }\n                console.log(\"✅ 文件修改已应用:\", filePath);\n                // 🔧 延迟通知编辑器刷新文件内容，确保文件写入完成\n                if (onFileSelect && fileId) {\n                    setTimeout(()=>{\n                        onFileSelect(fileId);\n                        console.log(\"✅ 已通知编辑器刷新文件:\", filePath);\n                    }, 200) // 200ms延迟确保文件系统完成写入\n                    ;\n                }\n            // 可选：显示成功提示\n            } else {\n                console.error(\"❌ 应用修改失败:\", result === null || result === void 0 ? void 0 : result.error);\n            }\n        } catch (error) {\n            console.error(\"❌ 应用差异修改失败:\", error);\n        }\n    }, [\n        artworkId,\n        onFileSelect\n    ]);\n    // 处理媒体文件上传成功\n    const handleMediaUploadSuccess = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((mediaFile)=>{\n        setUploadedMedia((prev)=>{\n            const newMediaFiles = [\n                ...prev,\n                mediaFile\n            ];\n            // 通知父组件媒体文件变化\n            if (onMediaFilesChange) {\n                onMediaFilesChange(newMediaFiles);\n            }\n            return newMediaFiles;\n        });\n        console.log(\"✅ 媒体文件上传成功:\", mediaFile.filename);\n    }, [\n        onMediaFilesChange\n    ]);\n    // 处理媒体文件上传错误\n    const handleMediaUploadError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((error)=>{\n        console.error(\"❌ 媒体文件上传失败:\", error);\n    // 可以在这里添加错误提示UI\n    }, []);\n    // 移除已上传的媒体文件\n    const removeMediaFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((fileId)=>{\n        setUploadedMedia((prev)=>{\n            const newMediaFiles = prev.filter((file)=>file.id !== fileId);\n            // 通知父组件媒体文件变化\n            if (onMediaFilesChange) {\n                onMediaFilesChange(newMediaFiles);\n            }\n            return newMediaFiles;\n        });\n    }, [\n        onMediaFilesChange\n    ]);\n    // 处理创建文件\n    const handleCreateFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filePath, content, operation)=>{\n        try {\n            if (!artworkId) {\n                console.error(\"❌ 缺少作品ID\");\n                return;\n            }\n            // 标准化文件路径，确保相对路径和绝对路径都能正确处理\n            const normalizedPath = normalizePath(filePath);\n            console.log(\"\\uD83D\\uDD0D 创建文件:\", {\n                original: filePath,\n                normalized: normalizedPath\n            });\n            const diffToolService = _services_diffToolService__WEBPACK_IMPORTED_MODULE_4__.DiffToolService.getInstance();\n            // 使用新的createFileWithPath方法，传入标准化后的路径\n            const result = await diffToolService.createFileWithPath(artworkId, normalizedPath, content, operation);\n            if (result.success && result.data) {\n                // 应用创建结果\n                await diffToolService.applyDiff(result.data);\n                console.log(\"✅ 文件创建并应用成功:\", filePath);\n                // 文件树会通过事件系统自动刷新，不再需要手动通知\n                console.log(\"✅ 文件创建完成，文件树将自动刷新\");\n                // 可选：跳转到新创建的文件\n                if (result.data.fileId) {\n                    handleJumpToEditor(filePath);\n                }\n            } else {\n                // 增强错误日志，提供详细的创建失败调试信息\n                console.error(\"❌ 文件创建失败:\", {\n                    path: normalizedPath,\n                    originalPath: filePath,\n                    error: result.error,\n                    operation: operation\n                });\n            }\n        } catch (error) {\n            console.error(\"❌ 创建文件失败:\", error);\n        }\n    }, [\n        artworkId,\n        handleJumpToEditor\n    ]);\n    // 🔧 渲染内容缓存，避免重复渲染相同内容\n    const renderedContentCache = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    // 🔧 简化消息渲染函数 - 添加缓存机制\n    const renderMessageContentInternal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((content)=>{\n        // 检查缓存\n        if (renderedContentCache.current.has(content)) {\n            return renderedContentCache.current.get(content);\n        }\n        // 第一步：先识别df代码块，避免被普通代码块处理干扰\n        const dfRegex = /```df\\s*\\n([\\s\\S]*?)```/g;\n        const diffRegex = /<diff>([\\s\\S]*?)<\\/diff>/g;\n        // 添加human代码块正则表达式，用于BrainstormingHelper功能\n        const humanRegex = /```human\\s*\\n\\s*neme:\\s*([^\\n]+)\\s*\\n\\s*currentPersona\\.description:\\s*([^\\n]+)\\s*\\n\\s*问题：\\s*([\\s\\S]*?)```/g;\n        // 第二步：提取普通代码块，但排除df代码块\n        const codeBlockRegex = /```(?!df\\s)(\\w+)?\\n([\\s\\S]*?)```/g;\n        const codeBlocks = [];\n        let processedContent = content;\n        let codeBlockMatch;\n        // 提取普通代码块并替换为占位符（排除df代码块）\n        while((codeBlockMatch = codeBlockRegex.exec(content)) !== null){\n            const placeholder = \"__CODE_BLOCK_\".concat(codeBlocks.length, \"__\");\n            codeBlocks.push({\n                placeholder,\n                content: codeBlockMatch[2],\n                lang: codeBlockMatch[1]\n            });\n            processedContent = processedContent.replace(codeBlockMatch[0], placeholder);\n        }\n        // 第三步：在处理后的内容中识别特殊标记（包括df代码块）\n        const parts = [];\n        let lastIndex = 0;\n        let match;\n        // 创建一个包含所有匹配项的数组，按位置排序\n        const allMatches = [];\n        // 收集diff匹配项\n        diffRegex.lastIndex = 0;\n        while((match = diffRegex.exec(processedContent)) !== null){\n            allMatches.push({\n                type: \"diff\",\n                index: match.index,\n                length: match[0].length,\n                content: match[1]\n            });\n        }\n        // 收集df匹配项（仅支持JSON格式）\n        dfRegex.lastIndex = 0;\n        while((match = dfRegex.exec(content)) !== null){\n            const dfContent = match[1].trim();\n            try {\n                // 只解析JSON格式\n                if (dfContent.startsWith(\"{\") && dfContent.endsWith(\"}\")) {\n                    const parsedDf = JSON.parse(dfContent);\n                    // 验证必需字段\n                    if (!parsedDf.file || !parsedDf.type) {\n                        throw new Error(\"缺少必需字段：file 和 type\");\n                    }\n                    // 验证操作类型\n                    if (![\n                        \"replace\",\n                        \"append\",\n                        \"create\"\n                    ].includes(parsedDf.type)) {\n                        throw new Error(\"不支持的操作类型: \".concat(parsedDf.type));\n                    }\n                    // 对于replace操作，验证find字段\n                    if (parsedDf.type === \"replace\" && !parsedDf.find) {\n                        throw new Error(\"replace操作需要find字段\");\n                    }\n                    allMatches.push({\n                        type: \"df\",\n                        index: match.index,\n                        length: match[0].length,\n                        filePath: parsedDf.file,\n                        operation: parsedDf.type,\n                        content: parsedDf.content || \"\",\n                        find: parsedDf.find || \"\"\n                    });\n                } else {\n                    throw new Error(\"df代码块必须使用JSON格式\");\n                }\n            } catch (error) {\n                console.error(\"❌ 解析df代码块失败:\", error);\n                console.error(\"\\uD83D\\uDCDD 正确格式示例:\");\n                console.error(\"```df\");\n                console.error(\"{\");\n                console.error('  \"file\": \"path/to/file\",');\n                console.error('  \"type\": \"replace\",');\n                console.error('  \"find\": \"要查找的内容\",');\n                console.error('  \"content\": \"替换内容\"');\n                console.error(\"}\");\n                console.error(\"```\");\n                console.error(\"\\uD83D\\uDCDD 实际内容:\", dfContent);\n                // 添加错误项，用于在UI中显示错误信息\n                allMatches.push({\n                    type: \"df\",\n                    index: match.index,\n                    length: match[0].length,\n                    filePath: \"格式错误\",\n                    operation: \"error\",\n                    content: dfContent,\n                    error: error instanceof Error ? error.message : String(error)\n                });\n            }\n        }\n        // 收集human匹配项（用于BrainstormingHelper功能）\n        humanRegex.lastIndex = 0;\n        while((match = humanRegex.exec(content)) !== null){\n            allMatches.push({\n                type: \"human\",\n                index: match.index,\n                length: match[0].length,\n                roleName: match[1].trim(),\n                roleDescription: match[2].trim(),\n                question: match[3].trim()\n            });\n        }\n        // 按位置排序所有匹配项\n        allMatches.sort((a, b)=>a.index - b.index);\n        // 第三步：处理所有匹配项和普通内容\n        for (const matchItem of allMatches){\n            // 添加匹配项前的普通内容\n            if (matchItem.index > lastIndex) {\n                const beforeContent = processedContent.substring(lastIndex, matchItem.index);\n                if (beforeContent.trim()) {\n                    parts.push({\n                        type: \"markdown\",\n                        content: beforeContent\n                    });\n                }\n            }\n            // 添加匹配项内容\n            parts.push(matchItem);\n            lastIndex = matchItem.index + matchItem.length;\n        }\n        // 添加最后剩余的普通内容\n        if (lastIndex < processedContent.length) {\n            const afterContent = processedContent.substring(lastIndex);\n            if (afterContent.trim()) {\n                parts.push({\n                    type: \"markdown\",\n                    content: afterContent\n                });\n            }\n        }\n        // 如果没有特殊内容，直接渲染markdown（恢复代码块）\n        if (parts.length === 0) {\n            const finalContent = restoreCodeBlocks(processedContent, codeBlocks);\n            const result = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"prose prose-invert max-w-none\",\n                dangerouslySetInnerHTML: {\n                    __html: renderMarkdown(finalContent)\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 830,\n                columnNumber: 9\n            }, this);\n            // 缓存结果\n            renderedContentCache.current.set(content, result);\n            // 限制缓存大小，避免内存泄漏\n            if (renderedContentCache.current.size > 50) {\n                const firstKey = renderedContentCache.current.keys().next().value;\n                renderedContentCache.current.delete(firstKey);\n            }\n            return result;\n        }\n        // 第四步：渲染混合内容（恢复代码块）\n        const result = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: parts.map((part, index)=>{\n                if (part.type === \"df\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CompactDiffDisplay__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        filePath: part.filePath,\n                        operation: part.operation,\n                        content: part.content,\n                        find: part.find,\n                        onJumpToEditor: handleJumpToEditor,\n                        onApplyChanges: handleApplyDiff,\n                        onCreateFile: handleCreateFile,\n                        onOpenDetailedDiff: onOpenDetailedDiff,\n                        artworkId: artworkId\n                    }, index, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 854,\n                        columnNumber: 15\n                    }, this);\n                } else if (part.type === \"human\") {\n                    // human代码块现在通过handleSendMessage统一处理，这里只显示原始内容\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-purple-900/20 border border-purple-500/30 rounded-lg p-4 my-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-purple-300 text-sm font-handwritten mb-2\",\n                                children: \"\\uD83E\\uDD16 辅助AI协同思考请求\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-300\",\n                                                children: \"角色：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 875,\n                                                columnNumber: 24\n                                            }, this),\n                                            part.roleName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 875,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-300\",\n                                                children: \"描述：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 24\n                                            }, this),\n                                            part.roleDescription\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-300\",\n                                                children: \"问题：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 24\n                                            }, this),\n                                            part.question\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 874,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400 mt-2\",\n                                children: \"ℹ️ 此请求将通过消息发送时自动处理\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 879,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 870,\n                        columnNumber: 15\n                    }, this);\n                } else {\n                    // 恢复markdown内容中的代码块\n                    const restoredContent = restoreCodeBlocks(part.content, codeBlocks);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose prose-invert max-w-none\",\n                        dangerouslySetInnerHTML: {\n                            __html: renderMarkdown(restoredContent)\n                        }\n                    }, index, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 888,\n                        columnNumber: 15\n                    }, this);\n                }\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n            lineNumber: 850,\n            columnNumber: 7\n        }, this);\n        // 缓存结果\n        renderedContentCache.current.set(content, result);\n        // 限制缓存大小，避免内存泄漏\n        if (renderedContentCache.current.size > 50) {\n            const firstKey = renderedContentCache.current.keys().next().value;\n            renderedContentCache.current.delete(firstKey);\n        }\n        return result;\n    }, [\n        handleJumpToEditor,\n        handleApplyDiff,\n        handleCreateFile,\n        onOpenDetailedDiff,\n        artworkId\n    ]);\n    // 辅助函数：恢复代码块\n    const restoreCodeBlocks = (content, codeBlocks)=>{\n        let restoredContent = content;\n        codeBlocks.forEach((block)=>{\n            const codeBlockHtml = \"```\".concat(block.lang || \"\", \"\\n\").concat(block.content, \"```\");\n            restoredContent = restoredContent.replace(block.placeholder, codeBlockHtml);\n        });\n        return restoredContent;\n    };\n    // Markdown渲染函数（简化版，完全移除diff处理）\n    const renderMarkdown = (text)=>{\n        // 代码块处理\n        const codeBlockRegex = /```(\\w+)?\\n([\\s\\S]*?)```/g;\n        const inlineCodeRegex = /`([^`]+)`/g;\n        // 标题处理\n        const headingRegex = /^(#{1,6})\\s+(.+)$/gm;\n        // 粗体和斜体\n        const boldRegex = /\\*\\*(.*?)\\*\\*/g;\n        const italicRegex = /\\*(.*?)\\*/g;\n        // 链接处理\n        const linkRegex = /\\[([^\\]]+)\\]\\(([^)]+)\\)/g;\n        return text// 代码块\n        .replace(codeBlockRegex, (match, lang, code)=>{\n            return '<div class=\"code-block bg-gray-800 rounded-lg p-3 my-2 overflow-x-auto\">\\n          '.concat(lang ? '<div class=\"text-xs text-gray-400 mb-2\">'.concat(lang, \"</div>\") : \"\", '\\n          <pre class=\"text-sm text-green-300 font-mono whitespace-pre-wrap\">').concat(code.trim(), \"</pre>\\n        </div>\");\n        })// 行内代码\n        .replace(inlineCodeRegex, '<code class=\"bg-gray-800 text-green-300 px-1 py-0.5 rounded text-sm font-mono\">$1</code>')// 标题\n        .replace(headingRegex, (match, hashes, title)=>{\n            const level = hashes.length;\n            const className = level === 1 ? \"text-lg font-bold text-amber-200 mt-4 mb-2\" : level === 2 ? \"text-md font-bold text-amber-200 mt-3 mb-2\" : \"text-sm font-bold text-amber-200 mt-2 mb-1\";\n            return \"<h\".concat(level, ' class=\"').concat(className, '\">').concat(title, \"</h\").concat(level, \">\");\n        })// 粗体\n        .replace(boldRegex, '<strong class=\"font-bold text-amber-100\">$1</strong>')// 斜体\n        .replace(italicRegex, '<em class=\"italic text-amber-100\">$1</em>')// 链接\n        .replace(linkRegex, '<a href=\"$2\" class=\"text-blue-400 hover:text-blue-300 underline\" target=\"_blank\" rel=\"noopener noreferrer\">$1</a>')// 换行\n        .replace(/\\n/g, \"<br>\");\n    };\n    // 自动调整输入框高度\n    const adjustTextareaHeight = ()=>{\n        if (textareaRef.current) {\n            textareaRef.current.style.height = \"auto\";\n            textareaRef.current.style.height = \"\".concat(Math.min(textareaRef.current.scrollHeight, 120), \"px\");\n        }\n    };\n    // 处理输入变化\n    const handleInputChange = (e)=>{\n        setInputMessage(e.target.value);\n        adjustTextareaHeight();\n    };\n    // 发送消息\n    const handleSend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        let messageContent = inputMessage.trim();\n        // 检查是否有激活的提示词配置\n        try {\n            const promptConfigService = _services_promptConfigService__WEBPACK_IMPORTED_MODULE_16__.PromptConfigService.getInstance();\n            const activeConfigResult = await promptConfigService.getActiveConfig();\n            if (activeConfigResult.success && activeConfigResult.data) {\n                const configContentResult = await promptConfigService.getConfigContent(activeConfigResult.data.id);\n                if (configContentResult.success && configContentResult.data) {\n                    // 将提示词内容拼接到用户输入前面\n                    messageContent = \"\".concat(configContentResult.data, \" \").concat(messageContent);\n                    console.log(\"\\uD83D\\uDD2E 应用提示词配置:\", activeConfigResult.data.name, \"内容:\", configContentResult.data);\n                }\n            }\n        } catch (error) {\n            console.error(\"获取提示词配置失败:\", error);\n        // 继续使用原始消息内容\n        }\n        // 检测是否包含```human代码块\n        const hasHumanBlock = (0,_utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_15__.hasHumanBlocks)(messageContent);\n        console.log(\"\\uD83D\\uDD35 开始发送用户消息:\", messageContent.substring(0, 30), hasHumanBlock ? \"(包含Human代码块)\" : \"\");\n        // 创建用户消息对象\n        const userMessage = {\n            id: generateMessageId(\"user\"),\n            type: \"user\",\n            content: messageContent,\n            timestamp: Date.now(),\n            createdAt: Date.now()\n        };\n        // 先清空输入框\n        setInputMessage(\"\");\n        // 重置输入框高度\n        if (textareaRef.current) {\n            textareaRef.current.style.height = \"auto\";\n        }\n        // 添加用户消息到历史记录\n        addUserMessage(userMessage);\n        if (hasHumanBlock) {\n            // 如果包含```human代码块，处理辅助AI调用\n            handleHumanBlockMessage(messageContent);\n        } else {\n            // 正常消息处理\n            setTimeout(()=>{\n                onSendMessage(messageContent);\n            }, 0);\n        }\n    }, [\n        inputMessage,\n        isLoading,\n        generateMessageId,\n        addUserMessage,\n        onSendMessage\n    ]);\n    // 处理包含```human代码块的消息\n    const handleHumanBlockMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message)=>{\n        // 生成消息唯一标识符，防止重复处理\n        const messageHash = btoa(encodeURIComponent(message)).replace(/[^a-zA-Z0-9]/g, \"\").substring(0, 16);\n        if (processedMessages.has(messageHash)) {\n            console.log(\"⚠️ 消息已处理过，跳过重复处理:\", messageHash);\n            return;\n        }\n        // 标记消息为已处理\n        setProcessedMessages((prev)=>new Set([\n                ...prev,\n                messageHash\n            ]));\n        const humanBlocks = (0,_utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_15__.extractHumanBlocks)(message);\n        console.log(\"\\uD83E\\uDD16 检测到Human代码块:\", humanBlocks.length, \"个\", \"消息ID:\", messageHash);\n        // 为每个```human代码块创建HelperResponseLayer\n        humanBlocks.forEach((block, index)=>{\n            const layerId = \"helper-\".concat(messageHash, \"-\").concat(index);\n            const messageLayer = {\n                id: layerId,\n                type: \"helper-response\",\n                content: \"\",\n                timestamp: Date.now(),\n                metadata: {\n                    humanBlock: {\n                        roleName: block.roleName,\n                        roleDescription: block.roleDescription,\n                        question: block.question\n                    },\n                    originalMessage: message,\n                    isExpanded: true,\n                    layerId\n                }\n            };\n            // 添加到消息层列表\n            setMessageLayers((prev)=>[\n                    ...prev,\n                    messageLayer\n                ]);\n        });\n        // 移除```human代码块后的消息内容\n        const cleanMessage = (0,_utils_humanBlockParser__WEBPACK_IMPORTED_MODULE_15__.removeHumanBlocks)(message);\n        // 如果还有其他内容，也发送给主AI\n        if (cleanMessage.trim()) {\n            setTimeout(()=>{\n                onSendMessage(cleanMessage);\n            }, 100);\n        }\n    }, [\n        onSendMessage,\n        processedMessages\n    ]);\n    // 处理辅助AI集成完成\n    const handleIntegrationComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((integratedMessage)=>{\n        console.log(\"\\uD83D\\uDD04 辅助AI集成完成，准备发送给主AI\");\n        setPendingIntegratedMessage(integratedMessage);\n    }, []);\n    // 处理主AI回复\n    const handleMainAIResponse = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((response)=>{\n        console.log(\"✅ 主AI回复完成\");\n    // 这里可以添加额外的处理逻辑\n    }, []);\n    // 处理辅助AI错误\n    const handleHelperError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((error)=>{\n        console.error(\"❌ 辅助AI处理错误:\", error);\n    // 可以显示错误提示\n    }, []);\n    // 发送集成消息给主AI\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingIntegratedMessage) {\n            setTimeout(()=>{\n                onSendMessage(pendingIntegratedMessage);\n                setPendingIntegratedMessage(\"\");\n            }, 500);\n        }\n    }, [\n        pendingIntegratedMessage,\n        onSendMessage\n    ]);\n    // 处理键盘事件\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    // 加载文件路径 - 添加数据完整性验证\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadFilePaths = async ()=>{\n            if (associatedFiles.length === 0) {\n                setFilePaths(new Map());\n                return;\n            }\n            const newFilePaths = new Map();\n            const validFileIds = [];\n            const invalidFileIds = [];\n            for (const fileId of associatedFiles){\n                // 数据完整性验证：检查fileId是否有效\n                if (!fileId || typeof fileId !== \"string\" || fileId.trim().length === 0) {\n                    console.warn(\"⚠️ 发现无效的文件ID:\", fileId);\n                    invalidFileIds.push(fileId);\n                    continue;\n                }\n                try {\n                    const filePath = await findFilePathById(fileId);\n                    // 验证文件路径是否有效\n                    if (filePath && !filePath.includes(\"未知文件\")) {\n                        newFilePaths.set(fileId, filePath);\n                        validFileIds.push(fileId);\n                    } else {\n                        console.warn(\"⚠️ 文件不存在或无法访问:\", fileId);\n                        invalidFileIds.push(fileId);\n                    }\n                } catch (error) {\n                    console.error(\"❌ 获取文件路径失败:\", fileId, error);\n                    invalidFileIds.push(fileId);\n                }\n            }\n            setFilePaths(newFilePaths);\n            // 如果发现无效文件，自动清理\n            if (invalidFileIds.length > 0) {\n                console.log(\"\\uD83E\\uDDF9 发现\", invalidFileIds.length, \"个无效文件，自动清理:\", invalidFileIds);\n                // 通知父组件更新文件关联，移除无效文件\n                if (validFileIds.length !== associatedFiles.length) {\n                    try {\n                        await chatHistoryService.saveCurrentFileAssociations(validFileIds);\n                        console.log(\"✅ 已自动清理无效文件关联\");\n                    } catch (error) {\n                        console.error(\"❌ 清理无效文件关联失败:\", error);\n                    }\n                }\n            }\n        };\n        loadFilePaths();\n    }, [\n        associatedFiles,\n        findFilePathById,\n        chatHistoryService\n    ]);\n    // 🔧 AI响应完成处理 - 使用防抖机制减少重复触发\n    const lastSavedContentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isComplete && responseContent && responseContent !== lastSavedContentRef.current) {\n            lastSavedContentRef.current = responseContent;\n            console.log(\"\\uD83E\\uDD16 AI响应完成，准备保存消息:\", {\n                isComplete,\n                contentLength: responseContent.length,\n                contentPreview: responseContent.substring(0, 50) + \"...\"\n            });\n            const assistantMessage = {\n                id: generateMessageId(\"assistant\"),\n                type: \"assistant\",\n                content: responseContent,\n                timestamp: Date.now(),\n                createdAt: Date.now()\n            };\n            setChatHistory((prev)=>{\n                console.log(\"\\uD83D\\uDCCB 当前历史记录数量:\", prev.length);\n                // 🔧 改进重复消息检测：检查最近的消息，而不是所有消息\n                // 只检查最近3条assistant消息，避免误判\n                const recentAssistantMessages = prev.filter((m)=>m.type === \"assistant\").slice(-3) // 只检查最近3条\n                ;\n                const duplicateExists = recentAssistantMessages.some((m)=>{\n                    const contentMatch = m.content === responseContent;\n                    const timeMatch = Math.abs(m.timestamp - assistantMessage.timestamp) < 5000 // 5秒内\n                    ;\n                    console.log(\"\\uD83D\\uDD0D 重复检测:\", {\n                        messageId: m.id.substring(0, 8),\n                        contentMatch,\n                        timeMatch,\n                        timeDiff: Math.abs(m.timestamp - assistantMessage.timestamp)\n                    });\n                    return contentMatch && timeMatch;\n                });\n                if (duplicateExists) {\n                    console.log(\"⚠️ 检测到重复消息，跳过添加\");\n                    return prev;\n                }\n                console.log(\"✅ 添加新的AI响应消息:\", assistantMessage.id.substring(0, 8));\n                const newHistory = [\n                    ...prev,\n                    assistantMessage\n                ];\n                // 异步保存历史记录\n                chatHistoryService.saveCurrentHistory(newHistory).catch((error)=>{\n                    console.error(\"❌ 保存AI响应消息失败:\", error);\n                });\n                // 通知父组件历史记录变化\n                if (onChatHistoryChange) {\n                    onChatHistoryChange(newHistory);\n                }\n                return newHistory;\n            });\n        }\n    }, [\n        isComplete,\n        responseContent,\n        generateMessageId,\n        chatHistoryService,\n        onChatHistoryChange\n    ]);\n    // 暂时禁用文本选择保护\n    // useEffect(() => {\n    //   if (selectionManagerRef.current && aiResponseRef.current) {\n    //     if (isStreaming) {\n    //       selectionManagerRef.current.clearSelection()\n    //     } else if (!selectionManagerRef.current.isSelecting()) {\n    //       selectionManagerRef.current.saveSelection(aiResponseRef.current)\n    //     }\n    //   }\n    // }, [isStreaming])\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 在DOM更新后恢复选择状态\n        if (selectionManagerRef.current && aiResponseRef.current && !isStreaming && isComplete) {\n            setTimeout(()=>{\n                var _selectionManagerRef_current;\n                (_selectionManagerRef_current = selectionManagerRef.current) === null || _selectionManagerRef_current === void 0 ? void 0 : _selectionManagerRef_current.restoreSelection(aiResponseRef.current);\n            }, 50) // 短延迟确保DOM更新完成\n            ;\n        }\n    }, [\n        isStreaming,\n        isComplete\n    ]) // 🔧 关键修复：移除responseContent依赖，避免持续重新渲染\n    ;\n    // 🔧 简化的滚动状态管理 - 参考StreamingResponse的简单模式\n    const scrollStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        shouldAutoScroll: true,\n        lastContentLength: 0\n    });\n    // 🔧 简化的滚动控制 - 参考StreamingResponse的直接滚动方式\n    const scrollToBottom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (chatContainerRef.current) {\n            chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n        }\n    }, []);\n    // 🔧 添加流式响应状态监控 - 移除responseContent.length依赖，避免频繁重渲染\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83C\\uDF0A 流式响应状态变化:\", {\n            isStreaming,\n            isComplete,\n            responseContentLength: responseContentRef.current.length,\n            timestamp: Date.now()\n        });\n    }, [\n        isStreaming,\n        isComplete\n    ]);\n    // 🔧 检测用户是否在底部附近\n    const isUserNearBottom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!chatContainerRef.current) return false;\n        const container = chatContainerRef.current;\n        return container.scrollTop + container.clientHeight >= container.scrollHeight - 100;\n    }, []);\n    // 🔧 聊天历史变化时的滚动控制 - 简化版本，参考StreamingResponse\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (chatContainerRef.current && chatHistory.length > 0) {\n            // 简化逻辑：如果用户在底部或启用自动滚动，直接滚动到底部\n            if (isUserNearBottom() || scrollStateRef.current.shouldAutoScroll) {\n                chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n            }\n        }\n    }, [\n        chatHistory.length,\n        isUserNearBottom\n    ]);\n    // 🔧 流式响应时的实时滚动 - 简化版本，参考StreamingResponse的直接模式\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isStreaming && responseContentRef.current && chatContainerRef.current) {\n            const currentLength = responseContentRef.current.length;\n            // 简化逻辑：内容增加时，如果用户在底部就直接滚动\n            if (currentLength > scrollStateRef.current.lastContentLength) {\n                scrollStateRef.current.lastContentLength = currentLength;\n                if (isUserNearBottom() || scrollStateRef.current.shouldAutoScroll) {\n                    // 直接滚动，无需额外的函数调用\n                    chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;\n                }\n            }\n        }\n    }, [\n        isStreaming,\n        isUserNearBottom\n    ]) // 🔧 简化依赖项\n    ;\n    // 🔧 用户滚动事件处理 - 智能检测用户滚动意图\n    const handleUserScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (chatContainerRef.current) {\n            // 当用户手动滚动时，根据位置决定是否启用自动滚动\n            scrollStateRef.current.shouldAutoScroll = isUserNearBottom();\n        }\n    }, [\n        isUserNearBottom\n    ]);\n    // 处理会话切换\n    const handleSessionSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (sessionId)=>{\n        try {\n            console.log(\"\\uD83D\\uDD04 开始切换会话:\", sessionId);\n            // 🔧 关键修复：在切换会话前，先清理当前AI响应状态\n            // 这是解决会话切换时AI响应残留问题的核心修复\n            console.log(\"\\uD83E\\uDDF9 清理AI响应状态...\");\n            // 通知父组件清理AI响应状态\n            // 这里需要父组件提供清理回调函数\n            if (onClearAIResponse) {\n                onClearAIResponse();\n            }\n            setCurrentSessionId(sessionId);\n            // 加载选中会话的历史记录\n            const sessionHistory = await chatHistoryService.loadHistory(sessionId);\n            setChatHistory(sessionHistory);\n            // 通知父组件对话历史变化\n            if (onChatHistoryChange) {\n                onChatHistoryChange(sessionHistory);\n            }\n            console.log(\"✅ 会话已切换:\", sessionId, \"历史记录:\", sessionHistory.length, \"条\");\n            console.log(\"✅ AI响应状态已清理\");\n        } catch (error) {\n            console.error(\"❌ 切换会话失败:\", error);\n        }\n    }, [\n        chatHistoryService,\n        onChatHistoryChange,\n        onClearAIResponse\n    ]);\n    // 处理文件关联变化\n    const handleFilesChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (newFiles)=>{\n        try {\n            console.log(\"\\uD83D\\uDD25 ChatInterface - 接收到文件关联变化:\", newFiles);\n            console.log(\"\\uD83D\\uDD25 ChatInterface - 当前associatedFiles:\", associatedFiles);\n            // 保存到ChatHistoryService\n            await chatHistoryService.saveCurrentFileAssociations(newFiles);\n            // 通知父组件\n            console.log(\"\\uD83D\\uDD25 ChatInterface - 调用父组件onFilesChange:\", newFiles);\n            onFilesChange(newFiles);\n            console.log(\"✅ 文件关联已更新:\", newFiles.length, \"个文件\");\n        } catch (error) {\n            console.error(\"❌ 保存文件关联失败:\", error);\n        }\n    }, [\n        chatHistoryService,\n        onFilesChange,\n        associatedFiles\n    ]);\n    // 处理单条消息插入到编辑器\n    const handleInsertMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageContent)=>{\n        console.log(\"\\uD83D\\uDD04 尝试插入消息内容到编辑器:\", messageContent.substring(0, 100) + \"...\");\n        if (onMessageContentInsert) {\n            console.log(\"✅ 使用消息内容插入弹窗\");\n            // 使用专门的消息内容插入接口，会显示插入弹窗\n            onMessageContentInsert(messageContent);\n        } else if (onContentInsert) {\n            console.log(\"✅ 直接使用onContentInsert接口插入内容\");\n            // 直接使用onContentInsert插入特定消息内容\n            onContentInsert(messageContent, {\n                position: \"cursor\",\n                addNewlines: true // 添加新行\n            });\n        } else if (onInsertToEditor) {\n            console.log(\"⚠️ 使用降级插入方法\");\n            // 创建一个临时的插入函数，直接操作编辑器\n            try {\n                // 尝试直接访问编辑器并插入内容\n                const activeEditor = document.querySelector('textarea, [contenteditable=\"true\"]');\n                if (activeEditor) {\n                    if (activeEditor instanceof HTMLTextAreaElement) {\n                        // 处理textarea\n                        const start = activeEditor.selectionStart || 0;\n                        const end = activeEditor.selectionEnd || 0;\n                        const currentValue = activeEditor.value;\n                        const newValue = currentValue.slice(0, start) + \"\\n\" + messageContent + \"\\n\" + currentValue.slice(end);\n                        activeEditor.value = newValue;\n                        activeEditor.selectionStart = activeEditor.selectionEnd = start + messageContent.length + 2;\n                        // 触发change事件\n                        const event = new Event(\"input\", {\n                            bubbles: true\n                        });\n                        activeEditor.dispatchEvent(event);\n                        console.log(\"✅ 直接插入到textarea成功\");\n                    } else if (activeEditor.contentEditable === \"true\") {\n                        // 处理contenteditable元素\n                        const selection = window.getSelection();\n                        if (selection && selection.rangeCount > 0) {\n                            const range = selection.getRangeAt(0);\n                            range.deleteContents();\n                            const textNode = document.createTextNode(\"\\n\" + messageContent + \"\\n\");\n                            range.insertNode(textNode);\n                            // 移动光标到插入内容后\n                            range.setStartAfter(textNode);\n                            range.setEndAfter(textNode);\n                            selection.removeAllRanges();\n                            selection.addRange(range);\n                            console.log(\"✅ 直接插入到contenteditable成功\");\n                        }\n                    }\n                } else {\n                    // 如果找不到编辑器，使用原有方法\n                    console.log(\"⚠️ 未找到编辑器元素，使用原有插入方法\");\n                    onInsertToEditor();\n                }\n            } catch (error) {\n                console.error(\"❌ 直接插入失败，使用原有方法:\", error);\n                onInsertToEditor();\n            }\n        } else {\n            console.warn(\"⚠️ 没有可用的插入接口\");\n        }\n    }, [\n        onMessageContentInsert,\n        onContentInsert,\n        onInsertToEditor\n    ]);\n    // 处理重新生成AI消息\n    const handleRegenerateMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageIndex)=>{\n        // 找到该AI消息前面的用户消息\n        const messages = chatHistory.slice(0, messageIndex);\n        const lastUserMessage = messages.reverse().find((msg)=>msg.type === \"user\");\n        if (lastUserMessage) {\n            console.log(\"\\uD83D\\uDD04 重新生成AI消息，基于用户消息:\", lastUserMessage.content.substring(0, 50));\n            console.log(\"\\uD83D\\uDCCA 历史消息限制：只包含索引 0-\".concat(messageIndex - 1, \" 的消息\"));\n            // 删除从当前AI消息开始的所有后续消息\n            const newHistory = chatHistory.slice(0, messageIndex);\n            setChatHistory(newHistory);\n            // 保存更新后的历史记录\n            chatHistoryService.saveCurrentHistory(newHistory);\n            // 重新发送用户消息，传递目标消息索引以限制历史消息范围\n            onSendMessage(lastUserMessage.content, messageIndex);\n            // 关闭工具面板\n            setActiveMessageTools(null);\n        }\n    }, [\n        chatHistory,\n        onSendMessage,\n        chatHistoryService\n    ]);\n    // 处理编辑用户消息\n    const handleEditMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageId, currentContent)=>{\n        setEditingMessageId(messageId);\n        setEditingContent(currentContent);\n        setActiveMessageTools(null);\n    }, []);\n    // 处理保存编辑的消息\n    const handleSaveEditedMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageId, newContent)=>{\n        const messageIndex = chatHistory.findIndex((msg)=>msg.id === messageId);\n        if (messageIndex === -1) return;\n        // 更新消息内容\n        const updatedMessage = {\n            ...chatHistory[messageIndex],\n            content: newContent,\n            updatedAt: Date.now()\n        };\n        // 删除该消息之后的所有消息\n        const newHistory = [\n            ...chatHistory.slice(0, messageIndex),\n            updatedMessage\n        ];\n        setChatHistory(newHistory);\n        // 保存更新后的历史记录\n        chatHistoryService.saveCurrentHistory(newHistory);\n        // 重新发送编辑后的消息\n        onSendMessage(newContent);\n        // 清除编辑状态\n        setEditingMessageId(null);\n        setEditingContent(\"\");\n        console.log(\"✅ 消息已编辑并重发:\", newContent.substring(0, 50));\n    }, [\n        chatHistory,\n        onSendMessage,\n        chatHistoryService\n    ]);\n    // 处理多项对比\n    const handleMultipleComparison = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((messageIndex)=>{\n        // 找到该AI消息前面的用户消息\n        const messages = chatHistory.slice(0, messageIndex);\n        const lastUserMessage = messages.reverse().find((msg)=>msg.type === \"user\");\n        if (lastUserMessage) {\n            console.log(\"\\uD83D\\uDD04 开启多项对比，基于用户消息:\", lastUserMessage.content.substring(0, 50));\n            console.log(\"\\uD83C\\uDFAF 记录触发消息索引:\", messageIndex);\n            // 设置对比内容并打开弹窗\n            setComparisonMessageContent(lastUserMessage.content);\n            setComparisonTriggerMessageIndex(messageIndex) // 记录触发的消息索引\n            ;\n            setShowMultipleComparison(true);\n            // 关闭工具面板\n            setActiveMessageTools(null);\n        }\n    }, [\n        chatHistory\n    ]);\n    // 处理多项对比结果选择\n    const handleComparisonResultSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((selectedResult)=>{\n        console.log(\"\\uD83C\\uDFAF 多项对比结果选择，替换最后一条AI消息:\", selectedResult.substring(0, 100) + \"...\");\n        // 替换最后一条AI消息的内容\n        setChatHistory((prev)=>{\n            const newHistory = [\n                ...prev\n            ];\n            // 从后往前找最后一条AI消息\n            for(let i = newHistory.length - 1; i >= 0; i--){\n                if (newHistory[i].type === \"assistant\") {\n                    // 替换这条AI消息的内容\n                    newHistory[i] = {\n                        ...newHistory[i],\n                        content: selectedResult,\n                        timestamp: Date.now() // 更新时间戳\n                    };\n                    console.log(\"✅ 已替换AI消息:\", newHistory[i].id);\n                    break;\n                }\n            }\n            // 保存更新后的历史记录\n            chatHistoryService.saveCurrentHistory(newHistory).catch((error)=>{\n                console.error(\"❌ 保存替换后的消息失败:\", error);\n            });\n            return newHistory;\n        });\n        // 关闭弹窗\n        setShowMultipleComparison(false);\n        setComparisonMessageContent(\"\");\n    }, [\n        chatHistoryService\n    ]);\n    // 处理取消编辑\n    const handleCancelEdit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setEditingMessageId(null);\n        setEditingContent(\"\");\n    }, []);\n    // 获取可用模型列表\n    const loadAvailableModels = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!(currentConfig === null || currentConfig === void 0 ? void 0 : currentConfig.apiKey)) {\n            console.warn(\"⚠️ 没有可用的API配置\");\n            return;\n        }\n        setIsLoadingModels(true);\n        try {\n            const aiService = _services_aiService__WEBPACK_IMPORTED_MODULE_5__.AIService.getInstance();\n            const result = await aiService.getAvailableModels(currentConfig);\n            if (result.success && result.data && result.data.length > 0) {\n                // 转换为模型对象格式\n                const models = result.data.map((modelId)=>({\n                        id: modelId,\n                        name: modelId.toUpperCase().replace(/-/g, \" \"),\n                        description: getModelDescription(modelId)\n                    }));\n                setAvailableModels(models);\n                console.log(\"✅ 成功获取模型列表:\", models.length, \"个模型\");\n            } else {\n                console.warn(\"⚠️ 获取模型列表失败\");\n                setAvailableModels([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取模型列表失败:\", error);\n            setAvailableModels([]);\n        } finally{\n            setIsLoadingModels(false);\n        }\n    }, [\n        currentConfig\n    ]);\n    // 获取模型描述\n    const getModelDescription = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const lowerModelId = modelId.toLowerCase();\n        // GPT系列模型\n        if (lowerModelId.includes(\"gpt-4\")) {\n            if (lowerModelId.includes(\"turbo\")) {\n                return \"更快的GPT-4，性价比更高\";\n            }\n            return \"最强大的模型，适合复杂任务\";\n        } else if (lowerModelId.includes(\"gpt-3.5\")) {\n            return \"快速且经济的选择\";\n        } else if (lowerModelId.includes(\"claude\")) {\n            return \"Anthropic Claude，强大的推理能力\";\n        } else if (lowerModelId.includes(\"gemini\")) {\n            return \"Google Gemini，多模态能力强\";\n        }\n        // 其他模型\n        return \"可用的AI模型: \".concat(modelId);\n    }, []);\n    // 点击外部关闭消息工具面板\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (activeMessageTools) {\n                // 检查点击是否在工具面板外部\n                const target = event.target;\n                if (!target.closest(\".message-tools-panel\") && !target.closest(\".message-tools-button\")) {\n                    setActiveMessageTools(null);\n                }\n            }\n            // 点击外部取消编辑状态（除非点击在编辑区域内）\n            if (editingMessageId) {\n                const target = event.target;\n                if (!target.closest(\"textarea\") && !target.closest(\"button\")) {\n                    handleCancelEdit();\n                }\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        activeMessageTools,\n        editingMessageId,\n        handleCancelEdit\n    ]);\n    // 暴露方法给父组件\n    react__WEBPACK_IMPORTED_MODULE_1___default().useImperativeHandle(ref, ()=>({\n            handleSessionSelect\n        }), [\n        handleSessionSelect\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            (associatedFiles.length > 0 || focusedFile) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 bg-gray-800/20 border-b border-amber-500/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2 cursor-pointer hover:bg-gray-700/20 rounded-md p-1 -m-1 transition-all duration-200\",\n                        onClick: ()=>setIsFileAssociationCollapsed(!isFileAssociationCollapsed),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                width: \"14\",\n                                height: \"14\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                className: \"text-amber-400 transition-transform duration-200 \".concat(isFileAssociationCollapsed ? \"rotate-0\" : \"rotate-90\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1695,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1688,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                width: \"14\",\n                                height: \"14\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"currentColor\",\n                                className: \"text-amber-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1698,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1697,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-amber-200 font-handwritten\",\n                                children: \"关联文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1700,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-400\",\n                                children: [\n                                    \"(\",\n                                    focusedFile ? associatedFiles.includes(focusedFile) ? associatedFiles.length : associatedFiles.length + 1 : associatedFiles.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1701,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500 ml-auto\",\n                                children: isFileAssociationCollapsed ? \"点击展开\" : \"点击折叠\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1704,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 1684,\n                        columnNumber: 11\n                    }, this),\n                    !isFileAssociationCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2 mt-2\",\n                        children: (()=>{\n                            // 创建文件显示列表，聚焦文件优先\n                            const allFiles = new Set([\n                                ...focusedFile ? [\n                                    focusedFile\n                                ] : [],\n                                ...associatedFiles\n                            ]);\n                            return Array.from(allFiles).map((fileId, index)=>{\n                                const filePath = filePaths.get(fileId) || \"加载中... (\".concat(fileId.substring(0, 8), \"...)\");\n                                const isFocused = focusedFile === fileId;\n                                const isManuallyAssociated = associatedFiles.includes(fileId);\n                                // 确定文件类型和样式\n                                const fileType = isFocused ? \"auto\" : \"manual\";\n                                const styleConfig = {\n                                    auto: {\n                                        className: \"bg-blue-500/20 text-blue-200 border-blue-500/50\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"12\",\n                                            height: \"12\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1731,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1730,\n                                            columnNumber: 25\n                                        }, this),\n                                        label: \"自动\",\n                                        title: \"当前编辑文件（自动关联）\"\n                                    },\n                                    manual: {\n                                        className: \"bg-green-500/20 text-green-200 border-green-500/50\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"12\",\n                                            height: \"12\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1741,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1740,\n                                            columnNumber: 25\n                                        }, this),\n                                        label: \"手动\",\n                                        title: \"手动关联文件\"\n                                    }\n                                };\n                                const config = styleConfig[fileType];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 px-2 py-1 \".concat(config.className, \" border rounded-md text-xs\"),\n                                    title: config.title,\n                                    children: [\n                                        config.icon,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs opacity-75 font-handwritten\",\n                                            children: config.label\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1758,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-mono\",\n                                            title: filePath,\n                                            children: filePath\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1759,\n                                            columnNumber: 23\n                                        }, this),\n                                        isManuallyAssociated && !isFocused && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                const newFiles = associatedFiles.filter((id)=>id !== fileId);\n                                                handleFilesChange(newFiles);\n                                            },\n                                            className: \"ml-1 p-0.5 text-green-300 hover:text-red-400 hover:bg-red-500/10 rounded transition-all duration-200\",\n                                            title: \"移除手动关联文件\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"10\",\n                                                height: \"10\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 1772,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1771,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1763,\n                                            columnNumber: 25\n                                        }, this),\n                                        isFocused && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-1 px-1 py-0.5 bg-blue-600/30 text-blue-100 rounded text-xs font-handwritten\",\n                                            children: \"当前\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1779,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, fileId, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1752,\n                                    columnNumber: 21\n                                }, this);\n                            });\n                        })()\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 1710,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 1683,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatContainerRef,\n                className: \"flex-1 overflow-y-auto custom-scrollbar p-4 space-y-4\",\n                onScroll: handleUserScroll,\n                children: [\n                    chatHistory.length === 0 && !isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center h-full text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto mb-4 rounded-lg bg-amber-500/20 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"32\",\n                                    height: \"32\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    className: \"text-amber-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 1802,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1801,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1800,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-handwritten text-amber-200 mb-2\",\n                                children: \"开始对话\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1805,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm font-handwritten\",\n                                children: currentConfig ? \"输入消息开始与AI对话\" : \"请先在配置页面设置API Key\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1808,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 1799,\n                        columnNumber: 11\n                    }, this),\n                    chatHistory.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex \".concat(message.type === \"user\" ? \"justify-end\" : \"justify-start\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-[80%] p-3 rounded-lg \".concat(message.type === \"user\" ? \"bg-blue-500/20 text-blue-200 border border-blue-500/50\" : \"bg-green-500/20 text-green-200 border border-green-500/50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4\",\n                                                        children: message.type === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1831,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1830,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M17.5,12A1.5,1.5 0 0,1 16,10.5A1.5,1.5 0 0,1 17.5,9A1.5,1.5 0 0,1 19,10.5A1.5,1.5 0 0,1 17.5,12M10,10.5C10,11.3 9.3,12 8.5,12S7,11.3 7,10.5C7,9.7 7.7,9 8.5,9S10,9.7 10,10.5M12,14C9.8,14 8,15.8 8,18V20H16V18C16,15.8 14.2,14 12,14M12,4C14.2,4 16,5.8 16,8S14.2,12 12,12S8,10.2 8,8S9.8,4 12,4M12,2A6,6 0 0,0 6,8C6,11.3 8.7,14 12,14S18,11.3 18,8A6,6 0 0,0 12,2Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1835,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1834,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1828,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-handwritten\",\n                                                        children: message.type === \"user\" ? \"你\" : \"AI助手\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1839,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs opacity-60\",\n                                                        children: new Date(message.timestamp).toLocaleTimeString()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1842,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1827,\n                                                columnNumber: 17\n                                            }, this),\n                                            message.type === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveMessageTools(activeMessageTools === message.id ? null : message.id),\n                                                        className: \"message-tools-button p-1 rounded-md bg-blue-400/10 hover:bg-blue-400/20 border border-blue-400/20 hover:border-blue-400/40 transition-all duration-200 text-blue-300 hover:text-blue-200 opacity-90 hover:opacity-100\",\n                                                        title: \"消息工具\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"12\",\n                                                            height: \"12\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1856,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1855,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1850,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    activeMessageTools === message.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"message-tools-panel absolute right-0 top-full mt-1 bg-gray-800 border border-blue-500/30 rounded-lg shadow-lg z-50 min-w-[120px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                handleEditMessage(message.id, message.content);\n                                                            },\n                                                            className: \"w-full px-3 py-2 text-left text-sm text-blue-200 hover:bg-blue-500/20 transition-all duration-200 font-handwritten flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    width: \"14\",\n                                                                    height: \"14\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1870,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 1869,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"编辑重发\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1863,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1862,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1849,\n                                                columnNumber: 19\n                                            }, this),\n                                            message.type === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setActiveMessageTools(activeMessageTools === message.id ? null : message.id),\n                                                        className: \"message-tools-button p-1 rounded-md bg-amber-400/10 hover:bg-amber-400/20 border border-amber-400/20 hover:border-amber-400/40 transition-all duration-200 text-amber-300 hover:text-amber-200 opacity-90 hover:opacity-100\",\n                                                        title: \"消息工具\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"12\",\n                                                            height: \"12\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1888,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1887,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1882,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    activeMessageTools === message.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"message-tools-panel absolute right-0 top-full mt-1 bg-gray-800 border border-green-500/30 rounded-lg shadow-lg z-50 min-w-[140px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    handleInsertMessage(message.content);\n                                                                    setActiveMessageTools(null);\n                                                                },\n                                                                className: \"w-full px-3 py-2 text-left text-sm text-green-200 hover:bg-green-500/20 transition-all duration-200 font-handwritten border-b border-green-500/20 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        width: \"14\",\n                                                                        height: \"14\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 1903,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1902,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"插入到编辑器\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1895,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    const messageIndex = chatHistory.findIndex((msg)=>msg.id === message.id);\n                                                                    if (messageIndex !== -1) {\n                                                                        handleRegenerateMessage(messageIndex);\n                                                                    }\n                                                                },\n                                                                className: \"w-full px-3 py-2 text-left text-sm text-amber-200 hover:bg-amber-500/20 transition-all duration-200 font-handwritten flex items-center gap-2 border-b border-amber-500/20\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        width: \"14\",\n                                                                        height: \"14\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 1917,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1916,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"重新生成\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1907,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    const messageIndex = chatHistory.findIndex((msg)=>msg.id === message.id);\n                                                                    if (messageIndex !== -1) {\n                                                                        handleMultipleComparison(messageIndex);\n                                                                    }\n                                                                },\n                                                                className: \"w-full px-3 py-2 text-left text-sm text-purple-200 hover:bg-purple-500/20 transition-all duration-200 font-handwritten flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        width: \"14\",\n                                                                        height: \"14\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V6.5H5V5H19M19,19H5V8.5H19V19Z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 1931,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 1930,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"多项对比\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 1921,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 1894,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 1881,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 1826,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: editingMessageId === message.id ? // 编辑模式\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: editingContent,\n                                                    onChange: (e)=>setEditingContent(e.target.value),\n                                                    className: \"w-full p-2 bg-gray-700 border border-blue-500/30 rounded-md text-blue-200 font-handwritten resize-none focus:outline-none focus:border-blue-500/60\",\n                                                    rows: Math.max(2, editingContent.split(\"\\n\").length),\n                                                    placeholder: \"编辑消息内容...\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 1945,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 justify-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleCancelEdit,\n                                                            className: \"px-3 py-1 text-xs bg-gray-600 hover:bg-gray-500 text-gray-200 rounded-md transition-all duration-200 font-handwritten\",\n                                                            children: \"取消\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1954,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSaveEditedMessage(message.id, editingContent),\n                                                            className: \"px-3 py-1 text-xs bg-blue-600 hover:bg-blue-500 text-white rounded-md transition-all duration-200 font-handwritten\",\n                                                            disabled: !editingContent.trim(),\n                                                            children: \"保存并重发\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 1960,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 1953,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1944,\n                                            columnNumber: 19\n                                        }, this) : // 正常显示模式\n                                        renderMessageContentInternal(message.content)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 1941,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1820,\n                                columnNumber: 13\n                            }, this)\n                        }, message.id, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                            lineNumber: 1816,\n                            columnNumber: 11\n                        }, this)),\n                    messageLayers.map((layer)=>{\n                        var _layer_metadata;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full\",\n                            children: layer.type === \"helper-response\" && ((_layer_metadata = layer.metadata) === null || _layer_metadata === void 0 ? void 0 : _layer_metadata.humanBlock) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_HelperResponseLayer__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                messageId: layer.id,\n                                humanBlockContent: layer.metadata.humanBlock,\n                                onIntegrationComplete: handleIntegrationComplete,\n                                onMainAIResponse: handleMainAIResponse,\n                                onError: handleHelperError,\n                                className: \"my-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 1982,\n                                columnNumber: 15\n                            }, this)\n                        }, layer.id, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                            lineNumber: 1980,\n                            columnNumber: 11\n                        }, this);\n                    }),\n                    (isLoading || isStreaming && !isComplete) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-[80%] p-3 bg-green-500/20 text-green-200 border border-green-500/50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M17.5,12A1.5,1.5 0 0,1 16,10.5A1.5,1.5 0 0,1 17.5,9A1.5,1.5 0 0,1 19,10.5A1.5,1.5 0 0,1 17.5,12M10,10.5C10,11.3 9.3,12 8.5,12S7,11.3 7,10.5C7,9.7 7.7,9 8.5,9S10,9.7 10,10.5M12,14C9.8,14 8,15.8 8,18V20H16V18C16,15.8 14.2,14 12,14M12,4C14.2,4 16,5.8 16,8S14.2,12 12,12S8,10.2 8,8S9.8,4 12,4M12,2A6,6 0 0,0 6,8C6,11.3 8.7,14 12,14S18,11.3 18,8A6,6 0 0,0 12,2Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2001,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2000,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 1999,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-handwritten\",\n                                            children: \"AI助手\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2004,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs opacity-60\",\n                                            children: isLoading && !responseContent ? \"正在思考...\" : isStreaming ? \"正在输入...\" : \"刚刚\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2005,\n                                            columnNumber: 17\n                                        }, this),\n                                        isStreaming && onStop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onStop,\n                                            className: \"ml-auto p-1 text-gray-400 hover:text-red-400 hover:bg-red-500/10 rounded transition-all duration-200\",\n                                            title: \"停止生成\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"12\",\n                                                height: \"12\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M6,6H18V18H6V6Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2016,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2015,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2010,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 1998,\n                                    columnNumber: 15\n                                }, this),\n                                responseContent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: aiResponseRef,\n                                    className: \"text-sm\",\n                                    children: [\n                                        renderMessageContentInternal(responseContent),\n                                        isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"animate-pulse text-amber-400 ml-1\",\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2027,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2024,\n                                    columnNumber: 17\n                                }, this) : /* 等待动画 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-amber-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-amber-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"0ms\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2034,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-amber-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"150ms\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2035,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-amber-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"300ms\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2036,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2033,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2032,\n                                    columnNumber: 17\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-2 bg-red-500/20 border border-red-500/50 rounded text-red-200 text-xs\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2045,\n                                                columnNumber: 21\n                                            }, this),\n                                            onRetry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onRetry,\n                                                className: \"ml-2 px-2 py-1 bg-red-500/30 hover:bg-red-500/50 rounded text-xs transition-colors\",\n                                                children: \"重试\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2047,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2044,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2043,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                            lineNumber: 1997,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 1996,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 1793,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-800/30 border-t border-amber-500/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        ref: textareaRef,\n                                        value: inputMessage,\n                                        onChange: handleInputChange,\n                                        onKeyDown: handleKeyDown,\n                                        placeholder: currentConfig ? \"输入消息...\" : \"请先配置API Key\",\n                                        disabled: !currentConfig || isLoading,\n                                        className: \"w-full min-h-[40px] max-h-[120px] p-3 pr-12 bg-gray-800 border border-gray-600 rounded-lg text-amber-200 placeholder-gray-500 resize-none focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 disabled:opacity-50 transition-all duration-200\",\n                                        rows: 1\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2066,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSend,\n                                        disabled: !inputMessage.trim() || !currentConfig || isLoading,\n                                        className: \"absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-md transition-all duration-200 \".concat(inputMessage.trim() && currentConfig && !isLoading ? \"text-amber-400 hover:text-amber-300 hover:bg-amber-500/20\" : \"text-gray-500 cursor-not-allowed\"),\n                                        title: isLoading ? \"发送中...\" : \"Enter 发送\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"16\",\n                                            height: \"16\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            className: \"animate-spin\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2089,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2088,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"16\",\n                                            height: \"16\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M2,21L23,12L2,3V10L17,12L2,14V21Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2093,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2092,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2078,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2065,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mt-3 px-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4 text-xs text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"12\",\n                                                        height: \"12\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"currentColor\",\n                                                        className: \"opacity-60\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2104,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M6.25,7.72H10V10.5H14V13.5H10V16.25H6.25V13.5H3.5V10.5H6.25V7.72Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2105,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2103,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Shift+Enter 换行\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2102,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"12\",\n                                                        height: \"12\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"currentColor\",\n                                                        className: \"opacity-60\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,19H5V5H19V19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2111,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M6.25,7.72H17.75V10.5H6.25V7.72Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2112,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2110,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Enter 发送\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2109,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2101,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: [\n                                                    inputMessage.length,\n                                                    \" 字符\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2119,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative tools-panel-container\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowToolsPanel(!showToolsPanel),\n                                                        className: \"group flex items-center gap-1 px-2 py-1 rounded-md text-xs transition-all duration-200 \".concat(showToolsPanel ? \"text-amber-300 bg-amber-500/20 border border-amber-500/40\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 border border-transparent\"),\n                                                        title: \"工具\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                width: \"12\",\n                                                                height: \"12\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: \"currentColor\",\n                                                                className: \"transition-transform duration-200 group-hover:rotate-12\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 2132,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2131,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"工具\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2134,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2123,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    showToolsPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute bottom-full right-0 mb-3 bg-gray-800/95 backdrop-blur-sm border border-amber-500/30 rounded-xl shadow-2xl p-2 z-50 animate-in fade-in-0 zoom-in-95 duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-amber-200 font-medium mb-2 px-2 py-1\",\n                                                                children: \"快捷工具\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2141,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setShowFileAssociation(true);\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        className: \"p-2 bg-blue-500/20 text-blue-200 border border-blue-500/50 rounded-lg hover:bg-blue-500/30 transition-all duration-200\",\n                                                                        title: \"关联文件\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                lineNumber: 2154,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2153,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2145,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setShowMediaUploader(!showMediaUploader);\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        className: \"p-2 bg-purple-500/20 text-purple-200 border border-purple-500/50 rounded-lg hover:bg-purple-500/30 transition-all duration-200\",\n                                                                        title: \"上传媒体文件\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M9,16V10H5L12,3L19,10H15V16H9M5,20V18H19V20H5Z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                lineNumber: 2168,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2167,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2159,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setShowPromptManager(true);\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        disabled: isLoading,\n                                                                        className: \"p-2 rounded-lg transition-all duration-200 \".concat(activePromptConfig ? \"bg-amber-500/30 text-amber-200 border border-amber-500/70\" : \"bg-amber-500/20 text-amber-200 border border-amber-500/50 hover:bg-amber-500/30\"),\n                                                                        title: activePromptConfig ? \"当前配置: \".concat(activePromptConfig.name) : \"管理提示词模板\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                    lineNumber: 2186,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M12,15.39L8.24,17.66L9.23,13.38L5.91,10.5L10.29,10.13L12,6.09L13.71,10.13L18.09,10.5L14.77,13.38L15.76,17.66M22,9.24L14.81,8.63L12,2L9.19,8.63L2,9.24L7.45,13.97L5.82,21L12,17.27L18.18,21L16.54,13.97L22,9.24Z\",\n                                                                                    opacity: \"0.6\",\n                                                                                    transform: \"scale(0.4) translate(24, 24)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                    lineNumber: 2187,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2185,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2173,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            if (onShowAudienceSettings) {\n                                                                                onShowAudienceSettings();\n                                                                            }\n                                                                            setShowToolsPanel(false);\n                                                                        },\n                                                                        className: \"p-2 bg-green-500/20 text-green-200 border border-green-500/50 rounded-lg hover:bg-green-500/30 transition-all duration-200\",\n                                                                        title: \"设置受众\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M16,4C18.11,4 19.81,5.69 19.81,7.8C19.81,9.91 18.11,11.6 16,11.6C13.89,11.6 12.2,9.91 12.2,7.8C12.2,5.69 13.89,4 16,4M16,13.4C18.67,13.4 24,14.73 24,17.4V20H8V17.4C8,14.73 13.33,13.4 16,13.4M8.8,11.6C6.69,11.6 5,9.91 5,7.8C5,5.69 6.69,4 8.8,4C9.13,4 9.45,4.05 9.75,4.14C9.28,5.16 9,6.3 9,7.5C9,8.7 9.28,9.84 9.75,10.86C9.45,10.95 9.13,11 8.8,11.6M8.8,13.4C7.12,13.4 3.5,14.26 3.5,17.4V20H6.5V17.4C6.5,16.55 7.45,15.1 8.8,13.4Z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                                lineNumber: 2206,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 2205,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                        lineNumber: 2195,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2143,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-full right-4 -mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-gray-800 border-r border-b border-amber-500/30 transform rotate-45\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 2213,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2212,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2139,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2122,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2118,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2100,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 2064,\n                        columnNumber: 9\n                    }, this),\n                    showMediaUploader && artworkId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-4 bg-gray-800/50 border border-purple-500/30 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium text-purple-200\",\n                                        children: \"媒体文件上传\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2226,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowMediaUploader(false),\n                                        className: \"text-gray-400 hover:text-gray-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"16\",\n                                            height: \"16\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                lineNumber: 2232,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2231,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2227,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2225,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MediaUploader__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                onUploadSuccess: handleMediaUploadSuccess,\n                                onError: handleMediaUploadError,\n                                artworkId: artworkId,\n                                className: \"mb-4\",\n                                maxFiles: 5\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2236,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 2224,\n                        columnNumber: 11\n                    }, this),\n                    uploadedMedia.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowMediaList(!showMediaList),\n                                className: \"flex items-center gap-2 px-3 py-2 bg-green-500/20 text-green-200 border border-green-500/50 rounded-md hover:bg-green-500/30 transition-all duration-200 font-handwritten text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"16\",\n                                        height: \"16\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2254,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2253,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"已上传 \",\n                                            uploadedMedia.length,\n                                            \" 个媒体文件\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"14\",\n                                        height: \"14\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"currentColor\",\n                                        className: \"transition-transform duration-200 \".concat(showMediaList ? \"rotate-180\" : \"\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2264,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                        lineNumber: 2257,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2249,\n                                columnNumber: 13\n                            }, this),\n                            showMediaList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 p-4 bg-gray-800/50 border border-green-500/30 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-3\",\n                                    children: uploadedMedia.map((mediaFile)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 p-3 bg-gray-700/50 rounded-lg border border-gray-600/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: mediaFile.type === \"image\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"20\",\n                                                            height: \"20\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            className: \"text-blue-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2282,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2281,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2280,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"20\",\n                                                            height: \"20\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            className: \"text-purple-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2288,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2287,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2286,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2278,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-200 truncate\",\n                                                            children: mediaFile.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2296,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: [\n                                                                mediaFile.mimeType,\n                                                                \" • \",\n                                                                (mediaFile.size / 1024 / 1024).toFixed(2),\n                                                                \" MB\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2299,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: new Date(mediaFile.uploadedAt).toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2302,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2295,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>removeMediaFile(mediaFile.id),\n                                                        className: \"w-8 h-8 bg-red-500/20 text-red-400 rounded-lg hover:bg-red-500/30 transition-colors flex items-center justify-center\",\n                                                        title: \"删除文件\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"14\",\n                                                            height: \"14\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                                lineNumber: 2315,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                            lineNumber: 2314,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                        lineNumber: 2309,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                                    lineNumber: 2308,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, mediaFile.id, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                            lineNumber: 2273,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                    lineNumber: 2271,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                                lineNumber: 2270,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                        lineNumber: 2248,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2063,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SessionManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: showSessionManager,\n                onClose: ()=>setShowSessionManager(false),\n                onSessionSelect: handleSessionSelect,\n                currentSessionId: currentSessionId\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2331,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FileAssociationTreeDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showFileAssociation,\n                onClose: ()=>setShowFileAssociation(false),\n                artworkId: artworkId,\n                onFilesChange: handleFilesChange\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2339,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PromptManagerModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isOpen: showPromptManager,\n                onClose: ()=>setShowPromptManager(false),\n                onConfigSelect: handlePromptConfigSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2347,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MultipleComparisonModal__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                isOpen: showMultipleComparison,\n                onClose: ()=>setShowMultipleComparison(false),\n                messageContent: comparisonMessageContent,\n                onResultSelect: handleComparisonResultSelect,\n                onSendMessage: onMultipleComparisonSend || onSendMessage,\n                targetMessageIndex: comparisonTriggerMessageIndex\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n                lineNumber: 2354,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\AIAssistant\\\\ChatInterface.tsx\",\n        lineNumber: 1678,\n        columnNumber: 5\n    }, this);\n}, \"RfxeR6O005vLzWT/JUmeZeKO01c=\")), \"RfxeR6O005vLzWT/JUmeZeKO01c=\");\n_c1 = ChatInterface;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ChatInterface);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatInterface$React.forwardRef\");\n$RefreshReg$(_c1, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AIAssistant/ChatInterface.tsx\n"));

/***/ })

});