"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_protobuf_protobuf_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/protobuf/protobuf.js":
/*!********************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/protobuf/protobuf.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/protobuf/protobuf.ts\nvar namedLiterals = [\"true\", \"false\"];\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"<\", \">\"]\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\"] }\n  ],\n  autoCloseBefore: \".,=}])>' \\n\t\",\n  indentationRules: {\n    increaseIndentPattern: new RegExp(\"^((?!\\\\/\\\\/).)*(\\\\{[^}\\\"'`]*|\\\\([^)\\\"'`]*|\\\\[[^\\\\]\\\"'`]*)$\"),\n    decreaseIndentPattern: new RegExp(\"^((?!.*?\\\\/\\\\*).*\\\\*/)?\\\\s*[\\\\}\\\\]].*$\")\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".proto\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  symbols: /[=><!~?:&|+\\-*/^%]+/,\n  keywords: [\n    \"syntax\",\n    \"import\",\n    \"weak\",\n    \"public\",\n    \"package\",\n    \"option\",\n    \"repeated\",\n    \"oneof\",\n    \"map\",\n    \"reserved\",\n    \"to\",\n    \"max\",\n    \"enum\",\n    \"message\",\n    \"service\",\n    \"rpc\",\n    \"stream\",\n    \"returns\",\n    \"package\",\n    \"optional\",\n    \"true\",\n    \"false\"\n  ],\n  builtinTypes: [\n    \"double\",\n    \"float\",\n    \"int32\",\n    \"int64\",\n    \"uint32\",\n    \"uint64\",\n    \"sint32\",\n    \"sint64\",\n    \"fixed32\",\n    \"fixed64\",\n    \"sfixed32\",\n    \"sfixed64\",\n    \"bool\",\n    \"string\",\n    \"bytes\"\n  ],\n  operators: [\"=\", \"+\", \"-\"],\n  namedLiterals,\n  escapes: `\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\|'|\\\\\\${)`,\n  identifier: /[a-zA-Z]\\w*/,\n  fullIdentifier: /@identifier(?:\\s*\\.\\s*@identifier)*/,\n  optionName: /(?:@identifier|\\(\\s*@fullIdentifier\\s*\\))(?:\\s*\\.\\s*@identifier)*/,\n  messageName: /@identifier/,\n  enumName: /@identifier/,\n  messageType: /\\.?\\s*(?:@identifier\\s*\\.\\s*)*@messageName/,\n  enumType: /\\.?\\s*(?:@identifier\\s*\\.\\s*)*@enumName/,\n  floatLit: /[0-9]+\\s*\\.\\s*[0-9]*(?:@exponent)?|[0-9]+@exponent|\\.[0-9]+(?:@exponent)?/,\n  exponent: /[eE]\\s*[+-]?\\s*[0-9]+/,\n  boolLit: /true\\b|false\\b/,\n  decimalLit: /[1-9][0-9]*/,\n  octalLit: /0[0-7]*/,\n  hexLit: /0[xX][0-9a-fA-F]+/,\n  type: /double|float|int32|int64|uint32|uint64|sint32|sint64|fixed32|fixed64|sfixed32|sfixed64|bool|string|bytes|@messageType|@enumType/,\n  keyType: /int32|int64|uint32|uint64|sint32|sint64|fixed32|fixed64|sfixed32|sfixed64|bool|string/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      [/syntax/, \"keyword\"],\n      [/=/, \"operators\"],\n      [/;/, \"delimiter\"],\n      [\n        /(\")(proto3)(\")/,\n        [\"string.quote\", \"string\", { token: \"string.quote\", switchTo: \"@topLevel.proto3\" }]\n      ],\n      [\n        /(\")(proto2)(\")/,\n        [\"string.quote\", \"string\", { token: \"string.quote\", switchTo: \"@topLevel.proto2\" }]\n      ],\n      [\n        // If no `syntax` provided, regarded as proto2\n        /.*?/,\n        { token: \"\", switchTo: \"@topLevel.proto2\" }\n      ]\n    ],\n    topLevel: [\n      // whitespace\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/=/, \"operators\"],\n      [/[;.]/, \"delimiter\"],\n      [\n        /@fullIdentifier/,\n        {\n          cases: {\n            option: { token: \"keyword\", next: \"@option.$S2\" },\n            enum: { token: \"keyword\", next: \"@enumDecl.$S2\" },\n            message: { token: \"keyword\", next: \"@messageDecl.$S2\" },\n            service: { token: \"keyword\", next: \"@serviceDecl.$S2\" },\n            extend: {\n              cases: {\n                \"$S2==proto2\": { token: \"keyword\", next: \"@extendDecl.$S2\" }\n              }\n            },\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    enumDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@enumBody.$S2\" }]\n    ],\n    enumBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/=/, \"operators\"],\n      [/;/, \"delimiter\"],\n      [/option\\b/, \"keyword\", \"@option.$S2\"],\n      [/@identifier/, \"identifier\"],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    messageDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@messageBody.$S2\" }]\n    ],\n    messageBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/=/, \"operators\"],\n      [/;/, \"delimiter\"],\n      [\n        \"(map)(s*)(<)\",\n        [\"keyword\", \"white\", { token: \"@brackets\", bracket: \"@open\", next: \"@map.$S2\" }]\n      ],\n      [\n        /@identifier/,\n        {\n          cases: {\n            option: { token: \"keyword\", next: \"@option.$S2\" },\n            enum: { token: \"keyword\", next: \"@enumDecl.$S2\" },\n            message: { token: \"keyword\", next: \"@messageDecl.$S2\" },\n            oneof: { token: \"keyword\", next: \"@oneofDecl.$S2\" },\n            extensions: {\n              cases: {\n                \"$S2==proto2\": { token: \"keyword\", next: \"@reserved.$S2\" }\n              }\n            },\n            reserved: { token: \"keyword\", next: \"@reserved.$S2\" },\n            \"(?:repeated|optional)\": { token: \"keyword\", next: \"@field.$S2\" },\n            required: {\n              cases: {\n                \"$S2==proto2\": { token: \"keyword\", next: \"@field.$S2\" }\n              }\n            },\n            \"$S2==proto3\": { token: \"@rematch\", next: \"@field.$S2\" }\n          }\n        }\n      ],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    extendDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@extendBody.$S2\" }]\n    ],\n    extendBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/(?:repeated|optional|required)/, \"keyword\", \"@field.$S2\"],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    options: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/@optionName/, \"annotation\"],\n      [/[()]/, \"annotation.brackets\"],\n      [/=/, \"operator\"],\n      [/\\]/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    option: [\n      { include: \"@whitespace\" },\n      [/@optionName/, \"annotation\"],\n      [/[()]/, \"annotation.brackets\"],\n      [/=/, \"operator\", \"@pop\"]\n    ],\n    oneofDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@oneofBody.$S2\" }]\n    ],\n    oneofBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/(@identifier)(\\s*)(=)/, [\"identifier\", \"white\", \"delimiter\"]],\n      [\n        /@fullIdentifier|\\./,\n        {\n          cases: {\n            \"@builtinTypes\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    reserved: [\n      { include: \"@whitespace\" },\n      [/,/, \"delimiter\"],\n      [/;/, \"delimiter\", \"@pop\"],\n      { include: \"@constant\" },\n      [/to\\b|max\\b/, \"keyword\"]\n    ],\n    map: [\n      { include: \"@whitespace\" },\n      [\n        /@fullIdentifier|\\./,\n        {\n          cases: {\n            \"@builtinTypes\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/,/, \"delimiter\"],\n      [/>/, { token: \"@brackets\", bracket: \"@close\", switchTo: \"identifier\" }]\n    ],\n    field: [\n      { include: \"@whitespace\" },\n      [\n        \"group\",\n        {\n          cases: {\n            \"$S2==proto2\": { token: \"keyword\", switchTo: \"@groupDecl.$S2\" }\n          }\n        }\n      ],\n      [/(@identifier)(\\s*)(=)/, [\"identifier\", \"white\", { token: \"delimiter\", next: \"@pop\" }]],\n      [\n        /@fullIdentifier|\\./,\n        {\n          cases: {\n            \"@builtinTypes\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ]\n    ],\n    groupDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [\"=\", \"operator\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@messageBody.$S2\" }],\n      { include: \"@constant\" }\n    ],\n    type: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\", \"@pop\"],\n      [/./, \"delimiter\"]\n    ],\n    identifier: [{ include: \"@whitespace\" }, [/@identifier/, \"identifier\", \"@pop\"]],\n    serviceDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@serviceBody.$S2\" }]\n    ],\n    serviceBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/option\\b/, \"keyword\", \"@option.$S2\"],\n      [/rpc\\b/, \"keyword\", \"@rpc.$S2\"],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    rpc: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [/\\(/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@request.$S2\" }],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", next: \"@methodOptions.$S2\" }],\n      [/;/, \"delimiter\", \"@pop\"]\n    ],\n    request: [\n      { include: \"@whitespace\" },\n      [\n        /@messageType/,\n        {\n          cases: {\n            stream: { token: \"keyword\", next: \"@type.$S2\" },\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/\\)/, { token: \"@brackets\", bracket: \"@close\", switchTo: \"@returns.$S2\" }]\n    ],\n    returns: [\n      { include: \"@whitespace\" },\n      [/returns\\b/, \"keyword\"],\n      [/\\(/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@response.$S2\" }]\n    ],\n    response: [\n      { include: \"@whitespace\" },\n      [\n        /@messageType/,\n        {\n          cases: {\n            stream: { token: \"keyword\", next: \"@type.$S2\" },\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/\\)/, { token: \"@brackets\", bracket: \"@close\", switchTo: \"@rpc.$S2\" }]\n    ],\n    methodOptions: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [\"option\", \"keyword\"],\n      [/@optionName/, \"annotation\"],\n      [/[()]/, \"annotation.brackets\"],\n      [/=/, \"operator\"],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@push\"],\n      // nested comment\n      [\"\\\\*/\", \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    stringSingle: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    constant: [\n      [\"@boolLit\", \"keyword.constant\"],\n      [\"@hexLit\", \"number.hex\"],\n      [\"@octalLit\", \"number.octal\"],\n      [\"@decimalLit\", \"number\"],\n      [\"@floatLit\", \"number.float\"],\n      [/(\"([^\"\\\\]|\\\\.)*|'([^'\\\\]|\\\\.)*)$/, \"string.invalid\"],\n      // non-terminated string\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }],\n      [/'/, { token: \"string.quote\", bracket: \"@open\", next: \"@stringSingle\" }],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", next: \"@prototext\" }],\n      [/identifier/, \"identifier\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    prototext: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/@identifier/, \"identifier\"],\n      [/[:;]/, \"delimiter\"],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/protobuf/protobuf.js\n"));

/***/ })

}]);