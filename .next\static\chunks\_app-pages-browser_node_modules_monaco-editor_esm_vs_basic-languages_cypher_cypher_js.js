"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_cypher_cypher_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/cypher/cypher.js":
/*!****************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/cypher/cypher.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/cypher/cypher.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: `.cypher`,\n  ignoreCase: true,\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \"ALL\",\n    \"AND\",\n    \"AS\",\n    \"ASC\",\n    \"ASCENDING\",\n    \"BY\",\n    \"CALL\",\n    \"CASE\",\n    \"CONTAINS\",\n    \"CREATE\",\n    \"DELETE\",\n    \"DESC\",\n    \"DESCENDING\",\n    \"DETACH\",\n    \"DISTINCT\",\n    \"ELSE\",\n    \"END\",\n    \"ENDS\",\n    \"EXISTS\",\n    \"IN\",\n    \"IS\",\n    \"LIMIT\",\n    \"MANDATORY\",\n    \"MATCH\",\n    \"MERGE\",\n    \"NOT\",\n    \"ON\",\n    \"ON\",\n    \"OPTIONAL\",\n    \"OR\",\n    \"ORDER\",\n    \"REMOVE\",\n    \"RETURN\",\n    \"SET\",\n    \"SKIP\",\n    \"STARTS\",\n    \"THEN\",\n    \"UNION\",\n    \"UNWIND\",\n    \"WHEN\",\n    \"WHERE\",\n    \"WITH\",\n    \"XOR\",\n    \"YIELD\"\n  ],\n  builtinLiterals: [\"true\", \"TRUE\", \"false\", \"FALSE\", \"null\", \"NULL\"],\n  builtinFunctions: [\n    \"abs\",\n    \"acos\",\n    \"asin\",\n    \"atan\",\n    \"atan2\",\n    \"avg\",\n    \"ceil\",\n    \"coalesce\",\n    \"collect\",\n    \"cos\",\n    \"cot\",\n    \"count\",\n    \"degrees\",\n    \"e\",\n    \"endNode\",\n    \"exists\",\n    \"exp\",\n    \"floor\",\n    \"head\",\n    \"id\",\n    \"keys\",\n    \"labels\",\n    \"last\",\n    \"left\",\n    \"length\",\n    \"log\",\n    \"log10\",\n    \"lTrim\",\n    \"max\",\n    \"min\",\n    \"nodes\",\n    \"percentileCont\",\n    \"percentileDisc\",\n    \"pi\",\n    \"properties\",\n    \"radians\",\n    \"rand\",\n    \"range\",\n    \"relationships\",\n    \"replace\",\n    \"reverse\",\n    \"right\",\n    \"round\",\n    \"rTrim\",\n    \"sign\",\n    \"sin\",\n    \"size\",\n    \"split\",\n    \"sqrt\",\n    \"startNode\",\n    \"stDev\",\n    \"stDevP\",\n    \"substring\",\n    \"sum\",\n    \"tail\",\n    \"tan\",\n    \"timestamp\",\n    \"toBoolean\",\n    \"toFloat\",\n    \"toInteger\",\n    \"toLower\",\n    \"toString\",\n    \"toUpper\",\n    \"trim\",\n    \"type\"\n  ],\n  operators: [\n    // Math operators\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"^\",\n    // Comparison operators\n    \"=\",\n    \"<>\",\n    \"<\",\n    \">\",\n    \"<=\",\n    \">=\",\n    // Pattern operators\n    \"->\",\n    \"<-\",\n    \"-->\",\n    \"<--\"\n  ],\n  escapes: /\\\\(?:[tbnrf\\\\\"'`]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+/,\n  octaldigits: /[0-7]+/,\n  hexdigits: /[0-9a-fA-F]+/,\n  tokenizer: {\n    root: [[/[{}[\\]()]/, \"@brackets\"], { include: \"common\" }],\n    common: [\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      // Cypher labels on nodes/relationships, e.g. (n:NodeLabel)-[e:RelationshipLabel]\n      [/:[a-zA-Z_][\\w]*/, \"type.identifier\"],\n      [\n        /[a-zA-Z_][\\w]*(?=\\()/,\n        {\n          cases: {\n            \"@builtinFunctions\": \"predefined.function\"\n          }\n        }\n      ],\n      [\n        /[a-zA-Z_$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@builtinLiterals\": \"predefined.literal\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/`/, \"identifier.escape\", \"@identifierBacktick\"],\n      // delimiter and operator after number because of `.\\d` floats and `:` in labels\n      [/[;,.:|]/, \"delimiter\"],\n      [\n        /[<>=%+\\-*/^]+/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/-?(@digits)[eE](-?(@digits))?/, \"number.float\"],\n      [/-?(@digits)?\\.(@digits)([eE]-?(@digits))?/, \"number.float\"],\n      [/-?0x(@hexdigits)/, \"number.hex\"],\n      [/-?0(@octaldigits)/, \"number.octal\"],\n      [/-?(@digits)/, \"number\"]\n    ],\n    strings: [\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@stringDouble\"],\n      [/'/, \"string\", \"@stringSingle\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/\\/\\/.*/, \"comment\"],\n      [/[^/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[/*]/, \"comment\"]\n    ],\n    stringDouble: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string\"],\n      [/\\\\./, \"string.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    stringSingle: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string\"],\n      [/\\\\./, \"string.invalid\"],\n      [/'/, \"string\", \"@pop\"]\n    ],\n    identifierBacktick: [\n      [/[^\\\\`]+/, \"identifier.escape\"],\n      [/@escapes/, \"identifier.escape\"],\n      [/\\\\./, \"identifier.escape.invalid\"],\n      [/`/, \"identifier.escape\", \"@pop\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/cypher/cypher.js\n"));

/***/ })

}]);