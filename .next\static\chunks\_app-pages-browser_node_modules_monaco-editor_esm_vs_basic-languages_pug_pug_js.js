"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_pug_pug_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/pug/pug.js":
/*!**********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/pug/pug.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/pug/pug.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] }\n  ],\n  folding: {\n    offSide: true\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".pug\",\n  ignoreCase: true,\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.array\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" }\n  ],\n  keywords: [\n    \"append\",\n    \"block\",\n    \"case\",\n    \"default\",\n    \"doctype\",\n    \"each\",\n    \"else\",\n    \"extends\",\n    \"for\",\n    \"if\",\n    \"in\",\n    \"include\",\n    \"mixin\",\n    \"typeof\",\n    \"unless\",\n    \"var\",\n    \"when\"\n  ],\n  tags: [\n    \"a\",\n    \"abbr\",\n    \"acronym\",\n    \"address\",\n    \"area\",\n    \"article\",\n    \"aside\",\n    \"audio\",\n    \"b\",\n    \"base\",\n    \"basefont\",\n    \"bdi\",\n    \"bdo\",\n    \"blockquote\",\n    \"body\",\n    \"br\",\n    \"button\",\n    \"canvas\",\n    \"caption\",\n    \"center\",\n    \"cite\",\n    \"code\",\n    \"col\",\n    \"colgroup\",\n    \"command\",\n    \"datalist\",\n    \"dd\",\n    \"del\",\n    \"details\",\n    \"dfn\",\n    \"div\",\n    \"dl\",\n    \"dt\",\n    \"em\",\n    \"embed\",\n    \"fieldset\",\n    \"figcaption\",\n    \"figure\",\n    \"font\",\n    \"footer\",\n    \"form\",\n    \"frame\",\n    \"frameset\",\n    \"h1\",\n    \"h2\",\n    \"h3\",\n    \"h4\",\n    \"h5\",\n    \"h6\",\n    \"head\",\n    \"header\",\n    \"hgroup\",\n    \"hr\",\n    \"html\",\n    \"i\",\n    \"iframe\",\n    \"img\",\n    \"input\",\n    \"ins\",\n    \"keygen\",\n    \"kbd\",\n    \"label\",\n    \"li\",\n    \"link\",\n    \"map\",\n    \"mark\",\n    \"menu\",\n    \"meta\",\n    \"meter\",\n    \"nav\",\n    \"noframes\",\n    \"noscript\",\n    \"object\",\n    \"ol\",\n    \"optgroup\",\n    \"option\",\n    \"output\",\n    \"p\",\n    \"param\",\n    \"pre\",\n    \"progress\",\n    \"q\",\n    \"rp\",\n    \"rt\",\n    \"ruby\",\n    \"s\",\n    \"samp\",\n    \"script\",\n    \"section\",\n    \"select\",\n    \"small\",\n    \"source\",\n    \"span\",\n    \"strike\",\n    \"strong\",\n    \"style\",\n    \"sub\",\n    \"summary\",\n    \"sup\",\n    \"table\",\n    \"tbody\",\n    \"td\",\n    \"textarea\",\n    \"tfoot\",\n    \"th\",\n    \"thead\",\n    \"time\",\n    \"title\",\n    \"tr\",\n    \"tracks\",\n    \"tt\",\n    \"u\",\n    \"ul\",\n    \"video\",\n    \"wbr\"\n  ],\n  // we include these common regular expressions\n  symbols: /[\\+\\-\\*\\%\\&\\|\\!\\=\\/\\.\\,\\:]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  tokenizer: {\n    root: [\n      // Tag or a keyword at start\n      [\n        /^(\\s*)([a-zA-Z_-][\\w-]*)/,\n        {\n          cases: {\n            \"$2@tags\": {\n              cases: {\n                \"@eos\": [\"\", \"tag\"],\n                \"@default\": [\"\", { token: \"tag\", next: \"@tag.$1\" }]\n              }\n            },\n            \"$2@keywords\": [\"\", { token: \"keyword.$2\" }],\n            \"@default\": [\"\", \"\"]\n          }\n        }\n      ],\n      // id\n      [\n        /^(\\s*)(#[a-zA-Z_-][\\w-]*)/,\n        {\n          cases: {\n            \"@eos\": [\"\", \"tag.id\"],\n            \"@default\": [\"\", { token: \"tag.id\", next: \"@tag.$1\" }]\n          }\n        }\n      ],\n      // class\n      [\n        /^(\\s*)(\\.[a-zA-Z_-][\\w-]*)/,\n        {\n          cases: {\n            \"@eos\": [\"\", \"tag.class\"],\n            \"@default\": [\"\", { token: \"tag.class\", next: \"@tag.$1\" }]\n          }\n        }\n      ],\n      // plain text with pipe\n      [/^(\\s*)(\\|.*)$/, \"\"],\n      { include: \"@whitespace\" },\n      // keywords\n      [\n        /[a-zA-Z_$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      // numbers\n      [/\\d+\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d+/, \"number\"],\n      // strings:\n      [/\"/, \"string\", '@string.\"'],\n      [/'/, \"string\", \"@string.'\"]\n    ],\n    tag: [\n      [/(\\.)(\\s*$)/, [{ token: \"delimiter\", next: \"@blockText.$S2.\" }, \"\"]],\n      [/\\s+/, { token: \"\", next: \"@simpleText\" }],\n      // id\n      [\n        /#[a-zA-Z_-][\\w-]*/,\n        {\n          cases: {\n            \"@eos\": { token: \"tag.id\", next: \"@pop\" },\n            \"@default\": \"tag.id\"\n          }\n        }\n      ],\n      // class\n      [\n        /\\.[a-zA-Z_-][\\w-]*/,\n        {\n          cases: {\n            \"@eos\": { token: \"tag.class\", next: \"@pop\" },\n            \"@default\": \"tag.class\"\n          }\n        }\n      ],\n      // attributes\n      [/\\(/, { token: \"delimiter.parenthesis\", next: \"@attributeList\" }]\n    ],\n    simpleText: [\n      [/[^#]+$/, { token: \"\", next: \"@popall\" }],\n      [/[^#]+/, { token: \"\" }],\n      // interpolation\n      [\n        /(#{)([^}]*)(})/,\n        {\n          cases: {\n            \"@eos\": [\n              \"interpolation.delimiter\",\n              \"interpolation\",\n              {\n                token: \"interpolation.delimiter\",\n                next: \"@popall\"\n              }\n            ],\n            \"@default\": [\"interpolation.delimiter\", \"interpolation\", \"interpolation.delimiter\"]\n          }\n        }\n      ],\n      [/#$/, { token: \"\", next: \"@popall\" }],\n      [/#/, \"\"]\n    ],\n    attributeList: [\n      [/\\s+/, \"\"],\n      [\n        /(\\w+)(\\s*=\\s*)(\"|')/,\n        [\"attribute.name\", \"delimiter\", { token: \"attribute.value\", next: \"@value.$3\" }]\n      ],\n      [/\\w+/, \"attribute.name\"],\n      [\n        /,/,\n        {\n          cases: {\n            \"@eos\": {\n              token: \"attribute.delimiter\",\n              next: \"@popall\"\n            },\n            \"@default\": \"attribute.delimiter\"\n          }\n        }\n      ],\n      [/\\)$/, { token: \"delimiter.parenthesis\", next: \"@popall\" }],\n      [/\\)/, { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/^(\\s*)(\\/\\/.*)$/, { token: \"comment\", next: \"@blockText.$1.comment\" }],\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/<!--/, { token: \"comment\", next: \"@comment\" }]\n    ],\n    blockText: [\n      [\n        /^\\s+.*$/,\n        {\n          cases: {\n            \"($S2\\\\s+.*$)\": { token: \"$S3\" },\n            \"@default\": { token: \"@rematch\", next: \"@popall\" }\n          }\n        }\n      ],\n      [/./, { token: \"@rematch\", next: \"@popall\" }]\n    ],\n    comment: [\n      [/[^<\\-]+/, \"comment.content\"],\n      [/-->/, { token: \"comment\", next: \"@pop\" }],\n      [/<!--/, \"comment.content.invalid\"],\n      [/[<\\-]/, \"comment.content\"]\n    ],\n    string: [\n      [\n        /[^\\\\\"'#]+/,\n        {\n          cases: {\n            \"@eos\": { token: \"string\", next: \"@popall\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [\n        /@escapes/,\n        {\n          cases: {\n            \"@eos\": { token: \"string.escape\", next: \"@popall\" },\n            \"@default\": \"string.escape\"\n          }\n        }\n      ],\n      [\n        /\\\\./,\n        {\n          cases: {\n            \"@eos\": {\n              token: \"string.escape.invalid\",\n              next: \"@popall\"\n            },\n            \"@default\": \"string.escape.invalid\"\n          }\n        }\n      ],\n      // interpolation\n      [/(#{)([^}]*)(})/, [\"interpolation.delimiter\", \"interpolation\", \"interpolation.delimiter\"]],\n      [/#/, \"string\"],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": { token: \"string\" }\n          }\n        }\n      ]\n    ],\n    // Almost identical to above, except for escapes and the output token\n    value: [\n      [\n        /[^\\\\\"']+/,\n        {\n          cases: {\n            \"@eos\": { token: \"attribute.value\", next: \"@popall\" },\n            \"@default\": \"attribute.value\"\n          }\n        }\n      ],\n      [\n        /\\\\./,\n        {\n          cases: {\n            \"@eos\": { token: \"attribute.value\", next: \"@popall\" },\n            \"@default\": \"attribute.value\"\n          }\n        }\n      ],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"attribute.value\", next: \"@pop\" },\n            \"@default\": { token: \"attribute.value\" }\n          }\n        }\n      ]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/pug/pug.js\n"));

/***/ })

}]);