"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_language_typescript_tsMode_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/language/typescript/tsMode.js":
/*!*************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/language/typescript/tsMode.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Adapter: function() { return /* binding */ Adapter; },\n/* harmony export */   CodeActionAdaptor: function() { return /* binding */ CodeActionAdaptor; },\n/* harmony export */   DefinitionAdapter: function() { return /* binding */ DefinitionAdapter; },\n/* harmony export */   DiagnosticsAdapter: function() { return /* binding */ DiagnosticsAdapter; },\n/* harmony export */   DocumentHighlightAdapter: function() { return /* binding */ DocumentHighlightAdapter; },\n/* harmony export */   FormatAdapter: function() { return /* binding */ FormatAdapter; },\n/* harmony export */   FormatHelper: function() { return /* binding */ FormatHelper; },\n/* harmony export */   FormatOnTypeAdapter: function() { return /* binding */ FormatOnTypeAdapter; },\n/* harmony export */   InlayHintsAdapter: function() { return /* binding */ InlayHintsAdapter; },\n/* harmony export */   Kind: function() { return /* binding */ Kind; },\n/* harmony export */   LibFiles: function() { return /* binding */ LibFiles; },\n/* harmony export */   OutlineAdapter: function() { return /* binding */ OutlineAdapter; },\n/* harmony export */   QuickInfoAdapter: function() { return /* binding */ QuickInfoAdapter; },\n/* harmony export */   ReferenceAdapter: function() { return /* binding */ ReferenceAdapter; },\n/* harmony export */   RenameAdapter: function() { return /* binding */ RenameAdapter; },\n/* harmony export */   SignatureHelpAdapter: function() { return /* binding */ SignatureHelpAdapter; },\n/* harmony export */   SuggestAdapter: function() { return /* binding */ SuggestAdapter; },\n/* harmony export */   WorkerManager: function() { return /* binding */ WorkerManager; },\n/* harmony export */   flattenDiagnosticMessageText: function() { return /* binding */ flattenDiagnosticMessageText; },\n/* harmony export */   getJavaScriptWorker: function() { return /* binding */ getJavaScriptWorker; },\n/* harmony export */   getTypeScriptWorker: function() { return /* binding */ getTypeScriptWorker; },\n/* harmony export */   setupJavaScript: function() { return /* binding */ setupJavaScript; },\n/* harmony export */   setupTypeScript: function() { return /* binding */ setupTypeScript; }\n/* harmony export */ });\n/* harmony import */ var _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../editor/editor.api.js */ \"(app-pages-browser)/./node_modules/monaco-editor/esm/vs/editor/editor.api.js?ce9f\");\n/* harmony import */ var _monaco_contribution_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./monaco.contribution.js */ \"(app-pages-browser)/./node_modules/monaco-editor/esm/vs/language/typescript/monaco.contribution.js\");\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, _editor_editor_api_js__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// src/language/typescript/workerManager.ts\nvar WorkerManager = class {\n  constructor(_modeId, _defaults) {\n    this._modeId = _modeId;\n    this._defaults = _defaults;\n    this._worker = null;\n    this._client = null;\n    this._configChangeListener = this._defaults.onDidChange(() => this._stopWorker());\n    this._updateExtraLibsToken = 0;\n    this._extraLibsChangeListener = this._defaults.onDidExtraLibsChange(\n      () => this._updateExtraLibs()\n    );\n  }\n  dispose() {\n    this._configChangeListener.dispose();\n    this._extraLibsChangeListener.dispose();\n    this._stopWorker();\n  }\n  _stopWorker() {\n    if (this._worker) {\n      this._worker.dispose();\n      this._worker = null;\n    }\n    this._client = null;\n  }\n  async _updateExtraLibs() {\n    if (!this._worker) {\n      return;\n    }\n    const myToken = ++this._updateExtraLibsToken;\n    const proxy = await this._worker.getProxy();\n    if (this._updateExtraLibsToken !== myToken) {\n      return;\n    }\n    proxy.updateExtraLibs(this._defaults.getExtraLibs());\n  }\n  _getClient() {\n    if (!this._client) {\n      this._client = (async () => {\n        this._worker = monaco_editor_core_exports.editor.createWebWorker({\n          // module that exports the create() method and returns a `TypeScriptWorker` instance\n          moduleId: \"vs/language/typescript/tsWorker\",\n          label: this._modeId,\n          keepIdleModels: true,\n          // passed in to the create() method\n          createData: {\n            compilerOptions: this._defaults.getCompilerOptions(),\n            extraLibs: this._defaults.getExtraLibs(),\n            customWorkerPath: this._defaults.workerOptions.customWorkerPath,\n            inlayHintsOptions: this._defaults.inlayHintsOptions\n          }\n        });\n        if (this._defaults.getEagerModelSync()) {\n          return await this._worker.withSyncedResources(\n            monaco_editor_core_exports.editor.getModels().filter((model) => model.getLanguageId() === this._modeId).map((model) => model.uri)\n          );\n        }\n        return await this._worker.getProxy();\n      })();\n    }\n    return this._client;\n  }\n  async getLanguageServiceWorker(...resources) {\n    const client = await this._getClient();\n    if (this._worker) {\n      await this._worker.withSyncedResources(resources);\n    }\n    return client;\n  }\n};\n\n// src/language/typescript/languageFeatures.ts\n\n\n// src/language/typescript/lib/lib.index.ts\nvar libFileSet = {};\nlibFileSet[\"lib.d.ts\"] = true;\nlibFileSet[\"lib.decorators.d.ts\"] = true;\nlibFileSet[\"lib.decorators.legacy.d.ts\"] = true;\nlibFileSet[\"lib.dom.asynciterable.d.ts\"] = true;\nlibFileSet[\"lib.dom.d.ts\"] = true;\nlibFileSet[\"lib.dom.iterable.d.ts\"] = true;\nlibFileSet[\"lib.es2015.collection.d.ts\"] = true;\nlibFileSet[\"lib.es2015.core.d.ts\"] = true;\nlibFileSet[\"lib.es2015.d.ts\"] = true;\nlibFileSet[\"lib.es2015.generator.d.ts\"] = true;\nlibFileSet[\"lib.es2015.iterable.d.ts\"] = true;\nlibFileSet[\"lib.es2015.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2015.proxy.d.ts\"] = true;\nlibFileSet[\"lib.es2015.reflect.d.ts\"] = true;\nlibFileSet[\"lib.es2015.symbol.d.ts\"] = true;\nlibFileSet[\"lib.es2015.symbol.wellknown.d.ts\"] = true;\nlibFileSet[\"lib.es2016.array.include.d.ts\"] = true;\nlibFileSet[\"lib.es2016.d.ts\"] = true;\nlibFileSet[\"lib.es2016.full.d.ts\"] = true;\nlibFileSet[\"lib.es2016.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2017.d.ts\"] = true;\nlibFileSet[\"lib.es2017.date.d.ts\"] = true;\nlibFileSet[\"lib.es2017.full.d.ts\"] = true;\nlibFileSet[\"lib.es2017.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2017.object.d.ts\"] = true;\nlibFileSet[\"lib.es2017.sharedmemory.d.ts\"] = true;\nlibFileSet[\"lib.es2017.string.d.ts\"] = true;\nlibFileSet[\"lib.es2017.typedarrays.d.ts\"] = true;\nlibFileSet[\"lib.es2018.asyncgenerator.d.ts\"] = true;\nlibFileSet[\"lib.es2018.asynciterable.d.ts\"] = true;\nlibFileSet[\"lib.es2018.d.ts\"] = true;\nlibFileSet[\"lib.es2018.full.d.ts\"] = true;\nlibFileSet[\"lib.es2018.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2018.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2018.regexp.d.ts\"] = true;\nlibFileSet[\"lib.es2019.array.d.ts\"] = true;\nlibFileSet[\"lib.es2019.d.ts\"] = true;\nlibFileSet[\"lib.es2019.full.d.ts\"] = true;\nlibFileSet[\"lib.es2019.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2019.object.d.ts\"] = true;\nlibFileSet[\"lib.es2019.string.d.ts\"] = true;\nlibFileSet[\"lib.es2019.symbol.d.ts\"] = true;\nlibFileSet[\"lib.es2020.bigint.d.ts\"] = true;\nlibFileSet[\"lib.es2020.d.ts\"] = true;\nlibFileSet[\"lib.es2020.date.d.ts\"] = true;\nlibFileSet[\"lib.es2020.full.d.ts\"] = true;\nlibFileSet[\"lib.es2020.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2020.number.d.ts\"] = true;\nlibFileSet[\"lib.es2020.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2020.sharedmemory.d.ts\"] = true;\nlibFileSet[\"lib.es2020.string.d.ts\"] = true;\nlibFileSet[\"lib.es2020.symbol.wellknown.d.ts\"] = true;\nlibFileSet[\"lib.es2021.d.ts\"] = true;\nlibFileSet[\"lib.es2021.full.d.ts\"] = true;\nlibFileSet[\"lib.es2021.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2021.promise.d.ts\"] = true;\nlibFileSet[\"lib.es2021.string.d.ts\"] = true;\nlibFileSet[\"lib.es2021.weakref.d.ts\"] = true;\nlibFileSet[\"lib.es2022.array.d.ts\"] = true;\nlibFileSet[\"lib.es2022.d.ts\"] = true;\nlibFileSet[\"lib.es2022.error.d.ts\"] = true;\nlibFileSet[\"lib.es2022.full.d.ts\"] = true;\nlibFileSet[\"lib.es2022.intl.d.ts\"] = true;\nlibFileSet[\"lib.es2022.object.d.ts\"] = true;\nlibFileSet[\"lib.es2022.regexp.d.ts\"] = true;\nlibFileSet[\"lib.es2022.sharedmemory.d.ts\"] = true;\nlibFileSet[\"lib.es2022.string.d.ts\"] = true;\nlibFileSet[\"lib.es2023.array.d.ts\"] = true;\nlibFileSet[\"lib.es2023.collection.d.ts\"] = true;\nlibFileSet[\"lib.es2023.d.ts\"] = true;\nlibFileSet[\"lib.es2023.full.d.ts\"] = true;\nlibFileSet[\"lib.es5.d.ts\"] = true;\nlibFileSet[\"lib.es6.d.ts\"] = true;\nlibFileSet[\"lib.esnext.collection.d.ts\"] = true;\nlibFileSet[\"lib.esnext.d.ts\"] = true;\nlibFileSet[\"lib.esnext.decorators.d.ts\"] = true;\nlibFileSet[\"lib.esnext.disposable.d.ts\"] = true;\nlibFileSet[\"lib.esnext.full.d.ts\"] = true;\nlibFileSet[\"lib.esnext.intl.d.ts\"] = true;\nlibFileSet[\"lib.esnext.object.d.ts\"] = true;\nlibFileSet[\"lib.esnext.promise.d.ts\"] = true;\nlibFileSet[\"lib.scripthost.d.ts\"] = true;\nlibFileSet[\"lib.webworker.asynciterable.d.ts\"] = true;\nlibFileSet[\"lib.webworker.d.ts\"] = true;\nlibFileSet[\"lib.webworker.importscripts.d.ts\"] = true;\nlibFileSet[\"lib.webworker.iterable.d.ts\"] = true;\n\n// src/language/typescript/languageFeatures.ts\nfunction flattenDiagnosticMessageText(diag, newLine, indent = 0) {\n  if (typeof diag === \"string\") {\n    return diag;\n  } else if (diag === void 0) {\n    return \"\";\n  }\n  let result = \"\";\n  if (indent) {\n    result += newLine;\n    for (let i = 0; i < indent; i++) {\n      result += \"  \";\n    }\n  }\n  result += diag.messageText;\n  indent++;\n  if (diag.next) {\n    for (const kid of diag.next) {\n      result += flattenDiagnosticMessageText(kid, newLine, indent);\n    }\n  }\n  return result;\n}\nfunction displayPartsToString(displayParts) {\n  if (displayParts) {\n    return displayParts.map((displayPart) => displayPart.text).join(\"\");\n  }\n  return \"\";\n}\nvar Adapter = class {\n  constructor(_worker) {\n    this._worker = _worker;\n  }\n  // protected _positionToOffset(model: editor.ITextModel, position: monaco.IPosition): number {\n  // \treturn model.getOffsetAt(position);\n  // }\n  // protected _offsetToPosition(model: editor.ITextModel, offset: number): monaco.IPosition {\n  // \treturn model.getPositionAt(offset);\n  // }\n  _textSpanToRange(model, span) {\n    let p1 = model.getPositionAt(span.start);\n    let p2 = model.getPositionAt(span.start + span.length);\n    let { lineNumber: startLineNumber, column: startColumn } = p1;\n    let { lineNumber: endLineNumber, column: endColumn } = p2;\n    return { startLineNumber, startColumn, endLineNumber, endColumn };\n  }\n};\nvar LibFiles = class {\n  constructor(_worker) {\n    this._worker = _worker;\n    this._libFiles = {};\n    this._hasFetchedLibFiles = false;\n    this._fetchLibFilesPromise = null;\n  }\n  isLibFile(uri) {\n    if (!uri) {\n      return false;\n    }\n    if (uri.path.indexOf(\"/lib.\") === 0) {\n      return !!libFileSet[uri.path.slice(1)];\n    }\n    return false;\n  }\n  getOrCreateModel(fileName) {\n    const uri = monaco_editor_core_exports.Uri.parse(fileName);\n    const model = monaco_editor_core_exports.editor.getModel(uri);\n    if (model) {\n      return model;\n    }\n    if (this.isLibFile(uri) && this._hasFetchedLibFiles) {\n      return monaco_editor_core_exports.editor.createModel(this._libFiles[uri.path.slice(1)], \"typescript\", uri);\n    }\n    const matchedLibFile = _monaco_contribution_js__WEBPACK_IMPORTED_MODULE_1__.typescriptDefaults.getExtraLibs()[fileName];\n    if (matchedLibFile) {\n      return monaco_editor_core_exports.editor.createModel(matchedLibFile.content, \"typescript\", uri);\n    }\n    return null;\n  }\n  _containsLibFile(uris) {\n    for (let uri of uris) {\n      if (this.isLibFile(uri)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  async fetchLibFilesIfNecessary(uris) {\n    if (!this._containsLibFile(uris)) {\n      return;\n    }\n    await this._fetchLibFiles();\n  }\n  _fetchLibFiles() {\n    if (!this._fetchLibFilesPromise) {\n      this._fetchLibFilesPromise = this._worker().then((w) => w.getLibFiles()).then((libFiles) => {\n        this._hasFetchedLibFiles = true;\n        this._libFiles = libFiles;\n      });\n    }\n    return this._fetchLibFilesPromise;\n  }\n};\nvar DiagnosticsAdapter = class extends Adapter {\n  constructor(_libFiles, _defaults, _selector, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n    this._defaults = _defaults;\n    this._selector = _selector;\n    this._disposables = [];\n    this._listener = /* @__PURE__ */ Object.create(null);\n    const onModelAdd = (model) => {\n      if (model.getLanguageId() !== _selector) {\n        return;\n      }\n      const maybeValidate = () => {\n        const { onlyVisible } = this._defaults.getDiagnosticsOptions();\n        if (onlyVisible) {\n          if (model.isAttachedToEditor()) {\n            this._doValidate(model);\n          }\n        } else {\n          this._doValidate(model);\n        }\n      };\n      let handle;\n      const changeSubscription = model.onDidChangeContent(() => {\n        clearTimeout(handle);\n        handle = window.setTimeout(maybeValidate, 500);\n      });\n      const visibleSubscription = model.onDidChangeAttached(() => {\n        const { onlyVisible } = this._defaults.getDiagnosticsOptions();\n        if (onlyVisible) {\n          if (model.isAttachedToEditor()) {\n            maybeValidate();\n          } else {\n            monaco_editor_core_exports.editor.setModelMarkers(model, this._selector, []);\n          }\n        }\n      });\n      this._listener[model.uri.toString()] = {\n        dispose() {\n          changeSubscription.dispose();\n          visibleSubscription.dispose();\n          clearTimeout(handle);\n        }\n      };\n      maybeValidate();\n    };\n    const onModelRemoved = (model) => {\n      monaco_editor_core_exports.editor.setModelMarkers(model, this._selector, []);\n      const key = model.uri.toString();\n      if (this._listener[key]) {\n        this._listener[key].dispose();\n        delete this._listener[key];\n      }\n    };\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidCreateModel((model) => onModelAdd(model))\n    );\n    this._disposables.push(monaco_editor_core_exports.editor.onWillDisposeModel(onModelRemoved));\n    this._disposables.push(\n      monaco_editor_core_exports.editor.onDidChangeModelLanguage((event) => {\n        onModelRemoved(event.model);\n        onModelAdd(event.model);\n      })\n    );\n    this._disposables.push({\n      dispose() {\n        for (const model of monaco_editor_core_exports.editor.getModels()) {\n          onModelRemoved(model);\n        }\n      }\n    });\n    const recomputeDiagostics = () => {\n      for (const model of monaco_editor_core_exports.editor.getModels()) {\n        onModelRemoved(model);\n        onModelAdd(model);\n      }\n    };\n    this._disposables.push(this._defaults.onDidChange(recomputeDiagostics));\n    this._disposables.push(this._defaults.onDidExtraLibsChange(recomputeDiagostics));\n    monaco_editor_core_exports.editor.getModels().forEach((model) => onModelAdd(model));\n  }\n  dispose() {\n    this._disposables.forEach((d) => d && d.dispose());\n    this._disposables = [];\n  }\n  async _doValidate(model) {\n    const worker = await this._worker(model.uri);\n    if (model.isDisposed()) {\n      return;\n    }\n    const promises = [];\n    const { noSyntaxValidation, noSemanticValidation, noSuggestionDiagnostics } = this._defaults.getDiagnosticsOptions();\n    if (!noSyntaxValidation) {\n      promises.push(worker.getSyntacticDiagnostics(model.uri.toString()));\n    }\n    if (!noSemanticValidation) {\n      promises.push(worker.getSemanticDiagnostics(model.uri.toString()));\n    }\n    if (!noSuggestionDiagnostics) {\n      promises.push(worker.getSuggestionDiagnostics(model.uri.toString()));\n    }\n    const allDiagnostics = await Promise.all(promises);\n    if (!allDiagnostics || model.isDisposed()) {\n      return;\n    }\n    const diagnostics = allDiagnostics.reduce((p, c) => c.concat(p), []).filter(\n      (d) => (this._defaults.getDiagnosticsOptions().diagnosticCodesToIgnore || []).indexOf(d.code) === -1\n    );\n    const relatedUris = diagnostics.map((d) => d.relatedInformation || []).reduce((p, c) => c.concat(p), []).map(\n      (relatedInformation) => relatedInformation.file ? monaco_editor_core_exports.Uri.parse(relatedInformation.file.fileName) : null\n    );\n    await this._libFiles.fetchLibFilesIfNecessary(relatedUris);\n    if (model.isDisposed()) {\n      return;\n    }\n    monaco_editor_core_exports.editor.setModelMarkers(\n      model,\n      this._selector,\n      diagnostics.map((d) => this._convertDiagnostics(model, d))\n    );\n  }\n  _convertDiagnostics(model, diag) {\n    const diagStart = diag.start || 0;\n    const diagLength = diag.length || 1;\n    const { lineNumber: startLineNumber, column: startColumn } = model.getPositionAt(diagStart);\n    const { lineNumber: endLineNumber, column: endColumn } = model.getPositionAt(\n      diagStart + diagLength\n    );\n    const tags = [];\n    if (diag.reportsUnnecessary) {\n      tags.push(monaco_editor_core_exports.MarkerTag.Unnecessary);\n    }\n    if (diag.reportsDeprecated) {\n      tags.push(monaco_editor_core_exports.MarkerTag.Deprecated);\n    }\n    return {\n      severity: this._tsDiagnosticCategoryToMarkerSeverity(diag.category),\n      startLineNumber,\n      startColumn,\n      endLineNumber,\n      endColumn,\n      message: flattenDiagnosticMessageText(diag.messageText, \"\\n\"),\n      code: diag.code.toString(),\n      tags,\n      relatedInformation: this._convertRelatedInformation(model, diag.relatedInformation)\n    };\n  }\n  _convertRelatedInformation(model, relatedInformation) {\n    if (!relatedInformation) {\n      return [];\n    }\n    const result = [];\n    relatedInformation.forEach((info) => {\n      let relatedResource = model;\n      if (info.file) {\n        relatedResource = this._libFiles.getOrCreateModel(info.file.fileName);\n      }\n      if (!relatedResource) {\n        return;\n      }\n      const infoStart = info.start || 0;\n      const infoLength = info.length || 1;\n      const { lineNumber: startLineNumber, column: startColumn } = relatedResource.getPositionAt(infoStart);\n      const { lineNumber: endLineNumber, column: endColumn } = relatedResource.getPositionAt(\n        infoStart + infoLength\n      );\n      result.push({\n        resource: relatedResource.uri,\n        startLineNumber,\n        startColumn,\n        endLineNumber,\n        endColumn,\n        message: flattenDiagnosticMessageText(info.messageText, \"\\n\")\n      });\n    });\n    return result;\n  }\n  _tsDiagnosticCategoryToMarkerSeverity(category) {\n    switch (category) {\n      case 1 /* Error */:\n        return monaco_editor_core_exports.MarkerSeverity.Error;\n      case 3 /* Message */:\n        return monaco_editor_core_exports.MarkerSeverity.Info;\n      case 0 /* Warning */:\n        return monaco_editor_core_exports.MarkerSeverity.Warning;\n      case 2 /* Suggestion */:\n        return monaco_editor_core_exports.MarkerSeverity.Hint;\n    }\n    return monaco_editor_core_exports.MarkerSeverity.Info;\n  }\n};\nvar SuggestAdapter = class _SuggestAdapter extends Adapter {\n  get triggerCharacters() {\n    return [\".\"];\n  }\n  async provideCompletionItems(model, position, _context, token) {\n    const wordInfo = model.getWordUntilPosition(position);\n    const wordRange = new monaco_editor_core_exports.Range(\n      position.lineNumber,\n      wordInfo.startColumn,\n      position.lineNumber,\n      wordInfo.endColumn\n    );\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const info = await worker.getCompletionsAtPosition(resource.toString(), offset);\n    if (!info || model.isDisposed()) {\n      return;\n    }\n    const suggestions = info.entries.map((entry) => {\n      let range = wordRange;\n      if (entry.replacementSpan) {\n        const p1 = model.getPositionAt(entry.replacementSpan.start);\n        const p2 = model.getPositionAt(entry.replacementSpan.start + entry.replacementSpan.length);\n        range = new monaco_editor_core_exports.Range(p1.lineNumber, p1.column, p2.lineNumber, p2.column);\n      }\n      const tags = [];\n      if (entry.kindModifiers !== void 0 && entry.kindModifiers.indexOf(\"deprecated\") !== -1) {\n        tags.push(monaco_editor_core_exports.languages.CompletionItemTag.Deprecated);\n      }\n      return {\n        uri: resource,\n        position,\n        offset,\n        range,\n        label: entry.name,\n        insertText: entry.name,\n        sortText: entry.sortText,\n        kind: _SuggestAdapter.convertKind(entry.kind),\n        tags\n      };\n    });\n    return {\n      suggestions\n    };\n  }\n  async resolveCompletionItem(item, token) {\n    const myItem = item;\n    const resource = myItem.uri;\n    const position = myItem.position;\n    const offset = myItem.offset;\n    const worker = await this._worker(resource);\n    const details = await worker.getCompletionEntryDetails(\n      resource.toString(),\n      offset,\n      myItem.label\n    );\n    if (!details) {\n      return myItem;\n    }\n    return {\n      uri: resource,\n      position,\n      label: details.name,\n      kind: _SuggestAdapter.convertKind(details.kind),\n      detail: displayPartsToString(details.displayParts),\n      documentation: {\n        value: _SuggestAdapter.createDocumentationString(details)\n      }\n    };\n  }\n  static convertKind(kind) {\n    switch (kind) {\n      case Kind.primitiveType:\n      case Kind.keyword:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Keyword;\n      case Kind.variable:\n      case Kind.localVariable:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Variable;\n      case Kind.memberVariable:\n      case Kind.memberGetAccessor:\n      case Kind.memberSetAccessor:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Field;\n      case Kind.function:\n      case Kind.memberFunction:\n      case Kind.constructSignature:\n      case Kind.callSignature:\n      case Kind.indexSignature:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Function;\n      case Kind.enum:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Enum;\n      case Kind.module:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Module;\n      case Kind.class:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Class;\n      case Kind.interface:\n        return monaco_editor_core_exports.languages.CompletionItemKind.Interface;\n      case Kind.warning:\n        return monaco_editor_core_exports.languages.CompletionItemKind.File;\n    }\n    return monaco_editor_core_exports.languages.CompletionItemKind.Property;\n  }\n  static createDocumentationString(details) {\n    let documentationString = displayPartsToString(details.documentation);\n    if (details.tags) {\n      for (const tag of details.tags) {\n        documentationString += `\n\n${tagToString(tag)}`;\n      }\n    }\n    return documentationString;\n  }\n};\nfunction tagToString(tag) {\n  let tagLabel = `*@${tag.name}*`;\n  if (tag.name === \"param\" && tag.text) {\n    const [paramName, ...rest] = tag.text;\n    tagLabel += `\\`${paramName.text}\\``;\n    if (rest.length > 0)\n      tagLabel += ` \\u2014 ${rest.map((r) => r.text).join(\" \")}`;\n  } else if (Array.isArray(tag.text)) {\n    tagLabel += ` \\u2014 ${tag.text.map((r) => r.text).join(\" \")}`;\n  } else if (tag.text) {\n    tagLabel += ` \\u2014 ${tag.text}`;\n  }\n  return tagLabel;\n}\nvar SignatureHelpAdapter = class _SignatureHelpAdapter extends Adapter {\n  constructor() {\n    super(...arguments);\n    this.signatureHelpTriggerCharacters = [\"(\", \",\"];\n  }\n  static _toSignatureHelpTriggerReason(context) {\n    switch (context.triggerKind) {\n      case monaco_editor_core_exports.languages.SignatureHelpTriggerKind.TriggerCharacter:\n        if (context.triggerCharacter) {\n          if (context.isRetrigger) {\n            return { kind: \"retrigger\", triggerCharacter: context.triggerCharacter };\n          } else {\n            return { kind: \"characterTyped\", triggerCharacter: context.triggerCharacter };\n          }\n        } else {\n          return { kind: \"invoked\" };\n        }\n      case monaco_editor_core_exports.languages.SignatureHelpTriggerKind.ContentChange:\n        return context.isRetrigger ? { kind: \"retrigger\" } : { kind: \"invoked\" };\n      case monaco_editor_core_exports.languages.SignatureHelpTriggerKind.Invoke:\n      default:\n        return { kind: \"invoked\" };\n    }\n  }\n  async provideSignatureHelp(model, position, token, context) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const info = await worker.getSignatureHelpItems(resource.toString(), offset, {\n      triggerReason: _SignatureHelpAdapter._toSignatureHelpTriggerReason(context)\n    });\n    if (!info || model.isDisposed()) {\n      return;\n    }\n    const ret = {\n      activeSignature: info.selectedItemIndex,\n      activeParameter: info.argumentIndex,\n      signatures: []\n    };\n    info.items.forEach((item) => {\n      const signature = {\n        label: \"\",\n        parameters: []\n      };\n      signature.documentation = {\n        value: displayPartsToString(item.documentation)\n      };\n      signature.label += displayPartsToString(item.prefixDisplayParts);\n      item.parameters.forEach((p, i, a) => {\n        const label = displayPartsToString(p.displayParts);\n        const parameter = {\n          label,\n          documentation: {\n            value: displayPartsToString(p.documentation)\n          }\n        };\n        signature.label += label;\n        signature.parameters.push(parameter);\n        if (i < a.length - 1) {\n          signature.label += displayPartsToString(item.separatorDisplayParts);\n        }\n      });\n      signature.label += displayPartsToString(item.suffixDisplayParts);\n      ret.signatures.push(signature);\n    });\n    return {\n      value: ret,\n      dispose() {\n      }\n    };\n  }\n};\nvar QuickInfoAdapter = class extends Adapter {\n  async provideHover(model, position, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const info = await worker.getQuickInfoAtPosition(resource.toString(), offset);\n    if (!info || model.isDisposed()) {\n      return;\n    }\n    const documentation = displayPartsToString(info.documentation);\n    const tags = info.tags ? info.tags.map((tag) => tagToString(tag)).join(\"  \\n\\n\") : \"\";\n    const contents = displayPartsToString(info.displayParts);\n    return {\n      range: this._textSpanToRange(model, info.textSpan),\n      contents: [\n        {\n          value: \"```typescript\\n\" + contents + \"\\n```\\n\"\n        },\n        {\n          value: documentation + (tags ? \"\\n\\n\" + tags : \"\")\n        }\n      ]\n    };\n  }\n};\nvar DocumentHighlightAdapter = class extends Adapter {\n  async provideDocumentHighlights(model, position, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const entries = await worker.getDocumentHighlights(resource.toString(), offset, [\n      resource.toString()\n    ]);\n    if (!entries || model.isDisposed()) {\n      return;\n    }\n    return entries.flatMap((entry) => {\n      return entry.highlightSpans.map((highlightSpans) => {\n        return {\n          range: this._textSpanToRange(model, highlightSpans.textSpan),\n          kind: highlightSpans.kind === \"writtenReference\" ? monaco_editor_core_exports.languages.DocumentHighlightKind.Write : monaco_editor_core_exports.languages.DocumentHighlightKind.Text\n        };\n      });\n    });\n  }\n};\nvar DefinitionAdapter = class extends Adapter {\n  constructor(_libFiles, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n  }\n  async provideDefinition(model, position, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const entries = await worker.getDefinitionAtPosition(resource.toString(), offset);\n    if (!entries || model.isDisposed()) {\n      return;\n    }\n    await this._libFiles.fetchLibFilesIfNecessary(\n      entries.map((entry) => monaco_editor_core_exports.Uri.parse(entry.fileName))\n    );\n    if (model.isDisposed()) {\n      return;\n    }\n    const result = [];\n    for (let entry of entries) {\n      const refModel = this._libFiles.getOrCreateModel(entry.fileName);\n      if (refModel) {\n        result.push({\n          uri: refModel.uri,\n          range: this._textSpanToRange(refModel, entry.textSpan)\n        });\n      }\n    }\n    return result;\n  }\n};\nvar ReferenceAdapter = class extends Adapter {\n  constructor(_libFiles, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n  }\n  async provideReferences(model, position, context, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const entries = await worker.getReferencesAtPosition(resource.toString(), offset);\n    if (!entries || model.isDisposed()) {\n      return;\n    }\n    await this._libFiles.fetchLibFilesIfNecessary(\n      entries.map((entry) => monaco_editor_core_exports.Uri.parse(entry.fileName))\n    );\n    if (model.isDisposed()) {\n      return;\n    }\n    const result = [];\n    for (let entry of entries) {\n      const refModel = this._libFiles.getOrCreateModel(entry.fileName);\n      if (refModel) {\n        result.push({\n          uri: refModel.uri,\n          range: this._textSpanToRange(refModel, entry.textSpan)\n        });\n      }\n    }\n    return result;\n  }\n};\nvar OutlineAdapter = class extends Adapter {\n  async provideDocumentSymbols(model, token) {\n    const resource = model.uri;\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const root = await worker.getNavigationTree(resource.toString());\n    if (!root || model.isDisposed()) {\n      return;\n    }\n    const convert = (item, containerLabel) => {\n      const result2 = {\n        name: item.text,\n        detail: \"\",\n        kind: outlineTypeTable[item.kind] || monaco_editor_core_exports.languages.SymbolKind.Variable,\n        range: this._textSpanToRange(model, item.spans[0]),\n        selectionRange: this._textSpanToRange(model, item.spans[0]),\n        tags: [],\n        children: item.childItems?.map((child) => convert(child, item.text)),\n        containerName: containerLabel\n      };\n      return result2;\n    };\n    const result = root.childItems ? root.childItems.map((item) => convert(item)) : [];\n    return result;\n  }\n};\nvar Kind = class {\n  static {\n    this.unknown = \"\";\n  }\n  static {\n    this.keyword = \"keyword\";\n  }\n  static {\n    this.script = \"script\";\n  }\n  static {\n    this.module = \"module\";\n  }\n  static {\n    this.class = \"class\";\n  }\n  static {\n    this.interface = \"interface\";\n  }\n  static {\n    this.type = \"type\";\n  }\n  static {\n    this.enum = \"enum\";\n  }\n  static {\n    this.variable = \"var\";\n  }\n  static {\n    this.localVariable = \"local var\";\n  }\n  static {\n    this.function = \"function\";\n  }\n  static {\n    this.localFunction = \"local function\";\n  }\n  static {\n    this.memberFunction = \"method\";\n  }\n  static {\n    this.memberGetAccessor = \"getter\";\n  }\n  static {\n    this.memberSetAccessor = \"setter\";\n  }\n  static {\n    this.memberVariable = \"property\";\n  }\n  static {\n    this.constructorImplementation = \"constructor\";\n  }\n  static {\n    this.callSignature = \"call\";\n  }\n  static {\n    this.indexSignature = \"index\";\n  }\n  static {\n    this.constructSignature = \"construct\";\n  }\n  static {\n    this.parameter = \"parameter\";\n  }\n  static {\n    this.typeParameter = \"type parameter\";\n  }\n  static {\n    this.primitiveType = \"primitive type\";\n  }\n  static {\n    this.label = \"label\";\n  }\n  static {\n    this.alias = \"alias\";\n  }\n  static {\n    this.const = \"const\";\n  }\n  static {\n    this.let = \"let\";\n  }\n  static {\n    this.warning = \"warning\";\n  }\n};\nvar outlineTypeTable = /* @__PURE__ */ Object.create(null);\noutlineTypeTable[Kind.module] = monaco_editor_core_exports.languages.SymbolKind.Module;\noutlineTypeTable[Kind.class] = monaco_editor_core_exports.languages.SymbolKind.Class;\noutlineTypeTable[Kind.enum] = monaco_editor_core_exports.languages.SymbolKind.Enum;\noutlineTypeTable[Kind.interface] = monaco_editor_core_exports.languages.SymbolKind.Interface;\noutlineTypeTable[Kind.memberFunction] = monaco_editor_core_exports.languages.SymbolKind.Method;\noutlineTypeTable[Kind.memberVariable] = monaco_editor_core_exports.languages.SymbolKind.Property;\noutlineTypeTable[Kind.memberGetAccessor] = monaco_editor_core_exports.languages.SymbolKind.Property;\noutlineTypeTable[Kind.memberSetAccessor] = monaco_editor_core_exports.languages.SymbolKind.Property;\noutlineTypeTable[Kind.variable] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.const] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.localVariable] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.variable] = monaco_editor_core_exports.languages.SymbolKind.Variable;\noutlineTypeTable[Kind.function] = monaco_editor_core_exports.languages.SymbolKind.Function;\noutlineTypeTable[Kind.localFunction] = monaco_editor_core_exports.languages.SymbolKind.Function;\nvar FormatHelper = class extends Adapter {\n  static _convertOptions(options) {\n    return {\n      ConvertTabsToSpaces: options.insertSpaces,\n      TabSize: options.tabSize,\n      IndentSize: options.tabSize,\n      IndentStyle: 2 /* Smart */,\n      NewLineCharacter: \"\\n\",\n      InsertSpaceAfterCommaDelimiter: true,\n      InsertSpaceAfterSemicolonInForStatements: true,\n      InsertSpaceBeforeAndAfterBinaryOperators: true,\n      InsertSpaceAfterKeywordsInControlFlowStatements: true,\n      InsertSpaceAfterFunctionKeywordForAnonymousFunctions: true,\n      InsertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis: false,\n      InsertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets: false,\n      InsertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces: false,\n      PlaceOpenBraceOnNewLineForControlBlocks: false,\n      PlaceOpenBraceOnNewLineForFunctions: false\n    };\n  }\n  _convertTextChanges(model, change) {\n    return {\n      text: change.newText,\n      range: this._textSpanToRange(model, change.span)\n    };\n  }\n};\nvar FormatAdapter = class extends FormatHelper {\n  constructor() {\n    super(...arguments);\n    this.canFormatMultipleRanges = false;\n  }\n  async provideDocumentRangeFormattingEdits(model, range, options, token) {\n    const resource = model.uri;\n    const startOffset = model.getOffsetAt({\n      lineNumber: range.startLineNumber,\n      column: range.startColumn\n    });\n    const endOffset = model.getOffsetAt({\n      lineNumber: range.endLineNumber,\n      column: range.endColumn\n    });\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const edits = await worker.getFormattingEditsForRange(\n      resource.toString(),\n      startOffset,\n      endOffset,\n      FormatHelper._convertOptions(options)\n    );\n    if (!edits || model.isDisposed()) {\n      return;\n    }\n    return edits.map((edit) => this._convertTextChanges(model, edit));\n  }\n};\nvar FormatOnTypeAdapter = class extends FormatHelper {\n  get autoFormatTriggerCharacters() {\n    return [\";\", \"}\", \"\\n\"];\n  }\n  async provideOnTypeFormattingEdits(model, position, ch, options, token) {\n    const resource = model.uri;\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const edits = await worker.getFormattingEditsAfterKeystroke(\n      resource.toString(),\n      offset,\n      ch,\n      FormatHelper._convertOptions(options)\n    );\n    if (!edits || model.isDisposed()) {\n      return;\n    }\n    return edits.map((edit) => this._convertTextChanges(model, edit));\n  }\n};\nvar CodeActionAdaptor = class extends FormatHelper {\n  async provideCodeActions(model, range, context, token) {\n    const resource = model.uri;\n    const start = model.getOffsetAt({\n      lineNumber: range.startLineNumber,\n      column: range.startColumn\n    });\n    const end = model.getOffsetAt({\n      lineNumber: range.endLineNumber,\n      column: range.endColumn\n    });\n    const formatOptions = FormatHelper._convertOptions(model.getOptions());\n    const errorCodes = context.markers.filter((m) => m.code).map((m) => m.code).map(Number);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const codeFixes = await worker.getCodeFixesAtPosition(\n      resource.toString(),\n      start,\n      end,\n      errorCodes,\n      formatOptions\n    );\n    if (!codeFixes || model.isDisposed()) {\n      return { actions: [], dispose: () => {\n      } };\n    }\n    const actions = codeFixes.filter((fix) => {\n      return fix.changes.filter((change) => change.isNewFile).length === 0;\n    }).map((fix) => {\n      return this._tsCodeFixActionToMonacoCodeAction(model, context, fix);\n    });\n    return {\n      actions,\n      dispose: () => {\n      }\n    };\n  }\n  _tsCodeFixActionToMonacoCodeAction(model, context, codeFix) {\n    const edits = [];\n    for (const change of codeFix.changes) {\n      for (const textChange of change.textChanges) {\n        edits.push({\n          resource: model.uri,\n          versionId: void 0,\n          textEdit: {\n            range: this._textSpanToRange(model, textChange.span),\n            text: textChange.newText\n          }\n        });\n      }\n    }\n    const action = {\n      title: codeFix.description,\n      edit: { edits },\n      diagnostics: context.markers,\n      kind: \"quickfix\"\n    };\n    return action;\n  }\n};\nvar RenameAdapter = class extends Adapter {\n  constructor(_libFiles, worker) {\n    super(worker);\n    this._libFiles = _libFiles;\n  }\n  async provideRenameEdits(model, position, newName, token) {\n    const resource = model.uri;\n    const fileName = resource.toString();\n    const offset = model.getOffsetAt(position);\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return;\n    }\n    const renameInfo = await worker.getRenameInfo(fileName, offset, {\n      allowRenameOfImportPath: false\n    });\n    if (renameInfo.canRename === false) {\n      return {\n        edits: [],\n        rejectReason: renameInfo.localizedErrorMessage\n      };\n    }\n    if (renameInfo.fileToRename !== void 0) {\n      throw new Error(\"Renaming files is not supported.\");\n    }\n    const renameLocations = await worker.findRenameLocations(\n      fileName,\n      offset,\n      /*strings*/\n      false,\n      /*comments*/\n      false,\n      /*prefixAndSuffix*/\n      false\n    );\n    if (!renameLocations || model.isDisposed()) {\n      return;\n    }\n    const edits = [];\n    for (const renameLocation of renameLocations) {\n      const model2 = this._libFiles.getOrCreateModel(renameLocation.fileName);\n      if (model2) {\n        edits.push({\n          resource: model2.uri,\n          versionId: void 0,\n          textEdit: {\n            range: this._textSpanToRange(model2, renameLocation.textSpan),\n            text: newName\n          }\n        });\n      } else {\n        throw new Error(`Unknown file ${renameLocation.fileName}.`);\n      }\n    }\n    return { edits };\n  }\n};\nvar InlayHintsAdapter = class extends Adapter {\n  async provideInlayHints(model, range, token) {\n    const resource = model.uri;\n    const fileName = resource.toString();\n    const start = model.getOffsetAt({\n      lineNumber: range.startLineNumber,\n      column: range.startColumn\n    });\n    const end = model.getOffsetAt({\n      lineNumber: range.endLineNumber,\n      column: range.endColumn\n    });\n    const worker = await this._worker(resource);\n    if (model.isDisposed()) {\n      return null;\n    }\n    const tsHints = await worker.provideInlayHints(fileName, start, end);\n    const hints = tsHints.map((hint) => {\n      return {\n        ...hint,\n        label: hint.text,\n        position: model.getPositionAt(hint.position),\n        kind: this._convertHintKind(hint.kind)\n      };\n    });\n    return { hints, dispose: () => {\n    } };\n  }\n  _convertHintKind(kind) {\n    switch (kind) {\n      case \"Parameter\":\n        return monaco_editor_core_exports.languages.InlayHintKind.Parameter;\n      case \"Type\":\n        return monaco_editor_core_exports.languages.InlayHintKind.Type;\n      default:\n        return monaco_editor_core_exports.languages.InlayHintKind.Type;\n    }\n  }\n};\n\n// src/language/typescript/tsMode.ts\nvar javaScriptWorker;\nvar typeScriptWorker;\nfunction setupTypeScript(defaults) {\n  typeScriptWorker = setupMode(defaults, \"typescript\");\n}\nfunction setupJavaScript(defaults) {\n  javaScriptWorker = setupMode(defaults, \"javascript\");\n}\nfunction getJavaScriptWorker() {\n  return new Promise((resolve, reject) => {\n    if (!javaScriptWorker) {\n      return reject(\"JavaScript not registered!\");\n    }\n    resolve(javaScriptWorker);\n  });\n}\nfunction getTypeScriptWorker() {\n  return new Promise((resolve, reject) => {\n    if (!typeScriptWorker) {\n      return reject(\"TypeScript not registered!\");\n    }\n    resolve(typeScriptWorker);\n  });\n}\nfunction setupMode(defaults, modeId) {\n  const disposables = [];\n  const providers = [];\n  const client = new WorkerManager(modeId, defaults);\n  disposables.push(client);\n  const worker = (...uris) => {\n    return client.getLanguageServiceWorker(...uris);\n  };\n  const libFiles = new LibFiles(worker);\n  function registerProviders() {\n    const { modeConfiguration } = defaults;\n    disposeAll(providers);\n    if (modeConfiguration.completionItems) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCompletionItemProvider(\n          modeId,\n          new SuggestAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.signatureHelp) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerSignatureHelpProvider(\n          modeId,\n          new SignatureHelpAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.hovers) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerHoverProvider(modeId, new QuickInfoAdapter(worker))\n      );\n    }\n    if (modeConfiguration.documentHighlights) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentHighlightProvider(\n          modeId,\n          new DocumentHighlightAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.definitions) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDefinitionProvider(\n          modeId,\n          new DefinitionAdapter(libFiles, worker)\n        )\n      );\n    }\n    if (modeConfiguration.references) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerReferenceProvider(\n          modeId,\n          new ReferenceAdapter(libFiles, worker)\n        )\n      );\n    }\n    if (modeConfiguration.documentSymbols) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentSymbolProvider(\n          modeId,\n          new OutlineAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.rename) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerRenameProvider(\n          modeId,\n          new RenameAdapter(libFiles, worker)\n        )\n      );\n    }\n    if (modeConfiguration.documentRangeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerDocumentRangeFormattingEditProvider(\n          modeId,\n          new FormatAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.onTypeFormattingEdits) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerOnTypeFormattingEditProvider(\n          modeId,\n          new FormatOnTypeAdapter(worker)\n        )\n      );\n    }\n    if (modeConfiguration.codeActions) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerCodeActionProvider(modeId, new CodeActionAdaptor(worker))\n      );\n    }\n    if (modeConfiguration.inlayHints) {\n      providers.push(\n        monaco_editor_core_exports.languages.registerInlayHintsProvider(modeId, new InlayHintsAdapter(worker))\n      );\n    }\n    if (modeConfiguration.diagnostics) {\n      providers.push(new DiagnosticsAdapter(libFiles, defaults, modeId, worker));\n    }\n  }\n  registerProviders();\n  disposables.push(asDisposable(providers));\n  return worker;\n}\nfunction asDisposable(disposables) {\n  return { dispose: () => disposeAll(disposables) };\n}\nfunction disposeAll(disposables) {\n  while (disposables.length) {\n    disposables.pop().dispose();\n  }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/language/typescript/tsMode.js\n"));

/***/ })

}]);