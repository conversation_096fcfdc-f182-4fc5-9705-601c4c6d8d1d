"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/services/textSegmentationService.ts":
/*!*************************************************!*\
  !*** ./src/services/textSegmentationService.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextSegmentationService: function() { return /* binding */ TextSegmentationService; }\n/* harmony export */ });\n/**\r\n * 文本分割服务\r\n * 提供智能文本分割的核心算法，支持按字数限制和句子边界进行分割\r\n */ class TextSegmentationService {\n    /**\r\n   * 获取文本分割服务单例\r\n   */ static getInstance() {\n        if (!TextSegmentationService.instance) {\n            TextSegmentationService.instance = new TextSegmentationService();\n        }\n        return TextSegmentationService.instance;\n    }\n    /**\r\n   * 生成UUID\r\n   */ generateUUID() {\n        return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n            const r = Math.random() * 16 | 0;\n            const v = c === \"x\" ? r : r & 0x3 | 0x8;\n            return v.toString(16);\n        });\n    }\n    /**\r\n   * 智能分割文本\r\n   * @param text 要分割的文本\r\n   * @param fileId 文件ID\r\n   * @param options 分割选项\r\n   * @returns 分割结果\r\n   */ intelligentSegment(text, fileId, options) {\n        const startTime = Date.now();\n        // 预处理文本\n        const processedText = this.preprocessText(text, options);\n        // 检测语言类型\n        const detectedLanguage = this.detectLanguage(processedText, options.language);\n        // 分割成句子\n        const sentences = this.detectSentenceBoundaries(processedText, detectedLanguage);\n        // 按字数限制分组\n        const segments = this.groupSentencesByWordLimit(sentences, fileId, detectedLanguage, options);\n        const processingTime = Date.now() - startTime;\n        const totalWords = segments.reduce((sum, segment)=>sum + segment.wordCount, 0);\n        return {\n            segments,\n            totalSegments: segments.length,\n            totalWords,\n            averageWordsPerSegment: segments.length > 0 ? Math.round(totalWords / segments.length) : 0,\n            processingTime\n        };\n    }\n    /**\r\n   * 预处理文本\r\n   * @param text 原始文本\r\n   * @param options 分割选项\r\n   * @returns 处理后的文本\r\n   */ preprocessText(text, options) {\n        let processedText = text;\n        // 保护代码块\n        if (options.preserveCodeBlocks) {\n            processedText = this.protectCodeBlocks(processedText);\n        }\n        // 标准化换行符\n        processedText = processedText.replace(/\\r\\n/g, \"\\n\").replace(/\\r/g, \"\\n\");\n        // 清理多余的空白字符，但保持段落结构\n        if (options.preserveParagraphs) {\n            // 保持段落分隔和单行换行，但清理行内多余空格\n            processedText = processedText.split(\"\\n\\n\").map((paragraph)=>{\n                // 保持单行换行符，只清理连续的空格和制表符\n                return paragraph.split(\"\\n\").map((line)=>line.replace(/[ \\t]+/g, \" \").trim()).filter((line)=>line.length > 0).join(\"\\n\");\n            }).filter((paragraph)=>paragraph.length > 0).join(\"\\n\\n\");\n        } else {\n            // 简单清理空白字符，但保持换行符\n            processedText = processedText.split(\"\\n\").map((line)=>line.replace(/[ \\t]+/g, \" \").trim()).filter((line)=>line.length > 0).join(\"\\n\");\n        }\n        return processedText;\n    }\n    /**\r\n   * 保护代码块，避免在代码块内部分割\r\n   * @param text 文本\r\n   * @returns 处理后的文本\r\n   */ protectCodeBlocks(text) {\n        // 重置代码块存储\n        this.currentCodeBlocks = [];\n        // 匹配代码块（```...``` 和 `...`）\n        const codeBlockRegex = /```[\\s\\S]*?```|`[^`\\n]*`/g;\n        // 替换代码块为占位符\n        const processedText = text.replace(codeBlockRegex, (match)=>{\n            const placeholder = \"__CODE_BLOCK_\".concat(this.currentCodeBlocks.length, \"__\");\n            this.currentCodeBlocks.push(match);\n            return placeholder;\n        });\n        return processedText;\n    }\n    /**\r\n   * 恢复代码块\r\n   * @param text 包含占位符的文本\r\n   * @returns 恢复后的文本\r\n   */ restoreCodeBlocks(text) {\n        // 如果没有代码块或没有占位符，直接返回\n        if (this.currentCodeBlocks.length === 0 || !text.includes(\"__CODE_BLOCK_\")) {\n            return text;\n        }\n        let restoredText = text;\n        this.currentCodeBlocks.forEach((codeBlock, index)=>{\n            const placeholder = \"__CODE_BLOCK_\".concat(index, \"__\");\n            restoredText = restoredText.replace(new RegExp(placeholder, \"g\"), codeBlock);\n        });\n        return restoredText;\n    }\n    /**\r\n   * 检测文本语言\r\n   * @param text 文本\r\n   * @param languageHint 语言提示\r\n   * @returns 检测到的语言\r\n   */ detectLanguage(text, languageHint) {\n        if (languageHint !== \"auto\") {\n            return languageHint;\n        }\n        // 统计中文字符和英文单词\n        const chineseChars = (text.match(/[\\u4e00-\\u9fff]/g) || []).length;\n        const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;\n        const totalChars = text.length;\n        // 如果中文字符占比超过30%，认为是中文\n        if (chineseChars / totalChars > 0.3) {\n            return englishWords > chineseChars * 0.5 ? \"mixed\" : \"zh\";\n        }\n        // 如果英文单词较多，认为是英文\n        if (englishWords > 10) {\n            return chineseChars > englishWords * 0.3 ? \"mixed\" : \"en\";\n        }\n        // 默认返回混合\n        return \"mixed\";\n    }\n    /**\r\n   * 检测句子边界\r\n   * @param text 文本\r\n   * @param language 语言类型\r\n   * @returns 句子数组\r\n   */ detectSentenceBoundaries(text, language) {\n        let sentences = [];\n        if (language === \"zh\" || language === \"mixed\") {\n            // 中文句子分割模式\n            sentences = this.splitChineseSentences(text);\n        } else {\n            // 英文句子分割模式\n            sentences = this.splitEnglishSentences(text);\n        }\n        // 清理空句子和过短的句子\n        return sentences.map((sentence)=>sentence.trim()).filter((sentence)=>sentence.length > 0);\n    }\n    /**\r\n   * 分割中文句子\r\n   * @param text 文本\r\n   * @returns 句子数组\r\n   */ splitChineseSentences(text) {\n        // 中文句子结束标点：。！？；\n        // 考虑引号、括号等配对标点\n        const sentenceEndRegex = /([。！？；](?:[\"\"\"''）】}]*)?)/g;\n        const sentences = [];\n        let lastIndex = 0;\n        let match;\n        while((match = sentenceEndRegex.exec(text)) !== null){\n            const sentence = text.substring(lastIndex, match.index + match[0].length).trim();\n            if (sentence.length > 0) {\n                sentences.push(sentence);\n            }\n            lastIndex = match.index + match[0].length;\n        }\n        // 处理最后一部分文本\n        if (lastIndex < text.length) {\n            const lastSentence = text.substring(lastIndex).trim();\n            if (lastSentence.length > 0) {\n                sentences.push(lastSentence);\n            }\n        }\n        return sentences;\n    }\n    /**\r\n   * 分割英文句子\r\n   * @param text 文本\r\n   * @returns 句子数组\r\n   */ splitEnglishSentences(text) {\n        // 英文句子结束标点：. ! ? ;\n        // 需要考虑缩写词（如 Mr. Dr. etc.）\n        const abbreviations = [\n            \"Mr\",\n            \"Mrs\",\n            \"Dr\",\n            \"Prof\",\n            \"etc\",\n            \"vs\",\n            \"e.g\",\n            \"i.e\"\n        ];\n        // 先处理缩写词，避免误分割\n        let processedText = text;\n        abbreviations.forEach((abbr)=>{\n            const regex = new RegExp(\"\\\\b\".concat(abbr, \"\\\\.\"), \"gi\");\n            processedText = processedText.replace(regex, \"\".concat(abbr, \"__DOT__\"));\n        });\n        // 分割句子\n        const sentenceEndRegex = /([.!?;](?:[\"')\\]]*)?)\\s+/g;\n        const sentences = [];\n        let lastIndex = 0;\n        let match;\n        while((match = sentenceEndRegex.exec(processedText)) !== null){\n            const sentence = processedText.substring(lastIndex, match.index + match[1].length).trim();\n            if (sentence.length > 0) {\n                sentences.push(sentence);\n            }\n            lastIndex = match.index + match[0].length;\n        }\n        // 处理最后一部分文本\n        if (lastIndex < processedText.length) {\n            const lastSentence = processedText.substring(lastIndex).trim();\n            if (lastSentence.length > 0) {\n                sentences.push(lastSentence);\n            }\n        }\n        // 恢复缩写词中的点号\n        return sentences.map((sentence)=>sentence.replace(/__DOT__/g, \".\"));\n    }\n    /**\r\n   * 按字数限制分组句子\r\n   * @param sentences 句子数组\r\n   * @param fileId 文件ID\r\n   * @param language 语言类型\r\n   * @param options 分割选项\r\n   * @returns 文本段落数组\r\n   */ groupSentencesByWordLimit(sentences, fileId, language, options) {\n        const segments = [];\n        let currentSegment = \"\";\n        let currentWordCount = 0;\n        let currentSentenceCount = 0;\n        let startIndex = 0;\n        for(let i = 0; i < sentences.length; i++){\n            const sentence = sentences[i];\n            const sentenceWordCount = this.countWords(sentence, language);\n            // 如果单个句子就超过最大字数限制，需要强制分割\n            if (sentenceWordCount > options.maxWords) {\n                // 先保存当前段落（如果有内容）\n                if (currentSegment.trim().length > 0) {\n                    segments.push(this.createTextSegment(fileId, currentSegment.trim(), startIndex, startIndex + currentSegment.length, currentWordCount, currentSentenceCount, language));\n                }\n                // 强制分割长句子\n                const forceSplitSegments = this.forceSplitLongSentence(sentence, fileId, language, options.maxWords, startIndex + currentSegment.length);\n                segments.push(...forceSplitSegments);\n                // 重置当前段落\n                currentSegment = \"\";\n                currentWordCount = 0;\n                currentSentenceCount = 0;\n                startIndex = startIndex + currentSegment.length + sentence.length;\n                continue;\n            }\n            // 检查是否可以添加到当前段落\n            if (currentWordCount + sentenceWordCount <= options.maxWords) {\n                // 可以添加\n                if (currentSegment.length > 0) {\n                    currentSegment += \" \";\n                }\n                currentSegment += sentence;\n                currentWordCount += sentenceWordCount;\n                currentSentenceCount++;\n            } else {\n                // 不能添加，需要创建新段落\n                if (currentSegment.trim().length > 0) {\n                    segments.push(this.createTextSegment(fileId, currentSegment.trim(), startIndex, startIndex + currentSegment.length, currentWordCount, currentSentenceCount, language));\n                }\n                // 开始新段落\n                startIndex = startIndex + currentSegment.length;\n                currentSegment = sentence;\n                currentWordCount = sentenceWordCount;\n                currentSentenceCount = 1;\n            }\n        }\n        // 处理最后一个段落\n        if (currentSegment.trim().length > 0) {\n            // 检查是否满足最小字数要求\n            if (!options.minWords || currentWordCount >= options.minWords) {\n                segments.push(this.createTextSegment(fileId, currentSegment.trim(), startIndex, startIndex + currentSegment.length, currentWordCount, currentSentenceCount, language));\n            } else if (segments.length > 0) {\n                // 如果不满足最小字数，合并到上一个段落\n                const lastSegment = segments[segments.length - 1];\n                lastSegment.content += \" \" + currentSegment.trim();\n                lastSegment.endIndex = startIndex + currentSegment.length;\n                lastSegment.wordCount += currentWordCount;\n                lastSegment.sentenceCount += currentSentenceCount;\n            } else {\n                // 如果是唯一的段落，即使不满足最小字数也要保留\n                segments.push(this.createTextSegment(fileId, currentSegment.trim(), startIndex, startIndex + currentSegment.length, currentWordCount, currentSentenceCount, language));\n            }\n        }\n        return segments;\n    }\n    /**\r\n   * 强制分割过长的句子\r\n   * @param sentence 长句子\r\n   * @param fileId 文件ID\r\n   * @param language 语言类型\r\n   * @param maxWords 最大字数\r\n   * @param baseIndex 基础索引\r\n   * @returns 分割后的段落数组\r\n   */ forceSplitLongSentence(sentence, fileId, language, maxWords, baseIndex) {\n        const segments = [];\n        const words = language === \"zh\" ? sentence.split(\"\") : sentence.split(/\\s+/);\n        let currentChunk = \"\";\n        let currentIndex = baseIndex;\n        for(let i = 0; i < words.length; i += maxWords){\n            const chunk = words.slice(i, i + maxWords);\n            const chunkText = language === \"zh\" ? chunk.join(\"\") : chunk.join(\" \");\n            const wordCount = this.countWords(chunkText, language);\n            segments.push(this.createTextSegment(fileId, chunkText, currentIndex, currentIndex + chunkText.length, wordCount, 1, language));\n            currentIndex += chunkText.length;\n        }\n        return segments;\n    }\n    /**\r\n   * 统计字数\r\n   * @param text 文本\r\n   * @param language 语言类型\r\n   * @returns 字数\r\n   */ countWords(text, language) {\n        if (language === \"zh\") {\n            // 中文按字符数计算\n            return text.replace(/\\s/g, \"\").length;\n        } else if (language === \"en\") {\n            // 英文按单词数计算\n            return text.trim().split(/\\s+/).filter((word)=>word.length > 0).length;\n        } else {\n            // 混合语言：中文字符数 + 英文单词数\n            const chineseChars = (text.match(/[\\u4e00-\\u9fff]/g) || []).length;\n            const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;\n            return chineseChars + englishWords;\n        }\n    }\n    /**\r\n   * 创建文本段落对象\r\n   * @param fileId 文件ID\r\n   * @param content 内容\r\n   * @param startIndex 起始索引\r\n   * @param endIndex 结束索引\r\n   * @param wordCount 字数\r\n   * @param sentenceCount 句子数\r\n   * @param language 语言类型\r\n   * @returns 文本段落对象\r\n   */ createTextSegment(fileId, content, startIndex, endIndex, wordCount, sentenceCount, language) {\n        // 恢复代码块（如果有）\n        const restoredContent = this.restoreCodeBlocks(content);\n        return {\n            id: this.generateUUID(),\n            fileId,\n            content: restoredContent,\n            startIndex,\n            endIndex,\n            wordCount,\n            sentenceCount,\n            language,\n            createdAt: Date.now()\n        };\n    }\n    /**\r\n   * 为文本内容添加句子引入标记\r\n   * @param content 原始内容\r\n   * @param language 语言类型\r\n   * @returns 添加标记后的内容\r\n   */ addSentenceMarkers(content) {\n        let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"auto\";\n        // 检测语言类型\n        const detectedLanguage = language === \"auto\" ? this.detectLanguage(content, \"auto\") : language;\n        // 分割成句子\n        const sentences = this.detectSentenceBoundaries(content, detectedLanguage);\n        // 为每个句子添加引入标记\n        const markedSentences = sentences.map((sentence)=>{\n            const trimmedSentence = sentence.trim();\n            if (trimmedSentence.length === 0) {\n                return \"\";\n            }\n            return \"\".concat(trimmedSentence);\n        }).filter((sentence)=>sentence.length > 0);\n        // 用换行符连接句子，作为句子间的分隔标记\n        return markedSentences.join(\"\\n\\n\");\n    }\n    /**\r\n   * 按字数分割文本（简单模式）\r\n   * @param text 文本\r\n   * @param fileId 文件ID\r\n   * @param maxWords 最大字数\r\n   * @returns 文本段落数组\r\n   */ segmentByWordCount(text, fileId) {\n        let maxWords = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 500;\n        const options = {\n            maxWords,\n            language: \"auto\",\n            preserveParagraphs: false,\n            preserveCodeBlocks: false\n        };\n        const result = this.intelligentSegment(text, fileId, options);\n        return result.segments;\n    }\n    constructor(){\n        // 存储当前处理的代码块\n        this.currentCodeBlocks = [];\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/textSegmentationService.ts\n"));

/***/ })

});