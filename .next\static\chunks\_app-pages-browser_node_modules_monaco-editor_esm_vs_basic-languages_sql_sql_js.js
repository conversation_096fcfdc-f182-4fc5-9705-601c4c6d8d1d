"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_sql_sql_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/sql/sql.js":
/*!**********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/sql/sql.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/sql/sql.ts\nvar conf = {\n  comments: {\n    lineComment: \"--\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".sql\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    // This list is generated using `keywords.js`\n    \"ABORT\",\n    \"ABSOLUTE\",\n    \"ACTION\",\n    \"ADA\",\n    \"ADD\",\n    \"AFTER\",\n    \"ALL\",\n    \"ALLOCATE\",\n    \"ALTER\",\n    \"ALWAYS\",\n    \"ANALYZE\",\n    \"AND\",\n    \"ANY\",\n    \"ARE\",\n    \"AS\",\n    \"ASC\",\n    \"ASSERTION\",\n    \"AT\",\n    \"ATTACH\",\n    \"AUTHORIZATION\",\n    \"AUTOINCREMENT\",\n    \"AVG\",\n    \"BACKUP\",\n    \"BEFORE\",\n    \"BEGIN\",\n    \"BETWEEN\",\n    \"BIT\",\n    \"BIT_LENGTH\",\n    \"BOTH\",\n    \"BREAK\",\n    \"BROWSE\",\n    \"BULK\",\n    \"BY\",\n    \"CASCADE\",\n    \"CASCADED\",\n    \"CASE\",\n    \"CAST\",\n    \"CATALOG\",\n    \"CHAR\",\n    \"CHARACTER\",\n    \"CHARACTER_LENGTH\",\n    \"CHAR_LENGTH\",\n    \"CHECK\",\n    \"CHECKPOINT\",\n    \"CLOSE\",\n    \"CLUSTERED\",\n    \"COALESCE\",\n    \"COLLATE\",\n    \"COLLATION\",\n    \"COLUMN\",\n    \"COMMIT\",\n    \"COMPUTE\",\n    \"CONFLICT\",\n    \"CONNECT\",\n    \"CONNECTION\",\n    \"CONSTRAINT\",\n    \"CONSTRAINTS\",\n    \"CONTAINS\",\n    \"CONTAINSTABLE\",\n    \"CONTINUE\",\n    \"CONVERT\",\n    \"CORRESPONDING\",\n    \"COUNT\",\n    \"CREATE\",\n    \"CROSS\",\n    \"CURRENT\",\n    \"CURRENT_DATE\",\n    \"CURRENT_TIME\",\n    \"CURRENT_TIMESTAMP\",\n    \"CURRENT_USER\",\n    \"CURSOR\",\n    \"DATABASE\",\n    \"DATE\",\n    \"DAY\",\n    \"DBCC\",\n    \"DEALLOCATE\",\n    \"DEC\",\n    \"DECIMAL\",\n    \"DECLARE\",\n    \"DEFAULT\",\n    \"DEFERRABLE\",\n    \"DEFERRED\",\n    \"DELETE\",\n    \"DENY\",\n    \"DESC\",\n    \"DESCRIBE\",\n    \"DESCRIPTOR\",\n    \"DETACH\",\n    \"DIAGNOSTICS\",\n    \"DISCONNECT\",\n    \"DISK\",\n    \"DISTINCT\",\n    \"DISTRIBUTED\",\n    \"DO\",\n    \"DOMAIN\",\n    \"DOUBLE\",\n    \"DROP\",\n    \"DUMP\",\n    \"EACH\",\n    \"ELSE\",\n    \"END\",\n    \"END-EXEC\",\n    \"ERRLVL\",\n    \"ESCAPE\",\n    \"EXCEPT\",\n    \"EXCEPTION\",\n    \"EXCLUDE\",\n    \"EXCLUSIVE\",\n    \"EXEC\",\n    \"EXECUTE\",\n    \"EXISTS\",\n    \"EXIT\",\n    \"EXPLAIN\",\n    \"EXTERNAL\",\n    \"EXTRACT\",\n    \"FAIL\",\n    \"FALSE\",\n    \"FETCH\",\n    \"FILE\",\n    \"FILLFACTOR\",\n    \"FILTER\",\n    \"FIRST\",\n    \"FLOAT\",\n    \"FOLLOWING\",\n    \"FOR\",\n    \"FOREIGN\",\n    \"FORTRAN\",\n    \"FOUND\",\n    \"FREETEXT\",\n    \"FREETEXTTABLE\",\n    \"FROM\",\n    \"FULL\",\n    \"FUNCTION\",\n    \"GENERATED\",\n    \"GET\",\n    \"GLOB\",\n    \"GLOBAL\",\n    \"GO\",\n    \"GOTO\",\n    \"GRANT\",\n    \"GROUP\",\n    \"GROUPS\",\n    \"HAVING\",\n    \"HOLDLOCK\",\n    \"HOUR\",\n    \"IDENTITY\",\n    \"IDENTITYCOL\",\n    \"IDENTITY_INSERT\",\n    \"IF\",\n    \"IGNORE\",\n    \"IMMEDIATE\",\n    \"IN\",\n    \"INCLUDE\",\n    \"INDEX\",\n    \"INDEXED\",\n    \"INDICATOR\",\n    \"INITIALLY\",\n    \"INNER\",\n    \"INPUT\",\n    \"INSENSITIVE\",\n    \"INSERT\",\n    \"INSTEAD\",\n    \"INT\",\n    \"INTEGER\",\n    \"INTERSECT\",\n    \"INTERVAL\",\n    \"INTO\",\n    \"IS\",\n    \"ISNULL\",\n    \"ISOLATION\",\n    \"JOIN\",\n    \"KEY\",\n    \"KILL\",\n    \"LANGUAGE\",\n    \"LAST\",\n    \"LEADING\",\n    \"LEFT\",\n    \"LEVEL\",\n    \"LIKE\",\n    \"LIMIT\",\n    \"LINENO\",\n    \"LOAD\",\n    \"LOCAL\",\n    \"LOWER\",\n    \"MATCH\",\n    \"MATERIALIZED\",\n    \"MAX\",\n    \"MERGE\",\n    \"MIN\",\n    \"MINUTE\",\n    \"MODULE\",\n    \"MONTH\",\n    \"NAMES\",\n    \"NATIONAL\",\n    \"NATURAL\",\n    \"NCHAR\",\n    \"NEXT\",\n    \"NO\",\n    \"NOCHECK\",\n    \"NONCLUSTERED\",\n    \"NONE\",\n    \"NOT\",\n    \"NOTHING\",\n    \"NOTNULL\",\n    \"NULL\",\n    \"NULLIF\",\n    \"NULLS\",\n    \"NUMERIC\",\n    \"OCTET_LENGTH\",\n    \"OF\",\n    \"OFF\",\n    \"OFFSET\",\n    \"OFFSETS\",\n    \"ON\",\n    \"ONLY\",\n    \"OPEN\",\n    \"OPENDATASOURCE\",\n    \"OPENQUERY\",\n    \"OPENROWSET\",\n    \"OPENXML\",\n    \"OPTION\",\n    \"OR\",\n    \"ORDER\",\n    \"OTHERS\",\n    \"OUTER\",\n    \"OUTPUT\",\n    \"OVER\",\n    \"OVERLAPS\",\n    \"PAD\",\n    \"PARTIAL\",\n    \"PARTITION\",\n    \"PASCAL\",\n    \"PERCENT\",\n    \"PIVOT\",\n    \"PLAN\",\n    \"POSITION\",\n    \"PRAGMA\",\n    \"PRECEDING\",\n    \"PRECISION\",\n    \"PREPARE\",\n    \"PRESERVE\",\n    \"PRIMARY\",\n    \"PRINT\",\n    \"PRIOR\",\n    \"PRIVILEGES\",\n    \"PROC\",\n    \"PROCEDURE\",\n    \"PUBLIC\",\n    \"QUERY\",\n    \"RAISE\",\n    \"RAISERROR\",\n    \"RANGE\",\n    \"READ\",\n    \"READTEXT\",\n    \"REAL\",\n    \"RECONFIGURE\",\n    \"RECURSIVE\",\n    \"REFERENCES\",\n    \"REGEXP\",\n    \"REINDEX\",\n    \"RELATIVE\",\n    \"RELEASE\",\n    \"RENAME\",\n    \"REPLACE\",\n    \"REPLICATION\",\n    \"RESTORE\",\n    \"RESTRICT\",\n    \"RETURN\",\n    \"RETURNING\",\n    \"REVERT\",\n    \"REVOKE\",\n    \"RIGHT\",\n    \"ROLLBACK\",\n    \"ROW\",\n    \"ROWCOUNT\",\n    \"ROWGUIDCOL\",\n    \"ROWS\",\n    \"RULE\",\n    \"SAVE\",\n    \"SAVEPOINT\",\n    \"SCHEMA\",\n    \"SCROLL\",\n    \"SECOND\",\n    \"SECTION\",\n    \"SECURITYAUDIT\",\n    \"SELECT\",\n    \"SEMANTICKEYPHRASETABLE\",\n    \"SEMANTICSIMILARITYDETAILSTABLE\",\n    \"SEMANTICSIMILARITYTABLE\",\n    \"SESSION\",\n    \"SESSION_USER\",\n    \"SET\",\n    \"SETUSER\",\n    \"SHUTDOWN\",\n    \"SIZE\",\n    \"SMALLINT\",\n    \"SOME\",\n    \"SPACE\",\n    \"SQL\",\n    \"SQLCA\",\n    \"SQLCODE\",\n    \"SQLERROR\",\n    \"SQLSTATE\",\n    \"SQLWARNING\",\n    \"STATISTICS\",\n    \"SUBSTRING\",\n    \"SUM\",\n    \"SYSTEM_USER\",\n    \"TABLE\",\n    \"TABLESAMPLE\",\n    \"TEMP\",\n    \"TEMPORARY\",\n    \"TEXTSIZE\",\n    \"THEN\",\n    \"TIES\",\n    \"TIME\",\n    \"TIMESTAMP\",\n    \"TIMEZONE_HOUR\",\n    \"TIMEZONE_MINUTE\",\n    \"TO\",\n    \"TOP\",\n    \"TRAILING\",\n    \"TRAN\",\n    \"TRANSACTION\",\n    \"TRANSLATE\",\n    \"TRANSLATION\",\n    \"TRIGGER\",\n    \"TRIM\",\n    \"TRUE\",\n    \"TRUNCATE\",\n    \"TRY_CONVERT\",\n    \"TSEQUAL\",\n    \"UNBOUNDED\",\n    \"UNION\",\n    \"UNIQUE\",\n    \"UNKNOWN\",\n    \"UNPIVOT\",\n    \"UPDATE\",\n    \"UPDATETEXT\",\n    \"UPPER\",\n    \"USAGE\",\n    \"USE\",\n    \"USER\",\n    \"USING\",\n    \"VACUUM\",\n    \"VALUE\",\n    \"VALUES\",\n    \"VARCHAR\",\n    \"VARYING\",\n    \"VIEW\",\n    \"VIRTUAL\",\n    \"WAITFOR\",\n    \"WHEN\",\n    \"WHENEVER\",\n    \"WHERE\",\n    \"WHILE\",\n    \"WINDOW\",\n    \"WITH\",\n    \"WITHIN GROUP\",\n    \"WITHOUT\",\n    \"WORK\",\n    \"WRITE\",\n    \"WRITETEXT\",\n    \"YEAR\",\n    \"ZONE\"\n  ],\n  operators: [\n    // Logical\n    \"ALL\",\n    \"AND\",\n    \"ANY\",\n    \"BETWEEN\",\n    \"EXISTS\",\n    \"IN\",\n    \"LIKE\",\n    \"NOT\",\n    \"OR\",\n    \"SOME\",\n    // Set\n    \"EXCEPT\",\n    \"INTERSECT\",\n    \"UNION\",\n    // Join\n    \"APPLY\",\n    \"CROSS\",\n    \"FULL\",\n    \"INNER\",\n    \"JOIN\",\n    \"LEFT\",\n    \"OUTER\",\n    \"RIGHT\",\n    // Predicates\n    \"CONTAINS\",\n    \"FREETEXT\",\n    \"IS\",\n    \"NULL\",\n    // Pivoting\n    \"PIVOT\",\n    \"UNPIVOT\",\n    // Merging\n    \"MATCHED\"\n  ],\n  builtinFunctions: [\n    // Aggregate\n    \"AVG\",\n    \"CHECKSUM_AGG\",\n    \"COUNT\",\n    \"COUNT_BIG\",\n    \"GROUPING\",\n    \"GROUPING_ID\",\n    \"MAX\",\n    \"MIN\",\n    \"SUM\",\n    \"STDEV\",\n    \"STDEVP\",\n    \"VAR\",\n    \"VARP\",\n    // Analytic\n    \"CUME_DIST\",\n    \"FIRST_VALUE\",\n    \"LAG\",\n    \"LAST_VALUE\",\n    \"LEAD\",\n    \"PERCENTILE_CONT\",\n    \"PERCENTILE_DISC\",\n    \"PERCENT_RANK\",\n    // Collation\n    \"COLLATE\",\n    \"COLLATIONPROPERTY\",\n    \"TERTIARY_WEIGHTS\",\n    // Azure\n    \"FEDERATION_FILTERING_VALUE\",\n    // Conversion\n    \"CAST\",\n    \"CONVERT\",\n    \"PARSE\",\n    \"TRY_CAST\",\n    \"TRY_CONVERT\",\n    \"TRY_PARSE\",\n    // Cryptographic\n    \"ASYMKEY_ID\",\n    \"ASYMKEYPROPERTY\",\n    \"CERTPROPERTY\",\n    \"CERT_ID\",\n    \"CRYPT_GEN_RANDOM\",\n    \"DECRYPTBYASYMKEY\",\n    \"DECRYPTBYCERT\",\n    \"DECRYPTBYKEY\",\n    \"DECRYPTBYKEYAUTOASYMKEY\",\n    \"DECRYPTBYKEYAUTOCERT\",\n    \"DECRYPTBYPASSPHRASE\",\n    \"ENCRYPTBYASYMKEY\",\n    \"ENCRYPTBYCERT\",\n    \"ENCRYPTBYKEY\",\n    \"ENCRYPTBYPASSPHRASE\",\n    \"HASHBYTES\",\n    \"IS_OBJECTSIGNED\",\n    \"KEY_GUID\",\n    \"KEY_ID\",\n    \"KEY_NAME\",\n    \"SIGNBYASYMKEY\",\n    \"SIGNBYCERT\",\n    \"SYMKEYPROPERTY\",\n    \"VERIFYSIGNEDBYCERT\",\n    \"VERIFYSIGNEDBYASYMKEY\",\n    // Cursor\n    \"CURSOR_STATUS\",\n    // Datatype\n    \"DATALENGTH\",\n    \"IDENT_CURRENT\",\n    \"IDENT_INCR\",\n    \"IDENT_SEED\",\n    \"IDENTITY\",\n    \"SQL_VARIANT_PROPERTY\",\n    // Datetime\n    \"CURRENT_TIMESTAMP\",\n    \"DATEADD\",\n    \"DATEDIFF\",\n    \"DATEFROMPARTS\",\n    \"DATENAME\",\n    \"DATEPART\",\n    \"DATETIME2FROMPARTS\",\n    \"DATETIMEFROMPARTS\",\n    \"DATETIMEOFFSETFROMPARTS\",\n    \"DAY\",\n    \"EOMONTH\",\n    \"GETDATE\",\n    \"GETUTCDATE\",\n    \"ISDATE\",\n    \"MONTH\",\n    \"SMALLDATETIMEFROMPARTS\",\n    \"SWITCHOFFSET\",\n    \"SYSDATETIME\",\n    \"SYSDATETIMEOFFSET\",\n    \"SYSUTCDATETIME\",\n    \"TIMEFROMPARTS\",\n    \"TODATETIMEOFFSET\",\n    \"YEAR\",\n    // Logical\n    \"CHOOSE\",\n    \"COALESCE\",\n    \"IIF\",\n    \"NULLIF\",\n    // Mathematical\n    \"ABS\",\n    \"ACOS\",\n    \"ASIN\",\n    \"ATAN\",\n    \"ATN2\",\n    \"CEILING\",\n    \"COS\",\n    \"COT\",\n    \"DEGREES\",\n    \"EXP\",\n    \"FLOOR\",\n    \"LOG\",\n    \"LOG10\",\n    \"PI\",\n    \"POWER\",\n    \"RADIANS\",\n    \"RAND\",\n    \"ROUND\",\n    \"SIGN\",\n    \"SIN\",\n    \"SQRT\",\n    \"SQUARE\",\n    \"TAN\",\n    // Metadata\n    \"APP_NAME\",\n    \"APPLOCK_MODE\",\n    \"APPLOCK_TEST\",\n    \"ASSEMBLYPROPERTY\",\n    \"COL_LENGTH\",\n    \"COL_NAME\",\n    \"COLUMNPROPERTY\",\n    \"DATABASE_PRINCIPAL_ID\",\n    \"DATABASEPROPERTYEX\",\n    \"DB_ID\",\n    \"DB_NAME\",\n    \"FILE_ID\",\n    \"FILE_IDEX\",\n    \"FILE_NAME\",\n    \"FILEGROUP_ID\",\n    \"FILEGROUP_NAME\",\n    \"FILEGROUPPROPERTY\",\n    \"FILEPROPERTY\",\n    \"FULLTEXTCATALOGPROPERTY\",\n    \"FULLTEXTSERVICEPROPERTY\",\n    \"INDEX_COL\",\n    \"INDEXKEY_PROPERTY\",\n    \"INDEXPROPERTY\",\n    \"OBJECT_DEFINITION\",\n    \"OBJECT_ID\",\n    \"OBJECT_NAME\",\n    \"OBJECT_SCHEMA_NAME\",\n    \"OBJECTPROPERTY\",\n    \"OBJECTPROPERTYEX\",\n    \"ORIGINAL_DB_NAME\",\n    \"PARSENAME\",\n    \"SCHEMA_ID\",\n    \"SCHEMA_NAME\",\n    \"SCOPE_IDENTITY\",\n    \"SERVERPROPERTY\",\n    \"STATS_DATE\",\n    \"TYPE_ID\",\n    \"TYPE_NAME\",\n    \"TYPEPROPERTY\",\n    // Ranking\n    \"DENSE_RANK\",\n    \"NTILE\",\n    \"RANK\",\n    \"ROW_NUMBER\",\n    // Replication\n    \"PUBLISHINGSERVERNAME\",\n    // Rowset\n    \"OPENDATASOURCE\",\n    \"OPENQUERY\",\n    \"OPENROWSET\",\n    \"OPENXML\",\n    // Security\n    \"CERTENCODED\",\n    \"CERTPRIVATEKEY\",\n    \"CURRENT_USER\",\n    \"HAS_DBACCESS\",\n    \"HAS_PERMS_BY_NAME\",\n    \"IS_MEMBER\",\n    \"IS_ROLEMEMBER\",\n    \"IS_SRVROLEMEMBER\",\n    \"LOGINPROPERTY\",\n    \"ORIGINAL_LOGIN\",\n    \"PERMISSIONS\",\n    \"PWDENCRYPT\",\n    \"PWDCOMPARE\",\n    \"SESSION_USER\",\n    \"SESSIONPROPERTY\",\n    \"SUSER_ID\",\n    \"SUSER_NAME\",\n    \"SUSER_SID\",\n    \"SUSER_SNAME\",\n    \"SYSTEM_USER\",\n    \"USER\",\n    \"USER_ID\",\n    \"USER_NAME\",\n    // String\n    \"ASCII\",\n    \"CHAR\",\n    \"CHARINDEX\",\n    \"CONCAT\",\n    \"DIFFERENCE\",\n    \"FORMAT\",\n    \"LEFT\",\n    \"LEN\",\n    \"LOWER\",\n    \"LTRIM\",\n    \"NCHAR\",\n    \"PATINDEX\",\n    \"QUOTENAME\",\n    \"REPLACE\",\n    \"REPLICATE\",\n    \"REVERSE\",\n    \"RIGHT\",\n    \"RTRIM\",\n    \"SOUNDEX\",\n    \"SPACE\",\n    \"STR\",\n    \"STUFF\",\n    \"SUBSTRING\",\n    \"UNICODE\",\n    \"UPPER\",\n    // System\n    \"BINARY_CHECKSUM\",\n    \"CHECKSUM\",\n    \"CONNECTIONPROPERTY\",\n    \"CONTEXT_INFO\",\n    \"CURRENT_REQUEST_ID\",\n    \"ERROR_LINE\",\n    \"ERROR_NUMBER\",\n    \"ERROR_MESSAGE\",\n    \"ERROR_PROCEDURE\",\n    \"ERROR_SEVERITY\",\n    \"ERROR_STATE\",\n    \"FORMATMESSAGE\",\n    \"GETANSINULL\",\n    \"GET_FILESTREAM_TRANSACTION_CONTEXT\",\n    \"HOST_ID\",\n    \"HOST_NAME\",\n    \"ISNULL\",\n    \"ISNUMERIC\",\n    \"MIN_ACTIVE_ROWVERSION\",\n    \"NEWID\",\n    \"NEWSEQUENTIALID\",\n    \"ROWCOUNT_BIG\",\n    \"XACT_STATE\",\n    // TextImage\n    \"TEXTPTR\",\n    \"TEXTVALID\",\n    // Trigger\n    \"COLUMNS_UPDATED\",\n    \"EVENTDATA\",\n    \"TRIGGER_NESTLEVEL\",\n    \"UPDATE\",\n    // ChangeTracking\n    \"CHANGETABLE\",\n    \"CHANGE_TRACKING_CONTEXT\",\n    \"CHANGE_TRACKING_CURRENT_VERSION\",\n    \"CHANGE_TRACKING_IS_COLUMN_IN_MASK\",\n    \"CHANGE_TRACKING_MIN_VALID_VERSION\",\n    // FullTextSearch\n    \"CONTAINSTABLE\",\n    \"FREETEXTTABLE\",\n    // SemanticTextSearch\n    \"SEMANTICKEYPHRASETABLE\",\n    \"SEMANTICSIMILARITYDETAILSTABLE\",\n    \"SEMANTICSIMILARITYTABLE\",\n    // FileStream\n    \"FILETABLEROOTPATH\",\n    \"GETFILENAMESPACEPATH\",\n    \"GETPATHLOCATOR\",\n    \"PATHNAME\",\n    // ServiceBroker\n    \"GET_TRANSMISSION_STATUS\"\n  ],\n  builtinVariables: [\n    // Configuration\n    \"@@DATEFIRST\",\n    \"@@DBTS\",\n    \"@@LANGID\",\n    \"@@LANGUAGE\",\n    \"@@LOCK_TIMEOUT\",\n    \"@@MAX_CONNECTIONS\",\n    \"@@MAX_PRECISION\",\n    \"@@NESTLEVEL\",\n    \"@@OPTIONS\",\n    \"@@REMSERVER\",\n    \"@@SERVERNAME\",\n    \"@@SERVICENAME\",\n    \"@@SPID\",\n    \"@@TEXTSIZE\",\n    \"@@VERSION\",\n    // Cursor\n    \"@@CURSOR_ROWS\",\n    \"@@FETCH_STATUS\",\n    // Datetime\n    \"@@DATEFIRST\",\n    // Metadata\n    \"@@PROCID\",\n    // System\n    \"@@ERROR\",\n    \"@@IDENTITY\",\n    \"@@ROWCOUNT\",\n    \"@@TRANCOUNT\",\n    // Stats\n    \"@@CONNECTIONS\",\n    \"@@CPU_BUSY\",\n    \"@@IDLE\",\n    \"@@IO_BUSY\",\n    \"@@PACKET_ERRORS\",\n    \"@@PACK_RECEIVED\",\n    \"@@PACK_SENT\",\n    \"@@TIMETICKS\",\n    \"@@TOTAL_ERRORS\",\n    \"@@TOTAL_READ\",\n    \"@@TOTAL_WRITE\"\n  ],\n  pseudoColumns: [\"$ACTION\", \"$IDENTITY\", \"$ROWGUID\", \"$PARTITION\"],\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@pseudoColumns\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@complexIdentifiers\" },\n      { include: \"@scopes\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[()]/, \"@brackets\"],\n      [\n        /[\\w@#$]+/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@builtinVariables\": \"predefined\",\n            \"@builtinFunctions\": \"predefined\",\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [/--+.*/, \"comment\"],\n      [/\\/\\*/, { token: \"comment.quote\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^*/]+/, \"comment\"],\n      // Not supporting nested comments, as nested comments seem to not be standard?\n      // i.e. http://stackoverflow.com/questions/728172/are-there-multiline-comment-delimiters-in-sql-that-are-vendor-agnostic\n      // [/\\/\\*/, { token: 'comment.quote', next: '@push' }],    // nested comment not allowed :-(\n      [/\\*\\//, { token: \"comment.quote\", next: \"@pop\" }],\n      [/./, \"comment\"]\n    ],\n    pseudoColumns: [\n      [\n        /[$][A-Za-z_][\\w@#$]*/,\n        {\n          cases: {\n            \"@pseudoColumns\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [\n      [/N'/, { token: \"string\", next: \"@string\" }],\n      [/'/, { token: \"string\", next: \"@string\" }]\n    ],\n    string: [\n      [/[^']+/, \"string\"],\n      [/''/, \"string\"],\n      [/'/, { token: \"string\", next: \"@pop\" }]\n    ],\n    complexIdentifiers: [\n      [/\\[/, { token: \"identifier.quote\", next: \"@bracketedIdentifier\" }],\n      [/\"/, { token: \"identifier.quote\", next: \"@quotedIdentifier\" }]\n    ],\n    bracketedIdentifier: [\n      [/[^\\]]+/, \"identifier\"],\n      [/]]/, \"identifier\"],\n      [/]/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    quotedIdentifier: [\n      [/[^\"]+/, \"identifier\"],\n      [/\"\"/, \"identifier\"],\n      [/\"/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    scopes: [\n      [/BEGIN\\s+(DISTRIBUTED\\s+)?TRAN(SACTION)?\\b/i, \"keyword\"],\n      [/BEGIN\\s+TRY\\b/i, { token: \"keyword.try\" }],\n      [/END\\s+TRY\\b/i, { token: \"keyword.try\" }],\n      [/BEGIN\\s+CATCH\\b/i, { token: \"keyword.catch\" }],\n      [/END\\s+CATCH\\b/i, { token: \"keyword.catch\" }],\n      [/(BEGIN|CASE)\\b/i, { token: \"keyword.block\" }],\n      [/END\\b/i, { token: \"keyword.block\" }],\n      [/WHEN\\b/i, { token: \"keyword.choice\" }],\n      [/THEN\\b/i, { token: \"keyword.choice\" }]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/sql/sql.js\n"));

/***/ })

}]);