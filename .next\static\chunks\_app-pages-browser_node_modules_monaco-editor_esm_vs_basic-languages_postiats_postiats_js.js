"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_postiats_postiats_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/postiats/postiats.js":
/*!********************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/postiats/postiats.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/postiats/postiats.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"(*\", \"*)\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"<\", \">\"]\n  ],\n  autoClosingPairs: [\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] }\n  ]\n};\nvar language = {\n  tokenPostfix: \".pats\",\n  // TODO: staload and dynload are followed by a special kind of string literals\n  // with {$IDENTIFER} variables, and it also may make sense to highlight\n  // the punctuation (. and / and \\) differently.\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  defaultToken: \"invalid\",\n  // keyword reference: https://github.com/githwxi/ATS-Postiats/blob/master/src/pats_lexing_token.dats\n  keywords: [\n    //\n    \"abstype\",\n    // ABSTYPE\n    \"abst0ype\",\n    // ABST0YPE\n    \"absprop\",\n    // ABSPROP\n    \"absview\",\n    // ABSVIEW\n    \"absvtype\",\n    // ABSVIEWTYPE\n    \"absviewtype\",\n    // ABSVIEWTYPE\n    \"absvt0ype\",\n    // ABSVIEWT0YPE\n    \"absviewt0ype\",\n    // ABSVIEWT0YPE\n    //\n    \"as\",\n    // T_AS\n    //\n    \"and\",\n    // T_AND\n    //\n    \"assume\",\n    // T_ASSUME\n    //\n    \"begin\",\n    // T_BEGIN\n    //\n    /*\n    \t\t\"case\", // CASE\n    */\n    //\n    \"classdec\",\n    // T_CLASSDEC\n    //\n    \"datasort\",\n    // T_DATASORT\n    //\n    \"datatype\",\n    // DATATYPE\n    \"dataprop\",\n    // DATAPROP\n    \"dataview\",\n    // DATAVIEW\n    \"datavtype\",\n    // DATAVIEWTYPE\n    \"dataviewtype\",\n    // DATAVIEWTYPE\n    //\n    \"do\",\n    // T_DO\n    //\n    \"end\",\n    // T_END\n    //\n    \"extern\",\n    // T_EXTERN\n    \"extype\",\n    // T_EXTYPE\n    \"extvar\",\n    // T_EXTVAR\n    //\n    \"exception\",\n    // T_EXCEPTION\n    //\n    \"fn\",\n    // FN // non-recursive\n    \"fnx\",\n    // FNX // mutual tail-rec.\n    \"fun\",\n    // FUN // general-recursive\n    //\n    \"prfn\",\n    // PRFN\n    \"prfun\",\n    // PRFUN\n    //\n    \"praxi\",\n    // PRAXI\n    \"castfn\",\n    // CASTFN\n    //\n    \"if\",\n    // T_IF\n    \"then\",\n    // T_THEN\n    \"else\",\n    // T_ELSE\n    //\n    \"ifcase\",\n    // T_IFCASE\n    //\n    \"in\",\n    // T_IN\n    //\n    \"infix\",\n    // INFIX\n    \"infixl\",\n    // INFIXL\n    \"infixr\",\n    // INFIXR\n    \"prefix\",\n    // PREFIX\n    \"postfix\",\n    // POSTFIX\n    //\n    \"implmnt\",\n    // IMPLMNT // 0\n    \"implement\",\n    // IMPLEMENT // 1\n    //\n    \"primplmnt\",\n    // PRIMPLMNT // ~1\n    \"primplement\",\n    // PRIMPLMNT // ~1\n    //\n    \"import\",\n    // T_IMPORT // for importing packages\n    //\n    /*\n    \t\t\"lam\", // LAM\n    \t\t\"llam\", // LLAM\n    \t\t\"fix\", // FIX\n    */\n    //\n    \"let\",\n    // T_LET\n    //\n    \"local\",\n    // T_LOCAL\n    //\n    \"macdef\",\n    // MACDEF\n    \"macrodef\",\n    // MACRODEF\n    //\n    \"nonfix\",\n    // T_NONFIX\n    //\n    \"symelim\",\n    // T_SYMELIM\n    \"symintr\",\n    // T_SYMINTR\n    \"overload\",\n    // T_OVERLOAD\n    //\n    \"of\",\n    // T_OF\n    \"op\",\n    // T_OP\n    //\n    \"rec\",\n    // T_REC\n    //\n    \"sif\",\n    // T_SIF\n    \"scase\",\n    // T_SCASE\n    //\n    \"sortdef\",\n    // T_SORTDEF\n    /*\n    // HX: [sta] is now deprecated\n    */\n    \"sta\",\n    // T_STACST\n    \"stacst\",\n    // T_STACST\n    \"stadef\",\n    // T_STADEF\n    \"static\",\n    // T_STATIC\n    /*\n    \t\t\"stavar\", // T_STAVAR\n    */\n    //\n    \"staload\",\n    // T_STALOAD\n    \"dynload\",\n    // T_DYNLOAD\n    //\n    \"try\",\n    // T_TRY\n    //\n    \"tkindef\",\n    // T_TKINDEF // HX-2012-05-23\n    //\n    /*\n    \t\t\"type\", // TYPE\n    */\n    \"typedef\",\n    // TYPEDEF\n    \"propdef\",\n    // PROPDEF\n    \"viewdef\",\n    // VIEWDEF\n    \"vtypedef\",\n    // VIEWTYPEDEF\n    \"viewtypedef\",\n    // VIEWTYPEDEF\n    //\n    /*\n    \t\t\"val\", // VAL\n    */\n    \"prval\",\n    // PRVAL\n    //\n    \"var\",\n    // VAR\n    \"prvar\",\n    // PRVAR\n    //\n    \"when\",\n    // T_WHEN\n    \"where\",\n    // T_WHERE\n    //\n    /*\n    \t\t\"for\", // T_FOR\n    \t\t\"while\", // T_WHILE\n    */\n    //\n    \"with\",\n    // T_WITH\n    //\n    \"withtype\",\n    // WITHTYPE\n    \"withprop\",\n    // WITHPROP\n    \"withview\",\n    // WITHVIEW\n    \"withvtype\",\n    // WITHVIEWTYPE\n    \"withviewtype\"\n    // WITHVIEWTYPE\n    //\n  ],\n  keywords_dlr: [\n    \"$delay\",\n    // DLRDELAY\n    \"$ldelay\",\n    // DLRLDELAY\n    //\n    \"$arrpsz\",\n    // T_DLRARRPSZ\n    \"$arrptrsize\",\n    // T_DLRARRPSZ\n    //\n    \"$d2ctype\",\n    // T_DLRD2CTYPE\n    //\n    \"$effmask\",\n    // DLREFFMASK\n    \"$effmask_ntm\",\n    // DLREFFMASK_NTM\n    \"$effmask_exn\",\n    // DLREFFMASK_EXN\n    \"$effmask_ref\",\n    // DLREFFMASK_REF\n    \"$effmask_wrt\",\n    // DLREFFMASK_WRT\n    \"$effmask_all\",\n    // DLREFFMASK_ALL\n    //\n    \"$extern\",\n    // T_DLREXTERN\n    \"$extkind\",\n    // T_DLREXTKIND\n    \"$extype\",\n    // T_DLREXTYPE\n    \"$extype_struct\",\n    // T_DLREXTYPE_STRUCT\n    //\n    \"$extval\",\n    // T_DLREXTVAL\n    \"$extfcall\",\n    // T_DLREXTFCALL\n    \"$extmcall\",\n    // T_DLREXTMCALL\n    //\n    \"$literal\",\n    // T_DLRLITERAL\n    //\n    \"$myfilename\",\n    // T_DLRMYFILENAME\n    \"$mylocation\",\n    // T_DLRMYLOCATION\n    \"$myfunction\",\n    // T_DLRMYFUNCTION\n    //\n    \"$lst\",\n    // DLRLST\n    \"$lst_t\",\n    // DLRLST_T\n    \"$lst_vt\",\n    // DLRLST_VT\n    \"$list\",\n    // DLRLST\n    \"$list_t\",\n    // DLRLST_T\n    \"$list_vt\",\n    // DLRLST_VT\n    //\n    \"$rec\",\n    // DLRREC\n    \"$rec_t\",\n    // DLRREC_T\n    \"$rec_vt\",\n    // DLRREC_VT\n    \"$record\",\n    // DLRREC\n    \"$record_t\",\n    // DLRREC_T\n    \"$record_vt\",\n    // DLRREC_VT\n    //\n    \"$tup\",\n    // DLRTUP\n    \"$tup_t\",\n    // DLRTUP_T\n    \"$tup_vt\",\n    // DLRTUP_VT\n    \"$tuple\",\n    // DLRTUP\n    \"$tuple_t\",\n    // DLRTUP_T\n    \"$tuple_vt\",\n    // DLRTUP_VT\n    //\n    \"$break\",\n    // T_DLRBREAK\n    \"$continue\",\n    // T_DLRCONTINUE\n    //\n    \"$raise\",\n    // T_DLRRAISE\n    //\n    \"$showtype\",\n    // T_DLRSHOWTYPE\n    //\n    \"$vcopyenv_v\",\n    // DLRVCOPYENV_V\n    \"$vcopyenv_vt\",\n    // DLRVCOPYENV_VT\n    //\n    \"$tempenver\",\n    // T_DLRTEMPENVER\n    //\n    \"$solver_assert\",\n    // T_DLRSOLASSERT\n    \"$solver_verify\"\n    // T_DLRSOLVERIFY\n  ],\n  keywords_srp: [\n    //\n    \"#if\",\n    // T_SRPIF\n    \"#ifdef\",\n    // T_SRPIFDEF\n    \"#ifndef\",\n    // T_SRPIFNDEF\n    //\n    \"#then\",\n    // T_SRPTHEN\n    //\n    \"#elif\",\n    // T_SRPELIF\n    \"#elifdef\",\n    // T_SRPELIFDEF\n    \"#elifndef\",\n    // T_SRPELIFNDEF\n    //\n    \"#else\",\n    // T_SRPELSE\n    \"#endif\",\n    // T_SRPENDIF\n    //\n    \"#error\",\n    // T_SRPERROR\n    //\n    \"#prerr\",\n    // T_SRPPRERR // outpui to stderr\n    \"#print\",\n    // T_SRPPRINT // output to stdout\n    //\n    \"#assert\",\n    // T_SRPASSERT\n    //\n    \"#undef\",\n    // T_SRPUNDEF\n    \"#define\",\n    // T_SRPDEFINE\n    //\n    \"#include\",\n    // T_SRPINCLUDE\n    \"#require\",\n    // T_SRPREQUIRE\n    //\n    \"#pragma\",\n    // T_SRPPRAGMA // HX: general pragma\n    \"#codegen2\",\n    // T_SRPCODEGEN2 // for level-2 codegen\n    \"#codegen3\"\n    // T_SRPCODEGEN3 // for level-3 codegen\n    //\n    // HX: end of special tokens\n    //\n  ],\n  irregular_keyword_list: [\n    \"val+\",\n    \"val-\",\n    \"val\",\n    \"case+\",\n    \"case-\",\n    \"case\",\n    \"addr@\",\n    \"addr\",\n    \"fold@\",\n    \"free@\",\n    \"fix@\",\n    \"fix\",\n    \"lam@\",\n    \"lam\",\n    \"llam@\",\n    \"llam\",\n    \"viewt@ype+\",\n    \"viewt@ype-\",\n    \"viewt@ype\",\n    \"viewtype+\",\n    \"viewtype-\",\n    \"viewtype\",\n    \"view+\",\n    \"view-\",\n    \"view@\",\n    \"view\",\n    \"type+\",\n    \"type-\",\n    \"type\",\n    \"vtype+\",\n    \"vtype-\",\n    \"vtype\",\n    \"vt@ype+\",\n    \"vt@ype-\",\n    \"vt@ype\",\n    \"viewt@ype+\",\n    \"viewt@ype-\",\n    \"viewt@ype\",\n    \"viewtype+\",\n    \"viewtype-\",\n    \"viewtype\",\n    \"prop+\",\n    \"prop-\",\n    \"prop\",\n    \"type+\",\n    \"type-\",\n    \"type\",\n    \"t@ype\",\n    \"t@ype+\",\n    \"t@ype-\",\n    \"abst@ype\",\n    \"abstype\",\n    \"absviewt@ype\",\n    \"absvt@ype\",\n    \"for*\",\n    \"for\",\n    \"while*\",\n    \"while\"\n  ],\n  keywords_types: [\n    \"bool\",\n    \"double\",\n    \"byte\",\n    \"int\",\n    \"short\",\n    \"char\",\n    \"void\",\n    \"unit\",\n    \"long\",\n    \"float\",\n    \"string\",\n    \"strptr\"\n  ],\n  // TODO: reference for this?\n  keywords_effects: [\n    \"0\",\n    // no effects\n    \"fun\",\n    \"clo\",\n    \"prf\",\n    \"funclo\",\n    \"cloptr\",\n    \"cloref\",\n    \"ref\",\n    \"ntm\",\n    \"1\"\n    // all effects\n  ],\n  operators: [\n    \"@\",\n    // T_AT\n    \"!\",\n    // T_BANG\n    \"|\",\n    // T_BAR\n    \"`\",\n    // T_BQUOTE\n    \":\",\n    // T_COLON\n    \"$\",\n    // T_DOLLAR\n    \".\",\n    // T_DOT\n    \"=\",\n    // T_EQ\n    \"#\",\n    // T_HASH\n    \"~\",\n    // T_TILDE\n    //\n    \"..\",\n    // T_DOTDOT\n    \"...\",\n    // T_DOTDOTDOT\n    //\n    \"=>\",\n    // T_EQGT\n    // \"=<\", // T_EQLT\n    \"=<>\",\n    // T_EQLTGT\n    \"=/=>\",\n    // T_EQSLASHEQGT\n    \"=>>\",\n    // T_EQGTGT\n    \"=/=>>\",\n    // T_EQSLASHEQGTGT\n    //\n    \"<\",\n    // T_LT // opening a tmparg\n    \">\",\n    // T_GT // closing a tmparg\n    //\n    \"><\",\n    // T_GTLT\n    //\n    \".<\",\n    // T_DOTLT\n    \">.\",\n    // T_GTDOT\n    //\n    \".<>.\",\n    // T_DOTLTGTDOT\n    //\n    \"->\",\n    // T_MINUSGT\n    //\"-<\", // T_MINUSLT\n    \"-<>\"\n    // T_MINUSLTGT\n    //\n    /*\n    \t\t\":<\", // T_COLONLT\n    */\n  ],\n  brackets: [\n    { open: \",(\", close: \")\", token: \"delimiter.parenthesis\" },\n    // meta-programming syntax\n    { open: \"`(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"%(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"'(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"'{\", close: \"}\", token: \"delimiter.parenthesis\" },\n    { open: \"@(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"@{\", close: \"}\", token: \"delimiter.brace\" },\n    { open: \"@[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"#[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  IDENTFST: /[a-zA-Z_]/,\n  IDENTRST: /[a-zA-Z0-9_'$]/,\n  symbolic: /[%&+-./:=@~`^|*!$#?<>]/,\n  digit: /[0-9]/,\n  digitseq0: /@digit*/,\n  xdigit: /[0-9A-Za-z]/,\n  xdigitseq0: /@xdigit*/,\n  INTSP: /[lLuU]/,\n  FLOATSP: /[fFlL]/,\n  fexponent: /[eE][+-]?[0-9]+/,\n  fexponent_bin: /[pP][+-]?[0-9]+/,\n  deciexp: /\\.[0-9]*@fexponent?/,\n  hexiexp: /\\.[0-9a-zA-Z]*@fexponent_bin?/,\n  irregular_keywords: /val[+-]?|case[+-]?|addr\\@?|fold\\@|free\\@|fix\\@?|lam\\@?|llam\\@?|prop[+-]?|type[+-]?|view[+-@]?|viewt@?ype[+-]?|t@?ype[+-]?|v(iew)?t@?ype[+-]?|abst@?ype|absv(iew)?t@?ype|for\\*?|while\\*?/,\n  ESCHAR: /[ntvbrfa\\\\\\?'\"\\(\\[\\{]/,\n  start: \"root\",\n  // The main tokenizer for ATS/Postiats\n  // reference: https://github.com/githwxi/ATS-Postiats/blob/master/src/pats_lexing.dats\n  tokenizer: {\n    root: [\n      // lexing_blankseq0\n      { regex: /[ \\t\\r\\n]+/, action: { token: \"\" } },\n      // NOTE: (*) is an invalid ML-like comment!\n      { regex: /\\(\\*\\)/, action: { token: \"invalid\" } },\n      {\n        regex: /\\(\\*/,\n        action: { token: \"comment\", next: \"lexing_COMMENT_block_ml\" }\n      },\n      {\n        regex: /\\(/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.parenthesis' }*/\n      },\n      {\n        regex: /\\)/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.parenthesis' }*/\n      },\n      {\n        regex: /\\[/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.bracket' }*/\n      },\n      {\n        regex: /\\]/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.bracket' }*/\n      },\n      {\n        regex: /\\{/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.brace' }*/\n      },\n      {\n        regex: /\\}/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.brace' }*/\n      },\n      // lexing_COMMA\n      {\n        regex: /,\\(/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.parenthesis' }*/\n      },\n      // meta-programming syntax\n      { regex: /,/, action: { token: \"delimiter.comma\" } },\n      { regex: /;/, action: { token: \"delimiter.semicolon\" } },\n      // lexing_AT\n      {\n        regex: /@\\(/,\n        action: \"@brackets\"\n        /* { token: 'delimiter.parenthesis' }*/\n      },\n      {\n        regex: /@\\[/,\n        action: \"@brackets\"\n        /* { token: 'delimiter.bracket' }*/\n      },\n      {\n        regex: /@\\{/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.brace' }*/\n      },\n      // lexing_COLON\n      {\n        regex: /:</,\n        action: { token: \"keyword\", next: \"@lexing_EFFECT_commaseq0\" }\n      },\n      // T_COLONLT\n      /*\n      \t\t\tlexing_DOT:\n      \n      \t\t\t. // SYMBOLIC => lexing_IDENT_sym\n      \t\t\t. FLOATDOT => lexing_FLOAT_deciexp\n      \t\t\t. DIGIT => T_DOTINT\n      \t\t\t*/\n      { regex: /\\.@symbolic+/, action: { token: \"identifier.sym\" } },\n      // FLOATDOT case\n      {\n        regex: /\\.@digit*@fexponent@FLOATSP*/,\n        action: { token: \"number.float\" }\n      },\n      { regex: /\\.@digit+/, action: { token: \"number.float\" } },\n      // T_DOTINT\n      // lexing_DOLLAR:\n      // '$' IDENTFST IDENTRST* => lexing_IDENT_dlr, _ => lexing_IDENT_sym\n      {\n        regex: /\\$@IDENTFST@IDENTRST*/,\n        action: {\n          cases: {\n            \"@keywords_dlr\": { token: \"keyword.dlr\" },\n            \"@default\": { token: \"namespace\" }\n            // most likely a module qualifier\n          }\n        }\n      },\n      // lexing_SHARP:\n      // '#' IDENTFST IDENTRST* => lexing_ident_srp, _ => lexing_IDENT_sym\n      {\n        regex: /\\#@IDENTFST@IDENTRST*/,\n        action: {\n          cases: {\n            \"@keywords_srp\": { token: \"keyword.srp\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      },\n      // lexing_PERCENT:\n      { regex: /%\\(/, action: { token: \"delimiter.parenthesis\" } },\n      {\n        regex: /^%{(#|\\^|\\$)?/,\n        action: {\n          token: \"keyword\",\n          next: \"@lexing_EXTCODE\",\n          nextEmbedded: \"text/javascript\"\n        }\n      },\n      { regex: /^%}/, action: { token: \"keyword\" } },\n      // lexing_QUOTE\n      { regex: /'\\(/, action: { token: \"delimiter.parenthesis\" } },\n      { regex: /'\\[/, action: { token: \"delimiter.bracket\" } },\n      { regex: /'\\{/, action: { token: \"delimiter.brace\" } },\n      [/(')(\\\\@ESCHAR|\\\\[xX]@xdigit+|\\\\@digit+)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'[^\\\\']'/, \"string\"],\n      // lexing_DQUOTE\n      [/\"/, \"string.quote\", \"@lexing_DQUOTE\"],\n      // lexing_BQUOTE\n      {\n        regex: /`\\(/,\n        action: \"@brackets\"\n        /* { token: 'delimiter.parenthesis' }*/\n      },\n      // TODO: otherwise, try lexing_IDENT_sym\n      { regex: /\\\\/, action: { token: \"punctuation\" } },\n      // just T_BACKSLASH\n      // lexing_IDENT_alp:\n      // NOTE: (?!regex) is syntax for \"not-followed-by\" regex\n      // to resolve ambiguity such as foreach$fwork being incorrectly lexed as [for] [each$fwork]!\n      {\n        regex: /@irregular_keywords(?!@IDENTRST)/,\n        action: { token: \"keyword\" }\n      },\n      {\n        regex: /@IDENTFST@IDENTRST*[<!\\[]?/,\n        action: {\n          cases: {\n            // TODO: dynload and staload should be specially parsed\n            // dynload whitespace+ \"special_string\"\n            // this special string is really:\n            //  '/' '\\\\' '.' => punctuation\n            // ({\\$)([a-zA-Z_][a-zA-Z_0-9]*)(}) => punctuation,keyword,punctuation\n            // [^\"] => identifier/literal\n            \"@keywords\": { token: \"keyword\" },\n            \"@keywords_types\": { token: \"type\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      },\n      // lexing_IDENT_sym:\n      {\n        regex: /\\/\\/\\/\\//,\n        action: { token: \"comment\", next: \"@lexing_COMMENT_rest\" }\n      },\n      { regex: /\\/\\/.*$/, action: { token: \"comment\" } },\n      {\n        regex: /\\/\\*/,\n        action: { token: \"comment\", next: \"@lexing_COMMENT_block_c\" }\n      },\n      // AS-20160627: specifically for effect annotations\n      {\n        regex: /-<|=</,\n        action: { token: \"keyword\", next: \"@lexing_EFFECT_commaseq0\" }\n      },\n      {\n        regex: /@symbolic+/,\n        action: {\n          cases: {\n            \"@operators\": \"keyword\",\n            \"@default\": \"operator\"\n          }\n        }\n      },\n      // lexing_ZERO:\n      // FIXME: this one is quite messy/unfinished yet\n      // TODO: lexing_INT_hex\n      // - testing_hexiexp => lexing_FLOAT_hexiexp\n      // - testing_fexponent_bin => lexing_FLOAT_hexiexp\n      // - testing_intspseq0 => T_INT_hex\n      // lexing_INT_hex:\n      {\n        regex: /0[xX]@xdigit+(@hexiexp|@fexponent_bin)@FLOATSP*/,\n        action: { token: \"number.float\" }\n      },\n      { regex: /0[xX]@xdigit+@INTSP*/, action: { token: \"number.hex\" } },\n      {\n        regex: /0[0-7]+(?![0-9])@INTSP*/,\n        action: { token: \"number.octal\" }\n      },\n      // lexing_INT_oct\n      //{regex: /0/, action: { token: 'number' } }, // INTZERO\n      // lexing_INT_dec:\n      // - testing_deciexp => lexing_FLOAT_deciexp\n      // - testing_fexponent => lexing_FLOAT_deciexp\n      // - otherwise => intspseq0 ([0-9]*[lLuU]?)\n      {\n        regex: /@digit+(@fexponent|@deciexp)@FLOATSP*/,\n        action: { token: \"number.float\" }\n      },\n      {\n        regex: /@digit@digitseq0@INTSP*/,\n        action: { token: \"number.decimal\" }\n      },\n      // DIGIT, if followed by digitseq0, is lexing_INT_dec\n      { regex: /@digit+@INTSP*/, action: { token: \"number\" } }\n    ],\n    lexing_COMMENT_block_ml: [\n      [/[^\\(\\*]+/, \"comment\"],\n      [/\\(\\*/, \"comment\", \"@push\"],\n      [/\\(\\*/, \"comment.invalid\"],\n      [/\\*\\)/, \"comment\", \"@pop\"],\n      [/\\*/, \"comment\"]\n    ],\n    lexing_COMMENT_block_c: [\n      [/[^\\/*]+/, \"comment\"],\n      // [/\\/\\*/, 'comment', '@push' ],    // nested C-style block comments not allowed\n      // [/\\/\\*/,    'comment.invalid' ],\t// NOTE: this breaks block comments in the shape of /* //*/\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    lexing_COMMENT_rest: [\n      [/$/, \"comment\", \"@pop\"],\n      // FIXME: does it match? docs say 'no'\n      [/.*/, \"comment\"]\n    ],\n    // NOTE: added by AS, specifically for highlighting\n    lexing_EFFECT_commaseq0: [\n      {\n        regex: /@IDENTFST@IDENTRST+|@digit+/,\n        action: {\n          cases: {\n            \"@keywords_effects\": { token: \"type.effect\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      },\n      { regex: /,/, action: { token: \"punctuation\" } },\n      { regex: />/, action: { token: \"@rematch\", next: \"@pop\" } }\n    ],\n    lexing_EXTCODE: [\n      {\n        regex: /^%}/,\n        action: {\n          token: \"@rematch\",\n          next: \"@pop\",\n          nextEmbedded: \"@pop\"\n        }\n      },\n      { regex: /[^%]+/, action: \"\" }\n    ],\n    lexing_DQUOTE: [\n      { regex: /\"/, action: { token: \"string.quote\", next: \"@pop\" } },\n      // AS-20160628: additional hi-lighting for variables in staload/dynload strings\n      {\n        regex: /(\\{\\$)(@IDENTFST@IDENTRST*)(\\})/,\n        action: [{ token: \"string.escape\" }, { token: \"identifier\" }, { token: \"string.escape\" }]\n      },\n      { regex: /\\\\$/, action: { token: \"string.escape\" } },\n      {\n        regex: /\\\\(@ESCHAR|[xX]@xdigit+|@digit+)/,\n        action: { token: \"string.escape\" }\n      },\n      { regex: /[^\\\\\"]+/, action: { token: \"string\" } }\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/postiats/postiats.js\n"));

/***/ })

}]);