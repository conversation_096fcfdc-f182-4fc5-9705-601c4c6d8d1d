"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_rust_rust_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/rust/rust.js":
/*!************************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/rust/rust.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/rust/rust.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#pragma\\\\s+region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#pragma\\\\s+endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  tokenPostfix: \".rust\",\n  defaultToken: \"invalid\",\n  keywords: [\n    \"as\",\n    \"async\",\n    \"await\",\n    \"box\",\n    \"break\",\n    \"const\",\n    \"continue\",\n    \"crate\",\n    \"dyn\",\n    \"else\",\n    \"enum\",\n    \"extern\",\n    \"false\",\n    \"fn\",\n    \"for\",\n    \"if\",\n    \"impl\",\n    \"in\",\n    \"let\",\n    \"loop\",\n    \"match\",\n    \"mod\",\n    \"move\",\n    \"mut\",\n    \"pub\",\n    \"ref\",\n    \"return\",\n    \"self\",\n    \"static\",\n    \"struct\",\n    \"super\",\n    \"trait\",\n    \"true\",\n    \"try\",\n    \"type\",\n    \"unsafe\",\n    \"use\",\n    \"where\",\n    \"while\",\n    \"catch\",\n    \"default\",\n    \"union\",\n    \"static\",\n    \"abstract\",\n    \"alignof\",\n    \"become\",\n    \"do\",\n    \"final\",\n    \"macro\",\n    \"offsetof\",\n    \"override\",\n    \"priv\",\n    \"proc\",\n    \"pure\",\n    \"sizeof\",\n    \"typeof\",\n    \"unsized\",\n    \"virtual\",\n    \"yield\"\n  ],\n  typeKeywords: [\n    \"Self\",\n    \"m32\",\n    \"m64\",\n    \"m128\",\n    \"f80\",\n    \"f16\",\n    \"f128\",\n    \"int\",\n    \"uint\",\n    \"float\",\n    \"char\",\n    \"bool\",\n    \"u8\",\n    \"u16\",\n    \"u32\",\n    \"u64\",\n    \"f32\",\n    \"f64\",\n    \"i8\",\n    \"i16\",\n    \"i32\",\n    \"i64\",\n    \"str\",\n    \"Option\",\n    \"Either\",\n    \"c_float\",\n    \"c_double\",\n    \"c_void\",\n    \"FILE\",\n    \"fpos_t\",\n    \"DIR\",\n    \"dirent\",\n    \"c_char\",\n    \"c_schar\",\n    \"c_uchar\",\n    \"c_short\",\n    \"c_ushort\",\n    \"c_int\",\n    \"c_uint\",\n    \"c_long\",\n    \"c_ulong\",\n    \"size_t\",\n    \"ptrdiff_t\",\n    \"clock_t\",\n    \"time_t\",\n    \"c_longlong\",\n    \"c_ulonglong\",\n    \"intptr_t\",\n    \"uintptr_t\",\n    \"off_t\",\n    \"dev_t\",\n    \"ino_t\",\n    \"pid_t\",\n    \"mode_t\",\n    \"ssize_t\"\n  ],\n  constants: [\"true\", \"false\", \"Some\", \"None\", \"Left\", \"Right\", \"Ok\", \"Err\"],\n  supportConstants: [\n    \"EXIT_FAILURE\",\n    \"EXIT_SUCCESS\",\n    \"RAND_MAX\",\n    \"EOF\",\n    \"SEEK_SET\",\n    \"SEEK_CUR\",\n    \"SEEK_END\",\n    \"_IOFBF\",\n    \"_IONBF\",\n    \"_IOLBF\",\n    \"BUFSIZ\",\n    \"FOPEN_MAX\",\n    \"FILENAME_MAX\",\n    \"L_tmpnam\",\n    \"TMP_MAX\",\n    \"O_RDONLY\",\n    \"O_WRONLY\",\n    \"O_RDWR\",\n    \"O_APPEND\",\n    \"O_CREAT\",\n    \"O_EXCL\",\n    \"O_TRUNC\",\n    \"S_IFIFO\",\n    \"S_IFCHR\",\n    \"S_IFBLK\",\n    \"S_IFDIR\",\n    \"S_IFREG\",\n    \"S_IFMT\",\n    \"S_IEXEC\",\n    \"S_IWRITE\",\n    \"S_IREAD\",\n    \"S_IRWXU\",\n    \"S_IXUSR\",\n    \"S_IWUSR\",\n    \"S_IRUSR\",\n    \"F_OK\",\n    \"R_OK\",\n    \"W_OK\",\n    \"X_OK\",\n    \"STDIN_FILENO\",\n    \"STDOUT_FILENO\",\n    \"STDERR_FILENO\"\n  ],\n  supportMacros: [\n    \"format!\",\n    \"print!\",\n    \"println!\",\n    \"panic!\",\n    \"format_args!\",\n    \"unreachable!\",\n    \"write!\",\n    \"writeln!\"\n  ],\n  operators: [\n    \"!\",\n    \"!=\",\n    \"%\",\n    \"%=\",\n    \"&\",\n    \"&=\",\n    \"&&\",\n    \"*\",\n    \"*=\",\n    \"+\",\n    \"+=\",\n    \"-\",\n    \"-=\",\n    \"->\",\n    \".\",\n    \"..\",\n    \"...\",\n    \"/\",\n    \"/=\",\n    \":\",\n    \";\",\n    \"<<\",\n    \"<<=\",\n    \"<\",\n    \"<=\",\n    \"=\",\n    \"==\",\n    \"=>\",\n    \">\",\n    \">=\",\n    \">>\",\n    \">>=\",\n    \"@\",\n    \"^\",\n    \"^=\",\n    \"|\",\n    \"|=\",\n    \"||\",\n    \"_\",\n    \"?\",\n    \"#\"\n  ],\n  escapes: /\\\\([nrt0\\\"''\\\\]|x\\h{2}|u\\{\\h{1,6}\\})/,\n  delimiters: /[,]/,\n  symbols: /[\\#\\!\\%\\&\\*\\+\\-\\.\\/\\:\\;\\<\\=\\>\\@\\^\\|_\\?]+/,\n  intSuffixes: /[iu](8|16|32|64|128|size)/,\n  floatSuffixes: /f(32|64)/,\n  tokenizer: {\n    root: [\n      // Raw string literals\n      [/r(#*)\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@stringraw.$1\" }],\n      [\n        /[a-zA-Z][a-zA-Z0-9_]*!?|_[a-zA-Z0-9_]+/,\n        {\n          cases: {\n            \"@typeKeywords\": \"keyword.type\",\n            \"@keywords\": \"keyword\",\n            \"@supportConstants\": \"keyword\",\n            \"@supportMacros\": \"keyword\",\n            \"@constants\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // Designator\n      [/\\$/, \"identifier\"],\n      // Lifetime annotations\n      [/'[a-zA-Z_][a-zA-Z0-9_]*(?=[^\\'])/, \"identifier\"],\n      // Byte literal\n      [/'(\\S|@escapes)'/, \"string.byteliteral\"],\n      // Strings\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }],\n      { include: \"@numbers\" },\n      // Whitespace + comments\n      { include: \"@whitespace\" },\n      [\n        /@delimiters/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"delimiter\"\n          }\n        }\n      ],\n      [/[{}()\\[\\]<>]/, \"@brackets\"],\n      [/@symbols/, { cases: { \"@operators\": \"operator\", \"@default\": \"\" } }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@push\"],\n      [\"\\\\*/\", \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    stringraw: [\n      [/[^\"#]+/, { token: \"string\" }],\n      [\n        /\"(#*)/,\n        {\n          cases: {\n            \"$1==$S2\": { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" },\n            \"@default\": { token: \"string\" }\n          }\n        }\n      ],\n      [/[\"#]/, { token: \"string\" }]\n    ],\n    numbers: [\n      //Octal\n      [/(0o[0-7_]+)(@intSuffixes)?/, { token: \"number\" }],\n      //Binary\n      [/(0b[0-1_]+)(@intSuffixes)?/, { token: \"number\" }],\n      //Exponent\n      [/[\\d][\\d_]*(\\.[\\d][\\d_]*)?[eE][+-][\\d_]+(@floatSuffixes)?/, { token: \"number\" }],\n      //Float\n      [/\\b(\\d\\.?[\\d_]*)(@floatSuffixes)?\\b/, { token: \"number\" }],\n      //Hexadecimal\n      [/(0x[\\da-fA-F]+)_?(@intSuffixes)?/, { token: \"number\" }],\n      //Integer\n      [/[\\d][\\d_]*(@intSuffixes?)?/, { token: \"number\" }]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/rust/rust.js\n"));

/***/ })

}]);