"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_csp_csp_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/csp/csp.js":
/*!**********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/csp/csp.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/csp/csp.ts\nvar conf = {\n  brackets: [],\n  autoClosingPairs: [],\n  surroundingPairs: []\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  // defaultToken: 'invalid',\n  keywords: [],\n  typeKeywords: [],\n  tokenPostfix: \".csp\",\n  operators: [],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  tokenizer: {\n    root: [\n      [/child-src/, \"string.quote\"],\n      [/connect-src/, \"string.quote\"],\n      [/default-src/, \"string.quote\"],\n      [/font-src/, \"string.quote\"],\n      [/frame-src/, \"string.quote\"],\n      [/img-src/, \"string.quote\"],\n      [/manifest-src/, \"string.quote\"],\n      [/media-src/, \"string.quote\"],\n      [/object-src/, \"string.quote\"],\n      [/script-src/, \"string.quote\"],\n      [/style-src/, \"string.quote\"],\n      [/worker-src/, \"string.quote\"],\n      [/base-uri/, \"string.quote\"],\n      [/plugin-types/, \"string.quote\"],\n      [/sandbox/, \"string.quote\"],\n      [/disown-opener/, \"string.quote\"],\n      [/form-action/, \"string.quote\"],\n      [/frame-ancestors/, \"string.quote\"],\n      [/report-uri/, \"string.quote\"],\n      [/report-to/, \"string.quote\"],\n      [/upgrade-insecure-requests/, \"string.quote\"],\n      [/block-all-mixed-content/, \"string.quote\"],\n      [/require-sri-for/, \"string.quote\"],\n      [/reflected-xss/, \"string.quote\"],\n      [/referrer/, \"string.quote\"],\n      [/policy-uri/, \"string.quote\"],\n      [/'self'/, \"string.quote\"],\n      [/'unsafe-inline'/, \"string.quote\"],\n      [/'unsafe-eval'/, \"string.quote\"],\n      [/'strict-dynamic'/, \"string.quote\"],\n      [/'unsafe-hashed-attributes'/, \"string.quote\"]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/csp/csp.js\n"));

/***/ })

}]);