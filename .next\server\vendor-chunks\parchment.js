"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parchment";
exports.ids = ["vendor-chunks/parchment"];
exports.modules = {

/***/ "(ssr)/./node_modules/parchment/dist/parchment.js":
/*!**************************************************!*\
  !*** ./node_modules/parchment/dist/parchment.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Attributor: () => (/* binding */ Attributor),\n/* harmony export */   AttributorStore: () => (/* binding */ AttributorStore$1),\n/* harmony export */   BlockBlot: () => (/* binding */ BlockBlot$1),\n/* harmony export */   ClassAttributor: () => (/* binding */ ClassAttributor$1),\n/* harmony export */   ContainerBlot: () => (/* binding */ ContainerBlot$1),\n/* harmony export */   EmbedBlot: () => (/* binding */ EmbedBlot$1),\n/* harmony export */   InlineBlot: () => (/* binding */ InlineBlot$1),\n/* harmony export */   LeafBlot: () => (/* binding */ LeafBlot$1),\n/* harmony export */   ParentBlot: () => (/* binding */ ParentBlot$1),\n/* harmony export */   Registry: () => (/* binding */ Registry),\n/* harmony export */   Scope: () => (/* binding */ Scope),\n/* harmony export */   ScrollBlot: () => (/* binding */ ScrollBlot$1),\n/* harmony export */   StyleAttributor: () => (/* binding */ StyleAttributor$1),\n/* harmony export */   TextBlot: () => (/* binding */ TextBlot$1)\n/* harmony export */ });\nvar Scope = /* @__PURE__ */ ((Scope2) => (Scope2[Scope2.TYPE = 3] = \"TYPE\", Scope2[Scope2.LEVEL = 12] = \"LEVEL\", Scope2[Scope2.ATTRIBUTE = 13] = \"ATTRIBUTE\", Scope2[Scope2.BLOT = 14] = \"BLOT\", Scope2[Scope2.INLINE = 7] = \"INLINE\", Scope2[Scope2.BLOCK = 11] = \"BLOCK\", Scope2[Scope2.BLOCK_BLOT = 10] = \"BLOCK_BLOT\", Scope2[Scope2.INLINE_BLOT = 6] = \"INLINE_BLOT\", Scope2[Scope2.BLOCK_ATTRIBUTE = 9] = \"BLOCK_ATTRIBUTE\", Scope2[Scope2.INLINE_ATTRIBUTE = 5] = \"INLINE_ATTRIBUTE\", Scope2[Scope2.ANY = 15] = \"ANY\", Scope2))(Scope || {});\nclass Attributor {\n  constructor(attrName, keyName, options = {}) {\n    this.attrName = attrName, this.keyName = keyName;\n    const attributeBit = Scope.TYPE & Scope.ATTRIBUTE;\n    this.scope = options.scope != null ? (\n      // Ignore type bits, force attribute bit\n      options.scope & Scope.LEVEL | attributeBit\n    ) : Scope.ATTRIBUTE, options.whitelist != null && (this.whitelist = options.whitelist);\n  }\n  static keys(node) {\n    return Array.from(node.attributes).map((item) => item.name);\n  }\n  add(node, value) {\n    return this.canAdd(node, value) ? (node.setAttribute(this.keyName, value), !0) : !1;\n  }\n  canAdd(_node, value) {\n    return this.whitelist == null ? !0 : typeof value == \"string\" ? this.whitelist.indexOf(value.replace(/[\"']/g, \"\")) > -1 : this.whitelist.indexOf(value) > -1;\n  }\n  remove(node) {\n    node.removeAttribute(this.keyName);\n  }\n  value(node) {\n    const value = node.getAttribute(this.keyName);\n    return this.canAdd(node, value) && value ? value : \"\";\n  }\n}\nclass ParchmentError extends Error {\n  constructor(message) {\n    message = \"[Parchment] \" + message, super(message), this.message = message, this.name = this.constructor.name;\n  }\n}\nconst _Registry = class _Registry {\n  constructor() {\n    this.attributes = {}, this.classes = {}, this.tags = {}, this.types = {};\n  }\n  static find(node, bubble = !1) {\n    if (node == null)\n      return null;\n    if (this.blots.has(node))\n      return this.blots.get(node) || null;\n    if (bubble) {\n      let parentNode = null;\n      try {\n        parentNode = node.parentNode;\n      } catch {\n        return null;\n      }\n      return this.find(parentNode, bubble);\n    }\n    return null;\n  }\n  create(scroll, input, value) {\n    const match2 = this.query(input);\n    if (match2 == null)\n      throw new ParchmentError(`Unable to create ${input} blot`);\n    const blotClass = match2, node = (\n      // @ts-expect-error Fix me later\n      input instanceof Node || input.nodeType === Node.TEXT_NODE ? input : blotClass.create(value)\n    ), blot = new blotClass(scroll, node, value);\n    return _Registry.blots.set(blot.domNode, blot), blot;\n  }\n  find(node, bubble = !1) {\n    return _Registry.find(node, bubble);\n  }\n  query(query, scope = Scope.ANY) {\n    let match2;\n    return typeof query == \"string\" ? match2 = this.types[query] || this.attributes[query] : query instanceof Text || query.nodeType === Node.TEXT_NODE ? match2 = this.types.text : typeof query == \"number\" ? query & Scope.LEVEL & Scope.BLOCK ? match2 = this.types.block : query & Scope.LEVEL & Scope.INLINE && (match2 = this.types.inline) : query instanceof Element && ((query.getAttribute(\"class\") || \"\").split(/\\s+/).some((name) => (match2 = this.classes[name], !!match2)), match2 = match2 || this.tags[query.tagName]), match2 == null ? null : \"scope\" in match2 && scope & Scope.LEVEL & match2.scope && scope & Scope.TYPE & match2.scope ? match2 : null;\n  }\n  register(...definitions) {\n    return definitions.map((definition) => {\n      const isBlot = \"blotName\" in definition, isAttr = \"attrName\" in definition;\n      if (!isBlot && !isAttr)\n        throw new ParchmentError(\"Invalid definition\");\n      if (isBlot && definition.blotName === \"abstract\")\n        throw new ParchmentError(\"Cannot register abstract class\");\n      const key = isBlot ? definition.blotName : isAttr ? definition.attrName : void 0;\n      return this.types[key] = definition, isAttr ? typeof definition.keyName == \"string\" && (this.attributes[definition.keyName] = definition) : isBlot && (definition.className && (this.classes[definition.className] = definition), definition.tagName && (Array.isArray(definition.tagName) ? definition.tagName = definition.tagName.map((tagName) => tagName.toUpperCase()) : definition.tagName = definition.tagName.toUpperCase(), (Array.isArray(definition.tagName) ? definition.tagName : [definition.tagName]).forEach((tag) => {\n        (this.tags[tag] == null || definition.className == null) && (this.tags[tag] = definition);\n      }))), definition;\n    });\n  }\n};\n_Registry.blots = /* @__PURE__ */ new WeakMap();\nlet Registry = _Registry;\nfunction match(node, prefix) {\n  return (node.getAttribute(\"class\") || \"\").split(/\\s+/).filter((name) => name.indexOf(`${prefix}-`) === 0);\n}\nclass ClassAttributor extends Attributor {\n  static keys(node) {\n    return (node.getAttribute(\"class\") || \"\").split(/\\s+/).map((name) => name.split(\"-\").slice(0, -1).join(\"-\"));\n  }\n  add(node, value) {\n    return this.canAdd(node, value) ? (this.remove(node), node.classList.add(`${this.keyName}-${value}`), !0) : !1;\n  }\n  remove(node) {\n    match(node, this.keyName).forEach((name) => {\n      node.classList.remove(name);\n    }), node.classList.length === 0 && node.removeAttribute(\"class\");\n  }\n  value(node) {\n    const value = (match(node, this.keyName)[0] || \"\").slice(this.keyName.length + 1);\n    return this.canAdd(node, value) ? value : \"\";\n  }\n}\nconst ClassAttributor$1 = ClassAttributor;\nfunction camelize(name) {\n  const parts = name.split(\"-\"), rest = parts.slice(1).map((part) => part[0].toUpperCase() + part.slice(1)).join(\"\");\n  return parts[0] + rest;\n}\nclass StyleAttributor extends Attributor {\n  static keys(node) {\n    return (node.getAttribute(\"style\") || \"\").split(\";\").map((value) => value.split(\":\")[0].trim());\n  }\n  add(node, value) {\n    return this.canAdd(node, value) ? (node.style[camelize(this.keyName)] = value, !0) : !1;\n  }\n  remove(node) {\n    node.style[camelize(this.keyName)] = \"\", node.getAttribute(\"style\") || node.removeAttribute(\"style\");\n  }\n  value(node) {\n    const value = node.style[camelize(this.keyName)];\n    return this.canAdd(node, value) ? value : \"\";\n  }\n}\nconst StyleAttributor$1 = StyleAttributor;\nclass AttributorStore {\n  constructor(domNode) {\n    this.attributes = {}, this.domNode = domNode, this.build();\n  }\n  attribute(attribute, value) {\n    value ? attribute.add(this.domNode, value) && (attribute.value(this.domNode) != null ? this.attributes[attribute.attrName] = attribute : delete this.attributes[attribute.attrName]) : (attribute.remove(this.domNode), delete this.attributes[attribute.attrName]);\n  }\n  build() {\n    this.attributes = {};\n    const blot = Registry.find(this.domNode);\n    if (blot == null)\n      return;\n    const attributes = Attributor.keys(this.domNode), classes = ClassAttributor$1.keys(this.domNode), styles = StyleAttributor$1.keys(this.domNode);\n    attributes.concat(classes).concat(styles).forEach((name) => {\n      const attr = blot.scroll.query(name, Scope.ATTRIBUTE);\n      attr instanceof Attributor && (this.attributes[attr.attrName] = attr);\n    });\n  }\n  copy(target) {\n    Object.keys(this.attributes).forEach((key) => {\n      const value = this.attributes[key].value(this.domNode);\n      target.format(key, value);\n    });\n  }\n  move(target) {\n    this.copy(target), Object.keys(this.attributes).forEach((key) => {\n      this.attributes[key].remove(this.domNode);\n    }), this.attributes = {};\n  }\n  values() {\n    return Object.keys(this.attributes).reduce(\n      (attributes, name) => (attributes[name] = this.attributes[name].value(this.domNode), attributes),\n      {}\n    );\n  }\n}\nconst AttributorStore$1 = AttributorStore, _ShadowBlot = class _ShadowBlot {\n  constructor(scroll, domNode) {\n    this.scroll = scroll, this.domNode = domNode, Registry.blots.set(domNode, this), this.prev = null, this.next = null;\n  }\n  static create(rawValue) {\n    if (this.tagName == null)\n      throw new ParchmentError(\"Blot definition missing tagName\");\n    let node, value;\n    return Array.isArray(this.tagName) ? (typeof rawValue == \"string\" ? (value = rawValue.toUpperCase(), parseInt(value, 10).toString() === value && (value = parseInt(value, 10))) : typeof rawValue == \"number\" && (value = rawValue), typeof value == \"number\" ? node = document.createElement(this.tagName[value - 1]) : value && this.tagName.indexOf(value) > -1 ? node = document.createElement(value) : node = document.createElement(this.tagName[0])) : node = document.createElement(this.tagName), this.className && node.classList.add(this.className), node;\n  }\n  // Hack for accessing inherited static methods\n  get statics() {\n    return this.constructor;\n  }\n  attach() {\n  }\n  clone() {\n    const domNode = this.domNode.cloneNode(!1);\n    return this.scroll.create(domNode);\n  }\n  detach() {\n    this.parent != null && this.parent.removeChild(this), Registry.blots.delete(this.domNode);\n  }\n  deleteAt(index, length) {\n    this.isolate(index, length).remove();\n  }\n  formatAt(index, length, name, value) {\n    const blot = this.isolate(index, length);\n    if (this.scroll.query(name, Scope.BLOT) != null && value)\n      blot.wrap(name, value);\n    else if (this.scroll.query(name, Scope.ATTRIBUTE) != null) {\n      const parent = this.scroll.create(this.statics.scope);\n      blot.wrap(parent), parent.format(name, value);\n    }\n  }\n  insertAt(index, value, def) {\n    const blot = def == null ? this.scroll.create(\"text\", value) : this.scroll.create(value, def), ref = this.split(index);\n    this.parent.insertBefore(blot, ref || void 0);\n  }\n  isolate(index, length) {\n    const target = this.split(index);\n    if (target == null)\n      throw new Error(\"Attempt to isolate at end\");\n    return target.split(length), target;\n  }\n  length() {\n    return 1;\n  }\n  offset(root = this.parent) {\n    return this.parent == null || this === root ? 0 : this.parent.children.offset(this) + this.parent.offset(root);\n  }\n  optimize(_context) {\n    this.statics.requiredContainer && !(this.parent instanceof this.statics.requiredContainer) && this.wrap(this.statics.requiredContainer.blotName);\n  }\n  remove() {\n    this.domNode.parentNode != null && this.domNode.parentNode.removeChild(this.domNode), this.detach();\n  }\n  replaceWith(name, value) {\n    const replacement = typeof name == \"string\" ? this.scroll.create(name, value) : name;\n    return this.parent != null && (this.parent.insertBefore(replacement, this.next || void 0), this.remove()), replacement;\n  }\n  split(index, _force) {\n    return index === 0 ? this : this.next;\n  }\n  update(_mutations, _context) {\n  }\n  wrap(name, value) {\n    const wrapper = typeof name == \"string\" ? this.scroll.create(name, value) : name;\n    if (this.parent != null && this.parent.insertBefore(wrapper, this.next || void 0), typeof wrapper.appendChild != \"function\")\n      throw new ParchmentError(`Cannot wrap ${name}`);\n    return wrapper.appendChild(this), wrapper;\n  }\n};\n_ShadowBlot.blotName = \"abstract\";\nlet ShadowBlot = _ShadowBlot;\nconst _LeafBlot = class _LeafBlot extends ShadowBlot {\n  /**\n   * Returns the value represented by domNode if it is this Blot's type\n   * No checking that domNode can represent this Blot type is required so\n   * applications needing it should check externally before calling.\n   */\n  static value(_domNode) {\n    return !0;\n  }\n  /**\n   * Given location represented by node and offset from DOM Selection Range,\n   * return index to that location.\n   */\n  index(node, offset) {\n    return this.domNode === node || this.domNode.compareDocumentPosition(node) & Node.DOCUMENT_POSITION_CONTAINED_BY ? Math.min(offset, 1) : -1;\n  }\n  /**\n   * Given index to location within blot, return node and offset representing\n   * that location, consumable by DOM Selection Range\n   */\n  position(index, _inclusive) {\n    let offset = Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);\n    return index > 0 && (offset += 1), [this.parent.domNode, offset];\n  }\n  /**\n   * Return value represented by this blot\n   * Should not change without interaction from API or\n   * user change detectable by update()\n   */\n  value() {\n    return {\n      [this.statics.blotName]: this.statics.value(this.domNode) || !0\n    };\n  }\n};\n_LeafBlot.scope = Scope.INLINE_BLOT;\nlet LeafBlot = _LeafBlot;\nconst LeafBlot$1 = LeafBlot;\nclass LinkedList {\n  constructor() {\n    this.head = null, this.tail = null, this.length = 0;\n  }\n  append(...nodes) {\n    if (this.insertBefore(nodes[0], null), nodes.length > 1) {\n      const rest = nodes.slice(1);\n      this.append(...rest);\n    }\n  }\n  at(index) {\n    const next = this.iterator();\n    let cur = next();\n    for (; cur && index > 0; )\n      index -= 1, cur = next();\n    return cur;\n  }\n  contains(node) {\n    const next = this.iterator();\n    let cur = next();\n    for (; cur; ) {\n      if (cur === node)\n        return !0;\n      cur = next();\n    }\n    return !1;\n  }\n  indexOf(node) {\n    const next = this.iterator();\n    let cur = next(), index = 0;\n    for (; cur; ) {\n      if (cur === node)\n        return index;\n      index += 1, cur = next();\n    }\n    return -1;\n  }\n  insertBefore(node, refNode) {\n    node != null && (this.remove(node), node.next = refNode, refNode != null ? (node.prev = refNode.prev, refNode.prev != null && (refNode.prev.next = node), refNode.prev = node, refNode === this.head && (this.head = node)) : this.tail != null ? (this.tail.next = node, node.prev = this.tail, this.tail = node) : (node.prev = null, this.head = this.tail = node), this.length += 1);\n  }\n  offset(target) {\n    let index = 0, cur = this.head;\n    for (; cur != null; ) {\n      if (cur === target)\n        return index;\n      index += cur.length(), cur = cur.next;\n    }\n    return -1;\n  }\n  remove(node) {\n    this.contains(node) && (node.prev != null && (node.prev.next = node.next), node.next != null && (node.next.prev = node.prev), node === this.head && (this.head = node.next), node === this.tail && (this.tail = node.prev), this.length -= 1);\n  }\n  iterator(curNode = this.head) {\n    return () => {\n      const ret = curNode;\n      return curNode != null && (curNode = curNode.next), ret;\n    };\n  }\n  find(index, inclusive = !1) {\n    const next = this.iterator();\n    let cur = next();\n    for (; cur; ) {\n      const length = cur.length();\n      if (index < length || inclusive && index === length && (cur.next == null || cur.next.length() !== 0))\n        return [cur, index];\n      index -= length, cur = next();\n    }\n    return [null, 0];\n  }\n  forEach(callback) {\n    const next = this.iterator();\n    let cur = next();\n    for (; cur; )\n      callback(cur), cur = next();\n  }\n  forEachAt(index, length, callback) {\n    if (length <= 0)\n      return;\n    const [startNode, offset] = this.find(index);\n    let curIndex = index - offset;\n    const next = this.iterator(startNode);\n    let cur = next();\n    for (; cur && curIndex < index + length; ) {\n      const curLength = cur.length();\n      index > curIndex ? callback(\n        cur,\n        index - curIndex,\n        Math.min(length, curIndex + curLength - index)\n      ) : callback(cur, 0, Math.min(curLength, index + length - curIndex)), curIndex += curLength, cur = next();\n    }\n  }\n  map(callback) {\n    return this.reduce((memo, cur) => (memo.push(callback(cur)), memo), []);\n  }\n  reduce(callback, memo) {\n    const next = this.iterator();\n    let cur = next();\n    for (; cur; )\n      memo = callback(memo, cur), cur = next();\n    return memo;\n  }\n}\nfunction makeAttachedBlot(node, scroll) {\n  const found = scroll.find(node);\n  if (found)\n    return found;\n  try {\n    return scroll.create(node);\n  } catch {\n    const blot = scroll.create(Scope.INLINE);\n    return Array.from(node.childNodes).forEach((child) => {\n      blot.domNode.appendChild(child);\n    }), node.parentNode && node.parentNode.replaceChild(blot.domNode, node), blot.attach(), blot;\n  }\n}\nconst _ParentBlot = class _ParentBlot extends ShadowBlot {\n  constructor(scroll, domNode) {\n    super(scroll, domNode), this.uiNode = null, this.build();\n  }\n  appendChild(other) {\n    this.insertBefore(other);\n  }\n  attach() {\n    super.attach(), this.children.forEach((child) => {\n      child.attach();\n    });\n  }\n  attachUI(node) {\n    this.uiNode != null && this.uiNode.remove(), this.uiNode = node, _ParentBlot.uiClass && this.uiNode.classList.add(_ParentBlot.uiClass), this.uiNode.setAttribute(\"contenteditable\", \"false\"), this.domNode.insertBefore(this.uiNode, this.domNode.firstChild);\n  }\n  /**\n   * Called during construction, should fill its own children LinkedList.\n   */\n  build() {\n    this.children = new LinkedList(), Array.from(this.domNode.childNodes).filter((node) => node !== this.uiNode).reverse().forEach((node) => {\n      try {\n        const child = makeAttachedBlot(node, this.scroll);\n        this.insertBefore(child, this.children.head || void 0);\n      } catch (err) {\n        if (err instanceof ParchmentError)\n          return;\n        throw err;\n      }\n    });\n  }\n  deleteAt(index, length) {\n    if (index === 0 && length === this.length())\n      return this.remove();\n    this.children.forEachAt(index, length, (child, offset, childLength) => {\n      child.deleteAt(offset, childLength);\n    });\n  }\n  descendant(criteria, index = 0) {\n    const [child, offset] = this.children.find(index);\n    return criteria.blotName == null && criteria(child) || criteria.blotName != null && child instanceof criteria ? [child, offset] : child instanceof _ParentBlot ? child.descendant(criteria, offset) : [null, -1];\n  }\n  descendants(criteria, index = 0, length = Number.MAX_VALUE) {\n    let descendants = [], lengthLeft = length;\n    return this.children.forEachAt(\n      index,\n      length,\n      (child, childIndex, childLength) => {\n        (criteria.blotName == null && criteria(child) || criteria.blotName != null && child instanceof criteria) && descendants.push(child), child instanceof _ParentBlot && (descendants = descendants.concat(\n          child.descendants(criteria, childIndex, lengthLeft)\n        )), lengthLeft -= childLength;\n      }\n    ), descendants;\n  }\n  detach() {\n    this.children.forEach((child) => {\n      child.detach();\n    }), super.detach();\n  }\n  enforceAllowedChildren() {\n    let done = !1;\n    this.children.forEach((child) => {\n      done || this.statics.allowedChildren.some(\n        (def) => child instanceof def\n      ) || (child.statics.scope === Scope.BLOCK_BLOT ? (child.next != null && this.splitAfter(child), child.prev != null && this.splitAfter(child.prev), child.parent.unwrap(), done = !0) : child instanceof _ParentBlot ? child.unwrap() : child.remove());\n    });\n  }\n  formatAt(index, length, name, value) {\n    this.children.forEachAt(index, length, (child, offset, childLength) => {\n      child.formatAt(offset, childLength, name, value);\n    });\n  }\n  insertAt(index, value, def) {\n    const [child, offset] = this.children.find(index);\n    if (child)\n      child.insertAt(offset, value, def);\n    else {\n      const blot = def == null ? this.scroll.create(\"text\", value) : this.scroll.create(value, def);\n      this.appendChild(blot);\n    }\n  }\n  insertBefore(childBlot, refBlot) {\n    childBlot.parent != null && childBlot.parent.children.remove(childBlot);\n    let refDomNode = null;\n    this.children.insertBefore(childBlot, refBlot || null), childBlot.parent = this, refBlot != null && (refDomNode = refBlot.domNode), (this.domNode.parentNode !== childBlot.domNode || this.domNode.nextSibling !== refDomNode) && this.domNode.insertBefore(childBlot.domNode, refDomNode), childBlot.attach();\n  }\n  length() {\n    return this.children.reduce((memo, child) => memo + child.length(), 0);\n  }\n  moveChildren(targetParent, refNode) {\n    this.children.forEach((child) => {\n      targetParent.insertBefore(child, refNode);\n    });\n  }\n  optimize(context) {\n    if (super.optimize(context), this.enforceAllowedChildren(), this.uiNode != null && this.uiNode !== this.domNode.firstChild && this.domNode.insertBefore(this.uiNode, this.domNode.firstChild), this.children.length === 0)\n      if (this.statics.defaultChild != null) {\n        const child = this.scroll.create(this.statics.defaultChild.blotName);\n        this.appendChild(child);\n      } else\n        this.remove();\n  }\n  path(index, inclusive = !1) {\n    const [child, offset] = this.children.find(index, inclusive), position = [[this, index]];\n    return child instanceof _ParentBlot ? position.concat(child.path(offset, inclusive)) : (child != null && position.push([child, offset]), position);\n  }\n  removeChild(child) {\n    this.children.remove(child);\n  }\n  replaceWith(name, value) {\n    const replacement = typeof name == \"string\" ? this.scroll.create(name, value) : name;\n    return replacement instanceof _ParentBlot && this.moveChildren(replacement), super.replaceWith(replacement);\n  }\n  split(index, force = !1) {\n    if (!force) {\n      if (index === 0)\n        return this;\n      if (index === this.length())\n        return this.next;\n    }\n    const after = this.clone();\n    return this.parent && this.parent.insertBefore(after, this.next || void 0), this.children.forEachAt(index, this.length(), (child, offset, _length) => {\n      const split = child.split(offset, force);\n      split != null && after.appendChild(split);\n    }), after;\n  }\n  splitAfter(child) {\n    const after = this.clone();\n    for (; child.next != null; )\n      after.appendChild(child.next);\n    return this.parent && this.parent.insertBefore(after, this.next || void 0), after;\n  }\n  unwrap() {\n    this.parent && this.moveChildren(this.parent, this.next || void 0), this.remove();\n  }\n  update(mutations, _context) {\n    const addedNodes = [], removedNodes = [];\n    mutations.forEach((mutation) => {\n      mutation.target === this.domNode && mutation.type === \"childList\" && (addedNodes.push(...mutation.addedNodes), removedNodes.push(...mutation.removedNodes));\n    }), removedNodes.forEach((node) => {\n      if (node.parentNode != null && // @ts-expect-error Fix me later\n      node.tagName !== \"IFRAME\" && document.body.compareDocumentPosition(node) & Node.DOCUMENT_POSITION_CONTAINED_BY)\n        return;\n      const blot = this.scroll.find(node);\n      blot != null && (blot.domNode.parentNode == null || blot.domNode.parentNode === this.domNode) && blot.detach();\n    }), addedNodes.filter((node) => node.parentNode === this.domNode && node !== this.uiNode).sort((a, b) => a === b ? 0 : a.compareDocumentPosition(b) & Node.DOCUMENT_POSITION_FOLLOWING ? 1 : -1).forEach((node) => {\n      let refBlot = null;\n      node.nextSibling != null && (refBlot = this.scroll.find(node.nextSibling));\n      const blot = makeAttachedBlot(node, this.scroll);\n      (blot.next !== refBlot || blot.next == null) && (blot.parent != null && blot.parent.removeChild(this), this.insertBefore(blot, refBlot || void 0));\n    }), this.enforceAllowedChildren();\n  }\n};\n_ParentBlot.uiClass = \"\";\nlet ParentBlot = _ParentBlot;\nconst ParentBlot$1 = ParentBlot;\nfunction isEqual(obj1, obj2) {\n  if (Object.keys(obj1).length !== Object.keys(obj2).length)\n    return !1;\n  for (const prop in obj1)\n    if (obj1[prop] !== obj2[prop])\n      return !1;\n  return !0;\n}\nconst _InlineBlot = class _InlineBlot extends ParentBlot$1 {\n  static create(value) {\n    return super.create(value);\n  }\n  static formats(domNode, scroll) {\n    const match2 = scroll.query(_InlineBlot.blotName);\n    if (!(match2 != null && domNode.tagName === match2.tagName)) {\n      if (typeof this.tagName == \"string\")\n        return !0;\n      if (Array.isArray(this.tagName))\n        return domNode.tagName.toLowerCase();\n    }\n  }\n  constructor(scroll, domNode) {\n    super(scroll, domNode), this.attributes = new AttributorStore$1(this.domNode);\n  }\n  format(name, value) {\n    if (name === this.statics.blotName && !value)\n      this.children.forEach((child) => {\n        child instanceof _InlineBlot || (child = child.wrap(_InlineBlot.blotName, !0)), this.attributes.copy(child);\n      }), this.unwrap();\n    else {\n      const format = this.scroll.query(name, Scope.INLINE);\n      if (format == null)\n        return;\n      format instanceof Attributor ? this.attributes.attribute(format, value) : value && (name !== this.statics.blotName || this.formats()[name] !== value) && this.replaceWith(name, value);\n    }\n  }\n  formats() {\n    const formats = this.attributes.values(), format = this.statics.formats(this.domNode, this.scroll);\n    return format != null && (formats[this.statics.blotName] = format), formats;\n  }\n  formatAt(index, length, name, value) {\n    this.formats()[name] != null || this.scroll.query(name, Scope.ATTRIBUTE) ? this.isolate(index, length).format(name, value) : super.formatAt(index, length, name, value);\n  }\n  optimize(context) {\n    super.optimize(context);\n    const formats = this.formats();\n    if (Object.keys(formats).length === 0)\n      return this.unwrap();\n    const next = this.next;\n    next instanceof _InlineBlot && next.prev === this && isEqual(formats, next.formats()) && (next.moveChildren(this), next.remove());\n  }\n  replaceWith(name, value) {\n    const replacement = super.replaceWith(name, value);\n    return this.attributes.copy(replacement), replacement;\n  }\n  update(mutations, context) {\n    super.update(mutations, context), mutations.some(\n      (mutation) => mutation.target === this.domNode && mutation.type === \"attributes\"\n    ) && this.attributes.build();\n  }\n  wrap(name, value) {\n    const wrapper = super.wrap(name, value);\n    return wrapper instanceof _InlineBlot && this.attributes.move(wrapper), wrapper;\n  }\n};\n_InlineBlot.allowedChildren = [_InlineBlot, LeafBlot$1], _InlineBlot.blotName = \"inline\", _InlineBlot.scope = Scope.INLINE_BLOT, _InlineBlot.tagName = \"SPAN\";\nlet InlineBlot = _InlineBlot;\nconst InlineBlot$1 = InlineBlot, _BlockBlot = class _BlockBlot extends ParentBlot$1 {\n  static create(value) {\n    return super.create(value);\n  }\n  static formats(domNode, scroll) {\n    const match2 = scroll.query(_BlockBlot.blotName);\n    if (!(match2 != null && domNode.tagName === match2.tagName)) {\n      if (typeof this.tagName == \"string\")\n        return !0;\n      if (Array.isArray(this.tagName))\n        return domNode.tagName.toLowerCase();\n    }\n  }\n  constructor(scroll, domNode) {\n    super(scroll, domNode), this.attributes = new AttributorStore$1(this.domNode);\n  }\n  format(name, value) {\n    const format = this.scroll.query(name, Scope.BLOCK);\n    format != null && (format instanceof Attributor ? this.attributes.attribute(format, value) : name === this.statics.blotName && !value ? this.replaceWith(_BlockBlot.blotName) : value && (name !== this.statics.blotName || this.formats()[name] !== value) && this.replaceWith(name, value));\n  }\n  formats() {\n    const formats = this.attributes.values(), format = this.statics.formats(this.domNode, this.scroll);\n    return format != null && (formats[this.statics.blotName] = format), formats;\n  }\n  formatAt(index, length, name, value) {\n    this.scroll.query(name, Scope.BLOCK) != null ? this.format(name, value) : super.formatAt(index, length, name, value);\n  }\n  insertAt(index, value, def) {\n    if (def == null || this.scroll.query(value, Scope.INLINE) != null)\n      super.insertAt(index, value, def);\n    else {\n      const after = this.split(index);\n      if (after != null) {\n        const blot = this.scroll.create(value, def);\n        after.parent.insertBefore(blot, after);\n      } else\n        throw new Error(\"Attempt to insertAt after block boundaries\");\n    }\n  }\n  replaceWith(name, value) {\n    const replacement = super.replaceWith(name, value);\n    return this.attributes.copy(replacement), replacement;\n  }\n  update(mutations, context) {\n    super.update(mutations, context), mutations.some(\n      (mutation) => mutation.target === this.domNode && mutation.type === \"attributes\"\n    ) && this.attributes.build();\n  }\n};\n_BlockBlot.blotName = \"block\", _BlockBlot.scope = Scope.BLOCK_BLOT, _BlockBlot.tagName = \"P\", _BlockBlot.allowedChildren = [\n  InlineBlot$1,\n  _BlockBlot,\n  LeafBlot$1\n];\nlet BlockBlot = _BlockBlot;\nconst BlockBlot$1 = BlockBlot, _ContainerBlot = class _ContainerBlot extends ParentBlot$1 {\n  checkMerge() {\n    return this.next !== null && this.next.statics.blotName === this.statics.blotName;\n  }\n  deleteAt(index, length) {\n    super.deleteAt(index, length), this.enforceAllowedChildren();\n  }\n  formatAt(index, length, name, value) {\n    super.formatAt(index, length, name, value), this.enforceAllowedChildren();\n  }\n  insertAt(index, value, def) {\n    super.insertAt(index, value, def), this.enforceAllowedChildren();\n  }\n  optimize(context) {\n    super.optimize(context), this.children.length > 0 && this.next != null && this.checkMerge() && (this.next.moveChildren(this), this.next.remove());\n  }\n};\n_ContainerBlot.blotName = \"container\", _ContainerBlot.scope = Scope.BLOCK_BLOT;\nlet ContainerBlot = _ContainerBlot;\nconst ContainerBlot$1 = ContainerBlot;\nclass EmbedBlot extends LeafBlot$1 {\n  static formats(_domNode, _scroll) {\n  }\n  format(name, value) {\n    super.formatAt(0, this.length(), name, value);\n  }\n  formatAt(index, length, name, value) {\n    index === 0 && length === this.length() ? this.format(name, value) : super.formatAt(index, length, name, value);\n  }\n  formats() {\n    return this.statics.formats(this.domNode, this.scroll);\n  }\n}\nconst EmbedBlot$1 = EmbedBlot, OBSERVER_CONFIG = {\n  attributes: !0,\n  characterData: !0,\n  characterDataOldValue: !0,\n  childList: !0,\n  subtree: !0\n}, MAX_OPTIMIZE_ITERATIONS = 100, _ScrollBlot = class _ScrollBlot extends ParentBlot$1 {\n  constructor(registry, node) {\n    super(null, node), this.registry = registry, this.scroll = this, this.build(), this.observer = new MutationObserver((mutations) => {\n      this.update(mutations);\n    }), this.observer.observe(this.domNode, OBSERVER_CONFIG), this.attach();\n  }\n  create(input, value) {\n    return this.registry.create(this, input, value);\n  }\n  find(node, bubble = !1) {\n    const blot = this.registry.find(node, bubble);\n    return blot ? blot.scroll === this ? blot : bubble ? this.find(blot.scroll.domNode.parentNode, !0) : null : null;\n  }\n  query(query, scope = Scope.ANY) {\n    return this.registry.query(query, scope);\n  }\n  register(...definitions) {\n    return this.registry.register(...definitions);\n  }\n  build() {\n    this.scroll != null && super.build();\n  }\n  detach() {\n    super.detach(), this.observer.disconnect();\n  }\n  deleteAt(index, length) {\n    this.update(), index === 0 && length === this.length() ? this.children.forEach((child) => {\n      child.remove();\n    }) : super.deleteAt(index, length);\n  }\n  formatAt(index, length, name, value) {\n    this.update(), super.formatAt(index, length, name, value);\n  }\n  insertAt(index, value, def) {\n    this.update(), super.insertAt(index, value, def);\n  }\n  optimize(mutations = [], context = {}) {\n    super.optimize(context);\n    const mutationsMap = context.mutationsMap || /* @__PURE__ */ new WeakMap();\n    let records = Array.from(this.observer.takeRecords());\n    for (; records.length > 0; )\n      mutations.push(records.pop());\n    const mark = (blot, markParent = !0) => {\n      blot == null || blot === this || blot.domNode.parentNode != null && (mutationsMap.has(blot.domNode) || mutationsMap.set(blot.domNode, []), markParent && mark(blot.parent));\n    }, optimize = (blot) => {\n      mutationsMap.has(blot.domNode) && (blot instanceof ParentBlot$1 && blot.children.forEach(optimize), mutationsMap.delete(blot.domNode), blot.optimize(context));\n    };\n    let remaining = mutations;\n    for (let i = 0; remaining.length > 0; i += 1) {\n      if (i >= MAX_OPTIMIZE_ITERATIONS)\n        throw new Error(\"[Parchment] Maximum optimize iterations reached\");\n      for (remaining.forEach((mutation) => {\n        const blot = this.find(mutation.target, !0);\n        blot != null && (blot.domNode === mutation.target && (mutation.type === \"childList\" ? (mark(this.find(mutation.previousSibling, !1)), Array.from(mutation.addedNodes).forEach((node) => {\n          const child = this.find(node, !1);\n          mark(child, !1), child instanceof ParentBlot$1 && child.children.forEach((grandChild) => {\n            mark(grandChild, !1);\n          });\n        })) : mutation.type === \"attributes\" && mark(blot.prev)), mark(blot));\n      }), this.children.forEach(optimize), remaining = Array.from(this.observer.takeRecords()), records = remaining.slice(); records.length > 0; )\n        mutations.push(records.pop());\n    }\n  }\n  update(mutations, context = {}) {\n    mutations = mutations || this.observer.takeRecords();\n    const mutationsMap = /* @__PURE__ */ new WeakMap();\n    mutations.map((mutation) => {\n      const blot = this.find(mutation.target, !0);\n      return blot == null ? null : mutationsMap.has(blot.domNode) ? (mutationsMap.get(blot.domNode).push(mutation), null) : (mutationsMap.set(blot.domNode, [mutation]), blot);\n    }).forEach((blot) => {\n      blot != null && blot !== this && mutationsMap.has(blot.domNode) && blot.update(mutationsMap.get(blot.domNode) || [], context);\n    }), context.mutationsMap = mutationsMap, mutationsMap.has(this.domNode) && super.update(mutationsMap.get(this.domNode), context), this.optimize(mutations, context);\n  }\n};\n_ScrollBlot.blotName = \"scroll\", _ScrollBlot.defaultChild = BlockBlot$1, _ScrollBlot.allowedChildren = [BlockBlot$1, ContainerBlot$1], _ScrollBlot.scope = Scope.BLOCK_BLOT, _ScrollBlot.tagName = \"DIV\";\nlet ScrollBlot = _ScrollBlot;\nconst ScrollBlot$1 = ScrollBlot, _TextBlot = class _TextBlot extends LeafBlot$1 {\n  static create(value) {\n    return document.createTextNode(value);\n  }\n  static value(domNode) {\n    return domNode.data;\n  }\n  constructor(scroll, node) {\n    super(scroll, node), this.text = this.statics.value(this.domNode);\n  }\n  deleteAt(index, length) {\n    this.domNode.data = this.text = this.text.slice(0, index) + this.text.slice(index + length);\n  }\n  index(node, offset) {\n    return this.domNode === node ? offset : -1;\n  }\n  insertAt(index, value, def) {\n    def == null ? (this.text = this.text.slice(0, index) + value + this.text.slice(index), this.domNode.data = this.text) : super.insertAt(index, value, def);\n  }\n  length() {\n    return this.text.length;\n  }\n  optimize(context) {\n    super.optimize(context), this.text = this.statics.value(this.domNode), this.text.length === 0 ? this.remove() : this.next instanceof _TextBlot && this.next.prev === this && (this.insertAt(this.length(), this.next.value()), this.next.remove());\n  }\n  position(index, _inclusive = !1) {\n    return [this.domNode, index];\n  }\n  split(index, force = !1) {\n    if (!force) {\n      if (index === 0)\n        return this;\n      if (index === this.length())\n        return this.next;\n    }\n    const after = this.scroll.create(this.domNode.splitText(index));\n    return this.parent.insertBefore(after, this.next || void 0), this.text = this.statics.value(this.domNode), after;\n  }\n  update(mutations, _context) {\n    mutations.some((mutation) => mutation.type === \"characterData\" && mutation.target === this.domNode) && (this.text = this.statics.value(this.domNode));\n  }\n  value() {\n    return this.text;\n  }\n};\n_TextBlot.blotName = \"text\", _TextBlot.scope = Scope.INLINE_BLOT;\nlet TextBlot = _TextBlot;\nconst TextBlot$1 = TextBlot;\n\n//# sourceMappingURL=parchment.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/parchment/dist/parchment.js\n");

/***/ })

};
;