"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_monaco-editor_esm_vs_basic-languages_vb_vb_js"],{

/***/ "(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/vb/vb.js":
/*!********************************************************************!*\
  !*** ./node_modules/monaco-editor/esm/vs/basic-languages/vb/vb.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conf: function() { return /* binding */ conf; },\n/* harmony export */   language: function() { return /* binding */ language; }\n/* harmony export */ });\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/vb/vb.ts\nvar conf = {\n  comments: {\n    lineComment: \"'\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"<\", \">\"],\n    [\"addhandler\", \"end addhandler\"],\n    [\"class\", \"end class\"],\n    [\"enum\", \"end enum\"],\n    [\"event\", \"end event\"],\n    [\"function\", \"end function\"],\n    [\"get\", \"end get\"],\n    [\"if\", \"end if\"],\n    [\"interface\", \"end interface\"],\n    [\"module\", \"end module\"],\n    [\"namespace\", \"end namespace\"],\n    [\"operator\", \"end operator\"],\n    [\"property\", \"end property\"],\n    [\"raiseevent\", \"end raiseevent\"],\n    [\"removehandler\", \"end removehandler\"],\n    [\"select\", \"end select\"],\n    [\"set\", \"end set\"],\n    [\"structure\", \"end structure\"],\n    [\"sub\", \"end sub\"],\n    [\"synclock\", \"end synclock\"],\n    [\"try\", \"end try\"],\n    [\"while\", \"end while\"],\n    [\"with\", \"end with\"],\n    [\"using\", \"end using\"],\n    [\"do\", \"loop\"],\n    [\"for\", \"next\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"<\", close: \">\", notIn: [\"string\", \"comment\"] }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#Region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#End Region\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".vb\",\n  ignoreCase: true,\n  brackets: [\n    { token: \"delimiter.bracket\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.array\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.angle\", open: \"<\", close: \">\" },\n    // Special bracket statement pairs\n    // according to https://msdn.microsoft.com/en-us/library/tsw2a11z.aspx\n    {\n      token: \"keyword.tag-addhandler\",\n      open: \"addhandler\",\n      close: \"end addhandler\"\n    },\n    { token: \"keyword.tag-class\", open: \"class\", close: \"end class\" },\n    { token: \"keyword.tag-enum\", open: \"enum\", close: \"end enum\" },\n    { token: \"keyword.tag-event\", open: \"event\", close: \"end event\" },\n    {\n      token: \"keyword.tag-function\",\n      open: \"function\",\n      close: \"end function\"\n    },\n    { token: \"keyword.tag-get\", open: \"get\", close: \"end get\" },\n    { token: \"keyword.tag-if\", open: \"if\", close: \"end if\" },\n    {\n      token: \"keyword.tag-interface\",\n      open: \"interface\",\n      close: \"end interface\"\n    },\n    { token: \"keyword.tag-module\", open: \"module\", close: \"end module\" },\n    {\n      token: \"keyword.tag-namespace\",\n      open: \"namespace\",\n      close: \"end namespace\"\n    },\n    {\n      token: \"keyword.tag-operator\",\n      open: \"operator\",\n      close: \"end operator\"\n    },\n    {\n      token: \"keyword.tag-property\",\n      open: \"property\",\n      close: \"end property\"\n    },\n    {\n      token: \"keyword.tag-raiseevent\",\n      open: \"raiseevent\",\n      close: \"end raiseevent\"\n    },\n    {\n      token: \"keyword.tag-removehandler\",\n      open: \"removehandler\",\n      close: \"end removehandler\"\n    },\n    { token: \"keyword.tag-select\", open: \"select\", close: \"end select\" },\n    { token: \"keyword.tag-set\", open: \"set\", close: \"end set\" },\n    {\n      token: \"keyword.tag-structure\",\n      open: \"structure\",\n      close: \"end structure\"\n    },\n    { token: \"keyword.tag-sub\", open: \"sub\", close: \"end sub\" },\n    {\n      token: \"keyword.tag-synclock\",\n      open: \"synclock\",\n      close: \"end synclock\"\n    },\n    { token: \"keyword.tag-try\", open: \"try\", close: \"end try\" },\n    { token: \"keyword.tag-while\", open: \"while\", close: \"end while\" },\n    { token: \"keyword.tag-with\", open: \"with\", close: \"end with\" },\n    // Other pairs\n    { token: \"keyword.tag-using\", open: \"using\", close: \"end using\" },\n    { token: \"keyword.tag-do\", open: \"do\", close: \"loop\" },\n    { token: \"keyword.tag-for\", open: \"for\", close: \"next\" }\n  ],\n  keywords: [\n    \"AddHandler\",\n    \"AddressOf\",\n    \"Alias\",\n    \"And\",\n    \"AndAlso\",\n    \"As\",\n    \"Async\",\n    \"Boolean\",\n    \"ByRef\",\n    \"Byte\",\n    \"ByVal\",\n    \"Call\",\n    \"Case\",\n    \"Catch\",\n    \"CBool\",\n    \"CByte\",\n    \"CChar\",\n    \"CDate\",\n    \"CDbl\",\n    \"CDec\",\n    \"Char\",\n    \"CInt\",\n    \"Class\",\n    \"CLng\",\n    \"CObj\",\n    \"Const\",\n    \"Continue\",\n    \"CSByte\",\n    \"CShort\",\n    \"CSng\",\n    \"CStr\",\n    \"CType\",\n    \"CUInt\",\n    \"CULng\",\n    \"CUShort\",\n    \"Date\",\n    \"Decimal\",\n    \"Declare\",\n    \"Default\",\n    \"Delegate\",\n    \"Dim\",\n    \"DirectCast\",\n    \"Do\",\n    \"Double\",\n    \"Each\",\n    \"Else\",\n    \"ElseIf\",\n    \"End\",\n    \"EndIf\",\n    \"Enum\",\n    \"Erase\",\n    \"Error\",\n    \"Event\",\n    \"Exit\",\n    \"False\",\n    \"Finally\",\n    \"For\",\n    \"Friend\",\n    \"Function\",\n    \"Get\",\n    \"GetType\",\n    \"GetXMLNamespace\",\n    \"Global\",\n    \"GoSub\",\n    \"GoTo\",\n    \"Handles\",\n    \"If\",\n    \"Implements\",\n    \"Imports\",\n    \"In\",\n    \"Inherits\",\n    \"Integer\",\n    \"Interface\",\n    \"Is\",\n    \"IsNot\",\n    \"Let\",\n    \"Lib\",\n    \"Like\",\n    \"Long\",\n    \"Loop\",\n    \"Me\",\n    \"Mod\",\n    \"Module\",\n    \"MustInherit\",\n    \"MustOverride\",\n    \"MyBase\",\n    \"MyClass\",\n    \"NameOf\",\n    \"Namespace\",\n    \"Narrowing\",\n    \"New\",\n    \"Next\",\n    \"Not\",\n    \"Nothing\",\n    \"NotInheritable\",\n    \"NotOverridable\",\n    \"Object\",\n    \"Of\",\n    \"On\",\n    \"Operator\",\n    \"Option\",\n    \"Optional\",\n    \"Or\",\n    \"OrElse\",\n    \"Out\",\n    \"Overloads\",\n    \"Overridable\",\n    \"Overrides\",\n    \"ParamArray\",\n    \"Partial\",\n    \"Private\",\n    \"Property\",\n    \"Protected\",\n    \"Public\",\n    \"RaiseEvent\",\n    \"ReadOnly\",\n    \"ReDim\",\n    \"RemoveHandler\",\n    \"Resume\",\n    \"Return\",\n    \"SByte\",\n    \"Select\",\n    \"Set\",\n    \"Shadows\",\n    \"Shared\",\n    \"Short\",\n    \"Single\",\n    \"Static\",\n    \"Step\",\n    \"Stop\",\n    \"String\",\n    \"Structure\",\n    \"Sub\",\n    \"SyncLock\",\n    \"Then\",\n    \"Throw\",\n    \"To\",\n    \"True\",\n    \"Try\",\n    \"TryCast\",\n    \"TypeOf\",\n    \"UInteger\",\n    \"ULong\",\n    \"UShort\",\n    \"Using\",\n    \"Variant\",\n    \"Wend\",\n    \"When\",\n    \"While\",\n    \"Widening\",\n    \"With\",\n    \"WithEvents\",\n    \"WriteOnly\",\n    \"Xor\"\n  ],\n  tagwords: [\n    \"If\",\n    \"Sub\",\n    \"Select\",\n    \"Try\",\n    \"Class\",\n    \"Enum\",\n    \"Function\",\n    \"Get\",\n    \"Interface\",\n    \"Module\",\n    \"Namespace\",\n    \"Operator\",\n    \"Set\",\n    \"Structure\",\n    \"Using\",\n    \"While\",\n    \"With\",\n    \"Do\",\n    \"Loop\",\n    \"For\",\n    \"Next\",\n    \"Property\",\n    \"Continue\",\n    \"AddHandler\",\n    \"RemoveHandler\",\n    \"Event\",\n    \"RaiseEvent\",\n    \"SyncLock\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?;\\.,:&|+\\-*\\/\\^%]+/,\n  integersuffix: /U?[DI%L&S@]?/,\n  floatsuffix: /[R#F!]?/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // whitespace\n      { include: \"@whitespace\" },\n      // special ending tag-words\n      [/next(?!\\w)/, { token: \"keyword.tag-for\" }],\n      [/loop(?!\\w)/, { token: \"keyword.tag-do\" }],\n      // usual ending tags\n      [\n        /end\\s+(?!for|do)(addhandler|class|enum|event|function|get|if|interface|module|namespace|operator|property|raiseevent|removehandler|select|set|structure|sub|synclock|try|while|with|using)/,\n        { token: \"keyword.tag-$1\" }\n      ],\n      // identifiers, tagwords, and keywords\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@tagwords\": { token: \"keyword.tag-$0\" },\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // Preprocessor directive\n      [/^\\s*#\\w+/, \"keyword\"],\n      // numbers\n      [/\\d*\\d+e([\\-+]?\\d+)?(@floatsuffix)/, \"number.float\"],\n      [/\\d*\\.\\d+(e[\\-+]?\\d+)?(@floatsuffix)/, \"number.float\"],\n      [/&H[0-9a-f]+(@integersuffix)/, \"number.hex\"],\n      [/&0[0-7]+(@integersuffix)/, \"number.octal\"],\n      [/\\d+(@integersuffix)/, \"number\"],\n      // date literal\n      [/#.*#/, \"number\"],\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      // strings\n      [/[\"\\u201c\\u201d]/, { token: \"string.quote\", next: \"@string\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/(\\'|REM(?!\\w)).*$/, \"comment\"]\n    ],\n    string: [\n      [/[^\"\\u201c\\u201d]+/, \"string\"],\n      [/[\"\\u201c\\u201d]{2}/, \"string.escape\"],\n      [/[\"\\u201c\\u201d]C?/, { token: \"string.quote\", next: \"@pop\" }]\n    ]\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/monaco-editor/esm/vs/basic-languages/vb/vb.js\n"));

/***/ })

}]);